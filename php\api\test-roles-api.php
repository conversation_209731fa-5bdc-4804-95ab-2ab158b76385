<?php
/**
 * Test Roles API
 * Tests the roles API endpoint to ensure it works properly
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Error handling
ini_set('display_errors', 1);
error_reporting(E_ALL);

$result = [
    'success' => false,
    'message' => '',
    'tests' => [],
    'timestamp' => date('Y-m-d H:i:s')
];

try {
    require_once '../config/database.php';
    
    // Test 1: Database connection
    $connectionTest = testDatabaseConnection();
    $result['tests'][] = [
        'name' => 'Database Connection',
        'status' => $connectionTest['success'] ? 'pass' : 'fail',
        'message' => $connectionTest['message']
    ];
    
    if (!$connectionTest['success']) {
        $result['message'] = 'Database connection failed';
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $pdo = getDatabaseConnection();
    
    // Test 2: Check if roles table exists
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
        $tableExists = $stmt->rowCount() > 0;
        
        $result['tests'][] = [
            'name' => 'Roles Table Exists',
            'status' => $tableExists ? 'pass' : 'fail',
            'message' => $tableExists ? 'Roles table found' : 'Roles table not found'
        ];
        
        if (!$tableExists) {
            $result['message'] = 'Roles table does not exist';
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            exit;
        }
        
    } catch (Exception $e) {
        $result['tests'][] = [
            'name' => 'Roles Table Check',
            'status' => 'fail',
            'message' => 'Error checking table: ' . $e->getMessage()
        ];
    }
    
    // Test 3: Check table structure
    try {
        $stmt = $pdo->query("DESCRIBE roles");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $columnNames = array_column($columns, 'Field');
        
        $requiredColumns = ['id', 'name', 'display_name', 'display_name_ar'];
        $missingColumns = array_diff($requiredColumns, $columnNames);
        
        $result['tests'][] = [
            'name' => 'Table Structure',
            'status' => empty($missingColumns) ? 'pass' : 'fail',
            'message' => empty($missingColumns) ? 
                'All required columns present: ' . implode(', ', $columnNames) :
                'Missing columns: ' . implode(', ', $missingColumns)
        ];
        
    } catch (Exception $e) {
        $result['tests'][] = [
            'name' => 'Table Structure Check',
            'status' => 'fail',
            'message' => 'Error checking structure: ' . $e->getMessage()
        ];
    }
    
    // Test 4: Test basic SELECT query
    try {
        $stmt = $pdo->query("SELECT id, name, display_name, display_name_ar FROM roles LIMIT 5");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['tests'][] = [
            'name' => 'Basic SELECT Query',
            'status' => 'pass',
            'message' => 'Found ' . count($roles) . ' roles',
            'data' => $roles
        ];
        
    } catch (Exception $e) {
        $result['tests'][] = [
            'name' => 'Basic SELECT Query',
            'status' => 'fail',
            'message' => 'Query failed: ' . $e->getMessage()
        ];
    }
    
    // Test 5: Test INSERT query (with rollback)
    try {
        $pdo->beginTransaction();
        
        $stmt = $pdo->prepare("INSERT INTO roles (name, display_name, display_name_ar) VALUES (?, ?, ?)");
        $stmt->execute(['test_role', 'Test Role', 'دور تجريبي']);
        
        $insertId = $pdo->lastInsertId();
        
        // Rollback the test insert
        $pdo->rollback();
        
        $result['tests'][] = [
            'name' => 'INSERT Query Test',
            'status' => 'pass',
            'message' => 'INSERT query successful (rolled back), ID would be: ' . $insertId
        ];
        
    } catch (Exception $e) {
        $pdo->rollback();
        $result['tests'][] = [
            'name' => 'INSERT Query Test',
            'status' => 'fail',
            'message' => 'INSERT failed: ' . $e->getMessage()
        ];
    }
    
    // Test 6: Test the actual roles API endpoint
    try {
        // Simulate API call
        $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/roles.php';
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);
        
        $apiResponse = @file_get_contents($apiUrl, false, $context);
        
        if ($apiResponse !== false) {
            $apiData = json_decode($apiResponse, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                $result['tests'][] = [
                    'name' => 'Roles API Endpoint',
                    'status' => isset($apiData['success']) && $apiData['success'] ? 'pass' : 'fail',
                    'message' => isset($apiData['success']) && $apiData['success'] ? 
                        'API returned success with ' . (isset($apiData['roles']) ? count($apiData['roles']) : 0) . ' roles' :
                        'API returned error: ' . ($apiData['message'] ?? 'Unknown error'),
                    'api_response' => $apiData
                ];
            } else {
                $result['tests'][] = [
                    'name' => 'Roles API Endpoint',
                    'status' => 'fail',
                    'message' => 'API returned invalid JSON: ' . $apiResponse
                ];
            }
        } else {
            $result['tests'][] = [
                'name' => 'Roles API Endpoint',
                'status' => 'fail',
                'message' => 'Could not reach API endpoint: ' . $apiUrl
            ];
        }
        
    } catch (Exception $e) {
        $result['tests'][] = [
            'name' => 'Roles API Endpoint',
            'status' => 'fail',
            'message' => 'API test error: ' . $e->getMessage()
        ];
    }
    
    // Calculate overall result
    $passedTests = array_filter($result['tests'], function($test) {
        return $test['status'] === 'pass';
    });
    
    $totalTests = count($result['tests']);
    $passedCount = count($passedTests);
    
    $result['success'] = $passedCount === $totalTests;
    $result['message'] = "Passed $passedCount out of $totalTests tests";
    $result['summary'] = [
        'total' => $totalTests,
        'passed' => $passedCount,
        'failed' => $totalTests - $passedCount,
        'success_rate' => round(($passedCount / $totalTests) * 100, 2) . '%'
    ];
    
} catch (Exception $e) {
    $result['message'] = 'Test failed with exception: ' . $e->getMessage();
    $result['tests'][] = [
        'name' => 'General Test',
        'status' => 'fail',
        'message' => 'Exception: ' . $e->getMessage()
    ];
}

echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
