<?php
/**
 * Comprehensive Feature Testing
 * Tests all implemented features for the Mossaab Landing Page system
 */

require_once 'php/config.php';
require_once 'php/SubscriptionLimits.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار شامل للميزات المطورة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .task-section { margin: 30px 0; padding: 20px; border: 2px solid #007bff; border-radius: 10px; background: #f8f9fa; }
        h1, h2, h3 { color: #333; }
        .test-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .feature-list { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 15px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .progress-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 5px 0; }
        .progress-fill { background: #28a745; height: 100%; transition: width 0.3s ease; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: right; border: 1px solid #ddd; }
        th { background: #f8f9fa; }
        .link-list { list-style: none; padding: 0; }
        .link-list li { margin: 5px 0; }
        .link-list a { color: #007bff; text-decoration: none; }
        .link-list a:hover { text-decoration: underline; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🧪 اختبار شامل للميزات المطورة - نظام مصعب للمتاجر المتعددة</h1>";

$testResults = [];
$overallSuccess = true;

try {
    $pdo = getPDOConnection();
    
    // TASK 1: Test Subscription-Based Limits
    echo "<div class='task-section'>";
    echo "<h2>📋 المهمة 1: نظام حدود الاشتراك</h2>";
    
    echo "<div class='section'>";
    echo "<h3>1.1 اختبار فئة SubscriptionLimits</h3>";
    
    try {
        $limitsManager = new SubscriptionLimits();
        
        // Get demo user
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $demoUser = $stmt->fetch();
        
        if ($demoUser) {
            $userId = $demoUser['id'];
            
            // Test limits and usage
            $limitsAndUsage = $limitsManager->getUserLimitsAndUsage($userId);
            
            echo "<div class='success'>✅ فئة SubscriptionLimits تعمل بشكل صحيح</div>";
            
            echo "<div class='stats-grid'>";
            echo "<div class='stat-card'>";
            echo "<h4>📦 المنتجات</h4>";
            echo "<p>المستخدم: {$limitsAndUsage['usage']['products']} / {$limitsAndUsage['limits']['products']}</p>";
            $productPercent = ($limitsAndUsage['usage']['products'] / $limitsAndUsage['limits']['products']) * 100;
            echo "<div class='progress-bar'><div class='progress-fill' style='width: {$productPercent}%'></div></div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<h4>🌐 صفحات الهبوط</h4>";
            echo "<p>المستخدم: {$limitsAndUsage['usage']['landing_pages']} / {$limitsAndUsage['limits']['landing_pages']}</p>";
            $lpPercent = ($limitsAndUsage['usage']['landing_pages'] / $limitsAndUsage['limits']['landing_pages']) * 100;
            echo "<div class='progress-bar'><div class='progress-fill' style='width: {$lpPercent}%'></div></div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<h4>📂 الفئات</h4>";
            echo "<p>المستخدم: {$limitsAndUsage['usage']['categories']} / {$limitsAndUsage['limits']['categories']}</p>";
            $catPercent = ($limitsAndUsage['usage']['categories'] / $limitsAndUsage['limits']['categories']) * 100;
            echo "<div class='progress-bar'><div class='progress-fill' style='width: {$catPercent}%'></div></div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<h4>💾 التخزين</h4>";
            echo "<p>المستخدم: {$limitsAndUsage['usage']['storage_mb']} / {$limitsAndUsage['limits']['storage_mb']} ميجابايت</p>";
            $storagePercent = ($limitsAndUsage['usage']['storage_mb'] / $limitsAndUsage['limits']['storage_mb']) * 100;
            echo "<div class='progress-bar'><div class='progress-fill' style='width: {$storagePercent}%'></div></div>";
            echo "</div>";
            echo "</div>";
            
            $testResults['subscription_limits'] = true;
        } else {
            throw new Exception('Demo user not found');
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في نظام حدود الاشتراك: " . $e->getMessage() . "</div>";
        $testResults['subscription_limits'] = false;
        $overallSuccess = false;
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h3>1.2 اختبار API حدود الاشتراك</h3>";
    
    // Test subscription limits API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/subscription-limits.php?action=limits');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $apiData = json_decode($response, true);
        if ($apiData && $apiData['success']) {
            echo "<div class='success'>✅ API حدود الاشتراك يعمل بشكل صحيح</div>";
            $testResults['subscription_api'] = true;
        } else {
            echo "<div class='error'>❌ API حدود الاشتراك يعيد خطأ</div>";
            $testResults['subscription_api'] = false;
            $overallSuccess = false;
        }
    } else {
        echo "<div class='warning'>⚠️ لا يمكن الوصول إلى API حدود الاشتراك (HTTP: {$httpCode})</div>";
        $testResults['subscription_api'] = false;
    }
    echo "</div>";
    echo "</div>";
    
    // TASK 2: Test Demo Landing Pages
    echo "<div class='task-section'>";
    echo "<h2>🌐 المهمة 2: صفحات الهبوط التجريبية</h2>";
    
    echo "<div class='section'>";
    echo "<h3>2.1 فحص صفحات الهبوط للمستخدم التجريبي</h3>";
    
    $stmt = $pdo->prepare("
        SELECT lp.*, p.titre as product_title, p.type as product_type
        FROM landing_pages lp
        LEFT JOIN produits p ON lp.produit_id = p.id
        LEFT JOIN users u ON lp.store_id = u.store_id
        WHERE u.email = ?
    ");
    $stmt->execute(['<EMAIL>']);
    $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($landingPages) >= 5) {
        echo "<div class='success'>✅ تم العثور على " . count($landingPages) . " صفحة هبوط للمستخدم التجريبي</div>";
        
        echo "<table>";
        echo "<tr><th>ID</th><th>العنوان</th><th>المنتج</th><th>النوع</th><th>القالب</th><th>الرابط</th></tr>";
        
        foreach (array_slice($landingPages, 0, 5) as $page) {
            echo "<tr>";
            echo "<td>{$page['id']}</td>";
            echo "<td>{$page['titre']}</td>";
            echo "<td>{$page['product_title']}</td>";
            echo "<td>{$page['product_type']}</td>";
            echo "<td>{$page['template_id']}</td>";
            echo "<td><a href='{$page['lien_url']}' target='_blank'>عرض</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $testResults['demo_landing_pages'] = true;
    } else {
        echo "<div class='error'>❌ عدد صفحات الهبوط غير كافي (" . count($landingPages) . "/5)</div>";
        $testResults['demo_landing_pages'] = false;
        $overallSuccess = false;
    }
    echo "</div>";
    echo "</div>";
    
    // TASK 3: Test User Management
    echo "<div class='task-section'>";
    echo "<h2>👥 المهمة 3: إدارة المستخدمين</h2>";
    
    echo "<div class='section'>";
    echo "<h3>3.1 اختبار API إدارة المستخدمين</h3>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/users.php?action=list');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $usersData = json_decode($response, true);
        if ($usersData && $usersData['success'] && count($usersData['users']) >= 2) {
            echo "<div class='success'>✅ API إدارة المستخدمين يعمل بشكل صحيح</div>";
            echo "<div class='info'>📊 عدد المستخدمين: " . count($usersData['users']) . "</div>";
            
            // Check for admin and demo users
            $hasAdmin = false;
            $hasDemo = false;
            
            foreach ($usersData['users'] as $user) {
                if (strpos($user['email'], 'admin') !== false) $hasAdmin = true;
                if (strpos($user['email'], 'demo') !== false) $hasDemo = true;
            }
            
            if ($hasAdmin && $hasDemo) {
                echo "<div class='success'>✅ تم العثور على المستخدم الإداري والتجريبي</div>";
                $testResults['user_management'] = true;
            } else {
                echo "<div class='warning'>⚠️ لم يتم العثور على جميع المستخدمين المطلوبين</div>";
                $testResults['user_management'] = false;
            }
        } else {
            echo "<div class='error'>❌ API إدارة المستخدمين لا يعيد البيانات المطلوبة</div>";
            $testResults['user_management'] = false;
            $overallSuccess = false;
        }
    } else {
        echo "<div class='warning'>⚠️ لا يمكن الوصول إلى API إدارة المستخدمين (HTTP: {$httpCode})</div>";
        $testResults['user_management'] = false;
    }
    echo "</div>";
    echo "</div>";
    
    // TASK 4: Test Demo User Login
    echo "<div class='task-section'>";
    echo "<h2>🔐 المهمة 4: نظام دخول المستخدم التجريبي</h2>";
    
    echo "<div class='section'>";
    echo "<h3>4.1 اختبار تسجيل الدخول</h3>";
    
    $loginData = [
        'email' => '<EMAIL>',
        'password' => 'demo123'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/user-auth.php?action=login');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $authData = json_decode($response, true);
        if ($authData && $authData['success']) {
            echo "<div class='success'>✅ تسجيل الدخول للمستخدم التجريبي يعمل بشكل صحيح</div>";
            echo "<div class='test-result'>";
            echo "<h4>👤 بيانات المستخدم:</h4>";
            echo "<ul>";
            echo "<li>ID: {$authData['user']['id']}</li>";
            echo "<li>البريد الإلكتروني: {$authData['user']['email']}</li>";
            echo "<li>الاسم: {$authData['user']['name']}</li>";
            echo "<li>الدور: {$authData['user']['role_id']}</li>";
            echo "</ul>";
            echo "</div>";
            $testResults['demo_login'] = true;
        } else {
            echo "<div class='error'>❌ فشل تسجيل الدخول: " . ($authData['message'] ?? 'خطأ غير معروف') . "</div>";
            $testResults['demo_login'] = false;
            $overallSuccess = false;
        }
    } else {
        echo "<div class='warning'>⚠️ لا يمكن الوصول إلى API المصادقة (HTTP: {$httpCode})</div>";
        $testResults['demo_login'] = false;
    }
    echo "</div>";
    echo "</div>";
    
    // Overall Results
    echo "<div class='task-section'>";
    echo "<h2>📊 ملخص النتائج الشامل</h2>";
    
    $successCount = array_sum($testResults);
    $totalTests = count($testResults);
    $successRate = ($successCount / $totalTests) * 100;
    
    if ($overallSuccess && $successRate >= 80) {
        echo "<div class='success'>";
        echo "<h3>🎉 نجح الاختبار الشامل!</h3>";
        echo "<p>تم تنفيذ جميع الميزات المطلوبة بنجاح ونسبة النجاح: {$successRate}%</p>";
        echo "</div>";
    } else {
        echo "<div class='warning'>";
        echo "<h3>⚠️ الاختبار مكتمل مع بعض التحذيرات</h3>";
        echo "<p>نسبة النجاح: {$successRate}% ({$successCount}/{$totalTests} اختبارات نجحت)</p>";
        echo "</div>";
    }
    
    echo "<div class='feature-list'>";
    echo "<h4>✅ الميزات المنجزة:</h4>";
    echo "<ul>";
    if ($testResults['subscription_limits']) echo "<li>✅ نظام حدود الاشتراك للمتاجر</li>";
    if ($testResults['demo_landing_pages']) echo "<li>✅ صفحات الهبوط التجريبية (5 صفحات)</li>";
    if ($testResults['user_management']) echo "<li>✅ إدارة المستخدمين في لوحة التحكم</li>";
    if ($testResults['demo_login']) echo "<li>✅ نظام دخول المستخدم التجريبي</li>";
    echo "</ul>";
    echo "</div>";
    
    if (!$overallSuccess) {
        echo "<div class='feature-list'>";
        echo "<h4>⚠️ الميزات التي تحتاج مراجعة:</h4>";
        echo "<ul>";
        if (!$testResults['subscription_limits']) echo "<li>❌ نظام حدود الاشتراك</li>";
        if (!$testResults['demo_landing_pages']) echo "<li>❌ صفحات الهبوط التجريبية</li>";
        if (!$testResults['user_management']) echo "<li>❌ إدارة المستخدمين</li>";
        if (!$testResults['demo_login']) echo "<li>❌ نظام دخول المستخدم التجريبي</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div class='info'>";
    echo "<h4>🔗 روابط مفيدة للاختبار:</h4>";
    echo "<ul class='link-list'>";
    echo "<li><a href='/admin/' target='_blank'>لوحة التحكم الإدارية</a></li>";
    echo "<li><a href='/login.html' target='_blank'>صفحة تسجيل الدخول</a></li>";
    echo "<li><a href='/dashboard/' target='_blank'>لوحة تحكم المتجر</a></li>";
    echo "<li><a href='/store.php?slug=mossaab-demo-store' target='_blank'>المتجر التجريبي</a></li>";
    echo "<li><a href='/php/api/users.php?action=list' target='_blank'>API إدارة المستخدمين</a></li>";
    echo "<li><a href='/php/api/subscription-limits.php?action=limits' target='_blank'>API حدود الاشتراك</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔑 بيانات الدخول للاختبار:</h4>";
    echo "<ul>";
    echo "<li><strong>المستخدم التجريبي:</strong> <EMAIL> / demo123</li>";
    echo "<li><strong>المدير:</strong> <EMAIL> / admin123</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في الاختبار الشامل: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
