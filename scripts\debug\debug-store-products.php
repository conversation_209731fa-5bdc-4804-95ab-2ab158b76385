<?php
require_once __DIR__ . '/../php/config.php';

echo "🔍 DEBUGGING STORE PRODUCTS DISPLAY\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    $pdo = getPDOConnection();
    
    // Simulate the exact store.php logic
    $storeSlug = 'mossaab-store';
    
    echo "📋 Step 1: Get Store Information\n";
    echo "-" . str_repeat("-", 30) . "\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            s.*,
            CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
            u.email as owner_email,
            u.phone as owner_phone
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.store_slug = ? AND s.status = 'active'
    ");
    $stmt->execute([$storeSlug]);
    $store = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$store) {
        echo "❌ Store not found!\n";
        exit;
    }
    
    echo "✅ Store found:\n";
    echo "   Store ID: {$store['id']}\n";
    echo "   Store Name: {$store['store_name']}\n";
    echo "   Store Slug: {$store['store_slug']}\n";
    echo "   Status: {$store['status']}\n\n";
    
    echo "📦 Step 2: Get Store Products\n";
    echo "-" . str_repeat("-", 30) . "\n";
    
    $stmt = $pdo->prepare("
        SELECT p.*
        FROM produits p
        WHERE (p.store_id = ? OR p.store_id IS NULL) AND p.actif = 1
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$store['id']]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Query executed with store_id = {$store['id']}\n";
    echo "Products found: " . count($products) . "\n\n";
    
    if (count($products) > 0) {
        echo "📋 Products Details:\n";
        foreach ($products as $index => $product) {
            echo "   " . ($index + 1) . ". {$product['titre']}\n";
            echo "      ID: {$product['id']}\n";
            echo "      Store ID: " . ($product['store_id'] ?? 'NULL') . "\n";
            echo "      Active: {$product['actif']}\n";
            echo "      Price: {$product['prix']} DZD\n";
            echo "      Description: " . substr(strip_tags($product['description'] ?? ''), 0, 50) . "...\n";
            echo "      Image URL: " . ($product['image_url'] ?? 'NULL') . "\n\n";
        }
    } else {
        echo "❌ No products found!\n";
    }
    
    echo "🌐 Step 3: Test Store Page Generation\n";
    echo "-" . str_repeat("-", 35) . "\n";
    
    // Simulate the HTML generation
    if (!empty($products)) {
        echo "✅ Products section will be displayed\n";
        echo "   Products grid will contain " . count($products) . " items\n";
        
        // Test first product HTML generation
        $firstProduct = $products[0];
        echo "\n📋 Sample Product HTML:\n";
        echo "   Title: " . htmlspecialchars($firstProduct['titre']) . "\n";
        echo "   Price: " . number_format($firstProduct['prix'], 0, '.', ',') . " DZD\n";
        echo "   Image: " . htmlspecialchars($firstProduct['image_url'] ?? 'images/default-product.jpg') . "\n";
        
        if (!empty($firstProduct['description'])) {
            $desc = htmlspecialchars(substr($firstProduct['description'], 0, 100));
            echo "   Description: " . $desc . "\n";
        }
    } else {
        echo "❌ Empty state will be displayed\n";
    }
    
    echo "\n🔍 Step 4: Check for Potential Issues\n";
    echo "-" . str_repeat("-", 35) . "\n";
    
    // Check if products have required fields
    $issues = [];
    
    foreach ($products as $product) {
        if (empty($product['titre'])) {
            $issues[] = "Product ID {$product['id']} has empty title";
        }
        if (empty($product['prix']) || $product['prix'] <= 0) {
            $issues[] = "Product ID {$product['id']} has invalid price";
        }
    }
    
    if (empty($issues)) {
        echo "✅ All products have valid data\n";
    } else {
        echo "⚠️ Issues found:\n";
        foreach ($issues as $issue) {
            echo "   • {$issue}\n";
        }
    }
    
    echo "\n🎯 DIAGNOSIS SUMMARY\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    if (count($products) > 0) {
        echo "✅ Store products query is working correctly\n";
        echo "✅ Found " . count($products) . " products for the store\n";
        echo "✅ Products have valid data structure\n\n";
        
        echo "🔗 The issue might be:\n";
        echo "   1. CSS styling hiding the products\n";
        echo "   2. JavaScript interfering with display\n";
        echo "   3. Browser caching old content\n";
        echo "   4. Server-side output buffering\n\n";
        
        echo "📋 RECOMMENDED ACTIONS:\n";
        echo "   1. Clear browser cache and refresh\n";
        echo "   2. Check browser developer tools for CSS issues\n";
        echo "   3. View page source to see if products are in HTML\n";
        echo "   4. Test with different browser\n";
    } else {
        echo "❌ No products found for the store\n";
        echo "   This explains why the store page shows empty state\n\n";
        
        echo "📋 RECOMMENDED ACTIONS:\n";
        echo "   1. Check if products were properly added to database\n";
        echo "   2. Verify store_id associations\n";
        echo "   3. Check product actif status\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
