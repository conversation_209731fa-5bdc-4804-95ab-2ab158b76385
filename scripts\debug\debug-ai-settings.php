<?php
// Debug script to test AI Settings API
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== AI Settings Debug ===\n";

try {
    echo "1. Testing bootstrap...\n";
    require_once 'config/bootstrap.php';
    echo "✅ Bootstrap loaded successfully\n";
    
    echo "2. Testing AIManager include...\n";
    require_once 'php/AIManager.php';
    echo "✅ AIManager included successfully\n";
    
    echo "3. Testing AIManager instantiation...\n";
    $aiManager = AIManager::getInstance();
    echo "✅ AIManager instantiated successfully\n";
    
    echo "4. Testing provider config...\n";
    $config = $aiManager->getProviderConfig('openai');
    echo "✅ Provider config retrieved: " . print_r($config, true) . "\n";
    
    echo "5. Testing get-ai-settings.php directly...\n";
    
    // Capture output from get-ai-settings.php
    ob_start();
    include 'api/get-ai-settings.php';
    $output = ob_get_clean();
    
    echo "API Output: " . $output . "\n";
    
    // Test if it's valid JSON
    $decoded = json_decode($output, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ Valid JSON returned\n";
        echo "Data: " . print_r($decoded, true) . "\n";
    } else {
        echo "❌ Invalid JSON: " . json_last_error_msg() . "\n";
        echo "Raw output: " . $output . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
