<?php
/**
 * Environment Variables Loader
 * Loads configuration from .env file
 */

class EnvLoader {
    private static $loaded = false;
    private static $config = [];
    
    public static function load($envPath = null) {
        if (self::$loaded) {
            return self::$config;
        }
        
        // Try different paths for .env file
        $possiblePaths = [
            $envPath,
            __DIR__ . '/../../.env',
            __DIR__ . '/../.env',
            dirname(__DIR__, 2) . '/.env',
            '.env'
        ];
        
        $envFile = null;
        foreach ($possiblePaths as $path) {
            if ($path && file_exists($path)) {
                $envFile = $path;
                break;
            }
        }
        
        if (!$envFile) {
            throw new Exception('.env file not found');
        }
        
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // <PERSON>p comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
                    (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
                    $value = substr($value, 1, -1);
                }
                
                self::$config[$key] = $value;
                
                // Also set as environment variable
                if (!isset($_ENV[$key])) {
                    $_ENV[$key] = $value;
                    putenv("$key=$value");
                }
            }
        }
        
        self::$loaded = true;
        return self::$config;
    }
    
    public static function get($key, $default = null) {
        if (!self::$loaded) {
            self::load();
        }
        
        return self::$config[$key] ?? $default;
    }
    
    public static function getDatabaseConfig() {
        if (!self::$loaded) {
            self::load();
        }
        
        return [
            'host' => self::get('DB_HOST', 'localhost'),
            'port' => self::get('DB_PORT', '3306'),
            'database' => self::get('DB_DATABASE', ''),
            'username' => self::get('DB_USERNAME', 'root'),
            'password' => self::get('DB_PASSWORD', ''),
            'charset' => self::get('DB_CHARSET', 'utf8mb4')
        ];
    }
    
    public static function createDatabaseConnection() {
        $config = self::getDatabaseConfig();
        
        if (empty($config['database'])) {
            throw new Exception('Database name not configured in .env file');
        }
        
        $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
        
        return new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
        ]);
    }
}
?>
