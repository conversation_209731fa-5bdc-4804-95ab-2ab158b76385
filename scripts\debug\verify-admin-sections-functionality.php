<?php
/**
 * Verify Admin Sections Functionality
 * Vérification complète de la fonctionnalité de toutes les sections de l'admin
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>التحقق من وظائف أقسام لوحة التحكم</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .verification-passed { border: 3px solid #28a745; background: #d4edda; }
        .verification-failed { border: 3px solid #dc3545; background: #f8d7da; }
        h1, h2, h3 { color: #333; }
        .check-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .check-failed { border-left-color: #dc3545; }
        .section-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 15px; margin: 20px 0; }
        .section-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .section-card.functional { border-left-color: #28a745; background: #d4edda; }
        .section-card.non-functional { border-left-color: #dc3545; background: #f8d7da; }
        .section-card.partial { border-left-color: #ffc107; background: #fff3cd; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔍 التحقق من وظائف أقسام لوحة التحكم</h1>";
echo "<p>فحص شامل لجميع أقسام الشريط الجانبي والتأكد من أنها قابلة للنقر وتعمل بشكل صحيح</p>";

$verificationResults = [];
$totalSections = 0;
$functionalSections = 0;

try {
    // VERIFICATION 1: Check Admin HTML Structure
    echo "<div class='section verification-passed'>";
    echo "<h2>✅ التحقق 1: فحص هيكل HTML للوحة التحكم</h2>";
    
    echo "<div class='check-result'>";
    echo "<h4>فحص admin/index.html للأقسام المطلوبة</h4>";
    
    if (file_exists('admin/index.html')) {
        $adminContent = file_get_contents('admin/index.html');
        
        $expectedSections = [
            'dashboard' => ['الرئيسية', 'dashboard', 'home'],
            'products' => ['إدارة المنتجات', 'products', 'منتجات'],
            'orders' => ['الطلبات', 'orders', 'طلبات'],
            'landing-pages' => ['صفحات هبوط', 'landing', 'هبوط'],
            'reports' => ['التقارير', 'reports', 'إحصائيات'],
            'settings' => ['إعدادات', 'settings', 'النظام'],
            'logout' => ['تسجيل الخروج', 'logout', 'خروج']
        ];
        
        echo "<div class='section-grid'>";
        foreach ($expectedSections as $sectionKey => $keywords) {
            echo "<div class='section-card";
            
            $totalSections++;
            $sectionFound = false;
            $sectionClickable = false;
            
            // Check if section exists in HTML
            foreach ($keywords as $keyword) {
                if (stripos($adminContent, $keyword) !== false) {
                    $sectionFound = true;
                    break;
                }
            }
            
            // Check for clickable elements
            if ($sectionFound) {
                $clickablePatterns = [
                    'onclick=',
                    'data-section=',
                    'data-target=',
                    'href=',
                    'addEventListener'
                ];
                
                foreach ($clickablePatterns as $pattern) {
                    if (stripos($adminContent, $pattern) !== false) {
                        $sectionClickable = true;
                        break;
                    }
                }
            }
            
            if ($sectionFound && $sectionClickable) {
                echo " functional'>";
                echo "<h4>✅ {$keywords[0]}</h4>";
                echo "<p>القسم: {$sectionKey}</p>";
                echo "<div class='success'>موجود وقابل للنقر</div>";
                $verificationResults[$sectionKey] = 'functional';
                $functionalSections++;
            } elseif ($sectionFound) {
                echo " partial'>";
                echo "<h4>⚠️ {$keywords[0]}</h4>";
                echo "<p>القسم: {$sectionKey}</p>";
                echo "<div class='warning'>موجود لكن قد لا يكون قابل للنقر</div>";
                $verificationResults[$sectionKey] = 'partial';
            } else {
                echo " non-functional'>";
                echo "<h4>❌ {$keywords[0]}</h4>";
                echo "<p>القسم: {$sectionKey}</p>";
                echo "<div class='error'>غير موجود</div>";
                $verificationResults[$sectionKey] = 'non-functional';
            }
            
            echo "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div class='error'>❌ admin/index.html غير موجود</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // VERIFICATION 2: Check JavaScript Files for Section Handlers
    echo "<div class='section verification-passed'>";
    echo "<h2>✅ التحقق 2: فحص ملفات JavaScript لمعالجات الأقسام</h2>";
    
    echo "<div class='check-result'>";
    echo "<h4>فحص admin.js للوظائف المطلوبة</h4>";
    
    if (file_exists('admin/js/admin.js')) {
        $adminJsContent = file_get_contents('admin/js/admin.js');
        
        $expectedFunctions = [
            'loadDashboardContent' => 'تحميل لوحة المعلومات',
            'loadProductsContent' => 'تحميل إدارة المنتجات',
            'loadOrdersContent' => 'تحميل الطلبات',
            'loadLandingPagesContent' => 'تحميل صفحات الهبوط',
            'loadReportsContent' => 'تحميل التقارير',
            'loadSettingsContent' => 'تحميل الإعدادات',
            'logout' => 'تسجيل الخروج'
        ];
        
        foreach ($expectedFunctions as $func => $description) {
            if (strpos($adminJsContent, $func) !== false) {
                echo "<div class='success'>✅ {$description}: موجود</div>";
            } else {
                echo "<div class='warning'>⚠️ {$description}: غير موجود أو بأسم مختلف</div>";
            }
        }
        
    } else {
        echo "<div class='error'>❌ admin/js/admin.js غير موجود</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // VERIFICATION 3: Check CSS for Section Styling
    echo "<div class='section verification-passed'>";
    echo "<h2>✅ التحقق 3: فحص CSS لتنسيق الأقسام</h2>";
    
    echo "<div class='check-result'>";
    echo "<h4>فحص ملفات CSS للأنماط المطلوبة</h4>";
    
    $cssFiles = [
        'admin/css/admin.css' => 'أنماط لوحة التحكم الرئيسية',
        'admin/css/ultimate-visibility-fix.css' => 'إصلاح الظهور',
        'admin/css/admin-loading-fix.css' => 'إصلاح التحميل'
    ];
    
    foreach ($cssFiles as $cssFile => $description) {
        if (file_exists($cssFile)) {
            $cssContent = file_get_contents($cssFile);
            $size = filesize($cssFile);
            echo "<div class='success'>✅ {$description}: موجود - الحجم: " . number_format($size) . " بايت</div>";
            
            // Check for important CSS classes
            $importantClasses = ['.sidebar', '.nav-item', '.content-section', '.admin-section'];
            $foundClasses = 0;
            foreach ($importantClasses as $class) {
                if (strpos($cssContent, $class) !== false) {
                    $foundClasses++;
                }
            }
            
            if ($foundClasses > 0) {
                echo "<div class='info'>ℹ️ يحتوي على {$foundClasses} من الفئات المهمة</div>";
            }
        } else {
            echo "<div class='warning'>⚠️ {$description}: غير موجود</div>";
        }
    }
    
    echo "</div>";
    echo "</div>";
    
    // VERIFICATION 4: Check API Endpoints
    echo "<div class='section verification-passed'>";
    echo "<h2>✅ التحقق 4: فحص نقاط API المطلوبة</h2>";
    
    echo "<div class='check-result'>";
    echo "<h4>فحص ملفات API للأقسام</h4>";
    
    $apiFiles = [
        'php/api/products.php' => 'API المنتجات',
        'php/api/landing-pages.php' => 'API صفحات الهبوط',
        'php/api/orders.php' => 'API الطلبات',
        'php/api/users.php' => 'API المستخدمين',
        'php/api/reports.php' => 'API التقارير'
    ];
    
    foreach ($apiFiles as $apiFile => $description) {
        if (file_exists($apiFile)) {
            echo "<div class='success'>✅ {$description}: موجود</div>";
        } else {
            echo "<div class='warning'>⚠️ {$description}: غير موجود</div>";
        }
    }
    
    echo "</div>";
    echo "</div>";
    
    // FINAL VERIFICATION RESULTS
    $functionalityRate = ($functionalSections / $totalSections) * 100;
    
    echo "<div class='section'>";
    echo "<h2>📊 نتائج التحقق النهائية</h2>";
    
    if ($functionalityRate >= 85) {
        echo "<div class='success'>";
        echo "<h3>🎉 ممتاز! معظم الأقسام تعمل بشكل صحيح ({$functionalSections}/{$totalSections})</h3>";
        echo "<p>نسبة الوظائف العاملة: " . round($functionalityRate, 1) . "%</p>";
        echo "</div>";
    } elseif ($functionalityRate >= 60) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ جيد! بعض الأقسام تحتاج تحسين ({$functionalSections}/{$totalSections})</h3>";
        echo "<p>نسبة الوظائف العاملة: " . round($functionalityRate, 1) . "%</p>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ يحتاج النظام إلى إصلاحات كبيرة ({$functionalSections}/{$totalSections})</h3>";
        echo "<p>نسبة الوظائف العاملة: " . round($functionalityRate, 1) . "%</p>";
        echo "</div>";
    }
    
    echo "<div class='info'>";
    echo "<h3>🔗 اختبار الوظائف:</h3>";
    echo "<ul>";
    echo "<li><a href='/admin/' target='_blank'>فتح لوحة التحكم</a> - اختبار مباشر</li>";
    echo "<li><a href='/test-admin-sections-functionality.html' target='_blank'>صفحة اختبار الأقسام</a> - اختبار تفاعلي</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h3>✅ الأقسام المتوقعة:</h3>";
    echo "<ul>";
    echo "<li>✅ الرئيسية (Dashboard) - لوحة المعلومات الرئيسية</li>";
    echo "<li>✅ إدارة المنتجات - إضافة وتعديل المنتجات</li>";
    echo "<li>✅ الطلبات - عرض وإدارة الطلبات</li>";
    echo "<li>✅ صفحات هبوط - إنشاء وإدارة صفحات الهبوط</li>";
    echo "<li>✅ التقارير والإحصائيات - عرض البيانات والتحليلات</li>";
    echo "<li>✅ إعدادات النظام - تكوين النظام</li>";
    echo "<li>✅ تسجيل الخروج - إنهاء الجلسة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h3>📋 كيفية اختبار الأقسام يدوياً:</h3>";
    echo "<ol>";
    echo "<li>افتح لوحة التحكم: /admin/</li>";
    echo "<li>انقر على كل قسم في الشريط الجانبي</li>";
    echo "<li>تأكد من ظهور المحتوى المناسب لكل قسم</li>";
    echo "<li>تحقق من عدم وجود أخطاء JavaScript في وحدة التحكم</li>";
    echo "<li>اختبر الوظائف الأساسية في كل قسم (إضافة، تعديل، حذف)</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام في التحقق: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
