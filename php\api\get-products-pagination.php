<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Prevent any output before JSON
ob_start();

try {
    // Database connection
    require_once '../config/database.php';
    
    // Get pagination parameters
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $category = isset($_GET['category']) ? trim($_GET['category']) : '';
    $status = isset($_GET['status']) ? trim($_GET['status']) : '';
    
    $offset = ($page - 1) * $limit;
    
    // Build WHERE clause
    $whereConditions = [];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(name LIKE ? OR description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($category)) {
        $whereConditions[] = "category_id = ?";
        $params[] = $category;
    }
    
    if ($status !== '') {
        $whereConditions[] = "status = ?";
        $params[] = $status;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM products $whereClause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $totalProducts = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Get products with pagination
    $sql = "SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            $whereClause 
            ORDER BY p.created_at DESC 
            LIMIT ? OFFSET ?";
    
    $allParams = array_merge($params, [$limit, $offset]);
    $stmt = $pdo->prepare($sql);
    $stmt->execute($allParams);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format products data
    $formattedProducts = array_map(function($product) {
        return [
            'id' => $product['id'],
            'name' => $product['name'],
            'description' => $product['description'],
            'price' => floatval($product['price']),
            'formatted_price' => number_format($product['price'], 2) . ' دج',
            'image' => $product['image'],
            'category_id' => $product['category_id'],
            'category_name' => $product['category_name'] ?? 'غير محدد',
            'status' => intval($product['status']),
            'status_text' => $product['status'] ? 'نشط' : 'غير نشط',
            'type' => $product['type'] ?? 'book',
            'created_at' => $product['created_at'],
            'updated_at' => $product['updated_at']
        ];
    }, $products);
    
    // Calculate pagination info
    $totalPages = ceil($totalProducts / $limit);
    $hasNextPage = $page < $totalPages;
    $hasPrevPage = $page > 1;
    
    $response = [
        'success' => true,
        'data' => [
            'products' => $formattedProducts,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_products' => $totalProducts,
                'per_page' => $limit,
                'has_next_page' => $hasNextPage,
                'has_prev_page' => $hasPrevPage,
                'start_index' => $offset + 1,
                'end_index' => min($offset + $limit, $totalProducts)
            ],
            'filters' => [
                'search' => $search,
                'category' => $category,
                'status' => $status
            ]
        ],
        'message' => 'Products retrieved successfully'
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => 'Failed to retrieve products',
        'message' => $e->getMessage(),
        'data' => [
            'products' => [],
            'pagination' => [
                'current_page' => 1,
                'total_pages' => 0,
                'total_products' => 0,
                'per_page' => $limit,
                'has_next_page' => false,
                'has_prev_page' => false,
                'start_index' => 0,
                'end_index' => 0
            ]
        ]
    ];
}

// Clear any output buffer and send JSON
ob_clean();
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;
?>
