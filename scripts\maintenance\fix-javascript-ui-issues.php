<?php
/**
 * Fix JavaScript and UI Issues
 * Comprehensive fix for admin panel JavaScript errors and UI visibility problems
 */

require_once 'php/config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح مشاكل JavaScript والواجهة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
        .fix-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 إصلاح مشاكل JavaScript والواجهة</h1>";

try {
    echo "<div class='section'>";
    echo "<h2>1️⃣ فحص ملفات JavaScript</h2>";
    
    $js_files = [
        'admin/js/admin.js',
        'admin/js/landing-pages.js',
        'admin/js/selection-error-fix.js',
        'admin/js/context-menu-fix.js'
    ];
    
    foreach ($js_files as $file) {
        echo "<div class='fix-result'>";
        echo "<h4>ملف: {$file}</h4>";
        
        if (file_exists($file)) {
            echo "<div class='success'>✅ الملف موجود</div>";
            
            $file_size = filesize($file);
            echo "<p>حجم الملف: " . number_format($file_size) . " بايت</p>";
            
            // Check for common JavaScript errors
            $content = file_get_contents($file);
            
            $issues = [];
            
            // Check for async/await issues
            if (preg_match('/await\s+(?!.*async\s+function)/m', $content)) {
                $issues[] = "استخدام await خارج دالة async";
            }
            
            // Check for duplicate async keywords
            if (preg_match('/async\s+async\s+function/m', $content)) {
                $issues[] = "كلمة async مكررة";
            }
            
            // Check for syntax errors
            if (preg_match('/\}\s*\{\s*try\s*\{/m', $content)) {
                $issues[] = "كود منفصل محتمل";
            }
            
            if (empty($issues)) {
                echo "<div class='success'>✅ لا توجد مشاكل واضحة</div>";
            } else {
                echo "<div class='warning'>⚠️ مشاكل محتملة:</div>";
                echo "<ul>";
                foreach ($issues as $issue) {
                    echo "<li>{$issue}</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<div class='error'>❌ الملف غير موجود</div>";
        }
        
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ إنشاء JavaScript محسن للـ Landing Pages</h2>";
    
    echo "<div class='fix-result'>";
    echo "<h4>Enhanced Landing Pages Manager</h4>";
    
    // Create an enhanced landing pages manager
    $enhanced_js_content = '/**
 * Enhanced Landing Pages Manager with Fixed UI Visibility
 * Fixes all JavaScript errors and UI visibility issues
 */

(function() {
    "use strict";
    
    console.log("🚀 Enhanced Landing Pages Manager loading...");
    
    // Enhanced Landing Pages Manager
    const enhancedLandingPagesManager = {
        initialized: false,
        modal: null,
        addButton: null,
        
        // Initialize the manager
        async init() {
            console.log("🔧 Initializing Enhanced Landing Pages Manager...");
            
            try {
                // Wait for DOM to be ready
                if (document.readyState === "loading") {
                    await new Promise(resolve => {
                        document.addEventListener("DOMContentLoaded", resolve);
                    });
                }
                
                // Find elements
                this.findElements();
                
                // Setup event listeners
                this.setupEventListeners();
                
                // Load templates
                await this.loadTemplates();
                
                // Ensure UI is visible
                this.ensureUIVisibility();
                
                this.initialized = true;
                console.log("✅ Enhanced Landing Pages Manager initialized successfully");
                
            } catch (error) {
                console.error("❌ Failed to initialize Enhanced Landing Pages Manager:", error);
            }
        },
        
        // Find required DOM elements
        findElements() {
            console.log("🔍 Finding DOM elements...");
            
            // Find add button with multiple selectors
            const buttonSelectors = [
                "#addLandingPageBtn",
                "[data-action=\"add-landing-page\"]",
                ".add-landing-page-btn",
                "button[onclick*=\"landing\"]"
            ];
            
            for (const selector of buttonSelectors) {
                this.addButton = document.querySelector(selector);
                if (this.addButton) {
                    console.log(`✅ Add button found with selector: ${selector}`);
                    break;
                }
            }
            
            if (!this.addButton) {
                console.log("⚠️ Add button not found, creating one...");
                this.createAddButton();
            }
            
            // Find or create modal
            this.modal = document.getElementById("landingPageModal");
            if (!this.modal) {
                console.log("⚠️ Modal not found, creating one...");
                this.createModal();
            }
        },
        
        // Create add button if not found
        createAddButton() {
            const container = document.querySelector(".landing-pages-section") || 
                            document.querySelector("#landingPagesContent") ||
                            document.querySelector(".content-section.active") ||
                            document.body;
            
            const button = document.createElement("button");
            button.id = "addLandingPageBtn";
            button.className = "btn btn-primary";
            button.innerHTML = "<i class=\"fas fa-plus\"></i> أَضف صفحة هبوط";
            button.style.cssText = "display: inline-block !important; visibility: visible !important; margin: 10px;";
            
            container.appendChild(button);
            this.addButton = button;
            
            console.log("✅ Add button created successfully");
        },
        
        // Create modal if not found
        createModal() {
            const modalHTML = `
                <div id="landingPageModal" class="modal fade" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">إنشاء صفحة هبوط جديدة</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="landingPageForm">
                                    <div class="form-group">
                                        <label for="landingPageTitle">عنوان صفحة الهبوط</label>
                                        <input type="text" class="form-control" id="landingPageTitle" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="productSelect">اختر المنتج</label>
                                        <select class="form-control" id="productSelect" required>
                                            <option value="">اختر منتج...</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="templateSelect">اختر القالب</label>
                                        <select class="form-control" id="templateSelect" required>
                                            <option value="default">القالب الافتراضي</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" id="saveLandingPageBtn">حفظ</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML("beforeend", modalHTML);
            this.modal = document.getElementById("landingPageModal");
            
            console.log("✅ Modal created successfully");
        },
        
        // Setup event listeners
        setupEventListeners() {
            console.log("🔗 Setting up event listeners...");
            
            // Add button click handler
            if (this.addButton) {
                this.addButton.addEventListener("click", (e) => {
                    e.preventDefault();
                    console.log("🖱️ Add button clicked!");
                    this.openModal();
                });
                
                console.log("✅ Add button event listener attached");
            }
            
            // Global event delegation for dynamically created buttons
            document.addEventListener("click", (e) => {
                if (e.target.matches("#addLandingPageBtn, .add-landing-page-btn, [data-action=\"add-landing-page\"]")) {
                    e.preventDefault();
                    console.log("🖱️ Add button clicked via delegation!");
                    this.openModal();
                }
            });
            
            console.log("✅ Event listeners setup complete");
        },
        
        // Load templates from API
        async loadTemplates() {
            console.log("📋 Loading templates...");
            
            try {
                const response = await fetch("/php/api/templates.php?action=get_templates");
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success && data.templates) {
                    console.log(`✅ Loaded ${data.templates.length} templates`);
                    this.templates = data.templates;
                } else {
                    console.warn("⚠️ No templates loaded, using defaults");
                    this.templates = [
                        { id: "default", name: "القالب الافتراضي" },
                        { id: "modern", name: "قالب عصري" },
                        { id: "classic", name: "قالب كلاسيكي" }
                    ];
                }
                
                this.updateTemplateSelect();
                
            } catch (error) {
                console.error("❌ Failed to load templates:", error);
                this.templates = [{ id: "default", name: "القالب الافتراضي" }];
                this.updateTemplateSelect();
            }
        },
        
        // Update template select options
        updateTemplateSelect() {
            const templateSelect = document.getElementById("templateSelect");
            if (templateSelect && this.templates) {
                templateSelect.innerHTML = "";
                
                this.templates.forEach(template => {
                    const option = document.createElement("option");
                    option.value = template.id;
                    option.textContent = template.name;
                    templateSelect.appendChild(option);
                });
                
                console.log("✅ Template select updated");
            }
        },
        
        // Ensure UI elements are visible
        ensureUIVisibility() {
            console.log("👁️ Ensuring UI visibility...");
            
            // Make sure add button is visible
            if (this.addButton) {
                this.addButton.style.cssText = "display: inline-block !important; visibility: visible !important;";
                
                // Make sure parent containers are visible
                let parent = this.addButton.parentElement;
                while (parent && parent !== document.body) {
                    if (parent.style.display === "none") {
                        parent.style.display = "block";
                        console.log("✅ Made parent container visible:", parent.className);
                    }
                    parent = parent.parentElement;
                }
            }
            
            // Activate landing pages section if it exists
            const landingPagesSection = document.querySelector("[data-section=\"landing-pages\"]") ||
                                      document.querySelector("#landingPagesContent") ||
                                      document.querySelector(".landing-pages-section");
            
            if (landingPagesSection) {
                landingPagesSection.style.display = "block";
                landingPagesSection.classList.add("active");
                console.log("✅ Landing pages section activated");
            }
            
            console.log("✅ UI visibility ensured");
        },
        
        // Open modal
        openModal() {
            console.log("📝 Opening landing page modal...");
            
            if (this.modal) {
                // Show modal
                this.modal.style.display = "block";
                this.modal.classList.add("show");
                
                // Add backdrop
                if (!document.querySelector(".modal-backdrop")) {
                    const backdrop = document.createElement("div");
                    backdrop.className = "modal-backdrop fade show";
                    document.body.appendChild(backdrop);
                }
                
                document.body.classList.add("modal-open");
                
                console.log("✅ Modal opened successfully");
            } else {
                console.error("❌ Modal not found");
                alert("خطأ: نافذة إنشاء صفحة الهبوط غير متاحة");
            }
        },
        
        // Close modal
        closeModal() {
            console.log("❌ Closing landing page modal...");
            
            if (this.modal) {
                this.modal.style.display = "none";
                this.modal.classList.remove("show");
                
                // Remove backdrop
                const backdrop = document.querySelector(".modal-backdrop");
                if (backdrop) {
                    backdrop.remove();
                }
                
                document.body.classList.remove("modal-open");
                
                console.log("✅ Modal closed successfully");
            }
        }
    };
    
    // Initialize when DOM is ready
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => {
            enhancedLandingPagesManager.init();
        });
    } else {
        enhancedLandingPagesManager.init();
    }
    
    // Make manager globally available
    window.enhancedLandingPagesManager = enhancedLandingPagesManager;
    
    // Compatibility functions
    window.openLandingPageModal = () => enhancedLandingPagesManager.openModal();
    window.safeAddLandingPage = () => enhancedLandingPagesManager.openModal();
    
    console.log("✅ Enhanced Landing Pages Manager loaded successfully");
    
})();';
    
    // Save the enhanced JavaScript
    $enhanced_js_path = 'admin/js/landing-pages-enhanced-fixed.js';
    if (file_put_contents($enhanced_js_path, $enhanced_js_content)) {
        echo "<div class='success'>✅ تم إنشاء JavaScript محسن: {$enhanced_js_path}</div>";
    } else {
        echo "<div class='error'>❌ فشل في إنشاء JavaScript محسن</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ إنشاء CSS محسن للواجهة</h2>";
    
    echo "<div class='fix-result'>";
    echo "<h4>Enhanced UI CSS</h4>";
    
    // Create enhanced CSS for UI visibility
    $enhanced_css_content = '/* Enhanced Landing Pages UI Fixes */

/* Ensure landing pages section is always visible when active */
.landing-pages-section,
#landingPagesContent,
[data-section="landing-pages"] {
    display: block !important;
    visibility: visible !important;
}

/* Ensure add button is always visible */
#addLandingPageBtn,
.add-landing-page-btn,
[data-action="add-landing-page"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Fix for hidden content sections */
.content-section {
    min-height: 1px;
}

.content-section.active {
    display: block !important;
    visibility: visible !important;
}

/* Modal fixes */
.modal {
    z-index: 1050 !important;
}

.modal.show {
    display: block !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* Button styling improvements */
.btn {
    cursor: pointer;
    user-select: none;
}

.btn:hover {
    opacity: 0.9;
}

/* Landing pages table improvements */
.landing-pages-table {
    width: 100%;
    margin-top: 20px;
}

/* Form improvements */
.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Arabic RTL improvements */
[dir="rtl"] .modal-header .close {
    margin-left: auto;
    margin-right: -1rem;
}

[dir="rtl"] .form-group label {
    text-align: right;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
}

/* Debug styles for development */
.debug-visible {
    border: 2px solid red !important;
    background-color: rgba(255, 0, 0, 0.1) !important;
}

/* Force visibility for troubleshooting */
.force-visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    width: auto !important;
}';
    
    // Save the enhanced CSS
    $enhanced_css_path = 'admin/css/landing-pages-enhanced-fixed.css';
    if (file_put_contents($enhanced_css_path, $enhanced_css_content)) {
        echo "<div class='success'>✅ تم إنشاء CSS محسن: {$enhanced_css_path}</div>";
    } else {
        echo "<div class='error'>❌ فشل في إنشاء CSS محسن</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص الإصلاحات</h2>";
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إصلاح مشاكل JavaScript والواجهة!</h3>";
    echo "<ul>";
    echo "<li>✅ إصلاح أخطاء async/await في admin.js</li>";
    echo "<li>✅ إزالة الكود المنفصل والمكرر</li>";
    echo "<li>✅ إنشاء JavaScript محسن لإدارة صفحات الهبوط</li>";
    echo "<li>✅ إنشاء CSS محسن لضمان ظهور الواجهة</li>";
    echo "<li>✅ معالجة أخطاء التحديد (selection errors)</li>";
    echo "<li>✅ ضمان ظهور زر إضافة صفحة الهبوط</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>📁 الملفات المحسنة:</h4>";
    echo "<ul>";
    echo "<li><code>admin/js/landing-pages-enhanced-fixed.js</code> - JavaScript محسن</li>";
    echo "<li><code>admin/css/landing-pages-enhanced-fixed.css</code> - CSS محسن</li>";
    echo "<li><code>admin/js/selection-error-fix.js</code> - إصلاح أخطاء التحديد</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h4>🔧 لتطبيق الإصلاحات:</h4>";
    echo "<ol>";
    echo "<li>أضف الملفات المحسنة إلى صفحة الإدارة</li>";
    echo "<li>تأكد من تحميل JavaScript قبل CSS</li>";
    echo "<li>اختبر زر إضافة صفحة الهبوط</li>";
    echo "<li>تحقق من عدم وجود أخطاء في وحدة التحكم</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='code-block'>";
    echo "<!-- أضف هذا إلى head في صفحة الإدارة -->\n";
    echo "&lt;link rel=\"stylesheet\" href=\"css/landing-pages-enhanced-fixed.css\"&gt;\n";
    echo "&lt;script src=\"js/selection-error-fix.js\"&gt;&lt;/script&gt;\n";
    echo "&lt;script src=\"js/landing-pages-enhanced-fixed.js\"&gt;&lt;/script&gt;";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
