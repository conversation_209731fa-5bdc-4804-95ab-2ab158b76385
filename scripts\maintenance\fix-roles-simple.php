<?php
/**
 * Simple Roles Table Fix
 * Creates and populates the roles table
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Database configuration
$host = 'localhost';
$port = '3307';
$dbname = 'poultraydz';
$username = 'root';
$password = 'root';

$results = [
    'success' => false,
    'message' => '',
    'fixes' => [],
    'errors' => [],
    'warnings' => []
];

try {
    // Create PDO connection
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    $results['fixes'][] = "Database connection established successfully";

    // Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() == 0) {
        // Create roles table
        $createTableSQL = "
            CREATE TABLE roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL DEFAULT '',
                display_name_ar VARCHAR(100) NOT NULL DEFAULT '',
                description TEXT DEFAULT NULL,
                permissions JSON DEFAULT NULL,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createTableSQL);
        $results['fixes'][] = "Created roles table with proper structure";
        
        // Insert default roles
        $defaultRoles = [
            ['admin', 'Administrator', 'مدير النظام', 'Full system access'],
            ['seller', 'Seller', 'بائع', 'Can manage own products and orders'],
            ['user', 'User', 'مستخدم', 'Basic user access'],
            ['moderator', 'Moderator', 'مشرف', 'Can moderate content'],
            ['editor', 'Editor', 'محرر', 'Can edit content']
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO roles (name, display_name, display_name_ar, description) 
            VALUES (?, ?, ?, ?)
        ");
        
        foreach ($defaultRoles as $role) {
            $insertStmt->execute($role);
        }
        
        $results['fixes'][] = "Inserted " . count($defaultRoles) . " default roles";
    } else {
        $results['fixes'][] = "Roles table already exists";
        
        // Check if table has data
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
        $count = $countStmt->fetch()['count'];
        
        if ($count == 0) {
            // Insert default roles
            $defaultRoles = [
                ['admin', 'Administrator', 'مدير النظام', 'Full system access'],
                ['seller', 'Seller', 'بائع', 'Can manage own products and orders'],
                ['user', 'User', 'مستخدم', 'Basic user access'],
                ['moderator', 'Moderator', 'مشرف', 'Can moderate content'],
                ['editor', 'Editor', 'محرر', 'Can edit content']
            ];
            
            $insertStmt = $pdo->prepare("
                INSERT INTO roles (name, display_name, display_name_ar, description) 
                VALUES (?, ?, ?, ?)
            ");
            
            foreach ($defaultRoles as $role) {
                $insertStmt->execute($role);
            }
            
            $results['fixes'][] = "Inserted " . count($defaultRoles) . " default roles into existing table";
        } else {
            $results['fixes'][] = "Roles table already has $count roles";
        }
    }

    // Verify the table works
    $testStmt = $pdo->query("SELECT id, name, display_name, display_name_ar FROM roles ORDER BY id");
    $roles = $testStmt->fetchAll();
    
    $results['fixes'][] = "Verification successful - found " . count($roles) . " roles";
    $results['roles'] = $roles;
    
    $results['success'] = true;
    $results['message'] = 'Roles table setup completed successfully';

} catch (Exception $e) {
    $results['success'] = false;
    $results['message'] = 'Failed to setup roles table: ' . $e->getMessage();
    $results['errors'][] = $e->getMessage();
}

echo json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
