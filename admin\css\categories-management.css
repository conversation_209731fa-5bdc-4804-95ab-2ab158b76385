/**
 * Categories Management Styles
 * أنماط إدارة الفئات
 */

/* Categories Management Container */
.categories-management-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.categories-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.categories-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.categories-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.header-actions .btn {
    padding: 10px 20px;
    border: 2px solid transparent;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-light {
    background: white;
    color: #667eea;
    border-color: white;
}

.btn-light:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-outline-light {
    background: transparent;
    color: white;
    border-color: white;
}

.btn-outline-light:hover {
    background: white;
    color: #667eea;
    transform: translateY(-2px);
}

/* Statistics Cards */
.categories-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: #667eea;
}

.stat-content h3 {
    margin: 0;
    font-size: 2.2rem;
    font-weight: 700;
    color: #333;
}

.stat-content p {
    margin: 8px 0 0 0;
    color: #666;
    font-size: 0.95rem;
}

/* Filters Section */
.categories-filters {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 16px;
    border: 2px solid;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.filter-btn:not(.active):hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

/* Categories Content */
.categories-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.categories-view-toggle {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    gap: 10px;
    background: #f8f9fa;
}

.view-btn {
    padding: 10px 16px;
    border: 2px solid #667eea;
    background: transparent;
    color: #667eea;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.view-btn.active {
    background: #667eea;
    color: white;
}

.view-btn:not(.active):hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

/* Categories Hierarchy */
.categories-hierarchy {
    padding: 20px;
}

.category-item {
    margin-bottom: 15px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.category-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.category-item.inactive {
    background: #f5f5f5;
    opacity: 0.7;
}

.category-item.featured {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
}

.category-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.category-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-details h4 {
    margin: 0 0 8px 0;
    font-size: 1.3rem;
    color: #333;
    font-weight: 600;
}

.category-details p {
    margin: 0 0 12px 0;
    color: #666;
    font-size: 0.95rem;
    line-height: 1.4;
}

.category-meta {
    display: flex;
    gap: 20px;
    font-size: 0.85rem;
    color: #999;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.meta-item i {
    font-size: 0.9rem;
}

/* Category Actions */
.category-actions {
    display: flex;
    gap: 8px;
    margin-right: 20px;
}

.action-btn {
    padding: 10px 14px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn.edit {
    background: #17a2b8;
    color: white;
}

.action-btn.edit:hover {
    background: #138496;
}

.action-btn.toggle.active {
    background: #28a745;
    color: white;
}

.action-btn.toggle.inactive {
    background: #6c757d;
    color: white;
}

.action-btn.toggle:hover {
    opacity: 0.8;
}

.action-btn.delete {
    background: #dc3545;
    color: white;
}

.action-btn.delete:hover {
    background: #c82333;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 25px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: white;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    padding: 5px;
    border-radius: 50%;
}

.modal-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;
}

/* Form Styles */
.form-section {
    margin-bottom: 30px;
}

.form-section h4 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.8rem;
}

.required {
    color: #dc3545;
    margin-right: 3px;
}

.checkbox-group {
    display: flex;
    gap: 25px;
    align-items: center;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-left: 8px;
    transform: scale(1.2);
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.btn-primary {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.btn-primary:hover {
    background: #5a67d8;
    border-color: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* Loading and Error States */
.loading-state,
.error-state {
    text-align: center;
    padding: 60px 20px;
}

.loading-spinner i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
    animation: spin 1s linear infinite;
}

.error-icon i {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 20px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .categories-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
    
    .categories-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .categories-filters > div {
        flex-direction: column;
        gap: 15px;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .category-info {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .category-actions {
        justify-content: center;
        margin-right: 0;
        margin-top: 15px;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .checkbox-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}
