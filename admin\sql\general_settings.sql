-- جدول الإعدادات العامة للموقع
-- General Settings Table

CREATE TABLE IF NOT EXISTS `general_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text,
  `setting_type` enum('text','textarea','number','boolean','email','url','file','select') DEFAULT 'text',
  `setting_group` varchar(50) DEFAULT 'general',
  `setting_label_ar` varchar(255) DEFAULT NULL,
  `setting_label_en` varchar(255) DEFAULT NULL,
  `setting_description_ar` text,
  `setting_description_en` text,
  `is_required` tinyint(1) DEFAULT 0,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_setting_key` (`setting_key`),
  KEY `idx_setting_group` (`setting_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الإعدادات الافتراضية
INSERT INTO `general_settings` (`setting_key`, `setting_value`, `setting_type`, `setting_group`, `setting_label_ar`, `setting_label_en`, `setting_description_ar`, `setting_description_en`, `is_required`, `sort_order`) VALUES
-- إعدادات الموقع الأساسية
('site_name', 'متجر الكتب الإلكترونية', 'text', 'site', 'اسم الموقع', 'Site Name', 'اسم الموقع الذي يظهر في العنوان والرأس', 'Site name displayed in title and header', 1, 1),
('site_description', 'أفضل متجر للكتب الإلكترونية والمنتجات الرقمية', 'textarea', 'site', 'وصف الموقع', 'Site Description', 'وصف مختصر للموقع يظهر في محركات البحث', 'Brief site description for search engines', 1, 2),
('site_keywords', 'كتب إلكترونية, منتجات رقمية, تسوق أونلاين', 'textarea', 'site', 'الكلمات المفتاحية', 'Site Keywords', 'الكلمات المفتاحية للموقع مفصولة بفواصل', 'Site keywords separated by commas', 0, 3),
('site_logo', '', 'file', 'site', 'شعار الموقع', 'Site Logo', 'شعار الموقع (PNG, JPG, SVG)', 'Site logo (PNG, JPG, SVG)', 0, 4),
('site_favicon', '', 'file', 'site', 'أيقونة الموقع', 'Site Favicon', 'أيقونة الموقع الصغيرة (ICO, PNG)', 'Site favicon (ICO, PNG)', 0, 5),
('site_url', 'http://localhost:8000', 'url', 'site', 'رابط الموقع', 'Site URL', 'الرابط الأساسي للموقع', 'Main site URL', 1, 6),

-- إعدادات اللغة والمنطقة
('default_language', 'ar', 'select', 'localization', 'اللغة الافتراضية', 'Default Language', 'اللغة الافتراضية للموقع', 'Default site language', 1, 10),
('timezone', 'Asia/Riyadh', 'select', 'localization', 'المنطقة الزمنية', 'Timezone', 'المنطقة الزمنية للموقع', 'Site timezone', 1, 11),
('date_format', 'Y-m-d', 'select', 'localization', 'تنسيق التاريخ', 'Date Format', 'تنسيق عرض التاريخ', 'Date display format', 1, 12),
('currency', 'DZD', 'select', 'localization', 'العملة الافتراضية', 'Default Currency', 'العملة المستخدمة في الموقع', 'Site default currency', 1, 13),
('currency_symbol', 'دج', 'text', 'localization', 'رمز العملة', 'Currency Symbol', 'رمز العملة المعروض', 'Displayed currency symbol', 1, 14),

-- إعدادات البريد الإلكتروني
('admin_email', '<EMAIL>', 'email', 'email', 'بريد المدير', 'Admin Email', 'البريد الإلكتروني للمدير', 'Administrator email address', 1, 20),
('smtp_host', '', 'text', 'email', 'خادم SMTP', 'SMTP Host', 'عنوان خادم البريد الإلكتروني', 'Email server host address', 0, 21),
('smtp_port', '587', 'number', 'email', 'منفذ SMTP', 'SMTP Port', 'منفذ خادم البريد الإلكتروني', 'Email server port', 0, 22),
('smtp_username', '', 'text', 'email', 'اسم مستخدم SMTP', 'SMTP Username', 'اسم المستخدم لخادم البريد', 'Email server username', 0, 23),
('smtp_password', '', 'text', 'email', 'كلمة مرور SMTP', 'SMTP Password', 'كلمة المرور لخادم البريد', 'Email server password', 0, 24),
('smtp_encryption', 'tls', 'select', 'email', 'تشفير SMTP', 'SMTP Encryption', 'نوع التشفير المستخدم', 'Encryption type used', 0, 25),

-- إعدادات الإشعارات
('enable_notifications', '1', 'boolean', 'notifications', 'تفعيل الإشعارات', 'Enable Notifications', 'تفعيل نظام الإشعارات', 'Enable notification system', 0, 30),
('email_notifications', '1', 'boolean', 'notifications', 'إشعارات البريد', 'Email Notifications', 'إرسال الإشعارات عبر البريد', 'Send notifications via email', 0, 31),
('sms_notifications', '0', 'boolean', 'notifications', 'إشعارات SMS', 'SMS Notifications', 'إرسال الإشعارات عبر الرسائل النصية', 'Send notifications via SMS', 0, 32),

-- إعدادات الأمان
('maintenance_mode', '0', 'boolean', 'security', 'وضع الصيانة', 'Maintenance Mode', 'تفعيل وضع الصيانة للموقع', 'Enable site maintenance mode', 0, 40),
('registration_enabled', '1', 'boolean', 'security', 'السماح بالتسجيل', 'Registration Enabled', 'السماح للمستخدمين الجدد بالتسجيل', 'Allow new user registration', 0, 41),
('email_verification', '1', 'boolean', 'security', 'تأكيد البريد', 'Email Verification', 'طلب تأكيد البريد الإلكتروني', 'Require email verification', 0, 42),

-- إعدادات الأداء
('cache_enabled', '1', 'boolean', 'performance', 'تفعيل التخزين المؤقت', 'Cache Enabled', 'تفعيل نظام التخزين المؤقت', 'Enable caching system', 0, 50),
('compress_output', '1', 'boolean', 'performance', 'ضغط المخرجات', 'Compress Output', 'ضغط محتوى الصفحات', 'Compress page output', 0, 51),
('minify_css', '1', 'boolean', 'performance', 'ضغط CSS', 'Minify CSS', 'ضغط ملفات الأنماط', 'Minify CSS files', 0, 52),
('minify_js', '1', 'boolean', 'performance', 'ضغط JavaScript', 'Minify JavaScript', 'ضغط ملفات JavaScript', 'Minify JavaScript files', 0, 53);

-- إنشاء جدول سجل التغييرات للإعدادات
CREATE TABLE IF NOT EXISTS `settings_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `old_value` text,
  `new_value` text,
  `changed_by` int(11) DEFAULT NULL,
  `changed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  PRIMARY KEY (`id`),
  KEY `idx_setting_key` (`setting_key`),
  KEY `idx_changed_by` (`changed_by`),
  KEY `idx_changed_at` (`changed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
