<?php
/**
 * Diagnose Critical Issues
 * Comprehensive diagnosis of config paths, API errors, and JavaScript issues
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>تشخيص المشاكل الحرجة</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .critical { border: 3px solid #dc3545; background: #f8d7da; }
        h1, h2, h3 { color: #333; }
        .test-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .test-failed { border-left-color: #dc3545; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
        .file-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .file-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🚨 تشخيص المشاكل الحرجة</h1>";
echo "<p>تشخيص شامل لمشاكل مسارات الملفات، أخطاء APIs، ومشاكل JavaScript</p>";

try {
    // CRITICAL ISSUE 1: Config Path Resolution
    echo "<div class='section critical'>";
    echo "<h2>🔥 المشكلة الحرجة 1: حل مسارات ملف التكوين</h2>";
    
    echo "<div class='test-result'>";
    echo "<h4>فحص مسارات ملفات التكوين</h4>";
    
    $configPaths = [
        'php/config.php' => 'الملف الرئيسي',
        'config/config.php' => 'ملف التكوين البديل',
        'php/config/config.php' => 'ملف التكوين الفرعي'
    ];
    
    $workingConfigPath = null;
    
    foreach ($configPaths as $path => $description) {
        $exists = file_exists($path);
        $status = $exists ? '✅' : '❌';
        echo "<p>{$status} {$path} ({$description})</p>";
        
        if ($exists && !$workingConfigPath) {
            $workingConfigPath = $path;
        }
    }
    
    if ($workingConfigPath) {
        echo "<div class='success'>✅ ملف التكوين الصالح: {$workingConfigPath}</div>";
    } else {
        echo "<div class='error'>❌ لم يتم العثور على أي ملف تكوين صالح</div>";
    }
    echo "</div>";
    
    // Test path resolution from API directory perspective
    echo "<div class='test-result'>";
    echo "<h4>اختبار حل المسار من دليل API</h4>";
    
    $apiToConfigPaths = [
        '../config.php' => 'من php/api/ إلى php/config.php',
        '../../config/config.php' => 'من php/api/ إلى config/config.php',
        '../config/config.php' => 'من php/api/ إلى php/config/config.php'
    ];
    
    $correctApiPath = null;
    
    foreach ($apiToConfigPaths as $relativePath => $description) {
        // Simulate being in php/api/ directory
        $fullPath = 'php/api/' . $relativePath;
        $resolvedPath = realpath($fullPath);
        $exists = $resolvedPath && file_exists($resolvedPath);
        
        $status = $exists ? '✅' : '❌';
        echo "<p>{$status} {$relativePath} ({$description})</p>";
        
        if ($exists && !$correctApiPath) {
            $correctApiPath = $relativePath;
        }
    }
    
    if ($correctApiPath) {
        echo "<div class='success'>✅ المسار الصحيح للـ API: {$correctApiPath}</div>";
    } else {
        echo "<div class='error'>❌ لم يتم العثور على مسار صحيح من دليل API</div>";
    }
    echo "</div>";
    echo "</div>";
    
    // CRITICAL ISSUE 2: API Testing
    echo "<div class='section critical'>";
    echo "<h2>🔥 المشكلة الحرجة 2: اختبار APIs</h2>";
    
    $apiFiles = [
        'php/api/templates.php',
        'php/api/landing-pages.php',
        'php/api/users.php',
        'php/api/subscription-limits.php'
    ];
    
    echo "<div class='file-grid'>";
    foreach ($apiFiles as $apiFile) {
        echo "<div class='file-card'>";
        echo "<h4>" . basename($apiFile) . "</h4>";
        
        if (file_exists($apiFile)) {
            echo "<div class='success'>✅ الملف موجود</div>";
            
            // Check syntax
            $output = [];
            $return_var = 0;
            exec("php -l \"{$apiFile}\" 2>&1", $output, $return_var);
            
            if ($return_var === 0) {
                echo "<div class='success'>✅ بناء الجملة صحيح</div>";
            } else {
                echo "<div class='error'>❌ خطأ في بناء الجملة:</div>";
                echo "<div class='code-block'>" . implode("\n", $output) . "</div>";
            }
            
            // Test include capability
            try {
                $content = file_get_contents($apiFile);
                if (strpos($content, "require_once '../config.php'") !== false) {
                    echo "<div class='warning'>⚠️ يستخدم '../config.php'</div>";
                } elseif (strpos($content, "require_once") !== false) {
                    echo "<div class='info'>ℹ️ يستخدم مسار require مختلف</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ خطأ في قراءة الملف</div>";
            }
        } else {
            echo "<div class='error'>❌ الملف غير موجود</div>";
        }
        
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    // CRITICAL ISSUE 3: JavaScript Files Check
    echo "<div class='section critical'>";
    echo "<h2>🔥 المشكلة الحرجة 3: فحص ملفات JavaScript</h2>";
    
    $jsFiles = [
        'js/utils.js' => 'ملف الأدوات المساعدة',
        'js/main.js' => 'الملف الرئيسي',
        'js/product-view.js' => 'عرض المنتجات',
        'js/auth.js' => 'المصادقة',
        'admin/js/admin.js' => 'إدارة النظام',
        'admin/js/landing-pages.js' => 'إدارة صفحات الهبوط',
        'admin/js/landing-pages-enhanced-fixed.js' => 'إدارة صفحات الهبوط المحسن'
    ];
    
    echo "<div class='file-grid'>";
    foreach ($jsFiles as $jsFile => $description) {
        echo "<div class='file-card'>";
        echo "<h4>{$description}</h4>";
        echo "<p><code>{$jsFile}</code></p>";
        
        if (file_exists($jsFile)) {
            $size = filesize($jsFile);
            echo "<div class='success'>✅ موجود - الحجم: " . number_format($size) . " بايت</div>";
            
            // Check for common JavaScript issues
            $content = file_get_contents($jsFile);
            $issues = [];
            
            // Check for HTML content in JS files (common cause of syntax errors)
            if (preg_match('/<!DOCTYPE|<html|<head|<body/i', $content)) {
                $issues[] = "يحتوي على محتوى HTML";
            }
            
            // Check for PHP tags in JS files
            if (preg_match('/<\?php|\?>/i', $content)) {
                $issues[] = "يحتوي على كود PHP";
            }
            
            // Check for async/await issues
            if (preg_match('/await\s+(?!.*async\s+function)/m', $content)) {
                $issues[] = "استخدام await خارج دالة async";
            }
            
            // Check for duplicate async keywords
            if (preg_match('/async\s+async\s+function/m', $content)) {
                $issues[] = "كلمة async مكررة";
            }
            
            if (empty($issues)) {
                echo "<div class='success'>✅ لا توجد مشاكل واضحة</div>";
            } else {
                echo "<div class='warning'>⚠️ مشاكل محتملة:</div>";
                foreach ($issues as $issue) {
                    echo "<div class='warning'>- {$issue}</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ غير موجود</div>";
        }
        
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    // CRITICAL ISSUE 4: UI Visibility Check
    echo "<div class='section critical'>";
    echo "<h2>🔥 المشكلة الحرجة 4: فحص ظهور الواجهة</h2>";
    
    echo "<div class='test-result'>";
    echo "<h4>فحص ملفات CSS والواجهة</h4>";
    
    $cssFiles = [
        'admin/css/style.css' => 'ملف الأنماط الرئيسي',
        'admin/css/landing-pages-enhanced-fixed.css' => 'أنماط صفحات الهبوط المحسنة',
        'css/style.css' => 'أنماط الموقع الرئيسي'
    ];
    
    foreach ($cssFiles as $cssFile => $description) {
        if (file_exists($cssFile)) {
            $size = filesize($cssFile);
            echo "<div class='success'>✅ {$description} - الحجم: " . number_format($size) . " بايت</div>";
        } else {
            echo "<div class='error'>❌ {$description} غير موجود</div>";
        }
    }
    
    // Check admin HTML files
    $adminFiles = [
        'admin/index.html' => 'الصفحة الرئيسية للإدارة',
        'admin/landing-pages.html' => 'صفحة إدارة صفحات الهبوط'
    ];
    
    foreach ($adminFiles as $adminFile => $description) {
        if (file_exists($adminFile)) {
            echo "<div class='success'>✅ {$description} موجود</div>";
            
            // Check for landing page button
            $content = file_get_contents($adminFile);
            if (strpos($content, 'addLandingPageBtn') !== false) {
                echo "<div class='info'>ℹ️ يحتوي على زر إضافة صفحة الهبوط</div>";
            }
        } else {
            echo "<div class='error'>❌ {$description} غير موجود</div>";
        }
    }
    echo "</div>";
    echo "</div>";
    
    // SOLUTION RECOMMENDATIONS
    echo "<div class='section'>";
    echo "<h2>💡 توصيات الحلول</h2>";
    
    echo "<div class='warning'>";
    echo "<h3>🔧 الإصلاحات المطلوبة بالأولوية:</h3>";
    echo "<ol>";
    echo "<li><strong>إصلاح مسارات ملف التكوين:</strong> تحديث جميع ملفات API لاستخدام المسار الصحيح</li>";
    echo "<li><strong>إصلاح ملفات JavaScript:</strong> إزالة محتوى HTML/PHP من ملفات JS</li>";
    echo "<li><strong>إصلاح أخطاء بناء الجملة:</strong> حل مشاكل async/await والدوال المكررة</li>";
    echo "<li><strong>إصلاح ظهور الواجهة:</strong> ضمان ظهور أزرار وعناصر الواجهة</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>📋 خطة العمل:</h3>";
    echo "<ol>";
    echo "<li>تشغيل سكريبت إصلاح مسارات التكوين</li>";
    echo "<li>تنظيف ملفات JavaScript من المحتوى غير المرغوب</li>";
    echo "<li>إصلاح أخطاء بناء الجملة في JavaScript</li>";
    echo "<li>تحديث ملفات CSS لضمان ظهور العناصر</li>";
    echo "<li>اختبار جميع APIs والواجهات</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام في التشخيص: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
