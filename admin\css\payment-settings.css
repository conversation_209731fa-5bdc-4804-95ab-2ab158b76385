/* Payment Settings Specific Styles */

/* Payment Settings Header */
.payment-settings-header {
    background: #ffffff;
    padding: 25px;
    border-radius: 16px;
    box-shadow: var(--card-shadow);
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 20px;
}

.section-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.section-title-content h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text-primary);
}

.section-subtitle {
    margin: 5px 0 0;
    color: var(--color-text-secondary);
    font-size: 0.9rem;
}

.settings-summary {
    display: flex;
    gap: 20px;
    align-items: center;
}

.summary-item {
    background: #f8fafc;
    padding: 12px 20px;
    border-radius: 10px;
    border: 1px solid var(--color-border);
}

.summary-label {
    color: var(--color-text-secondary);
    font-size: 0.85rem;
    display: block;
    margin-bottom: 4px;
}

.summary-value {
    color: var(--color-text-primary);
    font-weight: 600;
    font-size: 1.1rem;
}

.status-active {
    color: #10b981;
}

/* Payment Method Cards */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.setting-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: var(--transition-base);
    border: 1px solid var(--color-border);
}

.setting-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.setting-header {
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    border-bottom: 1px solid var(--color-border);
}

.setting-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.setting-info {
    flex: 1;
}

.setting-info h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-text-primary);
}

.setting-info p {
    margin: 4px 0 0;
    color: var(--color-text-secondary);
    font-size: 0.85rem;
}

.setting-toggle {
    margin-right: auto;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e2e8f0;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .slider:before {
    transform: translateX(24px);
}

/* Setting Content */
.setting-content {
    padding: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.enhanced-label {
    display: block;
    margin-bottom: 8px;
    color: var(--color-text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.enhanced-label i {
    margin-left: 8px;
    color: var(--color-text-secondary);
}

.enhanced-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    transition: var(--transition-base);
    font-size: 0.9rem;
}

.enhanced-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.enhanced-input.error {
    border-color: #ef4444;
}

.field-error {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 4px;
}

/* Test Button */
.test-payment-btn {
    margin-top: 15px;
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    background: #f8fafc;
    color: var(--color-text-primary);
    font-weight: 500;
    transition: var(--transition-base);
    cursor: pointer;
}

.test-payment-btn:hover {
    background: #f1f5f9;
}

.test-payment-btn.success {
    background: #10b981;
    color: white;
}

.test-payment-btn.error {
    background: #ef4444;
    color: white;
}

/* Save Actions */
.save-actions {
    position: sticky;
    bottom: 20px;
    background: white;
    padding: 20px;
    border-radius: 16px;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    z-index: 100;
}

.enhanced-save-btn,
.enhanced-test-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    gap: 8px;
}

.enhanced-save-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.enhanced-test-btn {
    background: #f8fafc;
    color: var(--color-text-primary);
}

.enhanced-save-btn:hover,
.enhanced-test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-settings-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .settings-summary {
        width: 100%;
        flex-direction: column;
        align-items: stretch;
    }

    .setting-card {
        width: 100%;
    }

    .save-actions {
        flex-direction: column;
    }

    .enhanced-save-btn,
    .enhanced-test-btn {
        width: 100%;
        justify-content: center;
    }
}