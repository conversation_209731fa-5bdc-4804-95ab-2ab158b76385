<?php

/**
 * Migration: FixMissingColumns
 * Created: 2025-07-22 10:22:17
 */

class FixMissingColumns
{
    private $pdo;

    public function __construct($pdo)
    {
        $this->pdo = $pdo;
    }

    /**
     * Run the migration
     */
    public function up()
    {
        echo "🔧 Adding missing columns to subscription_plans and user_roles tables...\n";

        // Add is_featured column to subscription_plans table
        $this->pdo->exec("
            ALTER TABLE subscription_plans
            ADD COLUMN is_featured TINYINT(1) DEFAULT 0 AFTER is_active
        ");
        echo "✅ Added is_featured column to subscription_plans table\n";

        // Add description column to user_roles table (consolidating description_ar and description_en)
        $this->pdo->exec("
            ALTER TABLE user_roles
            ADD COLUMN description TEXT AFTER display_name_en
        ");
        echo "✅ Added description column to user_roles table\n";

        // Add permissions column to user_roles table
        $this->pdo->exec("
            ALTER TABLE user_roles
            ADD COLUMN permissions JSON AFTER description
        ");
        echo "✅ Added permissions column to user_roles table\n";

        // Migrate existing description data from description_ar to description
        $this->pdo->exec("
            UPDATE user_roles
            SET description = description_ar
            WHERE description_ar IS NOT NULL AND description_ar != ''
        ");
        echo "✅ Migrated existing description data\n";

        // Set default permissions for existing roles
        $this->pdo->exec("
            UPDATE user_roles
            SET permissions = '[]'
            WHERE permissions IS NULL
        ");
        echo "✅ Set default permissions for existing roles\n";
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        echo "🔄 Removing added columns from subscription_plans and user_roles tables...\n";

        // Remove is_featured column from subscription_plans table
        $this->pdo->exec("ALTER TABLE subscription_plans DROP COLUMN is_featured");
        echo "✅ Removed is_featured column from subscription_plans table\n";

        // Remove description column from user_roles table
        $this->pdo->exec("ALTER TABLE user_roles DROP COLUMN description");
        echo "✅ Removed description column from user_roles table\n";

        // Remove permissions column from user_roles table
        $this->pdo->exec("ALTER TABLE user_roles DROP COLUMN permissions");
        echo "✅ Removed permissions column from user_roles table\n";
    }
}
