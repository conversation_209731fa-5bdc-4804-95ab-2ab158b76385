<?php
require_once 'php/config.php';

header('Content-Type: text/html');

echo "<h1>Landing Pages Table Structure</h1>";

try {
    $pdo = getPDOConnection();
    
    // Check table structure
    echo "<h2>Table Structure</h2>";
    $stmt = $pdo->query('DESCRIBE landing_pages');
    $columns = $stmt->fetchAll();
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "<td>" . $col['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check existing landing pages
    echo "<h2>Existing Landing Pages</h2>";
    $stmt = $pdo->query("SELECT * FROM landing_pages");
    $landingPages = $stmt->fetchAll();
    
    if ($landingPages) {
        echo "<p>Found " . count($landingPages) . " landing pages:</p>";
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($landingPages[0]) as $column) {
            echo "<th>" . htmlspecialchars($column) . "</th>";
        }
        echo "</tr>";
        
        foreach ($landingPages as $lp) {
            echo "<tr>";
            foreach ($lp as $value) {
                echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No landing pages found.</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p>❌ " . $e->getMessage() . "</p>";
}
?>
