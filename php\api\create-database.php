<?php
/**
 * Create Database Script
 * Creates the database if it doesn't exist
 */

// Detect if running via CLI or web
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// Error handling
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Load environment configuration
try {
    require_once __DIR__ . '/../config/env-loader.php';
    $envConfig = EnvLoader::getDatabaseConfig();
} catch (Exception $e) {
    $envConfig = [
        'host' => 'localhost',
        'port' => '3307',
        'database' => 'mossab-landing-page',
        'username' => 'root',
        'password' => ''
    ];
}

$results = [
    'success' => false,
    'message' => '',
    'steps' => [],
    'errors' => []
];

try {
    // First, try to connect without specifying database
    $dsn = "mysql:host={$envConfig['host']};port={$envConfig['port']};charset=utf8mb4";
    $pdo = new PDO($dsn, $envConfig['username'], $envConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    $results['steps'][] = "Connected to MySQL server successfully";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$envConfig['database']}'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        $results['steps'][] = "Database '{$envConfig['database']}' already exists";
        
        // Try to connect to the database
        $pdo->exec("USE `{$envConfig['database']}`");
        $results['steps'][] = "Successfully connected to database '{$envConfig['database']}'";
        
        // Check tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $results['steps'][] = "Found " . count($tables) . " tables in database";
        
        if (count($tables) > 0) {
            $results['steps'][] = "Tables: " . implode(', ', array_slice($tables, 0, 5)) . (count($tables) > 5 ? '...' : '');
        }
        
    } else {
        // Create database
        $pdo->exec("CREATE DATABASE `{$envConfig['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $results['steps'][] = "Created database '{$envConfig['database']}'";
        
        // Connect to the new database
        $pdo->exec("USE `{$envConfig['database']}`");
        $results['steps'][] = "Connected to new database";
    }
    
    // Test final connection
    $stmt = $pdo->query("SELECT DATABASE() as current_db, VERSION() as version");
    $info = $stmt->fetch();
    
    $results['success'] = true;
    $results['message'] = 'Database setup completed successfully';
    $results['database_info'] = [
        'name' => $info['current_db'],
        'version' => $info['version'],
        'host' => $envConfig['host'],
        'port' => $envConfig['port']
    ];
    
} catch (PDOException $e) {
    $results['errors'][] = "Database error: " . $e->getMessage();
    $results['message'] = 'Database setup failed: ' . $e->getMessage();
} catch (Exception $e) {
    $results['errors'][] = "General error: " . $e->getMessage();
    $results['message'] = 'Setup failed: ' . $e->getMessage();
}

// Output based on context
if ($isCLI) {
    echo "=== Database Creation Results ===\n";
    echo "Status: " . ($results['success'] ? 'SUCCESS' : 'FAILED') . "\n";
    echo "Message: " . $results['message'] . "\n\n";
    
    if (!empty($results['steps'])) {
        echo "Steps completed:\n";
        foreach ($results['steps'] as $step) {
            echo "  ✓ $step\n";
        }
        echo "\n";
    }
    
    if (!empty($results['errors'])) {
        echo "Errors:\n";
        foreach ($results['errors'] as $error) {
            echo "  ✗ $error\n";
        }
        echo "\n";
    }
    
    if (isset($results['database_info'])) {
        echo "Database Info:\n";
        echo "  Name: {$results['database_info']['name']}\n";
        echo "  Version: {$results['database_info']['version']}\n";
        echo "  Host: {$results['database_info']['host']}:{$results['database_info']['port']}\n";
    }
} else {
    echo json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?>
