<?php

/**
 * Roles Management API with Enhanced Error Handling
 * Handles CRUD operations for user roles and permissions
 * Addresses JSON parsing errors and implements comprehensive error handling
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../ApiErrorHandler.php';
require_once __DIR__ . '/../../config/environment.php';

// Initialize error handler to catch any issues before JSON output
ApiErrorHandler::init();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $pdo = EnvironmentConfig::getInstance()->getDatabaseConnection();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    // Validate HTTP method
    ApiErrorHandler::checkMethod(['GET', 'POST', 'PUT', 'DELETE']);

    // Handle different HTTP methods and actions
    switch ($method) {
        case 'GET':
            if ($action === 'list' || empty($action)) {
                handleGetRoles($pdo);
            } elseif ($action === 'permissions') {
                handleGetPermissions($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'POST':
            if ($action === 'create') {
                handleCreateRole($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'PUT':
            if ($action === 'update') {
                handleUpdateRole($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'DELETE':
            if ($action === 'delete') {
                handleDeleteRole($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        default:
            ApiErrorHandler::sendError('طريقة الطلب غير مسموحة', 405, 'METHOD_NOT_ALLOWED');
    }
} catch (Exception $e) {
    ApiErrorHandler::sendError(
        'خطأ في API الأدوار: ' . $e->getMessage(),
        500,
        'ROLES_API_ERROR'
    );
}

/**
 * Get all roles
 */
function handleGetRoles($pdo)
{
    try {
        $stmt = $pdo->prepare("
            SELECT
                r.*,
                COUNT(u.id) as user_count
            FROM user_roles r
            LEFT JOIN users u ON r.id = u.role_id
            GROUP BY r.id
            ORDER BY r.level ASC, r.display_name_ar ASC
        ");

        $stmt->execute();
        $roles = $stmt->fetchAll();

        // Format roles for response
        $formattedRoles = array_map(function ($role) {
            return [
                'id' => (int)$role['id'],
                'name' => $role['name'],
                'display_name_ar' => $role['display_name_ar'],
                'display_name_en' => $role['display_name_en'],
                'description' => $role['description'],
                'permissions' => json_decode($role['permissions'] ?? '[]', true),
                'level' => (int)$role['level'],
                'is_active' => (bool)$role['is_active'],
                'user_count' => (int)$role['user_count'],
                'created_at' => $role['created_at'],
                'updated_at' => $role['updated_at']
            ];
        }, $roles);

        ApiErrorHandler::sendSuccess([
            'roles' => $formattedRoles,
            'total' => count($formattedRoles)
        ], 'تم تحميل الأدوار بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في جلب الأدوار: ' . $e->getMessage(),
            500,
            'FETCH_ROLES_ERROR'
        );
    }
}

/**
 * Get available permissions
 */
function handleGetPermissions($pdo)
{
    try {
        // Define available permissions
        $permissions = [
            'users' => [
                'view' => 'عرض المستخدمين',
                'create' => 'إضافة مستخدمين',
                'edit' => 'تعديل المستخدمين',
                'delete' => 'حذف المستخدمين'
            ],
            'products' => [
                'view' => 'عرض المنتجات',
                'create' => 'إضافة منتجات',
                'edit' => 'تعديل المنتجات',
                'delete' => 'حذف المنتجات'
            ],
            'orders' => [
                'view' => 'عرض الطلبات',
                'edit' => 'تعديل الطلبات',
                'delete' => 'حذف الطلبات'
            ],
            'settings' => [
                'view' => 'عرض الإعدادات',
                'edit' => 'تعديل الإعدادات'
            ],
            'reports' => [
                'view' => 'عرض التقارير',
                'export' => 'تصدير التقارير'
            ],
            'roles' => [
                'view' => 'عرض الأدوار',
                'create' => 'إضافة أدوار',
                'edit' => 'تعديل الأدوار',
                'delete' => 'حذف الأدوار'
            ]
        ];

        ApiErrorHandler::sendSuccess([
            'permissions' => $permissions
        ], 'تم تحميل الصلاحيات بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في جلب الصلاحيات: ' . $e->getMessage(),
            500,
            'FETCH_PERMISSIONS_ERROR'
        );
    }
}

/**
 * Create a new role
 */
function handleCreateRole($pdo)
{
    try {
        $input = ApiErrorHandler::getJsonInput();
        if ($input === false) return; // Error already sent

        // Validate required fields
        $required = ['name', 'display_name_ar', 'display_name_en', 'level'];
        if (!ApiErrorHandler::validateRequired($input, $required)) return;

        // Check if role name already exists
        $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE name = ?");
        $stmt->execute([$input['name']]);
        if ($stmt->fetch()) {
            ApiErrorHandler::sendError('اسم الدور موجود بالفعل', 400, 'ROLE_NAME_EXISTS');
            return;
        }

        // Insert role
        $stmt = $pdo->prepare("
            INSERT INTO user_roles (name, display_name_ar, display_name_en, description, permissions, level, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $permissions = json_encode($input['permissions'] ?? []);

        $stmt->execute([
            $input['name'],
            $input['display_name_ar'],
            $input['display_name_en'],
            $input['description'] ?? '',
            $permissions,
            $input['level'],
            $input['is_active'] ?? 1
        ]);

        $roleId = $pdo->lastInsertId();

        ApiErrorHandler::sendSuccess([
            'role_id' => (int)$roleId
        ], 'تم إنشاء الدور بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في إنشاء الدور: ' . $e->getMessage(),
            500,
            'CREATE_ROLE_ERROR'
        );
    }
}

/**
 * Update an existing role
 */
function handleUpdateRole($pdo)
{
    try {
        $input = ApiErrorHandler::getJsonInput();
        if ($input === false) return; // Error already sent

        if (!isset($input['id']) || empty($input['id'])) {
            ApiErrorHandler::sendError('معرف الدور مطلوب', 400, 'MISSING_ROLE_ID');
            return;
        }

        $roleId = $input['id'];
        $updates = [];
        $params = [];

        // Build dynamic update query
        $allowedFields = ['name', 'display_name_ar', 'display_name_en', 'description', 'level', 'is_active'];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updates[] = "$field = ?";
                $params[] = $input[$field];
            }
        }

        // Handle permissions separately
        if (isset($input['permissions'])) {
            $updates[] = "permissions = ?";
            $params[] = json_encode($input['permissions']);
        }

        if (empty($updates)) {
            ApiErrorHandler::sendError('لا توجد بيانات للتحديث', 400, 'NO_UPDATE_DATA');
            return;
        }

        $updates[] = "updated_at = NOW()";
        $params[] = $roleId;

        $sql = "UPDATE user_roles SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        if ($stmt->rowCount() === 0) {
            ApiErrorHandler::sendError('الدور غير موجود', 404, 'ROLE_NOT_FOUND');
            return;
        }

        ApiErrorHandler::sendSuccess(null, 'تم تحديث الدور بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في تحديث الدور: ' . $e->getMessage(),
            500,
            'UPDATE_ROLE_ERROR'
        );
    }
}

/**
 * Delete a role
 */
function handleDeleteRole($pdo)
{
    try {
        $input = ApiErrorHandler::getJsonInput();
        if ($input === false) return; // Error already sent

        if (!isset($input['id']) || empty($input['id'])) {
            ApiErrorHandler::sendError('معرف الدور مطلوب', 400, 'MISSING_ROLE_ID');
            return;
        }

        $roleId = $input['id'];

        // Check if role is in use
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE role_id = ?");
        $stmt->execute([$roleId]);
        $userCount = $stmt->fetch()['count'];

        if ($userCount > 0) {
            ApiErrorHandler::sendError(
                "لا يمكن حذف الدور لأنه مستخدم من قبل $userCount مستخدم",
                400,
                'ROLE_IN_USE'
            );
            return;
        }

        // Delete role
        $stmt = $pdo->prepare("DELETE FROM user_roles WHERE id = ?");
        $stmt->execute([$roleId]);

        if ($stmt->rowCount() === 0) {
            ApiErrorHandler::sendError('الدور غير موجود', 404, 'ROLE_NOT_FOUND');
            return;
        }

        ApiErrorHandler::sendSuccess(null, 'تم حذف الدور بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في حذف الدور: ' . $e->getMessage(),
            500,
            'DELETE_ROLE_ERROR'
        );
    }
}
