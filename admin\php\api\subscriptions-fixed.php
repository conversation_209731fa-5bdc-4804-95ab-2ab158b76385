<?php

/**
 * Subscriptions API - Fixed Version
 * Handles all subscription-related API requests
 */

// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = 'localhost';
$port = '3307';
$dbname = 'mossab-landing-page';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get the action parameter
$action = $_GET['action'] ?? $_POST['action'] ?? 'plans';

try {
    switch ($action) {
        case 'plans':
            handleListPlans($pdo);
            break;
        case 'create':
            handleCreatePlan($pdo);
            break;
        case 'update':
            handleUpdatePlan($pdo);
            break;
        case 'delete':
            handleDeletePlan($pdo);
            break;
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action: ' . $action
            ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleListPlans($pdo)
{
    try {
        // Create subscription_plans table if it doesn't exist
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS subscription_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                price DECIMAL(10,2) NOT NULL,
                duration_months INT NOT NULL DEFAULT 1,
                features JSON,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($createTableSQL);

        // Insert sample data if table is empty
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM subscription_plans");
        $count = $countStmt->fetch()['count'];

        if ($count == 0) {
            $samplePlans = [
                [
                    'name' => 'الخطة الأساسية',
                    'description' => 'خطة مناسبة للمبتدئين',
                    'price' => 29.99,
                    'duration_months' => 1,
                    'features' => json_encode(['5 منتجات', 'دعم أساسي', 'تقارير شهرية'])
                ],
                [
                    'name' => 'الخطة المتقدمة',
                    'description' => 'خطة للشركات المتوسطة',
                    'price' => 59.99,
                    'duration_months' => 1,
                    'features' => json_encode(['50 منتج', 'دعم متقدم', 'تقارير أسبوعية', 'تحليلات متقدمة'])
                ],
                [
                    'name' => 'الخطة الاحترافية',
                    'description' => 'خطة للشركات الكبيرة',
                    'price' => 99.99,
                    'duration_months' => 1,
                    'features' => json_encode(['منتجات غير محدودة', 'دعم 24/7', 'تقارير يومية', 'API متقدم'])
                ]
            ];

            $insertStmt = $pdo->prepare("INSERT INTO subscription_plans (name, description, price, duration_months, features) VALUES (?, ?, ?, ?, ?)");
            foreach ($samplePlans as $plan) {
                $insertStmt->execute([$plan['name'], $plan['description'], $plan['price'], $plan['duration_months'], $plan['features']]);
            }
        }

        // Fetch all plans
        $stmt = $pdo->query("SELECT * FROM subscription_plans ORDER BY price ASC");
        $plans = $stmt->fetchAll();

        // Decode features JSON for each plan
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'], true) ?? [];
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'plans' => $plans,
                'total' => count($plans)
            ],
            'message' => 'Subscription plans retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve subscription plans: ' . $e->getMessage());
    }
}

function handleCreatePlan($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['name']) || !isset($input['price'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Plan name and price are required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO subscription_plans (name, description, price, duration_months, features, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            $input['price'],
            $input['duration_months'] ?? 1,
            json_encode($input['features'] ?? []),
            $input['status'] ?? 'active'
        ]);

        $planId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $planId,
                'name' => $input['name']
            ],
            'message' => 'Subscription plan created successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to create subscription plan: ' . $e->getMessage());
    }
}

function handleUpdatePlan($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['id']) || !isset($input['name']) || !isset($input['price'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Plan ID, name and price are required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("UPDATE subscription_plans SET name = ?, description = ?, price = ?, duration_months = ?, features = ?, status = ? WHERE id = ?");
        $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            $input['price'],
            $input['duration_months'] ?? 1,
            json_encode($input['features'] ?? []),
            $input['status'] ?? 'active',
            $input['id']
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $input['id'],
                'name' => $input['name']
            ],
            'message' => 'Subscription plan updated successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to update subscription plan: ' . $e->getMessage());
    }
}

function handleDeletePlan($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Plan ID is required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM subscription_plans WHERE id = ?");
        $stmt->execute([$input['id']]);

        echo json_encode([
            'success' => true,
            'message' => 'Subscription plan deleted successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to delete subscription plan: ' . $e->getMessage());
    }
}
