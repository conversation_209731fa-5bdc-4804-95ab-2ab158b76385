# 🚀 Server Setup Guide - <PERSON><PERSON><PERSON> Landing Page

## 🔧 Current Issues and Solutions

### Issue 1: Port Mismatch
**Problem**: The batch file runs on port 8080, but you're accessing port 8000.

**Solution**: Use the correct port or update the batch file.

### Issue 2: PHP Not Executing
**Problem**: API endpoints return PHP source code instead of JSON.

**Solution**: Ensure PHP server is running correctly.

## 📋 Quick Fix Steps

### Step 1: Use Correct Port
Access the application on the correct port:
- **Main Site**: `http://localhost:8080`
- **Admin Panel**: `http://localhost:8080/admin/`
- **API Test**: `http://localhost:8080/php/api/test-api.php`

### Step 2: Start PHP Server Correctly
Run the batch file from the project root:
```batch
start-php-server.bat
```

Or manually start the server:
```bash
cd /path/to/project
php -S localhost:8080 -t . router.php
```

### Step 3: Test API Endpoints
Test these simplified endpoints first:
1. `http://localhost:8080/php/api/test-api.php` - Basic PHP test
2. `http://localhost:8080/php/api/products-simple.php` - Simple products API
3. `http://localhost:8080/api/get-ai-settings.php` - AI settings API

### Step 4: Database Configuration
Ensure your `.env` file has correct database settings:
```env
DB_HOST=localhost
DB_PORT=3307
DB_USERNAME=root
DB_PASSWORD=
DB_DATABASE=mossab-landing-page
```

## 🧪 Testing Commands

### Test 1: Basic PHP Execution
```bash
curl http://localhost:8080/php/api/test-api.php
```

Expected response:
```json
{
    "success": true,
    "message": "PHP is working correctly",
    "timestamp": "2025-07-26 12:00:00",
    "server_info": {
        "php_version": "8.x.x",
        "request_method": "GET",
        "request_uri": "/php/api/test-api.php",
        "server_port": "8080"
    }
}
```

### Test 2: Products API
```bash
curl http://localhost:8080/php/api/products-simple.php
```

### Test 3: AI Settings API
```bash
curl http://localhost:8080/api/get-ai-settings.php
```

## 🔍 Troubleshooting

### If PHP Still Returns Source Code:
1. Check if PHP is installed: `php --version`
2. Verify the server is running on the correct port
3. Check for syntax errors in PHP files
4. Ensure the router.php file is working correctly

### If Database Connection Fails:
1. Verify MySQL is running on port 3307
2. Check database credentials in `.env` file
3. Ensure the database `mossab-landing-page` exists
4. Run database migrations if needed

### If CORS Issues Occur:
The APIs now include proper CORS headers:
```php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
```

## 📝 Next Steps

1. **Test the basic APIs** using the correct port (8080)
2. **Verify database connection** and run migrations if needed
3. **Check browser console** for any remaining errors
4. **Update frontend URLs** if they're hardcoded to port 8000

## 🆘 Emergency Fallback

If the main APIs still don't work, use these simplified versions:
- `php/api/test-api.php` - Basic functionality test
- `php/api/products-simple.php` - Simplified products API
- `api/get-ai-settings.php` - Fixed AI settings API

These have minimal dependencies and should work even if other components fail.
