<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Firebase Authentication Test</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem;
        direction: rtl;
      }

      .test-container {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        max-width: 800px;
        margin: 0 auto;
      }

      .test-header {
        text-align: center;
        margin-bottom: 2rem;
        color: #333;
      }

      .test-section {
        margin: 1.5rem 0;
        padding: 1rem;
        border: 1px solid #e1e5e9;
        border-radius: 5px;
      }

      .status {
        padding: 0.5rem;
        border-radius: 3px;
        margin: 0.5rem 0;
      }

      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      .test-button {
        background: #667eea;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 3px;
        cursor: pointer;
        margin: 0.25rem;
      }

      .test-button:hover {
        background: #5a6fd8;
      }

      .code-block {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 3px;
        padding: 1rem;
        font-family: "Courier New", monospace;
        font-size: 0.9rem;
        white-space: pre-wrap;
        direction: ltr;
        text-align: left;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <div class="test-header">
        <h1>🔥 Firebase Authentication Test</h1>
        <p>Production Authentication System Verification</p>
      </div>

      <div class="test-section">
        <h3>🔧 Firebase Initialization Status</h3>
        <div id="firebaseStatus"></div>
        <button class="test-button" onclick="checkFirebaseInit()">
          Check Firebase
        </button>
      </div>

      <div class="test-section">
        <h3>🔐 Authentication Status</h3>
        <div id="authStatus"></div>
        <button class="test-button" onclick="checkAuthStatus()">
          Check Auth
        </button>
      </div>

      <div class="test-section">
        <h3>👤 User Profile</h3>
        <div id="userProfile"></div>
        <button class="test-button" onclick="checkUserProfile()">
          Check Profile
        </button>
      </div>

      <div class="test-section">
        <h3>🧪 Quick Actions</h3>
        <button class="test-button" onclick="runAllTests()">
          Run All Tests
        </button>
        <button class="test-button" onclick="signOutUser()">Sign Out</button>
        <a
          href="login.html"
          class="test-button"
          style="text-decoration: none; display: inline-block"
          >Go to Login</a
        >
        <a
          href="index.html"
          class="test-button"
          style="text-decoration: none; display: inline-block"
          >Go to Dashboard</a
        >
      </div>

      <div class="test-section">
        <h3>📋 Test Results</h3>
        <div id="testResults" class="code-block"></div>
      </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module" src="js/firebase-config.js"></script>

    <script type="module">
      let testResults = [];

      function log(message, type = "info") {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}`;
        testResults.push(logEntry);

        const resultsDiv = document.getElementById("testResults");
        resultsDiv.textContent = testResults.slice(-20).join("\n");

        console.log(logEntry);
      }

      function showStatus(elementId, message, type = "info") {
        const element = document.getElementById(elementId);
        element.innerHTML = `<div class="status ${type}">${message}</div>`;
      }

      window.checkFirebaseInit = function () {
        log("Checking Firebase initialization...");

        let status = "";

        // Check if Firebase is loaded
        if (
          typeof window.firebaseAuth !== "undefined" &&
          window.firebaseAuth !== null
        ) {
          status +=
            '<div class="status success">✅ Firebase Auth Manager: LOADED</div>';

          // Get detailed initialization status
          if (
            typeof window.firebaseAuth.getInitializationStatus === "function"
          ) {
            const initStatus = window.firebaseAuth.getInitializationStatus();

            // Check Firebase app
            if (initStatus.app && window.firebaseAuth.app) {
              status +=
                '<div class="status success">✅ Firebase App: INITIALIZED</div>';
              status += `<div class="status info">Project ID: ${window.firebaseAuth.app.options.projectId}</div>`;
              status += `<div class="status info">Auth Domain: ${window.firebaseAuth.app.options.authDomain}</div>`;
            } else {
              status +=
                '<div class="status error">❌ Firebase App: NOT INITIALIZED</div>';
            }

            // Check Firestore
            if (initStatus.db && window.firebaseAuth.db) {
              status +=
                '<div class="status success">✅ Firestore: CONNECTED</div>';

              // Test Firestore connection
              if (
                typeof window.firebaseAuth.testFirestoreConnection ===
                "function"
              ) {
                window.firebaseAuth.testFirestoreConnection().then((result) => {
                  if (result) {
                    log("Firestore connectivity test: PASSED");
                  } else {
                    log("Firestore connectivity test: FAILED");
                  }
                });
              }
            } else {
              status +=
                '<div class="status error">❌ Firestore: NOT CONNECTED</div>';
            }

            // Check Auth
            if (initStatus.auth && window.firebaseAuth.auth) {
              status +=
                '<div class="status success">✅ Firebase Auth: READY</div>';
            } else {
              status +=
                '<div class="status error">❌ Firebase Auth: NOT READY</div>';
            }
          } else {
            status +=
              '<div class="status error">❌ Firebase Auth Manager: INCOMPLETE</div>';
          }
        } else {
          status +=
            '<div class="status error">❌ Firebase Auth Manager: NOT LOADED</div>';

          // Check if Firebase scripts are loaded
          if (typeof window.firebase !== "undefined") {
            status += '<div class="status info">ℹ️ Firebase SDK: LOADED</div>';
          } else {
            status +=
              '<div class="status error">❌ Firebase SDK: NOT LOADED</div>';
          }
        }

        showStatus("firebaseStatus", status);
        log("Firebase initialization check completed");
      };

      window.checkAuthStatus = function () {
        log("Checking authentication status...");

        if (typeof window.firebaseAuth === "undefined") {
          showStatus(
            "authStatus",
            '<div class="status error">❌ Firebase Auth not available</div>'
          );
          log("Firebase Auth not available");
          return;
        }

        const isAuthenticated = window.firebaseAuth.isAuthenticated();
        const currentUser = window.firebaseAuth.getCurrentUser();

        let status = "";

        if (isAuthenticated && currentUser.user) {
          status +=
            '<div class="status success">✅ Authentication: AUTHENTICATED</div>';
          status += `<div class="status info">Email: ${currentUser.user.email}</div>`;
          status += `<div class="status info">UID: ${currentUser.user.uid}</div>`;
          status += `<div class="status info">Email Verified: ${currentUser.user.emailVerified}</div>`;

          if (currentUser.isAdmin) {
            status +=
              '<div class="status success">✅ Admin Access: GRANTED</div>';
          } else {
            status += '<div class="status error">❌ Admin Access: DENIED</div>';
          }
        } else {
          status +=
            '<div class="status error">❌ Authentication: NOT AUTHENTICATED</div>';
        }

        showStatus("authStatus", status);
        log(
          `Auth status: ${
            isAuthenticated ? "authenticated" : "not authenticated"
          }`
        );
      };

      window.checkUserProfile = function () {
        log("Checking user profile...");

        if (typeof window.firebaseAuth === "undefined") {
          showStatus(
            "userProfile",
            '<div class="status error">❌ Firebase Auth not available</div>'
          );
          return;
        }

        const currentUser = window.firebaseAuth.getCurrentUser();

        if (!currentUser.user) {
          showStatus(
            "userProfile",
            '<div class="status error">❌ No authenticated user</div>'
          );
          log("No authenticated user found");
          return;
        }

        let status = "";

        if (currentUser.profile) {
          status += '<div class="status success">✅ Profile: LOADED</div>';
          status += `<div class="status info">Display Name: ${
            currentUser.profile.displayName || "Not set"
          }</div>`;
          status += `<div class="status info">Role: ${
            currentUser.profile.role || "Not set"
          }</div>`;
          status += `<div class="status info">Created: ${
            currentUser.profile.createdAt || "Unknown"
          }</div>`;
          status += `<div class="status info">Last Login: ${
            currentUser.profile.lastLogin || "Unknown"
          }</div>`;
          status += `<div class="status info">Active: ${
            currentUser.profile.isActive ? "Yes" : "No"
          }</div>`;
        } else {
          status += '<div class="status error">❌ Profile: NOT LOADED</div>';
        }

        showStatus("userProfile", status);
        log("User profile check completed");
      };

      window.runAllTests = function () {
        log("Running all tests...");
        checkFirebaseInit();
        setTimeout(() => checkAuthStatus(), 500);
        setTimeout(() => checkUserProfile(), 1000);
      };

      window.signOutUser = async function () {
        log("Signing out user...");

        if (typeof window.firebaseAuth === "undefined") {
          log("Firebase Auth not available");
          return;
        }

        try {
          await window.firebaseAuth.signOutUser();
          log("User signed out successfully");
          setTimeout(() => runAllTests(), 1000);
        } catch (error) {
          log(`Sign out error: ${error.message}`);
        }
      };

      // Initialize tests when page loads
      document.addEventListener("DOMContentLoaded", function () {
        log("Firebase Auth Test page loaded");

        // Wait for Firebase to initialize
        setTimeout(() => {
          runAllTests();
        }, 2000);
      });

      // Make functions globally available
      window.log = log;
      window.showStatus = showStatus;
    </script>
  </body>
</html>
