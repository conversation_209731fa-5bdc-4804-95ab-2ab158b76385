<?php
/**
 * Update Demo Store Configuration
 * Changes demo store slug from mossaab-main-store to mossaab-store
 */

require_once __DIR__ . '/../php/config.php';

echo "🔧 Updating Demo Store Configuration...\n\n";

try {
    $pdo = getPDOConnection();
    
    // Check current stores
    echo "📋 Current stores:\n";
    $stmt = $pdo->query("SELECT id, store_name, store_slug, status FROM stores");
    $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($stores as $store) {
        echo "  - ID: {$store['id']}\n";
        echo "    Name: {$store['store_name']}\n";
        echo "    Slug: {$store['store_slug']}\n";
        echo "    Status: {$store['status']}\n\n";
    }
    
    // Update the demo store
    echo "🔄 Updating demo store...\n";
    $stmt = $pdo->prepare("
        UPDATE stores 
        SET store_slug = ?, store_name = ? 
        WHERE store_slug = ? OR store_name LIKE '%مصعب%'
    ");
    
    $result = $stmt->execute(['mossaab-store', 'متجر مصعب', 'mossaab-main-store']);
    
    if ($result) {
        $rowsAffected = $stmt->rowCount();
        echo "✅ Demo store updated successfully! ({$rowsAffected} rows affected)\n\n";
        
        // Verify the update
        echo "📋 Updated stores:\n";
        $stmt = $pdo->query("SELECT id, store_name, store_slug, status FROM stores");
        $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($stores as $store) {
            echo "  - ID: {$store['id']}\n";
            echo "    Name: {$store['store_name']}\n";
            echo "    Slug: {$store['store_slug']}\n";
            echo "    Status: {$store['status']}\n\n";
        }
        
        // Test the API with updated data
        echo "🧪 Testing stores API with updated data...\n";
        $stmt = $pdo->prepare("
            SELECT
                s.*,
                CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
                u.email as owner_email,
                u.phone as owner_phone,
                u.created_at as user_created_at
            FROM stores s
            LEFT JOIN users u ON s.user_id = u.id
            ORDER BY s.created_at DESC
        ");
        
        $stmt->execute();
        $apiStores = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "✅ API query successful! Found " . count($apiStores) . " stores\n";
        
        foreach ($apiStores as $store) {
            echo "  Store: {$store['store_name']}\n";
            echo "  Slug: {$store['store_slug']}\n";
            echo "  Owner: {$store['owner_name']}\n";
            echo "  Email: {$store['owner_email']}\n\n";
        }
        
        echo "🎉 Demo store configuration updated successfully!\n";
        echo "🔗 New store URL: http://localhost:8000/store/mossaab-store\n";
        
    } else {
        echo "❌ Failed to update demo store.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error updating demo store: " . $e->getMessage() . "\n";
}
?>
