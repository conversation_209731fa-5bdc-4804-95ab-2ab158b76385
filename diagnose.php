<?php
/**
 * Diagnostic Script for Mossaab Landing Page
 * Helps identify server and configuration issues
 */

// Set headers for web access
if (isset($_SERVER['HTTP_HOST'])) {
    header('Content-Type: text/html; charset=utf-8');
} else {
    // CLI mode
    echo "=== Mossaab Landing Page Diagnostic ===\n";
}

function webOutput($message, $type = 'info') {
    if (isset($_SERVER['HTTP_HOST'])) {
        $color = $type === 'error' ? 'red' : ($type === 'success' ? 'green' : 'blue');
        echo "<div style='color: $color; margin: 5px 0;'>$message</div>";
    } else {
        echo "$message\n";
    }
}

function checkPHPVersion() {
    webOutput("=== PHP Version Check ===", 'info');
    webOutput("PHP Version: " . PHP_VERSION, 'success');
    webOutput("PHP SAPI: " . php_sapi_name(), 'info');
    
    if (version_compare(PHP_VERSION, '7.4.0', '>=')) {
        webOutput("✅ PHP version is compatible", 'success');
    } else {
        webOutput("❌ PHP version is too old. Requires 7.4+", 'error');
    }
}

function checkServerInfo() {
    webOutput("=== Server Information ===", 'info');
    webOutput("Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'), 'info');
    webOutput("Server Port: " . ($_SERVER['SERVER_PORT'] ?? 'Unknown'), 'info');
    webOutput("Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'CLI'), 'info');
    webOutput("Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? getcwd()), 'info');
}

function checkFileStructure() {
    webOutput("=== File Structure Check ===", 'info');
    
    $requiredFiles = [
        'index.html' => 'Main entry point',
        'php/config.php' => 'Main configuration',
        'php/api/products.php' => 'Products API',
        'api/get-ai-settings.php' => 'AI Settings API',
        'router.php' => 'URL router',
        '.env' => 'Environment configuration'
    ];
    
    foreach ($requiredFiles as $file => $description) {
        if (file_exists($file)) {
            webOutput("✅ $file - $description", 'success');
        } else {
            webOutput("❌ $file - $description (MISSING)", 'error');
        }
    }
}

function checkDatabaseConnection() {
    webOutput("=== Database Connection Check ===", 'info');
    
    // Try to load config
    $configFiles = ['php/config.php', 'config/config.php'];
    $configLoaded = false;
    
    foreach ($configFiles as $configFile) {
        if (file_exists($configFile)) {
            try {
                require_once $configFile;
                $configLoaded = true;
                webOutput("✅ Config loaded from: $configFile", 'success');
                break;
            } catch (Exception $e) {
                webOutput("❌ Error loading $configFile: " . $e->getMessage(), 'error');
            }
        }
    }
    
    if (!$configLoaded) {
        webOutput("❌ No config file found", 'error');
        return;
    }
    
    // Try database connection
    try {
        if (function_exists('getPDOConnection')) {
            $pdo = getPDOConnection();
            webOutput("✅ Database connection successful", 'success');
            
            // Test a simple query
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
            $result = $stmt->fetch();
            webOutput("✅ Products table accessible. Count: " . $result['count'], 'success');
            
        } else {
            webOutput("❌ getPDOConnection function not available", 'error');
        }
    } catch (Exception $e) {
        webOutput("❌ Database connection failed: " . $e->getMessage(), 'error');
    }
}

function checkAPIEndpoints() {
    webOutput("=== API Endpoints Check ===", 'info');
    
    $endpoints = [
        'php/api/test-api.php' => 'Basic API test',
        'php/api/products-simple.php' => 'Simple products API',
        'api/get-ai-settings.php' => 'AI settings API'
    ];
    
    foreach ($endpoints as $endpoint => $description) {
        if (file_exists($endpoint)) {
            webOutput("✅ $endpoint - $description", 'success');
            
            // Try to include and check for syntax errors
            try {
                $content = file_get_contents($endpoint);
                if (strpos($content, '<?php') === 0) {
                    webOutput("  ✅ Valid PHP file", 'success');
                } else {
                    webOutput("  ❌ Not a valid PHP file", 'error');
                }
            } catch (Exception $e) {
                webOutput("  ❌ Error reading file: " . $e->getMessage(), 'error');
            }
        } else {
            webOutput("❌ $endpoint - $description (MISSING)", 'error');
        }
    }
}

function checkPermissions() {
    webOutput("=== Permissions Check ===", 'info');
    
    $directories = [
        'uploads' => 'File uploads',
        'uploads/products' => 'Product images',
        'cache' => 'Cache directory',
        'logs' => 'Log files'
    ];
    
    foreach ($directories as $dir => $description) {
        if (is_dir($dir)) {
            if (is_writable($dir)) {
                webOutput("✅ $dir - $description (writable)", 'success');
            } else {
                webOutput("⚠️ $dir - $description (not writable)", 'error');
            }
        } else {
            webOutput("❌ $dir - $description (missing)", 'error');
        }
    }
}

// HTML wrapper for web access
if (isset($_SERVER['HTTP_HOST'])) {
    echo "<!DOCTYPE html><html><head><title>Mossaab Landing Page Diagnostic</title></head><body>";
    echo "<h1>🔍 Mossaab Landing Page Diagnostic</h1>";
}

// Run all checks
checkPHPVersion();
checkServerInfo();
checkFileStructure();
checkDatabaseConnection();
checkAPIEndpoints();
checkPermissions();

// Recommendations
webOutput("=== Recommendations ===", 'info');
webOutput("1. Ensure you're accessing the correct port (8080, not 8000)", 'info');
webOutput("2. Start the server using: php -S localhost:8080 -t . router.php", 'info');
webOutput("3. Test basic API: http://localhost:8080/php/api/test-api.php", 'info');
webOutput("4. Check database configuration in .env file", 'info');

if (isset($_SERVER['HTTP_HOST'])) {
    echo "</body></html>";
}
?>
