# Script d'Organisation Simple du Projet
# Landing Pages SaaS

Write-Host "Demarrage du nettoyage et organisation du projet..." -ForegroundColor Green

# Definir le repertoire racine du projet
$ProjectRoot = Get-Location
Write-Host "Repertoire du projet: $ProjectRoot" -ForegroundColor Cyan

# Creer la structure de dossiers pour l'organisation
$FoldersToCreate = @(
    "scripts\tests",
    "scripts\debug",
    "scripts\setup",
    "scripts\maintenance",
    "documentation\summaries",
    "documentation\guides",
    "temp\backup",
    "temp\logs"
)

Write-Host "Creation de la structure de dossiers..." -ForegroundColor Yellow
foreach ($folder in $FoldersToCreate) {
    $fullPath = Join-Path $ProjectRoot $folder
    if (-not (Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "  Cree: $folder" -ForegroundColor Green
    } else {
        Write-Host "  Existe deja: $folder" -ForegroundColor Yellow
    }
}

# Fonction pour deplacer les fichiers
function Move-ProjectFiles {
    param(
        [string]$SourceDir,
        [string]$TargetDir,
        [string[]]$Patterns
    )
    
    $movedCount = 0
    foreach ($pattern in $Patterns) {
        $files = Get-ChildItem -Path $SourceDir -Name $pattern -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            $sourcePath = Join-Path $SourceDir $file
            $targetPath = Join-Path $TargetDir $file
            
            if (Test-Path $sourcePath) {
                try {
                    Move-Item -Path $sourcePath -Destination $targetPath -Force
                    Write-Host "    Deplace: $file" -ForegroundColor Green
                    $movedCount++
                }
                catch {
                    Write-Host "    Erreur lors du deplacement de $file" -ForegroundColor Red
                }
            }
        }
    }
    return $movedCount
}

# Deplacer les fichiers de test depuis la racine
Write-Host "Deplacement des fichiers de test depuis la racine..." -ForegroundColor Yellow
$testPatterns = @(
    "test-*.html",
    "test-*.php",
    "*-test.html",
    "*-test.php",
    "debug-*.html",
    "debug-*.php",
    "admin-test*.html"
)
$testsDir = Join-Path $ProjectRoot "scripts\tests"
$testsMoved = Move-ProjectFiles -SourceDir $ProjectRoot -TargetDir $testsDir -Patterns $testPatterns

# Deplacer les fichiers de debug depuis la racine
Write-Host "Deplacement des fichiers de debug depuis la racine..." -ForegroundColor Yellow
$debugPatterns = @(
    "debug*.php",
    "diagnose*.php",
    "check-*.php",
    "verify-*.php",
    "comprehensive-*.php",
    "analyze*.php",
    "quick-*.php"
)
$debugDir = Join-Path $ProjectRoot "scripts\debug"
$debugMoved = Move-ProjectFiles -SourceDir $ProjectRoot -TargetDir $debugDir -Patterns $debugPatterns

# Deplacer les fichiers de setup depuis la racine
Write-Host "Deplacement des fichiers de setup depuis la racine..." -ForegroundColor Yellow
$setupPatterns = @(
    "setup-*.php",
    "create-*.php",
    "populate-*.php",
    "add-*.php",
    "implement-*.php",
    "migration-*.php",
    "import*.php"
)
$setupDir = Join-Path $ProjectRoot "scripts\setup"
$setupMoved = Move-ProjectFiles -SourceDir $ProjectRoot -TargetDir $setupDir -Patterns $setupPatterns

# Deplacer les fichiers de maintenance depuis la racine
Write-Host "Deplacement des fichiers de maintenance depuis la racine..." -ForegroundColor Yellow
$maintenancePatterns = @(
    "fix-*.php",
    "update-*.php",
    "run-*.php",
    "master-fix-*.php",
    "complete-*.php",
    "integrate-*.php"
)
$maintenanceDir = Join-Path $ProjectRoot "scripts\maintenance"
$maintenanceMoved = Move-ProjectFiles -SourceDir $ProjectRoot -TargetDir $maintenanceDir -Patterns $maintenancePatterns

# Deplacer les fichiers de documentation depuis la racine
Write-Host "Deplacement des fichiers de documentation depuis la racine..." -ForegroundColor Yellow
$docPatterns = @(
    "*_SUMMARY.md",
    "*_FIXES_SUMMARY.md",
    "*_COMPLETE_SUMMARY.md",
    "*_IMPLEMENTATION_SUMMARY.md",
    "FIXES_SUMMARY.md",
    "CRITICAL_*.md",
    "ADMIN_*.md",
    "CHANGELOG.md",
    "*_GUIDE.md",
    "*_DOCUMENTATION.md",
    "DEPLOYMENT_GUIDE.md",
    "TECHNICAL_*.md",
    "GUIDE_*.md",
    "CODE_*.md"
)
$summariesDir = Join-Path $ProjectRoot "documentation\summaries"
$guidesDir = Join-Path $ProjectRoot "documentation\guides"

# Separer les fichiers de documentation
$summaryPatterns = @("*_SUMMARY.md", "*_FIXES_SUMMARY.md", "FIXES_SUMMARY.md", "CRITICAL_*.md", "ADMIN_*.md", "CHANGELOG.md")
$guidePatterns = @("*_GUIDE.md", "*_DOCUMENTATION.md", "DEPLOYMENT_GUIDE.md", "TECHNICAL_*.md", "GUIDE_*.md", "CODE_*.md")

$summariesMoved = Move-ProjectFiles -SourceDir $ProjectRoot -TargetDir $summariesDir -Patterns $summaryPatterns
$guidesMoved = Move-ProjectFiles -SourceDir $ProjectRoot -TargetDir $guidesDir -Patterns $guidePatterns

# Deplacer les fichiers de logs
Write-Host "Deplacement des fichiers de logs depuis la racine..." -ForegroundColor Yellow
$logPatterns = @(
    "*.err",
    "*.log",
    "query-result.txt",
    "console-errors*.err"
)
$logsDir = Join-Path $ProjectRoot "temp\logs"
$logsMoved = Move-ProjectFiles -SourceDir $ProjectRoot -TargetDir $logsDir -Patterns $logPatterns

# Traiter le dossier admin/
$adminDir = Join-Path $ProjectRoot "admin"
if (Test-Path $adminDir) {
    Write-Host "Deplacement des fichiers depuis admin/..." -ForegroundColor Yellow
    
    # Deplacer les fichiers de test depuis admin/
    $adminTestsMoved = Move-ProjectFiles -SourceDir $adminDir -TargetDir $testsDir -Patterns $testPatterns
    
    # Deplacer les fichiers de debug depuis admin/
    $adminDebugMoved = Move-ProjectFiles -SourceDir $adminDir -TargetDir $debugDir -Patterns $debugPatterns
    
    # Deplacer les fichiers de setup depuis admin/
    $adminSetupMoved = Move-ProjectFiles -SourceDir $adminDir -TargetDir $setupDir -Patterns $setupPatterns
    
    # Deplacer les fichiers de maintenance depuis admin/
    $adminMaintenanceMoved = Move-ProjectFiles -SourceDir $adminDir -TargetDir $maintenanceDir -Patterns $maintenancePatterns
    
    # Deplacer les fichiers de documentation depuis admin/
    $adminSummariesMoved = Move-ProjectFiles -SourceDir $adminDir -TargetDir $summariesDir -Patterns $summaryPatterns
    $adminGuidesMoved = Move-ProjectFiles -SourceDir $adminDir -TargetDir $guidesDir -Patterns $guidePatterns
}

# Nettoyer les fichiers temporaires
Write-Host "Nettoyage des fichiers temporaires..." -ForegroundColor Yellow
$tempPatterns = @("*.tmp", "*.bak", "*~", "*.orig", "Thumbs.db", ".DS_Store")
$cleanedCount = 0

foreach ($pattern in $tempPatterns) {
    $tempFiles = Get-ChildItem -Path $ProjectRoot -Name $pattern -Recurse -ErrorAction SilentlyContinue
    foreach ($file in $tempFiles) {
        $filePath = Join-Path $ProjectRoot $file
        try {
            Remove-Item -Path $filePath -Force
            Write-Host "  Supprime: $file" -ForegroundColor Red
            $cleanedCount++
        }
        catch {
            Write-Host "  Erreur lors de la suppression de $file" -ForegroundColor Red
        }
    }
}

# Calculer le total des fichiers deplaces
$totalMoved = $testsMoved + $debugMoved + $setupMoved + $maintenanceMoved + $summariesMoved + $guidesMoved + $logsMoved
if (Test-Path $adminDir) {
    $totalMoved += $adminTestsMoved + $adminDebugMoved + $adminSetupMoved + $adminMaintenanceMoved + $adminSummariesMoved + $adminGuidesMoved
}

# Creer des fichiers README pour chaque dossier
Write-Host "Creation des fichiers README..." -ForegroundColor Yellow

$readmeContent = @{
    "scripts\tests" = "# Fichiers de Tests`n`nCe dossier contient tous les fichiers de test et de validation du systeme."
    "scripts\debug" = "# Fichiers de Debug`n`nCe dossier contient les outils de diagnostic et de debogage."
    "scripts\setup" = "# Scripts d'Installation`n`nCe dossier contient les scripts d'installation et de configuration."
    "scripts\maintenance" = "# Scripts de Maintenance`n`nCe dossier contient les scripts de maintenance et de reparation."
    "documentation\summaries" = "# Resumes et Rapports`n`nCe dossier contient tous les resumes de corrections et rapports d'implementation."
    "documentation\guides" = "# Guides et Documentation`n`nCe dossier contient les guides techniques et la documentation."
}

foreach ($folder in $readmeContent.Keys) {
    $readmePath = Join-Path $ProjectRoot "$folder\README.md"
    $content = $readmeContent[$folder]
    
    if (-not (Test-Path $readmePath)) {
        Set-Content -Path $readmePath -Value $content -Encoding UTF8
        Write-Host "  Cree: $folder\README.md" -ForegroundColor Green
    }
}

# Generer un rapport final
$reportContent = @"
# Rapport d'Organisation du Projet
Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

## Resume
- Fichiers deplaces: $totalMoved
- Fichiers nettoyes: $cleanedCount
- Dossiers crees: $($FoldersToCreate.Count)

## Structure organisee

### Scripts
- scripts/tests/ - Fichiers de test et validation
- scripts/debug/ - Outils de diagnostic
- scripts/setup/ - Scripts d'installation
- scripts/maintenance/ - Scripts de maintenance

### Documentation
- documentation/summaries/ - Resumes et rapports
- documentation/guides/ - Guides techniques

### Temporaire
- temp/backup/ - Sauvegardes temporaires
- temp/logs/ - Fichiers de logs

## Fichiers principaux conserves a la racine
- index.html - Page d'accueil
- landing-page-template.php - Template principal
- router.php - Routeur principal
- config/ - Configuration
- css/, js/, php/ - Ressources principales
- admin/ - Interface d'administration (nettoyee)

## Prochaines etapes recommandees
1. Verifier que tous les liens fonctionnent
2. Mettre a jour les chemins dans les fichiers de configuration
3. Tester l'application apres reorganisation
4. Archiver les anciens fichiers de test si necessaire

---
Rapport genere automatiquement par le script d'organisation
"@

$reportPath = Join-Path $ProjectRoot "ORGANIZATION_REPORT.md"
Set-Content -Path $reportPath -Value $reportContent -Encoding UTF8

# Afficher le resume final
Write-Host "" 
Write-Host "ORGANISATION TERMINEE !" -ForegroundColor Green -BackgroundColor Black
Write-Host "Resume:" -ForegroundColor Cyan
Write-Host "  Fichiers deplaces: $totalMoved" -ForegroundColor White
Write-Host "  Fichiers nettoyes: $cleanedCount" -ForegroundColor White
Write-Host "  Dossiers crees: $($FoldersToCreate.Count)" -ForegroundColor White
Write-Host "  Rapport genere: ORGANIZATION_REPORT.md" -ForegroundColor White

Write-Host "Le projet est maintenant organise et nettoye !" -ForegroundColor Green
Write-Host "Prochaines etapes:" -ForegroundColor Yellow
Write-Host "  1. Verifier les liens et chemins" -ForegroundColor White
Write-Host "  2. Tester l'application" -ForegroundColor White
Write-Host "  3. Consulter le rapport: ORGANIZATION_REPORT.md" -ForegroundColor White

Write-Host "Projet pret pour la production !" -ForegroundColor Green