/**
 * Variable Conflicts Fix
 * Resolves JavaScript variable naming conflicts in admin panel
 */

// Wrap all global variables in a namespace to avoid conflicts
window.AdminPanel = window.AdminPanel || {};

// Store references to avoid redeclaration errors
if (!window.AdminPanel.stylesAdded) {
    window.AdminPanel.stylesAdded = new Set();
}

// Helper function to safely add styles
window.AdminPanel.addUniqueStyle = function(styleId, cssContent) {
    if (!document.getElementById(styleId) && !window.AdminPanel.stylesAdded.has(styleId)) {
        const styleElement = document.createElement('style');
        styleElement.id = styleId;
        styleElement.textContent = cssContent;
        document.head.appendChild(styleElement);
        window.AdminPanel.stylesAdded.add(styleId);
        return styleElement;
    }
    return document.getElementById(styleId);
};

// Helper function to safely declare global variables
window.AdminPanel.safeGlobal = function(name, value) {
    if (typeof window[name] === 'undefined') {
        window[name] = value;
    }
    return window[name];
};

// Initialize stores array safely
window.AdminPanel.stores = window.AdminPanel.stores || [];

// Prevent multiple initializations
window.AdminPanel.initialized = window.AdminPanel.initialized || {};

// Safe initialization wrapper
window.AdminPanel.safeInit = function(moduleName, initFunction) {
    if (!window.AdminPanel.initialized[moduleName]) {
        window.AdminPanel.initialized[moduleName] = true;
        return initFunction();
    }
};

console.log('✅ Variable conflicts fix loaded successfully');