/**
 * Submenu Visibility Fix CSS
 * إصلاح مشكلة عدم ظهور عناصر القائمة الفرعية
 */

/* Force admin settings submenu to be visible */
.admin-settings-submenu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
    overflow: visible !important;
    padding: 12px 0 16px 0 !important;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.15) 100%) !important;
    border-radius: 0 0 12px 12px !important;
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    width: 100% !important;
    height: auto !important;
    z-index: 1 !important;
}

/* Force submenu items to be visible */
.admin-settings-submenu li {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    width: calc(100% - 24px) !important;
    height: auto !important;
    margin: 4px 12px !important;
    padding: 14px 18px !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 10px !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex-direction: row !important;
    box-sizing: border-box !important;
    z-index: 2 !important;
}

/* Ensure submenu items content is visible */
.admin-settings-submenu li i {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 1.1rem !important;
    width: 24px !important;
    margin-left: 12px !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-align: center !important;
}

.admin-settings-submenu li span {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-align: right !important;
    flex: 1 !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
}

/* Hover effects */
.admin-settings-submenu li:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    color: #ffffff !important;
    transform: translateX(-4px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

.admin-settings-submenu li:hover i,
.admin-settings-submenu li:hover span {
    color: #ffffff !important;
}

/* Active state */
.admin-settings-submenu li.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.12) 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4) !important;
    transform: translateX(-2px) !important;
}

.admin-settings-submenu li.active i,
.admin-settings-submenu li.active span {
    color: #ffffff !important;
}

/* Force admin settings menu to be expanded */
.admin-settings-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.admin-settings-menu .admin-settings-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force arrow to show expanded state */
.admin-settings-arrow {
    transform: rotate(180deg) !important;
}

/* Override any conflicting styles */
.admin-nav .admin-settings-submenu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
    overflow: visible !important;
}

.admin-nav .admin-settings-submenu li {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Specific targeting for security settings and subscription management */
.admin-settings-submenu li[data-section="securitySettings"],
.admin-settings-submenu li[data-section="subscriptionsManagement"] {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    width: calc(100% - 24px) !important;
    height: auto !important;
    margin: 4px 12px !important;
    padding: 14px 18px !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 10px !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    cursor: pointer !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex-direction: row !important;
    box-sizing: border-box !important;
    z-index: 2 !important;
}

/* Debug styles - uncomment to see boundaries */
/*
.admin-settings-submenu {
    border: 2px solid red !important;
}

.admin-settings-submenu li {
    border: 2px solid green !important;
}

.admin-settings-submenu li[data-section="securitySettings"] {
    border: 3px solid blue !important;
}

.admin-settings-submenu li[data-section="subscriptionsManagement"] {
    border: 3px solid orange !important;
}
*/

/* Mobile responsive */
@media (max-width: 768px) {
    .admin-settings-submenu li {
        margin: 3px 8px !important;
        padding: 12px 14px !important;
        width: calc(100% - 16px) !important;
    }
    
    .admin-settings-submenu li i {
        font-size: 1rem !important;
        width: 20px !important;
        margin-left: 10px !important;
    }
    
    .admin-settings-submenu li span {
        font-size: 0.85rem !important;
    }
}

/* Print styles */
@media print {
    .admin-settings-submenu {
        display: block !important;
        visibility: visible !important;
    }
    
    .admin-settings-submenu li {
        display: flex !important;
        visibility: visible !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .admin-settings-submenu li {
        border: 2px solid currentColor !important;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .admin-settings-submenu li {
        transition: none !important;
    }
    
    .admin-settings-submenu li:hover {
        transform: none !important;
    }
}
