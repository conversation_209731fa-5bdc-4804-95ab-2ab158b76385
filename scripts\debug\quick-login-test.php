<?php
session_start();
require_once '../php/config.php';

echo "<h1>🔐 Test de connexion rapide</h1>";

// Test direct login
if (isset($_GET['user']) && isset($_GET['pass'])) {
    try {
        $pdo = getPDOConnection();
        
        // Check if user exists
        $stmt = $pdo->prepare('SELECT * FROM admins WHERE nom_utilisateur = ?');
        $stmt->execute([$_GET['user']]);
        $admin = $stmt->fetch();
        
        if ($admin && password_verify($_GET['pass'], $admin['mot_de_passe'])) {
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['nom_utilisateur'];
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
            echo "✅ <strong>Connexion réussie!</strong><br>";
            echo "Utilisateur: {$admin['nom_utilisateur']}<br>";
            echo "Email: {$admin['email']}<br>";
            echo "Rôle: {$admin['role']}<br>";
            echo "</div>";
            
            echo "<br><a href='index.html' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Aller au tableau de bord</a>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
            echo "❌ <strong>Échec de la connexion!</strong><br>";
            echo "Nom d'utilisateur ou mot de passe incorrect.";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "❌ <strong>Erreur:</strong> " . $e->getMessage();
        echo "</div>";
    }
}

// Show login form
echo "<h2>Connexion directe</h2>";
echo "<form method='GET' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label>Nom d'utilisateur:</label><br>";
echo "<input type='text' name='user' required style='width: 200px; padding: 8px; margin-top: 5px;'>";
echo "</div>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label>Mot de passe:</label><br>";
echo "<input type='password' name='pass' required style='width: 200px; padding: 8px; margin-top: 5px;'>";
echo "</div>";
echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Se connecter</button>";
echo "</form>";

// Quick login buttons
echo "<h3>Connexion rapide avec comptes de démonstration</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";

$demoAccounts = [
    ['user' => 'admin', 'pass' => 'admin123', 'label' => 'Admin'],
    ['user' => 'mossaab', 'pass' => 'mossaab2024', 'label' => 'Mossaab'],
    ['user' => 'manager', 'pass' => 'manager123', 'label' => 'Manager'],
    ['user' => 'demo', 'pass' => 'demo123', 'label' => 'Demo']
];

foreach ($demoAccounts as $account) {
    echo "<a href='?user={$account['user']}&pass={$account['pass']}' ";
    echo "style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px;'>";
    echo "🔑 {$account['label']}";
    echo "</a>";
}

echo "</div>";

// Show current session status
echo "<h3>Statut de session actuel</h3>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
if (isset($_SESSION['admin_id'])) {
    echo "✅ <strong>Connecté</strong><br>";
    echo "ID: {$_SESSION['admin_id']}<br>";
    echo "Nom d'utilisateur: {$_SESSION['admin_username']}<br>";
    echo "<br><a href='?logout=1' style='background: #dc3545; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;'>Se déconnecter</a>";
} else {
    echo "❌ <strong>Non connecté</strong>";
}
echo "</div>";

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    echo "<script>window.location.href = window.location.pathname;</script>";
}

echo "<h3>🔗 Liens utiles</h3>";
echo "<ul>";
echo "<li><a href='setup-demo-users.php'>Configurer les utilisateurs de démonstration</a></li>";
echo "<li><a href='login-simple.html'>Page de connexion simple</a></li>";
echo "<li><a href='login.html'>Page de connexion originale</a></li>";
echo "<li><a href='index.html'>Tableau de bord admin</a></li>";
echo "</ul>";
?>
