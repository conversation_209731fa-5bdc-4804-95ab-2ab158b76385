<?php
/**
 * Multi-User System Implementation
 * Add necessary database columns and update APIs for user isolation
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <title>Multi-User System Implementation</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
        .step { background: #e9ecef; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #6c757d; }
        .step.completed { border-left-color: #28a745; background: #d4edda; }
        .step.failed { border-left-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 Multi-User System Implementation</h1>";
echo "<p>Implementing user isolation and role-based access control</p>";

$steps = [];
$totalSteps = 0;
$completedSteps = 0;

try {
    require_once 'php/config.php';
    $pdo = getPDOConnection();
    
    if (!$pdo) {
        throw new Exception("Database connection failed");
    }
    
    echo "<div class='success'>✅ Database connection established</div>";
    
    // Step 1: Add user_id to commandes table
    echo "<div class='section'>";
    echo "<h2>📋 Step 1: Update Orders Table (commandes)</h2>";
    $totalSteps++;
    
    try {
        // Check if user_id column already exists
        $stmt = $pdo->query("DESCRIBE commandes");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('user_id', $columns)) {
            $sql = "ALTER TABLE commandes ADD COLUMN user_id INT(11) NULL AFTER id";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Added user_id column to commandes table</div>";
        } else {
            echo "<div class='info'>ℹ️ user_id column already exists in commandes table</div>";
        }
        
        // Add foreign key constraint
        try {
            $sql = "ALTER TABLE commandes ADD CONSTRAINT fk_commandes_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Added foreign key constraint for commandes.user_id</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='info'>ℹ️ Foreign key constraint already exists</div>";
            } else {
                echo "<div class='warning'>⚠️ Could not add foreign key: " . $e->getMessage() . "</div>";
            }
        }
        
        $completedSteps++;
        $steps[] = "✅ Orders table updated successfully";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error updating commandes table: " . $e->getMessage() . "</div>";
        $steps[] = "❌ Orders table update failed";
    }
    echo "</div>";
    
    // Step 2: Add user_id to produits table (if not using store relationship)
    echo "<div class='section'>";
    echo "<h2>📦 Step 2: Update Products Table (produits)</h2>";
    $totalSteps++;
    
    try {
        // Check if user_id column already exists
        $stmt = $pdo->query("DESCRIBE produits");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('user_id', $columns)) {
            $sql = "ALTER TABLE produits ADD COLUMN user_id INT(11) NULL AFTER store_id";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Added user_id column to produits table</div>";
            
            // Update existing products to have user_id based on store ownership
            $sql = "UPDATE produits p 
                    JOIN stores s ON p.store_id = s.id 
                    JOIN users u ON s.id = u.store_id 
                    SET p.user_id = u.id";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Updated existing products with user_id</div>";
            
        } else {
            echo "<div class='info'>ℹ️ user_id column already exists in produits table</div>";
        }
        
        // Add foreign key constraint
        try {
            $sql = "ALTER TABLE produits ADD CONSTRAINT fk_produits_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Added foreign key constraint for produits.user_id</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='info'>ℹ️ Foreign key constraint already exists</div>";
            } else {
                echo "<div class='warning'>⚠️ Could not add foreign key: " . $e->getMessage() . "</div>";
            }
        }
        
        $completedSteps++;
        $steps[] = "✅ Products table updated successfully";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error updating produits table: " . $e->getMessage() . "</div>";
        $steps[] = "❌ Products table update failed";
    }
    echo "</div>";
    
    // Step 3: Add user_id to landing_pages table
    echo "<div class='section'>";
    echo "<h2>🚀 Step 3: Update Landing Pages Table</h2>";
    $totalSteps++;
    
    try {
        // Check if user_id column already exists
        $stmt = $pdo->query("DESCRIBE landing_pages");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('user_id', $columns)) {
            $sql = "ALTER TABLE landing_pages ADD COLUMN user_id INT(11) NULL AFTER store_id";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Added user_id column to landing_pages table</div>";
            
            // Update existing landing pages to have user_id based on store ownership
            $sql = "UPDATE landing_pages lp 
                    JOIN stores s ON lp.store_id = s.id 
                    JOIN users u ON s.id = u.store_id 
                    SET lp.user_id = u.id";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Updated existing landing pages with user_id</div>";
            
        } else {
            echo "<div class='info'>ℹ️ user_id column already exists in landing_pages table</div>";
        }
        
        // Add foreign key constraint
        try {
            $sql = "ALTER TABLE landing_pages ADD CONSTRAINT fk_landing_pages_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Added foreign key constraint for landing_pages.user_id</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='info'>ℹ️ Foreign key constraint already exists</div>";
            } else {
                echo "<div class='warning'>⚠️ Could not add foreign key: " . $e->getMessage() . "</div>";
            }
        }
        
        $completedSteps++;
        $steps[] = "✅ Landing pages table updated successfully";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error updating landing_pages table: " . $e->getMessage() . "</div>";
        $steps[] = "❌ Landing pages table update failed";
    }
    echo "</div>";
    
    // Step 4: Create admin role if not exists
    echo "<div class='section'>";
    echo "<h2>👑 Step 4: Ensure Admin Role Exists</h2>";
    $totalSteps++;
    
    try {
        // Check if admin role exists
        $stmt = $pdo->prepare("SELECT * FROM user_roles WHERE name = 'admin' OR id = 1");
        $stmt->execute();
        $adminRole = $stmt->fetch();
        
        if (!$adminRole) {
            $sql = "INSERT INTO user_roles (id, name, description, permissions) VALUES (1, 'admin', 'Administrator', 'all')";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Created admin role</div>";
        } else {
            echo "<div class='info'>ℹ️ Admin role already exists</div>";
        }
        
        // Check if seller role exists
        $stmt = $pdo->prepare("SELECT * FROM user_roles WHERE name = 'seller' OR id = 2");
        $stmt->execute();
        $sellerRole = $stmt->fetch();
        
        if (!$sellerRole) {
            $sql = "INSERT INTO user_roles (id, name, description, permissions) VALUES (2, 'seller', 'Seller/Vendor', 'products,orders,landing_pages')";
            $pdo->exec($sql);
            echo "<div class='success'>✅ Created seller role</div>";
        } else {
            echo "<div class='info'>ℹ️ Seller role already exists</div>";
        }
        
        $completedSteps++;
        $steps[] = "✅ User roles configured successfully";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error configuring user roles: " . $e->getMessage() . "</div>";
        $steps[] = "❌ User roles configuration failed";
    }
    echo "</div>";
    
    // Step 5: Verify data integrity
    echo "<div class='section'>";
    echo "<h2>🔍 Step 5: Verify Data Integrity</h2>";
    $totalSteps++;
    
    try {
        // Check products with user relationships
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits WHERE user_id IS NOT NULL");
        $productsWithUsers = $stmt->fetch()['count'];
        echo "<div class='info'>📦 Products with user relationships: $productsWithUsers</div>";
        
        // Check landing pages with user relationships
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM landing_pages WHERE user_id IS NOT NULL");
        $landingPagesWithUsers = $stmt->fetch()['count'];
        echo "<div class='info'>🚀 Landing pages with user relationships: $landingPagesWithUsers</div>";
        
        // Check users and their roles
        $stmt = $pdo->query("SELECT u.username, u.email, ur.name as role_name FROM users u LEFT JOIN user_roles ur ON u.role_id = ur.id");
        $users = $stmt->fetchAll();
        
        echo "<div class='info'>👥 Current users and roles:</div>";
        echo "<div class='code-block'>";
        foreach ($users as $user) {
            echo "• {$user['username']} ({$user['email']}) - Role: {$user['role_name']}\n";
        }
        echo "</div>";
        
        $completedSteps++;
        $steps[] = "✅ Data integrity verified";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error verifying data integrity: " . $e->getMessage() . "</div>";
        $steps[] = "❌ Data integrity verification failed";
    }
    echo "</div>";
    
    // Summary
    echo "<div class='section'>";
    echo "<h2>📊 Implementation Summary</h2>";
    
    $successRate = ($completedSteps / $totalSteps) * 100;
    
    if ($successRate >= 80) {
        echo "<div class='success'>";
        echo "<h3>🎉 Multi-User System Implementation Successful! ($completedSteps/$totalSteps)</h3>";
        echo "<p>Success Rate: " . round($successRate, 1) . "%</p>";
        echo "</div>";
    } else {
        echo "<div class='warning'>";
        echo "<h3>⚠️ Partial Implementation ($completedSteps/$totalSteps)</h3>";
        echo "<p>Success Rate: " . round($successRate, 1) . "%</p>";
        echo "</div>";
    }
    
    echo "<h4>📋 Implementation Steps:</h4>";
    echo "<ul>";
    foreach ($steps as $step) {
        echo "<li>$step</li>";
    }
    echo "</ul>";
    
    if ($successRate >= 80) {
        echo "<div class='info'>";
        echo "<h4>🔗 Next Steps:</h4>";
        echo "<ul>";
        echo "<li>✅ Update API endpoints to implement user isolation</li>";
        echo "<li>✅ Modify admin interface to show user ownership</li>";
        echo "<li>✅ Implement role-based access control in frontend</li>";
        echo "<li>✅ Create admin-specific views for all users' data</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Critical Error: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
