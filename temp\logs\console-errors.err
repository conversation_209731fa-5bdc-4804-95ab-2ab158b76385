Uncaught SyntaxError: catch without try payment-settings.js:244:7
Uncaught ReferenceError: togglePaymentMethod is not defined
    onclick http://localhost:8000/admin/payment_settings.html:1
5 payment_settings.html:1:1
























Starting TinyMCE initialization... admin.js:960:13
Added tinymce class to: productDescription admin.js:979:17
TinyMCE initialization - Found textareas: 1 admin.js:981:13
Navigation item clicked: 
<li class="active" data-section="settings">
admin.js:460:21
Switching to section: settings admin.js:474:21
Loading settings... admin.js:532:33
Loading settings... admin.js:251:17
Starting TinyMCE initialization... admin.js:960:13
Added tinymce class to: productDescription admin.js:979:17
TinyMCE initialization - Found textareas: 1 admin.js:981:13
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow index.html
TinyMCE editor initialized: storeAddress admin.js:1007:29
ReferenceError: fdmExtUtils is not defined contextmenuhlpr.js:24:25
Settings response text: {"success":true,"settings":{"store_name":"Mossaab Store","store_description":"Your one-stop shop for books and electronics","contact_email":"<EMAIL>","phone_number":"+213 000000000","address":"Algeria","shipping_policy":"Standard shipping within 3-5 business days","return_policy":"30-day return policy for unused items"}} admin.js:259:17
Parsed settings data: 
Object { success: true, settings: {…} }
admin.js:275:17
Final settings object: 
Object { store_name: "Mossaab Store", store_description: "Your one-stop shop for books and electronics", contact_email: "<EMAIL>", phone_number: "+213 000000000", address: "Algeria", shipping_policy: "Standard shipping within 3-5 business days", return_policy: "30-day return policy for unused items" }
admin.js:290:17
Settings loaded successfully admin.js:311:17
Settings response text: {"success":true,"settings":{"store_name":"Mossaab Store","store_description":"Your one-stop shop for books and electronics","contact_email":"<EMAIL>","phone_number":"+213 000000000","address":"Algeria","shipping_policy":"Standard shipping within 3-5 business days","return_policy":"30-day return policy for unused items"}} admin.js:259:17
Parsed settings data: 
Object { success: true, settings: {…} }
admin.js:275:17
Final settings object: 
Object { store_name: "Mossaab Store", store_description: "Your one-stop shop for books and electronics", contact_email: "<EMAIL>", phone_number: "+213 000000000", address: "Algeria", shipping_policy: "Standard shipping within 3-5 business days", return_policy: "30-day return policy for unused items" }
admin.js:290:17
Settings loaded successfully admin.js:311:17
TypeError: can't access property "rangeCount", selection is null 3 contextmenuhlpr.js:8:9
Navigation item clicked: 
<li class="" data-section="books">
admin.js:460:21
Switching to section: books admin.js:474:21
Loading books... admin.js:505:33
📦 Calling loadProducts function... admin.js:507:21
📦 Loading products... admin.js:728:17
Navigation item clicked: 
<li class="" data-section="dashboard">
admin.js:460:21
Switching to section: dashboard admin.js:474:21
Loading dashboard... admin.js:497:33
API Response: 
Object { success: true, products: (2) […] }
admin.js:734:17
Products data: 
Array [ {…}, {…} ]
admin.js:749:17
Found 2 products admin.js:750:17
Adding view more link for product: 
Object { id: 19, has_landing_page: 0, landing_url: null }
admin.js:1396:13
Adding view more link for product: 
Object { id: 20, has_landing_page: 1, landing_url: "/Mossaab-LandingPage/landing-page.php?id=20" }
admin.js:1396:13
Dashboard data loaded: 
Object { products: {…}, orders: {…}, sales: {…}, landing_pages: {…}, books: {…}, charts: {…}, recent_orders: [] }
admin.js:658:17
✅ Dashboard statistics updated successfully admin.js:703:17
Navigation item clicked: 
<li class="" data-section="books">
admin.js:460:21
Switching to section: books admin.js:474:21
Loading books... admin.js:505:33
📦 Calling loadProducts function... admin.js:507:21
📦 Loading products... admin.js:728:17
API Response: 
Object { success: true, products: (2) […] }
admin.js:734:17
Products data: 
Array [ {…}, {…} ]
admin.js:749:17
Found 2 products admin.js:750:17
Adding view more link for product: 
Object { id: 19, has_landing_page: 0, landing_url: null }
admin.js:1396:13
Adding view more link for product: 
Object { id: 20, has_landing_page: 1, landing_url: "/Mossaab-LandingPage/landing-page.php?id=20" }
admin.js:1396:13
Navigation item clicked: 
<li class="" data-section="landingPages">
admin.js:460:21
Switching to section: landingPages admin.js:474:21
Loading landing pages... admin.js:514:33
Landing Pages Manager is available admin.js:517:37
Landing Pages Manager already initialized, refreshing data... admin.js:523:41
🔄 Loading landing pages... landing-pages.js:536:17
📡 Making API call to: ../php/api/landing-pages.php?_t=1752190198783 landing-pages.js:541:21
📡 Raw API Response: 
Object { success: true, data: (1) […] }
landing-pages.js:544:21
📡 Response type: object landing-pages.js:545:21
📡 Response keys: 
Array [ "success", "data" ]
landing-pages.js:546:21
📋 Landing pages data: 
Array [ {…} ]
landing-pages.js:549:21
📋 Pages type: object landing-pages.js:550:21
📋 Is array: true landing-pages.js:551:21
📋 Pages length: 1 landing-pages.js:552:21
🎨 Displaying landing pages: 
Array [ {…} ]
landing-pages.js:572:17
🎨 Pages parameter type: object landing-pages.js:573:17
🎨 Pages parameter value: 
Array [ {…} ]
landing-pages.js:574:17
🎨 Container found: true landing-pages.js:577:17
🎨 Container element: 
<div id="landingPagesContainer" style="margin-top: 20px; opacity: 1;">
landing-pages.js:578:17
📄 Displaying 1 landing pages landing-pages.js:593:17
🖊️ Starting edit for landing page ID: 21 landing-pages.js:1294:17
Web Audio notification played successfully admin.js:147:21
📡 Fetching page data... landing-pages.js:1314:21
📄 Raw response: {"success":true,"data":{"id":21,"produit_id":20,"titre":"Test Update - 8:55:03 PM","contenu_droit":"Test right content updated","contenu_gauche":"Test left content updated","lien_url":"\/landing\/prod... landing-pages.js:1322:21
✅ Page data loaded: 
Object { id: 21, produit_id: 20, titre: "Test Update - 8:55:03 PM", contenu_droit: "Test right content updated", contenu_gauche: "Test left content updated", lien_url: "/landing/product-20-686ffe7c061d1", created_at: "2025-07-10 18:55:08", updated_at: "2025-07-10 20:55:03", template_id: "custom", product_title: "حقيبة ظهر رياضية مقاومة للماء", … }
landing-pages.js:1346:21
🚀 Opening modal for editing... landing-pages.js:1349:21
📝 Opening edit modal with data: 
Object { id: 21, produit_id: 20, titre: "Test Update - 8:55:03 PM", contenu_droit: "Test right content updated", contenu_gauche: "Test left content updated", lien_url: "/landing/product-20-686ffe7c061d1", created_at: "2025-07-10 18:55:08", updated_at: "2025-07-10 20:55:03", template_id: "custom", product_title: "حقيبة ظهر رياضية مقاومة للماء", … }
landing-pages.js:1359:17
🚀 Opening modal... landing-pages.js:638:17
Landing pages section display: block landing-pages.js:659:25
🔄 Modal reset to step 1 landing-pages.js:295:17
Refreshing product selection... landing-pages.js:412:17
📦 Loading active products for landing page selection... landing-pages.js:355:21
Initializing TinyMCE for landing pages modal landing-pages.js:810:17
No TinyMCE editors found to clean up landing-pages.js:992:25
TinyMCE initialization attempt 1/5 landing-pages.js:821:25
TinyMCE initialization successful landing-pages.js:947:29
✅ Modal opened successfully landing-pages.js:700:21
Modal styles: 
Object { display: "block", opacity: "1", visibility: "visible", zIndex: "9999", position: "fixed" }
landing-pages.js:703:21
Found 1 active products out of 2 total products landing-pages.js:373:21
Active products loaded successfully landing-pages.js:392:21
📝 Filling edit form with: 
Object { id: 21, produit_id: 20, titre: "Test Update - 8:55:03 PM", contenu_droit: "Test right content updated", contenu_gauche: "Test left content updated", lien_url: "/landing/product-20-686ffe7c061d1", created_at: "2025-07-10 18:55:08", updated_at: "2025-07-10 20:55:03", template_id: "custom", product_title: "حقيبة ظهر رياضية مقاومة للماء", … }
landing-pages.js:1371:17
✅ Form marked as edit mode landing-pages.js:1377:25
✅ Modal title updated landing-pages.js:1384:25
✅ Title field filled landing-pages.js:1391:25
✅ Product selection filled landing-pages.js:1398:25
✅ Submit button updated landing-pages.js:1405:25
✅ Edit form filled successfully landing-pages.js:1413:21
📝 Filling content editors... landing-pages.js:1421:17
✅ Right content editor filled landing-pages.js:1431:29
✅ Left content editor filled landing-pages.js:1436:29
Navigation item clicked: 
<li class="" data-section="settings">
admin.js:460:21
Switching to section: settings admin.js:474:21
Loading settings... admin.js:532:33
Loading settings... admin.js:251:17
Starting TinyMCE initialization... admin.js:960:13
Added tinymce class to: productDescription admin.js:979:17
TinyMCE initialization - Found textareas: 1 admin.js:981:13
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow index.html
ReferenceError: fdmExtUtils is not defined contextmenuhlpr.js:24:25
Settings response text: {"success":true,"settings":{"store_name":"Mossaab Store","store_description":"Your one-stop shop for books and electronics","contact_email":"<EMAIL>","phone_number":"+213 000000000","address":"Algeria","shipping_policy":"Standard shipping within 3-5 business days","return_policy":"30-day return policy for unused items"}} admin.js:259:17
Parsed settings data: 
Object { success: true, settings: {…} }
admin.js:275:17
Final settings object: 
Object { store_name: "Mossaab Store", store_description: "Your one-stop shop for books and electronics", contact_email: "<EMAIL>", phone_number: "+213 000000000", address: "Algeria", shipping_policy: "Standard shipping within 3-5 business days", return_policy: "30-day return policy for unused items" }
admin.js:290:17
Settings loaded successfully admin.js:311:17
TinyMCE editor initialized: storeAddress admin.js:1007:29
✅ Template selected: 
Object { id: "bag", name: "قالب الحقائب", description: "قالب مخصص للحقائب والإكسسوارات", icon: "fas fa-shopping-bag", preview: "/images/template-bag-preview.jpg", content: {…} }
landing-pages.js:158:21
Applying template: 
Object { titre: "الأناقة والعملية - {product_title}", contenu_droit: "\n                <h3>🎒 لماذا هذه الحقيبة هي رفيقك المثالي؟</h3>\n                <ul>\n                    <li><strong>تصميم عصري وأنيق:</strong> تناسب جميع مناسباتك</li>\n                    <li><strong>مواد عالية الجودة:</strong> مقاومة للماء والتمزق</li>\n                    <li><strong>سعة تخزين ذكية:</strong> جيوب متعددة ومنظمة</li>\n                    <li><strong>مريحة للحمل:</strong> أحزمة مبطنة وقابلة للتعديل</li>\n                </ul>\n\n                <h3>✨ مواصفات الحقيبة:</h3>\n                <p>• <strong>المادة:</strong> مواد متينة وعالية الجودة<br>\n                • <strong>السعة:</strong> مساحة واسعة لجميع احتياجاتك<br>\n                • <strong>الوزن:</strong> خفيفة الوزن وسهلة الحمل<br>\n                • <strong>الألوان:</strong> متوفرة بألوان متنوعة</p>", contenu_gauche: '\n                <h3>🌍 مثالية لـ:</h3>\n                <p>الاستخدام اليومي، طلاب الجامعات، الموظفين، ومحبي السفر. تصميمها المرن يجعلها مناسبة لكل الظروف.</p>\n\n                <h3>🌟 آراء العملاء</h3>\n                <blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">\n                    "حقيبة عملية جداً وأنيقة. أستخدمها كل يوم ولا أستغني عنها!"\n                    <cite style="display: block; margin-top: 10px; font-weight: bold;">- فاطمة، مصممة</cite>\n                </blockquote>\n\n                <h3>🎁 احصل على خصم 20% اليوم!</h3>\n                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">\n                    <h4 style="margin: 0 0 10px 0;">عرض خاص للمتسوقين الأوائل!</h4>\n                    <p style="font-size: 1.2em; margin: 0;">اطلبها الآن واستفد من التوصيل المجاني!</p>\n                </div>', image_position: "right", text_position: "left" }
landing-pages.js:205:17
Initializing TinyMCE for landing pages modal landing-pages.js:810:17
No TinyMCE editors found to clean up landing-pages.js:992:25
TinyMCE initialization attempt 1/5 landing-pages.js:821:25
📝 Moved to content step landing-pages.js:181:17
TinyMCE initialization successful landing-pages.js:947:29
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow tinymce.min.js:4:18466
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow tinymce.min.js:4:18466
Content set successfully for rightContent landing-pages.js:246:37
Content set successfully for leftContent landing-pages.js:246:37
✅ Template applied to form successfully landing-pages.js:265:21
🚪 Closing modal... landing-pages.js:717:17
No TinyMCE editors found to clean up landing-pages.js:992:25
✅ Modal closed successfully landing-pages.js:802:21
Navigation item clicked: 
<li class="" data-section="books">
admin.js:460:21
Switching to section: books admin.js:474:21
Loading books... admin.js:505:33
📦 Calling loadProducts function... admin.js:507:21
📦 Loading products... admin.js:728:17
API Response: 
Object { success: true, products: (2) […] }
admin.js:734:17
Products data: 
Array [ {…}, {…} ]
admin.js:749:17
Found 2 products admin.js:750:17
Adding view more link for product: 
Object { id: 19, has_landing_page: 0, landing_url: null }
admin.js:1396:13
Adding view more link for product: 
Object { id: 20, has_landing_page: 1, landing_url: "/Mossaab-LandingPage/landing-page.php?id=20" }
admin.js:1396:13
Toggle product status called: 
Object { productId: 20, currentStatus: 0 }
admin.js:812:13
Sending request with data: 
Object { productId: "20", active: "1" }
admin.js:828:17
Response status: 200 admin.js:838:17
Response text: {"success":true,"message":"Product status updated successfully"} admin.js:842:17
Parsed response data: 
Object { success: true, message: "Product status updated successfully" }
admin.js:856:17
Web Audio notification played successfully admin.js:147:21
Refreshing product selection... landing-pages.js:412:17
📦 Loading active products for landing page selection... landing-pages.js:355:21
Found 2 active products out of 2 total products landing-pages.js:373:21
Active products loaded successfully landing-pages.js:392:21
Navigation item clicked: 
<li class="" data-section="landingPages">
admin.js:460:21
Switching to section: landingPages admin.js:474:21
Loading landing pages... admin.js:514:33
Landing Pages Manager is available admin.js:517:37
Landing Pages Manager already initialized, refreshing data... admin.js:523:41
🔄 Loading landing pages... landing-pages.js:536:17
📡 Making API call to: ../php/api/landing-pages.php?_t=1752190283421 landing-pages.js:541:21
📡 Raw API Response: 
Object { success: true, data: (1) […] }
landing-pages.js:544:21
📡 Response type: object landing-pages.js:545:21
📡 Response keys: 
Array [ "success", "data" ]
landing-pages.js:546:21
📋 Landing pages data: 
Array [ {…} ]
landing-pages.js:549:21
📋 Pages type: object landing-pages.js:550:21
📋 Is array: true landing-pages.js:551:21
📋 Pages length: 1 landing-pages.js:552:21
🎨 Displaying landing pages: 
Array [ {…} ]
landing-pages.js:572:17
🎨 Pages parameter type: object landing-pages.js:573:17
🎨 Pages parameter value: 
Array [ {…} ]
landing-pages.js:574:17
🎨 Container found: true landing-pages.js:577:17
🎨 Container element: 
<div id="landingPagesContainer" style="margin-top: 20px; opacity: 1;">
landing-pages.js:578:17
📄 Displaying 1 landing pages landing-pages.js:593:17
🖊️ Starting edit for landing page ID: 21 landing-pages.js:1294:17
Web Audio notification played successfully admin.js:147:21
📡 Fetching page data... landing-pages.js:1314:21
📄 Raw response: {"success":true,"data":{"id":21,"produit_id":20,"titre":"Test Update - 8:55:03 PM","contenu_droit":"Test right content updated","contenu_gauche":"Test left content updated","lien_url":"\/landing\/prod... landing-pages.js:1322:21
✅ Page data loaded: 
Object { id: 21, produit_id: 20, titre: "Test Update - 8:55:03 PM", contenu_droit: "Test right content updated", contenu_gauche: "Test left content updated", lien_url: "/landing/product-20-686ffe7c061d1", created_at: "2025-07-10 18:55:08", updated_at: "2025-07-10 20:55:03", template_id: "custom", product_title: "حقيبة ظهر رياضية مقاومة للماء", … }
landing-pages.js:1346:21
🚀 Opening modal for editing... landing-pages.js:1349:21
📝 Opening edit modal with data: 
Object { id: 21, produit_id: 20, titre: "Test Update - 8:55:03 PM", contenu_droit: "Test right content updated", contenu_gauche: "Test left content updated", lien_url: "/landing/product-20-686ffe7c061d1", created_at: "2025-07-10 18:55:08", updated_at: "2025-07-10 20:55:03", template_id: "custom", product_title: "حقيبة ظهر رياضية مقاومة للماء", … }
landing-pages.js:1359:17
🚀 Opening modal... landing-pages.js:638:17
Landing pages section display: block landing-pages.js:659:25
🔄 Modal reset to step 1 landing-pages.js:295:17
Refreshing product selection... landing-pages.js:412:17
📦 Loading active products for landing page selection... landing-pages.js:355:21
Initializing TinyMCE for landing pages modal landing-pages.js:810:17
No TinyMCE editors found to clean up landing-pages.js:992:25
TinyMCE initialization attempt 1/5 landing-pages.js:821:25
TinyMCE initialization successful landing-pages.js:947:29
✅ Modal opened successfully landing-pages.js:700:21
Modal styles: 
Object { display: "block", opacity: "1", visibility: "visible", zIndex: "9999", position: "fixed" }
landing-pages.js:703:21
📝 Filling edit form with: 
Object { id: 21, produit_id: 20, titre: "Test Update - 8:55:03 PM", contenu_droit: "Test right content updated", contenu_gauche: "Test left content updated", lien_url: "/landing/product-20-686ffe7c061d1", created_at: "2025-07-10 18:55:08", updated_at: "2025-07-10 20:55:03", template_id: "custom", product_title: "حقيبة ظهر رياضية مقاومة للماء", … }
landing-pages.js:1371:17
✅ Form marked as edit mode landing-pages.js:1377:25
✅ Modal title updated landing-pages.js:1384:25
✅ Title field filled landing-pages.js:1391:25
✅ Product selection filled landing-pages.js:1398:25
✅ Submit button updated landing-pages.js:1405:25
✅ Edit form filled successfully landing-pages.js:1413:21
Found 2 active products out of 2 total products landing-pages.js:373:21
Active products loaded successfully landing-pages.js:392:21
📝 Filling content editors... landing-pages.js:1421:17
✅ Right content editor filled landing-pages.js:1431:29
✅ Left content editor filled landing-pages.js:1436:29
Navigation item clicked: 
<li class="" data-section="settings">
admin.js:460:21
Switching to section: settings admin.js:474:21
Loading settings... admin.js:532:33
Loading settings... admin.js:251:17
Starting TinyMCE initialization... admin.js:960:13
Added tinymce class to: productDescription admin.js:979:17
TinyMCE initialization - Found textareas: 1 admin.js:981:13
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow index.html
✅ Template selected: 
Object { id: "bag", name: "قالب الحقائب", description: "قالب مخصص للحقائب والإكسسوارات", icon: "fas fa-shopping-bag", preview: "/images/template-bag-preview.jpg", content: {…} }
landing-pages.js:158:21
Settings response text: {"success":true,"settings":{"store_name":"Mossaab Store","store_description":"Your one-stop shop for books and electronics","contact_email":"<EMAIL>","phone_number":"+213 000000000","address":"Algeria","shipping_policy":"Standard shipping within 3-5 business days","return_policy":"30-day return policy for unused items"}} admin.js:259:17
Parsed settings data: 
Object { success: true, settings: {…} }
admin.js:275:17
Final settings object: 
Object { store_name: "Mossaab Store", store_description: "Your one-stop shop for books and electronics", contact_email: "<EMAIL>", phone_number: "+213 000000000", address: "Algeria", shipping_policy: "Standard shipping within 3-5 business days", return_policy: "30-day return policy for unused items" }
admin.js:290:17
Settings loaded successfully admin.js:311:17
TinyMCE editor initialized: storeAddress admin.js:1007:29
Applying template: 
Object { titre: "الأناقة والعملية - {product_title}", contenu_droit: "\n                <h3>🎒 لماذا هذه الحقيبة هي رفيقك المثالي؟</h3>\n                <ul>\n                    <li><strong>تصميم عصري وأنيق:</strong> تناسب جميع مناسباتك</li>\n                    <li><strong>مواد عالية الجودة:</strong> مقاومة للماء والتمزق</li>\n                    <li><strong>سعة تخزين ذكية:</strong> جيوب متعددة ومنظمة</li>\n                    <li><strong>مريحة للحمل:</strong> أحزمة مبطنة وقابلة للتعديل</li>\n                </ul>\n\n                <h3>✨ مواصفات الحقيبة:</h3>\n                <p>• <strong>المادة:</strong> مواد متينة وعالية الجودة<br>\n                • <strong>السعة:</strong> مساحة واسعة لجميع احتياجاتك<br>\n                • <strong>الوزن:</strong> خفيفة الوزن وسهلة الحمل<br>\n                • <strong>الألوان:</strong> متوفرة بألوان متنوعة</p>", contenu_gauche: '\n                <h3>🌍 مثالية لـ:</h3>\n                <p>الاستخدام اليومي، طلاب الجامعات، الموظفين، ومحبي السفر. تصميمها المرن يجعلها مناسبة لكل الظروف.</p>\n\n                <h3>🌟 آراء العملاء</h3>\n                <blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">\n                    "حقيبة عملية جداً وأنيقة. أستخدمها كل يوم ولا أستغني عنها!"\n                    <cite style="display: block; margin-top: 10px; font-weight: bold;">- فاطمة، مصممة</cite>\n                </blockquote>\n\n                <h3>🎁 احصل على خصم 20% اليوم!</h3>\n                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">\n                    <h4 style="margin: 0 0 10px 0;">عرض خاص للمتسوقين الأوائل!</h4>\n                    <p style="font-size: 1.2em; margin: 0;">اطلبها الآن واستفد من التوصيل المجاني!</p>\n                </div>', image_position: "right", text_position: "left" }
landing-pages.js:205:17
Initializing TinyMCE for landing pages modal landing-pages.js:810:17
No TinyMCE editors found to clean up landing-pages.js:992:25
TinyMCE initialization attempt 1/5 landing-pages.js:821:25
📝 Moved to content step landing-pages.js:181:17
TinyMCE initialization successful landing-pages.js:947:29
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow tinymce.min.js:4:18466
Lors du rendu de l’élément <html>, les valeurs utilisées des propriétés CSS « write-mode », « direction » et « text-orientation » sur l’élément <html> sont extraites des valeurs calculées de l’élément <body>, et non à partir des valeurs propres à l’élément <html>. Envisagez de définir ces propriétés sur la pseudo-classe CSS :root. Pour plus d’informations, voir « Le mode d’écriture principal » dans https://www.w3.org/TR/css-writing-modes-3/#principal-flow tinymce.min.js:4:18466
Content set successfully for rightContent landing-pages.js:246:37
Content set successfully for leftContent landing-pages.js:246:37
✅ Template applied to form successfully landing-pages.js:265:21
📸 Added 3 images to preview. Total previews: 0 landing-pages.js:1095:17
MouseEvent.mozInputSource est obsolète. Veuillez utiliser plutôt PointerEvent.pointerType. tinymce.min.js:4:51163
Content changed in editor: rightContent admin.js:1012:29
Current content: <h3>🎒 لماذا هذه الحقيبة هي رفيقك المثالي؟</h3>
<ul>
<li><strong>تصميم عصري وأنيق:</strong> تناسب جميع مناسباتك</li>
<li><strong>مواد عالية الجودة:</strong> مقاومة للماء والتمزق</li>
<li><strong>سعة تخزين ذكية:</strong> جيوب متعددة ومنظمة</li>
<li><strong>مريحة للحمل:</strong> أحزمة مبطنة وقابلة للتعديل</li>
</ul>
<h3>✨ مواصفات الحقيبة:</h3>
<p>&bull; <strong>المادة:</strong> مواد متينة وعالية الجودة<br>&bull; <strong>السعة:</strong> مساحة واسعة لجميع احتياجاتك<br>&bull; <strong>الوزن:</strong> خفيفة الوزن وسهلة الحمل<br>&bull; <strong>الألوان:</strong> متوفرة بألوان متنوعة</p> admin.js:1013:29
📤 Submitting form... Edit mode landing-pages.js:1182:17
📸 Added 3 files to form data landing-pages.js:1211:21
🌐 Added 0 URL images to form data landing-pages.js:1221:17
📤 Sending form data to API... landing-pages.js:1224:21
Content changed in editor: leftContent admin.js:1012:29
XHRPUT
http://localhost:8000/php/api/landing-pages.php
[HTTP/1.1 500 Internal Server Error 842ms]

Current content: <h3>🌍 مثالية لـ:</h3>
<p>الاستخدام اليومي، طلاب الجامعات، الموظفين، ومحبي السفر. تصميمها المرن يجعلها مناسبة لكل الظروف.</p>
<h3>🌟 آراء العملاء</h3>
<blockquote style="border-right: 3px solid #667eea; padding-right: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">"حقيبة عملية جداً وأنيقة. أستخدمها كل يوم ولا أستغني عنها!" <cite style="display: block; margin-top: 10px; font-weight: bold;">- فاطمة، مصممة</cite></blockquote>
<h3>🎁 احصل على خصم 20% اليوم!</h3>
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
<h4 style="margin: 0 0 10px 0;">عرض خاص للمتسوقين الأوائل!</h4>
<p style="font-size: 1.2em; margin: 0;">اطلبها الآن واستفد من التوصيل المجاني!</p>
</div> admin.js:1013:29
📥 Response status: 500 landing-pages.js:1231:21
❌ HTTP Error: 500 {"success":false,"message":"Server error occurred","error_code":500} <anonymous code>:1:145535
❌ Error submitting form: Error: HTTP 500: {"success":false,"message":"Server error occurred","error_code":500}
    handleSubmit http://localhost:8000/admin/js/landing-pages.js:1236
    bindEvents http://localhost:8000/admin/js/landing-pages.js:318
    bindEvents http://localhost:8000/admin/js/landing-pages.js:318
    init http://localhost:8000/admin/js/landing-pages.js:90
    <anonymous> http://localhost:8000/admin/js/landing-pages.js:1920
    EventListener.handleEvent* http://localhost:8000/admin/js/landing-pages.js:1917
<anonymous code>:1:145535
Web Audio notification played successfully admin.js:147:21
Erreur dans les liens source : Error: JSON.parse: unexpected character at line 1 column 1 of the JSON data
Stack in the worker:parseSourceMapInput@resource://devtools/client/shared/vendor/source-map/lib/util.js:163:15
_factory@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:1066:22
SourceMapConsumer@resource://devtools/client/shared/vendor/source-map/lib/source-map-consumer.js:26:12
_fetch@resource://devtools/client/shared/source-map-loader/utils/fetchSourceMap.js:83:19

URL de la ressource : http://localhost:8000/admin/%3Canonymous%20code%3E
URL du lien source : installHook.js.map
📤 Submitting form... Edit mode landing-pages.js:1182:17
📸 Added 3 files to form data landing-pages.js:1211:21
🌐 Added 0 URL images to form data landing-pages.js:1221:17
📤 Sending form data to API... landing-pages.js:1224:21
XHRPUT
http://localhost:8000/php/api/landing-pages.php
[HTTP/1.1 500 Internal Server Error 4947ms]

📥 Response status: 500 landing-pages.js:1231:21
❌ HTTP Error: 500 {"success":false,"message":"Server error occurred","error_code":500} <anonymous code>:1:145535
❌ Error submitting form: Error: HTTP 500: {"success":false,"message":"Server error occurred","error_code":500}
    handleSubmit http://localhost:8000/admin/js/landing-pages.js:1236
    bindEvents http://localhost:8000/admin/js/landing-pages.js:318
    bindEvents http://localhost:8000/admin/js/landing-pages.js:318
    init http://localhost:8000/admin/js/landing-pages.js:90
    <anonymous> http://localhost:8000/admin/js/landing-pages.js:1920
    EventListener.handleEvent* http://localhost:8000/admin/js/landing-pages.js:1917
<anonymous code>:1:145535
Web Audio notification played successfully admin.js:147:21
