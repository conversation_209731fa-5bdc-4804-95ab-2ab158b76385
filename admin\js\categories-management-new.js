/**
 * Categories Management JavaScript
 * إدارة الفئات - JavaScript
 */

// Global variables
let categoriesData = [];
let currentCategory = null;
let isLoading = false;

/**
 * Load categories management content
 */
function loadCategoriesManagementContent() {
    console.log('🗂️ Loading categories management content...');

    const container = document.getElementById('categoriesManagementContent');
    if (!container) {
        console.error('Categories management container not found');
        return;
    }

    // Show loading state
    showLoadingState(container);

    // Load categories from server
    fetchCategories()
        .then(data => {
            if (data.success) {
                categoriesData = data.data.categories;
                renderCategoriesManagement(data.data);
            } else {
                throw new Error(data.message || 'فشل في تحميل الفئات');
            }
        })
        .catch(error => {
            console.error('Error loading categories:', error);
            showErrorState(container, error.message);
        });
}

/**
 * Fetch categories from server
 */
async function fetchCategories(includeInactive = false) {
    try {
        const url = `php/categories.php?action=get_all${includeInactive ? '&include_inactive=true' : ''}`;
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        throw new Error('خطأ في الاتصال بالخادم: ' + error.message);
    }
}

/**
 * Show loading state
 */
function showLoadingState(container) {
    container.innerHTML = `
        <div class="loading-state" style="text-align: center; padding: 40px;">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
            </div>
            <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
        </div>
    `;
}

/**
 * Show error state
 */
function showErrorState(container, message) {
    container.innerHTML = `
        <div class="error-state" style="text-align: center; padding: 40px; color: #dc3545;">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
            </div>
            <h4>خطأ في تحميل إدارة الفئات</h4>
            <p>${message}</p>
            <button class="btn btn-primary" onclick="loadCategoriesManagementContent()">
                <i class="fas fa-redo"></i> إعادة المحاولة
            </button>
        </div>
    `;
}

/**
 * Render categories management interface
 */
function renderCategoriesManagement(data) {
    const container = document.getElementById('categoriesManagementContent');

    const html = `
        <div class="categories-management-container">
            <!-- Header -->
            <div class="categories-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px;">
                <div class="header-content">
                    <h2 style="margin: 0; font-size: 1.8rem;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة وتنظيم فئات المنتجات والمحتوى</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-light" onclick="showAddCategoryModal()" style="margin-left: 10px;">
                        <i class="fas fa-plus"></i> إضافة فئة جديدة
                    </button>
                    <button class="btn btn-outline-light" onclick="refreshCategories()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="categories-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div class="stat-icon" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.total}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">إجمالي الفئات</p>
                    </div>
                </div>
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div class="stat-icon" style="font-size: 2rem; color: #28a745; margin-bottom: 10px;">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.categories.filter(c => c.parent_id === null).length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات الرئيسية</p>
                    </div>
                </div>
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div class="stat-icon" style="font-size: 2rem; color: #17a2b8; margin-bottom: 10px;">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.categories.filter(c => c.parent_id !== null).length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات الفرعية</p>
                    </div>
                </div>
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div class="stat-icon" style="font-size: 2rem; color: #ffc107; margin-bottom: 10px;">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.categories.filter(c => c.is_featured == 1).length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات المميزة</p>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="categories-filters" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 30px;">
                <div style="display: flex; gap: 20px; align-items: center; flex-wrap: wrap;">
                    <div class="search-box" style="flex: 1; min-width: 250px; position: relative;">
                        <input type="text" id="categoriesSearch" placeholder="البحث في الفئات..." onkeyup="searchCategories()" style="width: 100%; padding: 10px 40px 10px 15px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px;">
                        <i class="fas fa-search" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: #999;"></i>
                    </div>
                    <div class="filter-buttons" style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="filter-btn active" data-filter="all" onclick="filterCategories('all')" style="padding: 8px 16px; border: 2px solid #667eea; background: #667eea; color: white; border-radius: 6px; cursor: pointer; transition: all 0.3s;">
                            الكل (${data.total})
                        </button>
                        <button class="filter-btn" data-filter="main" onclick="filterCategories('main')" style="padding: 8px 16px; border: 2px solid #28a745; background: transparent; color: #28a745; border-radius: 6px; cursor: pointer; transition: all 0.3s;">
                            الرئيسية (${data.categories.filter(c => c.parent_id === null).length})
                        </button>
                        <button class="filter-btn" data-filter="sub" onclick="filterCategories('sub')" style="padding: 8px 16px; border: 2px solid #17a2b8; background: transparent; color: #17a2b8; border-radius: 6px; cursor: pointer; transition: all 0.3s;">
                            الفرعية (${data.categories.filter(c => c.parent_id !== null).length})
                        </button>
                        <button class="filter-btn" data-filter="featured" onclick="filterCategories('featured')" style="padding: 8px 16px; border: 2px solid #ffc107; background: transparent; color: #ffc107; border-radius: 6px; cursor: pointer; transition: all 0.3s;">
                            المميزة (${data.categories.filter(c => c.is_featured == 1).length})
                        </button>
                    </div>
                </div>
            </div>

            <!-- Categories List -->
            <div class="categories-content" style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                <div class="categories-view-toggle" style="padding: 15px 20px; border-bottom: 1px solid #e0e0e0; display: flex; gap: 10px;">
                    <button class="view-btn active" data-view="hierarchy" onclick="switchView('hierarchy')" style="padding: 8px 16px; border: 2px solid #667eea; background: #667eea; color: white; border-radius: 6px; cursor: pointer;">
                        <i class="fas fa-sitemap"></i> عرض هرمي
                    </button>
                    <button class="view-btn" data-view="table" onclick="switchView('table')" style="padding: 8px 16px; border: 2px solid #667eea; background: transparent; color: #667eea; border-radius: 6px; cursor: pointer;">
                        <i class="fas fa-table"></i> عرض جدولي
                    </button>
                </div>

                <div id="categoriesListContainer" style="padding: 20px;">
                    ${renderCategoriesHierarchy(data.hierarchy)}
                </div>
            </div>
        </div>

        <!-- Category Modal -->
        <div id="categoryModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
            <div class="modal-content" style="background: white; border-radius: 12px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto;">
                <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #e0e0e0; display: flex; justify-content: space-between; align-items: center;">
                    <h3 id="modalTitle" style="margin: 0;">إضافة فئة جديدة</h3>
                    <button class="modal-close" onclick="closeCategoryModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #999;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" style="padding: 20px;">
                    <form id="categoryForm">
                        <input type="hidden" id="categoryId" name="id">

                        <!-- Basic Information -->
                        <div class="form-section" style="margin-bottom: 25px;">
                            <h4 style="margin-bottom: 15px; color: #333; border-bottom: 2px solid #667eea; padding-bottom: 5px;"><i class="fas fa-info-circle"></i> المعلومات الأساسية</h4>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div class="form-group">
                                    <label for="nameAr" style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم بالعربية <span style="color: red;">*</span></label>
                                    <input type="text" id="nameAr" name="name_ar" required style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 6px; box-sizing: border-box;">
                                </div>
                                <div class="form-group">
                                    <label for="nameEn" style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم بالإنجليزية</label>
                                    <input type="text" id="nameEn" name="name_en" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 6px; box-sizing: border-box;">
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div class="form-group">
                                    <label for="slug" style="display: block; margin-bottom: 5px; font-weight: bold;">الرابط المختصر</label>
                                    <input type="text" id="slug" name="slug" placeholder="سيتم إنشاؤه تلقائياً" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 6px; box-sizing: border-box;">
                                </div>
                                <div class="form-group">
                                    <label for="parentId" style="display: block; margin-bottom: 5px; font-weight: bold;">الفئة الأب</label>
                                    <select id="parentId" name="parent_id" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 6px; box-sizing: border-box;">
                                        <option value="">فئة رئيسية</option>
                                        ${renderParentOptions(data.categories)}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group" style="margin-bottom: 15px;">
                                <label for="descriptionAr" style="display: block; margin-bottom: 5px; font-weight: bold;">الوصف بالعربية</label>
                                <textarea id="descriptionAr" name="description_ar" rows="3" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 6px; box-sizing: border-box; resize: vertical;"></textarea>
                            </div>
                        </div>

                        <!-- Appearance -->
                        <div class="form-section" style="margin-bottom: 25px;">
                            <h4 style="margin-bottom: 15px; color: #333; border-bottom: 2px solid #667eea; padding-bottom: 5px;"><i class="fas fa-palette"></i> المظهر</h4>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                <div class="form-group">
                                    <label for="icon" style="display: block; margin-bottom: 5px; font-weight: bold;">الأيقونة</label>
                                    <input type="text" id="icon" name="icon" placeholder="fas fa-folder" value="fas fa-folder" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 6px; box-sizing: border-box;">
                                    <small style="color: #666; font-size: 0.8em;">استخدم أيقونات Font Awesome</small>
                                </div>
                                <div class="form-group">
                                    <label for="color" style="display: block; margin-bottom: 5px; font-weight: bold;">اللون</label>
                                    <input type="color" id="color" name="color" value="#667eea" style="width: 100%; padding: 5px; border: 2px solid #e0e0e0; border-radius: 6px; box-sizing: border-box; height: 42px;">
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="form-section">
                            <h4 style="margin-bottom: 15px; color: #333; border-bottom: 2px solid #667eea; padding-bottom: 5px;"><i class="fas fa-cog"></i> الإعدادات</h4>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; align-items: end;">
                                <div class="form-group">
                                    <label for="sortOrder" style="display: block; margin-bottom: 5px; font-weight: bold;">ترتيب العرض</label>
                                    <input type="number" id="sortOrder" name="sort_order" value="0" min="0" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 6px; box-sizing: border-box;">
                                </div>
                                <div class="checkbox-group" style="display: flex; gap: 20px;">
                                    <label style="display: flex; align-items: center; cursor: pointer;">
                                        <input type="checkbox" id="isActive" name="is_active" checked style="margin-left: 8px;">
                                        نشط
                                    </label>
                                    <label style="display: flex; align-items: center; cursor: pointer;">
                                        <input type="checkbox" id="isFeatured" name="is_featured" style="margin-left: 8px;">
                                        مميز
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="padding: 20px; border-top: 1px solid #e0e0e0; display: flex; justify-content: flex-end; gap: 10px;">
                    <button type="button" class="btn btn-secondary" onclick="closeCategoryModal()" style="padding: 10px 20px; border: 2px solid #6c757d; background: transparent; color: #6c757d; border-radius: 6px; cursor: pointer;">
                        إلغاء
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveCategoryForm()" style="padding: 10px 20px; border: 2px solid #667eea; background: #667eea; color: white; border-radius: 6px; cursor: pointer;">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;

    // Initialize event listeners
    initializeCategoriesEvents();
}

/**
 * Render categories hierarchy
 */
function renderCategoriesHierarchy(hierarchy, level = 0) {
    let html = '<div class="categories-hierarchy">';

    hierarchy.forEach(category => {
        const indent = level * 20;
        const statusClass = category.is_active == 1 ? 'active' : 'inactive';
        const featuredClass = category.is_featured == 1 ? 'featured' : '';

        html += `
            <div class="category-item ${statusClass} ${featuredClass}" style="margin-right: ${indent}px; margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; ${category.is_active == 1 ? 'background: #f8f9fa;' : 'background: #f5f5f5; opacity: 0.7;'}">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="category-info" style="display: flex; align-items: center; gap: 15px;">
                        <div class="category-icon" style="color: ${category.color}; font-size: 1.5rem;">
                            <i class="${category.icon || 'fas fa-folder'}"></i>
                        </div>
                        <div class="category-details">
                            <h4 style="margin: 0 0 5px 0; color: #333;">${category.name_ar}</h4>
                            <p style="margin: 0 0 10px 0; color: #666; font-size: 0.9em;">${category.description_ar || 'لا يوجد وصف'}</p>
                            <div class="category-meta" style="display: flex; gap: 15px; font-size: 0.8em; color: #999;">
                                <span class="meta-item">
                                    <i class="fas fa-layer-group"></i>
                                    ${category.subcategories_count} فئة فرعية
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-box"></i>
                                    ${category.products_count || 0} منتج
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-eye"></i>
                                    ${category.views_count || 0} مشاهدة
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="category-actions" style="display: flex; gap: 5px;">
                        <button class="action-btn edit" onclick="editCategory(${category.id})" title="تعديل" style="padding: 8px 12px; border: none; background: #17a2b8; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle ${category.is_active == 1 ? 'active' : 'inactive'}"
                                onclick="toggleCategoryStatus(${category.id})"
                                title="${category.is_active == 1 ? 'إلغاء التفعيل' : 'تفعيل'}"
                                style="padding: 8px 12px; border: none; background: ${category.is_active == 1 ? '#28a745' : '#6c757d'}; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-${category.is_active == 1 ? 'eye-slash' : 'eye'}"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteCategory(${category.id})" title="حذف" style="padding: 8px 12px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Render children
        if (category.children && category.children.length > 0) {
            html += renderCategoriesHierarchy(category.children, level + 1);
        }
    });

    html += '</div>';
    return html;
}

/**
 * Render parent options for select
 */
function renderParentOptions(categories, selectedId = null, level = 0) {
    let html = '';

    categories.forEach(category => {
        if (category.parent_id === null || level === 0) {
            const indent = '—'.repeat(level);
            const selected = selectedId == category.id ? 'selected' : '';

            html += `<option value="${category.id}" ${selected}>${indent} ${category.name_ar}</option>`;

            // Add subcategories
            const subcategories = categories.filter(c => c.parent_id == category.id);
            if (subcategories.length > 0) {
                html += renderParentOptionsRecursive(subcategories, categories, selectedId, level + 1);
            }
        }
    });

    return html;
}

/**
 * Recursive helper for parent options
 */
function renderParentOptionsRecursive(subcategories, allCategories, selectedId, level) {
    let html = '';

    subcategories.forEach(category => {
        const indent = '—'.repeat(level);
        const selected = selectedId == category.id ? 'selected' : '';

        html += `<option value="${category.id}" ${selected}>${indent} ${category.name_ar}</option>`;

        // Add deeper subcategories
        const deeperSubs = allCategories.filter(c => c.parent_id == category.id);
        if (deeperSubs.length > 0) {
            html += renderParentOptionsRecursive(deeperSubs, allCategories, selectedId, level + 1);
        }
    });

    return html;
}

/**
 * Initialize event listeners
 */
function initializeCategoriesEvents() {
    // Auto-generate slug from Arabic name
    const nameArInput = document.getElementById('nameAr');
    const slugInput = document.getElementById('slug');

    if (nameArInput && slugInput) {
        nameArInput.addEventListener('input', function() {
            if (!slugInput.value || slugInput.dataset.autoGenerated === 'true') {
                slugInput.value = generateSlug(this.value);
                slugInput.dataset.autoGenerated = 'true';
            }
        });

        slugInput.addEventListener('input', function() {
            this.dataset.autoGenerated = 'false';
        });
    }
}

/**
 * Generate slug from text
 */
function generateSlug(text) {
    // Simple slug generation for Arabic text
    const arabicToEnglish = {
        'ا': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
        'د': 'd', 'ذ': 'th', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh', 'ص': 's',
        'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
        'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y',
        'ة': 'h', 'ى': 'a', 'ء': 'a', 'أ': 'a', 'إ': 'i', 'آ': 'a', 'ؤ': 'o', 'ئ': 'e'
    };

    let slug = text;
    for (const [arabic, english] of Object.entries(arabicToEnglish)) {
        slug = slug.replace(new RegExp(arabic, 'g'), english);
    }

    slug = slug.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/[\s-]+/g, '-')
        .replace(/^-+|-+$/g, '');

    return slug;
}

/**
 * Show add category modal
 */
function showAddCategoryModal() {
    document.getElementById('categoryModal').style.display = 'flex';
    document.getElementById('modalTitle').textContent = 'إضافة فئة جديدة';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    currentCategory = null;
}

/**
 * Close category modal
 */
function closeCategoryModal() {
    document.getElementById('categoryModal').style.display = 'none';
    currentCategory = null;
}

/**
 * Refresh categories list
 */
function refreshCategories() {
    loadCategoriesManagementContent();
}

/**
 * Edit category
 */
async function editCategory(id) {
    try {
        const response = await fetch(`php/categories.php?action=get_by_id&id=${id}`);
        const data = await response.json();

        if (data.success) {
            currentCategory = data.data.category;
            populateCategoryForm(currentCategory);
            document.getElementById('modalTitle').textContent = 'تعديل الفئة';
            document.getElementById('categoryModal').style.display = 'flex';
        } else {
            showNotification('خطأ في جلب بيانات الفئة: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Error fetching category:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

/**
 * Toggle category status
 */
async function toggleCategoryStatus(id) {
    if (!confirm('هل أنت متأكد من تغيير حالة هذه الفئة؟')) {
        return;
    }

    try {
        const response = await fetch('php/categories.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=toggle_status&id=${id}`
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
            refreshCategories();
        } else {
            showNotification('خطأ: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Error toggling category status:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

/**
 * Delete category
 */
async function deleteCategory(id) {
    if (!confirm('هل أنت متأكد من حذف هذه الفئة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }

    try {
        const response = await fetch('php/categories.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete&id=${id}`
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
            refreshCategories();
        } else {
            showNotification('خطأ: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Error deleting category:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

/**
 * Save category form
 */
async function saveCategoryForm() {
    const form = document.getElementById('categoryForm');
    const formData = new FormData(form);

    // Add action
    const action = currentCategory ? 'update' : 'create';
    formData.append('action', action);

    if (currentCategory) {
        formData.append('id', currentCategory.id);
    }

    // Convert checkboxes to proper values
    formData.set('is_active', document.getElementById('isActive').checked ? '1' : '0');
    formData.set('is_featured', document.getElementById('isFeatured').checked ? '1' : '0');

    try {
        const response = await fetch('php/categories.php', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message, 'success');
            closeCategoryModal();
            refreshCategories();
        } else {
            showNotification('خطأ: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('Error saving category:', error);
        showNotification('خطأ في الاتصال بالخادم', 'error');
    }
}

/**
 * Search categories
 */
function searchCategories() {
    const query = document.getElementById('categoriesSearch').value.toLowerCase();
    const categoryItems = document.querySelectorAll('.category-item');

    categoryItems.forEach(item => {
        const categoryName = item.querySelector('h4').textContent.toLowerCase();
        const categoryDesc = item.querySelector('p').textContent.toLowerCase();

        if (categoryName.includes(query) || categoryDesc.includes(query)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

/**
 * Filter categories
 */
function filterCategories(filter) {
    // Update active filter button
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

    const categoryItems = document.querySelectorAll('.category-item');

    categoryItems.forEach(item => {
        let show = false;

        switch (filter) {
            case 'all':
                show = true;
                break;
            case 'main':
                // Check if it's a main category (no parent)
                show = !item.style.marginRight || item.style.marginRight === '0px';
                break;
            case 'sub':
                // Check if it's a subcategory (has margin)
                show = item.style.marginRight && item.style.marginRight !== '0px';
                break;
            case 'featured':
                show = item.classList.contains('featured');
                break;
            case 'inactive':
                show = item.classList.contains('inactive');
                break;
        }

        item.style.display = show ? 'block' : 'none';
    });
}

/**
 * Switch view
 */
function switchView(view) {
    // Update active view button
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');

    // TODO: Implement different view layouts
    console.log('Switching to view:', view);
}

/**
 * Populate category form with data
 */
function populateCategoryForm(category) {
    document.getElementById('categoryId').value = category.id;
    document.getElementById('nameAr').value = category.name_ar || '';
    document.getElementById('nameEn').value = category.name_en || '';
    document.getElementById('slug').value = category.slug || '';
    document.getElementById('parentId').value = category.parent_id || '';
    document.getElementById('descriptionAr').value = category.description_ar || '';
    document.getElementById('icon').value = category.icon || 'fas fa-folder';
    document.getElementById('color').value = category.color || '#667eea';
    document.getElementById('sortOrder').value = category.sort_order || 0;
    document.getElementById('isActive').checked = category.is_active == 1;
    document.getElementById('isFeatured').checked = category.is_featured == 1;
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Use existing notification system if available
    if (typeof notificationManager !== 'undefined') {
        switch (type) {
            case 'success':
                notificationManager.showSuccess(message);
                break;
            case 'error':
                notificationManager.showError(message);
                break;
            default:
                notificationManager.showInfo(message);
        }
    } else {
        // Fallback to alert
        alert(message);
    }
}

// Make functions globally available
window.loadCategoriesManagementContent = loadCategoriesManagementContent;
