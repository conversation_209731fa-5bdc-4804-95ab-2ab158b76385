<?php
/**
 * Simplified Products API
 * Basic version without complex dependencies
 */

// Set proper headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Try to load config
    $configLoaded = false;
    $pdo = null;
    
    // Try different config paths
    $configPaths = [
        __DIR__ . '/../config.php',
        __DIR__ . '/../../config/config.php',
        __DIR__ . '/../config/config.php'
    ];
    
    foreach ($configPaths as $configPath) {
        if (file_exists($configPath)) {
            require_once $configPath;
            $configLoaded = true;
            break;
        }
    }
    
    if (!$configLoaded) {
        throw new Exception('Configuration file not found');
    }
    
    // Try to get database connection
    if (function_exists('getPDOConnection')) {
        $pdo = getPDOConnection();
    } else {
        // Fallback database connection
        $host = 'localhost';
        $port = '3307';
        $dbname = 'mossab-landing-page';
        $username = 'root';
        $password = '';
        
        $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
    }
    
    // Handle different HTTP methods
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetProducts($pdo);
            break;
        case 'POST':
            handleCreateProduct($pdo);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

function handleGetProducts($pdo) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM produits WHERE actif = 1 ORDER BY created_at DESC LIMIT 50");
        $stmt->execute();
        $products = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $products,
            'count' => count($products),
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}

function handleCreateProduct($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        // Basic validation
        if (empty($input['titre']) || empty($input['prix'])) {
            throw new Exception('Title and price are required');
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO produits (type, titre, description, prix, stock, actif, created_at) 
            VALUES (?, ?, ?, ?, ?, 1, NOW())
        ");
        
        $stmt->execute([
            $input['type'] ?? 'book',
            $input['titre'],
            $input['description'] ?? '',
            $input['prix'],
            $input['stock'] ?? 0
        ]);
        
        $productId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'Product created successfully',
            'product_id' => $productId,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Error creating product: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}
?>
