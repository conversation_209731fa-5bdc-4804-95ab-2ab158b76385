/**
 * Configuration de Compression et Optimisation
 * Landing Pages SaaS - "صفحات هبوط للجميع"
 * 
 * Ce fichier configure les outils de build pour optimiser automatiquement
 * le code selon les recommandations du rapport de compression.
 */

const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const ImageMinimizerPlugin = require('imagemin-webpack-plugin').default;
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');

module.exports = {
  // Configuration Webpack pour la compression
  webpack: {
    mode: 'production',
    entry: {
      // Bundles optimisés selon l'analyse
      core: './js/main.js',
      utils: './js/utils.js',
      cart: './js/cart.js',
      admin: './admin/js/admin.js',
      'landing-pages': './admin/js/landing-pages.js'
    },
    
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: 'js/[name].bundle.min.js',
      chunkFilename: 'js/[name].[contenthash].chunk.js',
      clean: true
    },
    
    optimization: {
      minimize: true,
      minimizer: [
        // Compression JavaScript
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true, // Supprimer console.log en production
              drop_debugger: true,
              pure_funcs: ['console.log', 'console.warn'], // Fonctions à supprimer
              dead_code: true, // Supprimer le code mort
              unused: true // Supprimer les variables non utilisées
            },
            mangle: {
              safari10: true // Compatibilité Safari
            },
            format: {
              comments: false // Supprimer les commentaires
            }
          },
          extractComments: false
        }),
        
        // Compression CSS
        new CssMinimizerPlugin({
          minimizerOptions: {
            preset: [
              'default',
              {
                discardComments: { removeAll: true },
                normalizeWhitespace: true,
                colormin: true,
                convertValues: true,
                discardDuplicates: true,
                mergeLonghand: true,
                mergeRules: true,
                minifyFontValues: true,
                minifySelectors: true
              }
            ]
          }
        })
      ],
      
      // Code splitting intelligent
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          // Vendor libraries
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10
          },
          
          // Code commun
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true
          },
          
          // CSS commun
          styles: {
            name: 'styles',
            test: /\.css$/,
            chunks: 'all',
            enforce: true
          }
        }
      }
    },
    
    plugins: [
      new CleanWebpackPlugin(),
      
      // Compression Gzip
      new CompressionPlugin({
        algorithm: 'gzip',
        test: /\.(js|css|html|svg)$/,
        threshold: 8192,
        minRatio: 0.8
      }),
      
      // Compression Brotli (meilleure que Gzip)
      new CompressionPlugin({
        filename: '[path][base].br',
        algorithm: 'brotliCompress',
        test: /\.(js|css|html|svg)$/,
        compressionOptions: {
          level: 11
        },
        threshold: 8192,
        minRatio: 0.8
      }),
      
      // Optimisation des images
      new ImageMinimizerPlugin({
        test: /\.(jpe?g|png|gif|svg)$/i,
        minimizer: {
          implementation: ImageMinimizerPlugin.imageminMinify,
          options: {
            plugins: [
              ['imagemin-mozjpeg', { quality: 80 }],
              ['imagemin-pngquant', { quality: [0.6, 0.8] }],
              ['imagemin-svgo', {
                plugins: [
                  {
                    name: 'preset-default',
                    params: {
                      overrides: {
                        removeViewBox: false
                      }
                    }
                  }
                ]
              }]
            ]
          }
        },
        generator: [
          // Génération WebP
          {
            type: 'asset',
            preset: 'webp-custom-name',
            implementation: ImageMinimizerPlugin.imageminGenerate,
            options: {
              plugins: ['imagemin-webp']
            }
          }
        ]
      })
    ],
    
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env'],
              plugins: [
                '@babel/plugin-proposal-object-rest-spread',
                '@babel/plugin-transform-async-to-generator'
              ]
            }
          }
        },
        {
          test: /\.css$/,
          use: [
            'style-loader',
            'css-loader',
            {
              loader: 'postcss-loader',
              options: {
                postcssOptions: {
                  plugins: [
                    ['autoprefixer'],
                    ['cssnano', {
                      preset: 'default'
                    }]
                  ]
                }
              }
            }
          ]
        }
      ]
    }
  },
  
  // Configuration PostCSS pour optimisation CSS
  postcss: {
    plugins: {
      'autoprefixer': {},
      'cssnano': {
        preset: [
          'default',
          {
            discardComments: {
              removeAll: true
            },
            normalizeWhitespace: true,
            colormin: true,
            convertValues: true,
            discardDuplicates: true,
            mergeLonghand: true,
            mergeRules: true,
            minifyFontValues: true,
            minifySelectors: true,
            reduceIdents: false, // Garder les noms de classes pour le debug
            zindex: false // Ne pas optimiser z-index pour éviter les conflits
          }
        ]
      },
      'postcss-combine-duplicated-selectors': {},
      'postcss-merge-rules': {},
      'postcss-discard-unused': {}
    }
  },
  
  // Configuration pour la suppression du code mort
  deadCodeElimination: {
    // Fonctions JavaScript non utilisées détectées
    unusedFunctions: [
      'showLegacyNotification',
      'oldApiCall',
      'deprecatedCartFunctions',
      'legacyImageLoader',
      'oldValidationSystem'
    ],
    
    // Classes CSS non utilisées
    unusedCssClasses: [
      '.legacy-button',
      '.old-modal',
      '.deprecated-grid',
      '.unused-animation'
    ],
    
    // Fichiers à exclure de la compilation
    excludeFiles: [
      'js/legacy/',
      'css/old-styles/',
      'admin/js/deprecated/'
    ]
  },
  
  // Configuration Critical CSS
  criticalCss: {
    // Styles critiques à inliner
    inline: [
      '.header',
      '.hero',
      '.cta-button',
      '.loading-indicator',
      '.notification'
    ],
    
    // Dimensions pour l'extraction
    dimensions: [
      {
        width: 1200,
        height: 900
      },
      {
        width: 768,
        height: 1024
      },
      {
        width: 375,
        height: 667
      }
    ]
  },
  
  // Configuration Service Worker
  serviceWorker: {
    cacheStrategy: {
      // Cache statique
      static: {
        urlPattern: /\.(css|js|woff2|png|jpg|svg)$/,
        handler: 'CacheFirst',
        options: {
          cacheName: 'static-assets-v1',
          expiration: {
            maxEntries: 100,
            maxAgeSeconds: 30 * 24 * 60 * 60 // 30 jours
          }
        }
      },
      
      // Cache API
      api: {
        urlPattern: /\/api\//,
        handler: 'NetworkFirst',
        options: {
          cacheName: 'api-cache-v1',
          expiration: {
            maxEntries: 50,
            maxAgeSeconds: 5 * 60 // 5 minutes
          }
        }
      },
      
      // Cache pages
      pages: {
        urlPattern: /\.(html|php)$/,
        handler: 'StaleWhileRevalidate',
        options: {
          cacheName: 'pages-cache-v1',
          expiration: {
            maxEntries: 30,
            maxAgeSeconds: 24 * 60 * 60 // 24 heures
          }
        }
      }
    }
  },
  
  // Configuration de monitoring des performances
  performance: {
    // Métriques à surveiller
    metrics: [
      'first-contentful-paint',
      'largest-contentful-paint',
      'cumulative-layout-shift',
      'time-to-interactive'
    ],
    
    // Seuils d'alerte
    thresholds: {
      fcp: 1500, // ms
      lcp: 2500, // ms
      cls: 0.1,
      tti: 3000 // ms
    },
    
    // Reporting
    reporting: {
      endpoint: '/api/performance-metrics',
      sampleRate: 0.1 // 10% des sessions
    }
  }
};

// Export pour utilisation dans les scripts de build
if (typeof module !== 'undefined' && module.exports) {
  module.exports = module.exports;
}