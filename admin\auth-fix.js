// Auth Fix Script - Prevents infinite redirects and handles Firebase errors
console.log('🔧 Auth Fix Script loaded');

// Prevent infinite redirects by tracking navigation
let redirectCount = 0;
const maxRedirects = 3;
const redirectKey = 'auth_redirect_count';

// Reset redirect counter on page load
if (performance.navigation.type === performance.navigation.TYPE_NAVIGATE) {
    sessionStorage.removeItem(redirectKey);
    redirectCount = 0;
} else {
    redirectCount = parseInt(sessionStorage.getItem(redirectKey) || '0');
}

// Safe redirect function with enhanced loop prevention
window.safeRedirect = function(url) {
    // Check if we're already on the target page
    const currentPath = window.location.pathname;
    const targetPath = url.includes('/') ? url : `/admin/${url}`;

    if (currentPath.includes(url.replace('.html', ''))) {
        console.log('⚠️ Already on target page, skipping redirect');
        return false;
    }

    // Check if redirect is already in progress
    if (window.redirectInProgress) {
        console.log('⚠️ Redirect already in progress, skipping duplicate');
        return false;
    }

    redirectCount++;
    sessionStorage.setItem(redirectKey, redirectCount.toString());

    if (redirectCount > maxRedirects) {
        console.error('❌ Too many redirects detected, stopping to prevent infinite loop');
        alert('خطأ: تم اكتشاف حلقة إعادة توجيه لا نهائية. يرجى إعادة تحميل الصفحة.');
        // Reset redirect count to allow manual navigation
        sessionStorage.removeItem(redirectKey);
        return false;
    }

    console.log(`🔄 Safe redirect to: ${url} (attempt ${redirectCount}/${maxRedirects})`);
    window.redirectInProgress = true;

    // Clear redirect flag after navigation
    setTimeout(() => {
        window.redirectInProgress = false;
    }, 1000);

    window.location.href = url;
    return true;
};

// Enhanced Firebase checker
window.waitForFirebase = function(callback, timeout = 10000) {
    const startTime = Date.now();
    const checkInterval = 200;

    function check() {
        if (window.firebaseAuth && typeof window.firebaseAuth.isAuthenticated === 'function') {
            console.log('✅ Firebase is ready');
            callback(true);
            return;
        }

        if (Date.now() - startTime > timeout) {
            console.error('❌ Firebase timeout - not loaded within', timeout, 'ms');
            callback(false);
            return;
        }

        setTimeout(check, checkInterval);
    }

    check();
};

// Safe authentication check
window.safeAuthCheck = function(onAuthenticated, onNotAuthenticated, onError) {
    console.log('🔍 Starting safe auth check...');

    waitForFirebase((firebaseReady) => {
        if (!firebaseReady) {
            console.error('❌ Firebase not ready for auth check');
            if (onError) onError('Firebase not ready');
            return;
        }

        try {
            const isAuth = window.firebaseAuth.isAuthenticated();
            const userInfo = window.firebaseAuth.getCurrentUser();

            console.log('🔐 Auth status:', isAuth);
            console.log('👤 User info:', userInfo);

            if (isAuth && userInfo.user) {
                if (onAuthenticated) onAuthenticated(userInfo);
            } else {
                if (onNotAuthenticated) onNotAuthenticated();
            }
        } catch (error) {
            console.error('❌ Error during auth check:', error);
            if (onError) onError(error.message);
        }
    });
};

// Enhanced Firebase callbacks with proper redirect handling
window.onFirebaseUserSignedIn = function(user, profile) {
    console.log('🔐 Firebase user signed in:', user.email);
    console.log('👤 User profile:', profile);

    // Ignore test users and anonymous authentication
    if (!user || !user.email || user.email === '<EMAIL>' ||
        (profile && (profile.testProfile === true || profile.offline === true))) {
        console.log('🧪 Ignoring test/anonymous user authentication');
        return;
    }

    // Check if we're on login page and user has admin privileges
    const currentPath = window.location.pathname;
    if (currentPath.includes('login.html') && profile) {
        // CRITICAL FIX: Only redirect if this is a fresh login, not an automatic auth state change
        // Check if user just completed a login form submission (window or sessionStorage)
        const sessionManualLogin = sessionStorage.getItem('manualLogin') === 'true';
        const sessionLoginTime = parseInt(sessionStorage.getItem('manualLoginTime') || '0');
        const timeSinceLogin = Date.now() - sessionLoginTime;
        const isRecentLogin = timeSinceLogin < 30000; // 30 seconds

        const isManualLogin = window.loginFormSubmitted || (sessionManualLogin && isRecentLogin);

        console.log('🔍 Manual login check:', {
            windowLoginFormSubmitted: window.loginFormSubmitted,
            sessionManualLogin: sessionManualLogin,
            timeSinceLogin: timeSinceLogin,
            isRecentLogin: isRecentLogin,
            isManualLogin: isManualLogin,
            currentPath: currentPath
        });

        if (!isManualLogin) {
            console.log('🔄 Automatic auth state change detected on login page - NOT redirecting');
            console.log('💡 User was already logged in, staying on login page to avoid redirect loop');
            console.log('⚠️ Pour forcer la redirection, définissez window.loginFormSubmitted = true');
            return;
        }

        // Reset the manual login flags
        console.log('🔄 Resetting loginFormSubmitted flags');
        window.loginFormSubmitted = false;
        sessionStorage.removeItem('manualLogin');
        sessionStorage.removeItem('manualLoginTime');

        // Skip admin check for login page mode (minimal profile)
        if (profile.loginPageMode === true) {
            console.log('📱 Login page mode detected, skipping admin privilege check');
            console.log('✅ User authenticated successfully, redirecting to admin dashboard');
            // Add delay to prevent race conditions
            setTimeout(() => {
                if (typeof window.safeRedirect === 'function') {
                    window.safeRedirect('index.html');
                } else {
                    console.warn('⚠️ safeRedirect not available, using fallback');
                    window.location.href = 'index.html';
                }
            }, 500);
            return;
        }

        // Normal admin privilege check for full profiles
        if (['admin', 'super_admin', 'owner', 'manager'].includes(profile.role)) {
            console.log('✅ Admin user authenticated, redirecting to admin dashboard');
            // Add delay to prevent race conditions
            setTimeout(() => {
                if (typeof window.safeRedirect === 'function') {
                    window.safeRedirect('index.html');
                } else {
                    console.warn('⚠️ safeRedirect not available, using fallback');
                    window.location.href = 'index.html';
                }
            }, 500);
        } else {
            console.warn('⚠️ User does not have admin privileges');
            const errorDiv = document.getElementById('errorMessage');
            if (errorDiv) {
                errorDiv.style.display = 'block';
                errorDiv.textContent = 'ليس لديك صلاحيات للوصول إلى لوحة التحكم';
            }
            // Sign out non-admin user only if not in login page mode
            if (window.firebaseAuth) {
                setTimeout(() => {
                    window.firebaseAuth.signOutUser();
                }, 1000);
            }
        }
    }
};

// Prevent multiple declarations and infinite redirections
if (!window.onFirebaseUserSignedOut) {
    window.onFirebaseUserSignedOut = function() {
        console.log('🔓 Firebase user signed out');

        // Clear any admin state
        document.body.classList.remove('admin-authenticated');

        // Only redirect if we're not already on login page and not in the middle of a redirect
        const currentPath = window.location.pathname;
        const isLoginPage = currentPath.includes('login.html') || currentPath.includes('admin-login.html');
        const isAdminArea = currentPath.includes('/admin/');

        // Prevent infinite redirections
        if (window.redirectInProgress) {
            console.log('⚠️ Redirect already in progress, skipping');
            return;
        }

        if (!isLoginPage && isAdminArea) {
            console.log('🔄 Redirecting to login after sign out');
            window.redirectInProgress = true;

            // Add delay to prevent race conditions
            setTimeout(() => {
                window.redirectInProgress = false;
                if (typeof window.safeRedirect === 'function') {
                    window.safeRedirect('login.html');
                } else {
                    console.warn('⚠️ safeRedirect not available, using fallback');
                    window.location.href = 'login.html';
                }
            }, 500);
        }
    };
}

// Error handler for JSON parse errors
window.addEventListener('error', function(event) {
    if (event.message && event.message.includes('JSON.parse')) {
        console.error('❌ JSON Parse Error detected:', event.message);
        console.log('🔧 This usually means Firebase is not properly initialized or server is returning HTML instead of JSON');

        // Show user-friendly error
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.style.display = 'block';
            errorDiv.textContent = 'خطأ في الاتصال بالخادم. يرجى إعادة تحميل الصفحة.';
        }
    }
});

// Fix Google Analytics cookie warnings
function fixGoogleAnalyticsCookies() {
    // Override document.cookie setter to prevent 'expires' attribute warnings
    const originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') ||
                                   Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');

    if (originalCookieDescriptor && originalCookieDescriptor.set) {
        const originalCookieSetter = originalCookieDescriptor.set;

        Object.defineProperty(document, 'cookie', {
            set: function(value) {
                // Suppress console warnings for Google Analytics cookies
                if (value.includes('_ga') && value.includes('expires')) {
                    // Silently set the cookie without logging warnings
                    try {
                        originalCookieSetter.call(this, value);
                    } catch (e) {
                        // Ignore cookie setting errors
                    }
                } else {
                    originalCookieSetter.call(this, value);
                }
            },
            get: originalCookieDescriptor.get,
            configurable: true
        });
    }
}

// Apply cookie fix on load
fixGoogleAnalyticsCookies();

// Debug info
console.log('🔧 Auth Fix Script initialized');
console.log('📊 Redirect count:', redirectCount);
console.log('🌐 Current URL:', window.location.href);
console.log('📄 Page type:', performance.navigation.type);

// Export for global use
window.authFix = {
    safeRedirect: window.safeRedirect,
    waitForFirebase: window.waitForFirebase,
    safeAuthCheck: window.safeAuthCheck,
    redirectCount: redirectCount,
    maxRedirects: maxRedirects
};
