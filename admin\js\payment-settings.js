/**
 * Payment Settings Management JavaScript
 * Enhanced payment method configuration with Algerian payment support
 */

// Constants for payment methods and validation patterns
const PAYMENT_METHODS = {
    COD: 'cod',
    BANK: 'bank',
    CCP: 'ccp',
    BARIDIMOB: 'baridimob'
};

const VALIDATION_PATTERNS = {
    CCP_NUMBER: /^\d{10}$/,
    PHONE_NUMBER: /^(\+213|0)(5|6|7)\d{8}$/,
    BANK_ACCOUNT: /^\d{10,20}$/
};

let paymentSettings = {
    cod: {
        enabled: true,
        fee: 0,
        minOrder: 1000,
        areas: 'جميع ولايات الجزائر',
        notes: 'يرجى تحضير المبلغ المطلوب عند الاستلام',
        maxOrder: 50000
    },
    bank: {
        enabled: false,
        name: '',
        account: '',
        holder: '',
        swift: '',
        instructions: '',
        rib: '',
        branch: ''
    },
    ccp: {
        enabled: false,
        number: '',
        key: '',
        holder: '',
        instructions: '',
        agency: '',
        wilaya: ''
    },
    baridimob: {
        enabled: false,
        number: '',
        holder: '',
        instructions: '',
        qrCode: ''
    },
    general: {
        currency: 'دج',
        currencyPosition: 'after',
        currencySpacing: true,
        timeout: 3,
        thankYouMessage: 'شكراً لك على طلبك! سنتواصل معك قريباً لتأكيد الطلب وترتيب التوصيل.',
        taxRate: 0,
        processingFee: 0,
        decimalPlaces: 2,
        thousandSeparator: ',',
        decimalSeparator: '.'
    }
};


let paymentSettingsLoaded = false;

/**
 * Initialize payment settings management
 */
async function initializePaymentSettings() {
    console.log('💳 Initializing Payment Settings...');

    if (paymentSettingsLoaded) {
        console.log('Payment settings already loaded');
        return;
    }

    try {
        showLoading('جاري تحميل إعدادات الدفع...');
        await loadPaymentSettings();
        updateStatistics();
        setupEventListeners();
        initializeFormValidation();
        initializeCurrencySettings();

        paymentSettingsLoaded = true;
        console.log('✅ Payment Settings initialized successfully');
        showSuccess('تم تحميل إعدادات الدفع بنجاح');
    } catch (error) {
        console.error('Error initializing payment settings:', error);
        showError('حدث خطأ أثناء تحميل إعدادات الدفع');
    } finally {
        hideLoading();
    }
}

/**
 * Initialize currency settings and formatting
 */
function initializeCurrencySettings() {
    const currencySelect = document.getElementById('currency-select');
    const currencyPositionSelect = document.getElementById('currency-position');
    const currencySpacingToggle = document.getElementById('currency-spacing');
    const decimalPlacesInput = document.getElementById('decimal-places');
    const thousandSeparatorInput = document.getElementById('thousand-separator');
    const decimalSeparatorInput = document.getElementById('decimal-separator');

    if (currencySelect) {
        currencySelect.value = paymentSettings.general.currency;
        currencySelect.addEventListener('change', updateCurrencyPreview);
    }

    if (currencyPositionSelect) {
        currencyPositionSelect.value = paymentSettings.general.currencyPosition;
        currencyPositionSelect.addEventListener('change', updateCurrencyPreview);
    }

    if (currencySpacingToggle) {
        currencySpacingToggle.checked = paymentSettings.general.currencySpacing;
        currencySpacingToggle.addEventListener('change', updateCurrencyPreview);
    }

    if (decimalPlacesInput) {
        decimalPlacesInput.value = paymentSettings.general.decimalPlaces;
        decimalPlacesInput.addEventListener('input', updateCurrencyPreview);
    }

    if (thousandSeparatorInput) {
        thousandSeparatorInput.value = paymentSettings.general.thousandSeparator;
        thousandSeparatorInput.addEventListener('input', updateCurrencyPreview);
    }

    if (decimalSeparatorInput) {
        decimalSeparatorInput.value = paymentSettings.general.decimalSeparator;
        decimalSeparatorInput.addEventListener('input', updateCurrencyPreview);
    }

    updateCurrencyPreview();
}

/**
 * Update currency preview based on current settings
 */
function updateCurrencyPreview() {
    const previewElement = document.getElementById('currency-preview');
    if (!previewElement) return;

    const amount = 1234.56;
    const formattedAmount = formatCurrency(amount);
    previewElement.textContent = `مثال: ${formattedAmount}`;
}

/**
 * Format currency amount according to settings
 */
function formatCurrency(amount) {
    const {
        currency,
        currencyPosition,
        currencySpacing,
        decimalPlaces,
        thousandSeparator,
        decimalSeparator
    } = paymentSettings.general;

    const space = currencySpacing ? ' ' : '';
    const formattedNumber = amount.toFixed(decimalPlaces)
        .replace('.', decimalSeparator)
        .replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);

    return currencyPosition === 'before'
        ? `${currency}${space}${formattedNumber}`
        : `${formattedNumber}${space}${currency}`;
}

// Initialize the page (fallback for direct access)
document.addEventListener('DOMContentLoaded', function() {
    console.log('💳 Payment settings DOM loaded');
    // Small delay to ensure all elements are ready
    setTimeout(initializePaymentSettings, 100);
});

/**
 * Setup event listeners for payment settings
 */
function setupEventListeners() {
    // Auto-save on input changes with debouncing
    let saveTimeout;
    document.addEventListener('input', function(event) {
        if (event.target.matches('input:not([type="checkbox"]), textarea, select')) {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                validateAndSave(event.target);
            }, 1000); // 1 second debounce
        }
    });

    // Reset settings button
    const resetBtn = document.getElementById('reset-settings-btn');
    if (resetBtn) {
        resetBtn.addEventListener('click', confirmResetSettings);
    }

    // Test all payment methods button
    const testAllBtn = document.getElementById('test-all-methods-btn');
    if (testAllBtn) {
        testAllBtn.addEventListener('click', testAllPaymentMethods);
    }

    // Save all settings button
    const saveAllBtn = document.getElementById('save-all-settings-btn');
    if (saveAllBtn) {
        saveAllBtn.addEventListener('click', saveAllSettings);
    }

    // Currency settings event listeners
    const currencyInputs = document.querySelectorAll('#currencySettings input, #currencySettings select');
    currencyInputs.forEach(input => {
        input.addEventListener('change', function() {
            validateAndSave(this);
            updateCurrencyPreview();
        });
    });

    // Toggle switches for payment methods
    document.addEventListener('change', function(event) {
        if (event.target.matches('input[type="checkbox"]')) {
            handleToggleChange(event.target);
        }
    });

    // Prevent form submission
    document.addEventListener('submit', function(event) {
        event.preventDefault();
    });

    // Test payment method buttons
    document.addEventListener('click', function(event) {
        const testButton = event.target.closest('.test-payment-btn');
        if (testButton) {
            const method = testButton.dataset.method;
            testPaymentMethod(method);
        }
    });

    // Test all payment methods button
    const testAllButton = document.getElementById('test-all-methods');
    if (testAllButton) {
        testAllButton.addEventListener('click', testAllPaymentMethods);
    }

    // Save all settings button
    const saveButton = document.getElementById('save-settings');
    if (saveButton) {
        saveButton.addEventListener('click', saveAllSettings);
    }

    // Reset settings button
    const resetButton = document.getElementById('reset-settings');
    if (resetButton) {
        resetButton.addEventListener('click', confirmResetSettings);
    }
}

/**
 * Validate and save input changes
 */
async function validateAndSave(inputElement) {
    const method = inputElement.closest('[data-method]')?.dataset.method;
    const isValid = await validateInput(inputElement, method);

    if (isValid) {
        autoSaveSettings();
        updateMethodStatus(method);
    }
}

/**
 * Validate input based on type and method
 */
async function validateInput(input, method) {
    const value = input.value.trim();
    const id = input.id;

    // Skip validation for optional empty fields
    if (!value && !input.required) {
        clearFieldError(input);
        return true;
    }

    switch (method) {
        case PAYMENT_METHODS.CCP:
            if (id === 'ccp-number') {
                return validateCCPNumber(value);
            }
            break;

        case PAYMENT_METHODS.BARIDIMOB:
            if (id === 'mobile-number') {
                return validatePhoneNumber(value, input);
            }
            break;

        case PAYMENT_METHODS.BANK:
            if (id === 'bank-account') {
                return validateBankAccount(value);
            }
            break;
    }

    // General numeric validation
    if (input.type === 'number') {
        const min = parseFloat(input.min);
        const max = parseFloat(input.max);
        const numValue = parseFloat(value);

        if (isNaN(numValue)) {
            showFieldError(input, 'يرجى إدخال رقم صحيح');
            return false;
        }

        if (!isNaN(min) && numValue < min) {
            showFieldError(input, `الحد الأدنى هو ${min}`);
            return false;
        }

        if (!isNaN(max) && numValue > max) {
            showFieldError(input, `الحد الأقصى هو ${max}`);
            return false;
        }
    }

    clearFieldError(input);
    return true;
}

/**
 * Update method status and UI
 */
function updateMethodStatus(method) {
    if (!method) return;

    const methodSection = document.querySelector(`[data-method="${method}"]`);
    if (!methodSection) return;

    const isValid = validateMethodSettings(method);
    const statusIndicator = methodSection.querySelector('.method-status');

    if (statusIndicator) {
        statusIndicator.className = `method-status ${isValid ? 'valid' : 'invalid'}`;
        statusIndicator.title = isValid ? 'إعدادات صحيحة' : 'إعدادات غير مكتملة';
    }

    updateActiveMethodsCount();
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    // Add validation for CCP number format
    const ccpNumberInput = document.getElementById('ccp-number');
    if (ccpNumberInput) {
        ccpNumberInput.addEventListener('input', function() {
            validateCCPNumber(this.value);
        });
    }

    // Add validation for phone numbers
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function() {
            validatePhoneNumber(this.value, this);
        });
    });

    // Add validation for bank account numbers
    const bankAccountInput = document.getElementById('bank-account');
    if (bankAccountInput) {
        bankAccountInput.addEventListener('input', function() {
            validateBankAccount(this.value);
        });
    }
}

/**
 * Handle toggle switch changes
 */
function handleToggleChange(toggleElement) {
    const method = toggleElement.id.replace('Enabled', '').replace('-enabled', '');
    const isEnabled = toggleElement.checked;

    // Update settings object
    if (paymentSettings[method]) {
        paymentSettings[method].enabled = isEnabled;
    }

    // Show/hide method configuration
    const configSection = document.querySelector(`[data-method="${method}"] .setting-content`);
    if (configSection) {
        configSection.style.display = isEnabled ? 'block' : 'none';
    }

    // Update statistics
    updateStatistics();

    // Auto-save
    autoSaveSettings();

    console.log(`Payment method ${method} ${isEnabled ? 'enabled' : 'disabled'}`);
}

/**
 * Load payment settings from API with localStorage fallback
 */
async function loadPaymentSettings() {
    try {
        showLoading('جاري تحميل إعدادات الدفع...');

        // Try to load from API first
        const response = await fetch('../php/api/payment-settings.php');

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.settings) {
                paymentSettings = mergeSettings(paymentSettings, data.settings);
                localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
            }
        } else {
            // Try to load from localStorage as fallback
            const savedSettings = localStorage.getItem('paymentSettings');
            if (savedSettings) {
                paymentSettings = mergeSettings(paymentSettings, JSON.parse(savedSettings));
            }
        }

        // Apply settings to UI
        applySettingsToUI();
        updateAllMethodStatuses();

    } catch (error) {
        console.error('Error loading payment settings:', error);
        showError('خطأ في تحميل إعدادات الدفع');

        // Try localStorage as last resort
        try {
            const savedSettings = localStorage.getItem('paymentSettings');
            if (savedSettings) {
                paymentSettings = mergeSettings(paymentSettings, JSON.parse(savedSettings));
                applySettingsToUI();
                updateAllMethodStatuses();
            }
        } catch (localError) {
            console.error('Error loading from localStorage:', localError);
        }
    } finally {
        hideLoading();
    }
}

/**
 * Merge settings while preserving defaults
 */
function mergeSettings(defaults, newSettings) {
    const merged = { ...defaults };

    // Merge each payment method's settings
    Object.keys(merged).forEach(method => {
        if (newSettings[method]) {
            merged[method] = {
                ...merged[method],
                ...newSettings[method]
            };
        }
    });

    return merged;
}

/**
 * Update status indicators for all payment methods
 */
function updateAllMethodStatuses() {
    Object.values(PAYMENT_METHODS).forEach(method => {
        updateMethodStatus(method);
    });
}

// Apply settings to UI elements
function applySettingsToUI() {
    // COD settings
    const codFee = document.getElementById('cod-fee');
    if (codFee) codFee.value = paymentSettings.cod.fee || 0;

    const codMinOrder = document.getElementById('cod-min-order');
    if (codMinOrder) codMinOrder.value = paymentSettings.cod.minOrder || 1000;

    const codAreas = document.getElementById('cod-areas');
    if (codAreas) codAreas.value = paymentSettings.cod.areas || '';

    const codNotes = document.getElementById('cod-notes');
    if (codNotes) codNotes.value = paymentSettings.cod.notes || '';

    // Bank settings
    const bankName = document.getElementById('bank-name');
    if (bankName) bankName.value = paymentSettings.bank.name || '';

    const bankAccount = document.getElementById('bank-account');
    if (bankAccount) bankAccount.value = paymentSettings.bank.account || '';

    const bankHolder = document.getElementById('bank-holder');
    if (bankHolder) bankHolder.value = paymentSettings.bank.holder || '';

    const bankSwift = document.getElementById('bank-swift');
    if (bankSwift) bankSwift.value = paymentSettings.bank.swift || '';

    const bankInstructions = document.getElementById('bank-instructions');
    if (bankInstructions) bankInstructions.value = paymentSettings.bank.instructions || '';

    // CCP settings
    const ccpNumber = document.getElementById('ccp-number');
    if (ccpNumber) ccpNumber.value = paymentSettings.ccp.number || '';

    const ccpKey = document.getElementById('ccp-key');
    if (ccpKey) ccpKey.value = paymentSettings.ccp.key || '';

    const ccpHolder = document.getElementById('ccp-holder');
    if (ccpHolder) ccpHolder.value = paymentSettings.ccp.holder || '';

    const ccpInstructions = document.getElementById('ccp-instructions');
    if (ccpInstructions) ccpInstructions.value = paymentSettings.ccp.instructions || '';

    // Mobile settings
    const mobileNumber = document.getElementById('mobile-number');
    if (mobileNumber) mobileNumber.value = paymentSettings.mobile.number || '';

    const mobileApp = document.getElementById('mobile-app');
    if (mobileApp) mobileApp.value = paymentSettings.mobile.app || 'baridimob';

    const mobileHolder = document.getElementById('mobile-holder');
    if (mobileHolder) mobileHolder.value = paymentSettings.mobile.holder || '';

    const mobileInstructions = document.getElementById('mobile-instructions');
    if (mobileInstructions) mobileInstructions.value = paymentSettings.mobile.instructions || '';

    // General settings
    const defaultCurrency = document.getElementById('default-currency');
    if (defaultCurrency) defaultCurrency.value = paymentSettings.general.currency || 'DZD';

    const paymentTimeout = document.getElementById('payment-timeout');
    if (paymentTimeout) paymentTimeout.value = paymentSettings.general.timeout || 3;

    const thankYouMessage = document.getElementById('thank-you-message');
    if (thankYouMessage) thankYouMessage.value = paymentSettings.general.thankYouMessage || '';

    // Update toggle switches
    updateToggleSwitch('cod', paymentSettings.cod.enabled);
    updateToggleSwitch('bank', paymentSettings.bank.enabled);
    updateToggleSwitch('ccp', paymentSettings.ccp.enabled);
    updateToggleSwitch('baridimob', paymentSettings.baridimob.enabled);
}

/**
 * Validation functions for Algerian payment methods
 */
function validateCCPNumber(ccpNumber) {
    const ccpPattern = /^\d{10}$/; // CCP numbers are typically 10 digits
    const input = document.getElementById('ccp-number');

    if (ccpNumber && !ccpPattern.test(ccpNumber)) {
        showFieldError(input, 'رقم الحساب الجاري البريدي يجب أن يكون 10 أرقام');
        return false;
    } else {
        clearFieldError(input);
        return true;
    }
}

function validatePhoneNumber(phoneNumber, inputElement) {
    const algerianPhonePattern = /^(\+213|0)(5|6|7)\d{8}$/;

    if (phoneNumber && !algerianPhonePattern.test(phoneNumber)) {
        showFieldError(inputElement, 'رقم الهاتف يجب أن يبدأ بـ 05، 06، أو 07');
        return false;
    } else {
        clearFieldError(inputElement);
        return true;
    }
}

function validateBankAccount(accountNumber) {
    const input = document.getElementById('bank-account');

    if (accountNumber && accountNumber.length < 10) {
        showFieldError(input, 'رقم الحساب المصرفي قصير جداً');
        return false;
    } else {
        clearFieldError(input);
        return true;
    }
}

function showFieldError(inputElement, message) {
    // Remove existing error
    clearFieldError(inputElement);

    // Add error class
    inputElement.classList.add('error');

    // Create error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;

    // Insert after input
    inputElement.parentNode.insertBefore(errorDiv, inputElement.nextSibling);
}

function clearFieldError(inputElement) {
    inputElement.classList.remove('error');
    const errorDiv = inputElement.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Test payment method functionality
 */
async function testPaymentMethod(method) {
    if (!validateMethodSettings(method)) {
        showError(`يرجى إكمال جميع إعدادات ${getPaymentMethodName(method)} قبل الاختبار`);
        return;
    }

    const testBtn = document.querySelector(`[data-method="${method}"] .test-payment-btn`);
    const originalText = testBtn.innerHTML;

    try {
        // Show loading state
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';
        testBtn.disabled = true;

        // Get method-specific settings
        const settings = getMethodSettings(method);

        // Make API test call
        const response = await fetch('../php/api/test-payment.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                method: method,
                settings: settings
            })
        });

        const result = await response.json();

        if (result.success) {
            testBtn.innerHTML = '<i class="fas fa-check"></i> تم الاختبار بنجاح';
            testBtn.classList.add('success');
            showSuccess(`تم اختبار طريقة الدفع ${getPaymentMethodName(method)} بنجاح`);
        } else {
            throw new Error(result.message || 'فشل الاختبار');
        }

    } catch (error) {
        console.error('Payment test error:', error);
        testBtn.innerHTML = '<i class="fas fa-times"></i> فشل الاختبار';
        testBtn.classList.add('error');
        showError(`فشل في اختبار طريقة الدفع ${getPaymentMethodName(method)}: ${error.message}`);
    } finally {
        // Reset button after 3 seconds
        setTimeout(() => {
            testBtn.innerHTML = originalText;
            testBtn.classList.remove('success', 'error');
            testBtn.disabled = false;
        }, 3000);
    }
}

/**
 * Test all enabled payment methods
 */
async function testAllPaymentMethods() {
    const enabledMethods = Object.values(PAYMENT_METHODS)
        .filter(method => paymentSettings[method].enabled);

    if (enabledMethods.length === 0) {
        showWarning('لا توجد طرق دفع مفعلة للاختبار');
        return;
    }

    showLoading('جاري اختبار جميع طرق الدفع المفعلة...');

    try {
        const results = await Promise.allSettled(
            enabledMethods.map(method => testPaymentMethod(method))
        );

        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;

        if (failed === 0) {
            showSuccess(`تم اختبار جميع طرق الدفع (${successful}) بنجاح`);
        } else {
            showWarning(`تم اختبار ${successful} طرق دفع بنجاح، وفشل ${failed} طرق`);
        }
    } catch (error) {
        console.error('Test all payment methods error:', error);
        showError('حدث خطأ أثناء اختبار طرق الدفع');
    } finally {
        hideLoading();
    }
}

/**
 * Get method-specific settings for testing
 */
function getMethodSettings(method) {
    const settings = { ...paymentSettings[method] };

    // Add method-specific test data
    switch (method) {
        case PAYMENT_METHODS.COD:
            settings.testOrder = {
                amount: settings.minOrder + 100,
                address: 'عنوان اختباري'
            };
            break;

        case PAYMENT_METHODS.BANK:
            settings.testTransaction = {
                amount: 1000,
                reference: 'TEST-' + Date.now()
            };
            break;

        case PAYMENT_METHODS.CCP:
            settings.testPayment = {
                amount: 1000,
                reference: 'CCP-TEST-' + Date.now()
            };
            break;

        case PAYMENT_METHODS.BARIDIMOB:
            settings.testTransfer = {
                amount: 1000,
                reference: 'BARIDIMOB-' + Date.now()
            };
            break;
    }

    return settings;
}

/**
 * Validate method-specific settings
 */
function validateMethodSettings(method) {
    if (!method || !paymentSettings[method]) return false;

    const settings = paymentSettings[method];
    if (!settings.enabled) return true; // Disabled methods are considered valid

    switch (method) {
        case PAYMENT_METHODS.COD:
            return settings.fee >= 0 &&
                   settings.minOrder > 0 &&
                   settings.maxOrder > settings.minOrder &&
                   settings.areas.trim() !== '';

        case PAYMENT_METHODS.BANK:
            return settings.name.trim() !== '' &&
                   settings.account.trim() !== '' &&
                   settings.holder.trim() !== '' &&
                   VALIDATION_PATTERNS.BANK_ACCOUNT.test(settings.account);

        case PAYMENT_METHODS.CCP:
            return settings.number.trim() !== '' &&
                   settings.key.trim() !== '' &&
                   settings.holder.trim() !== '' &&
                   VALIDATION_PATTERNS.CCP_NUMBER.test(settings.number);

        case PAYMENT_METHODS.BARIDIMOB:
            return settings.number.trim() !== '' &&
                   settings.holder.trim() !== '' &&
                   VALIDATION_PATTERNS.PHONE_NUMBER.test(settings.number);

        default:
            return false;
    }
}

function getPaymentMethodName(method) {
    const names = {
        'cod': 'الدفع عند الاستلام',
        'bank': 'التحويل المصرفي',
        'ccp': 'الحساب الجاري البريدي',
        'baridimob': 'بريدي موب'
    };
    return names[method] || method;
}

// Load settings from API
async function loadFromAPI() {
    try {
        console.log('Loading payment settings from API...');
        const response = await fetch('../php/api/payment-settings.php');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('API Response:', data);

        if (data.success && data.settings) {
            paymentSettings = { ...paymentSettings, ...data.settings };
            applySettingsToUI();

            // Update statistics from API
            if (data.statistics) {
                document.getElementById('activePaymentMethods').textContent = data.statistics.active_methods || 0;
                document.getElementById('totalTransactions').textContent = data.statistics.total_transactions || 0;
                document.getElementById('successRate').textContent = (data.statistics.success_rate || 0) + '%';
            }

            console.log('Payment settings loaded from API successfully');
        }
    } catch (error) {
        console.error('Error loading from API:', error);
        console.log('Falling back to localStorage settings');
    }
}

// Toggle payment method
function togglePaymentMethod(method) {
    const toggleSwitch = document.querySelector(`#${method}-method .toggle-switch`);
    const config = document.getElementById(`${method}-config`);
    const paymentMethod = document.getElementById(`${method}-method`);

    const isCurrentlyActive = toggleSwitch.classList.contains('active');
    const newState = !isCurrentlyActive;

    // Update toggle switch
    updateToggleSwitch(method, newState);

    // Update settings
    paymentSettings[method].enabled = newState;

    // Save settings
    saveSettings();

    // Update statistics
    updateStatistics();

    showSuccess(`تم ${newState ? 'تفعيل' : 'إلغاء'} طريقة الدفع بنجاح`);
}

// Update toggle switch appearance
function updateToggleSwitch(method, enabled) {
    const toggleSwitch = document.querySelector(`#${method}-method .toggle-switch`);
    const config = document.getElementById(`${method}-config`);
    const paymentMethod = document.getElementById(`${method}-method`);

    if (enabled) {
        toggleSwitch.classList.add('active');
        config.classList.add('show');
        paymentMethod.classList.add('active');
    } else {
        toggleSwitch.classList.remove('active');
        config.classList.remove('show');
        paymentMethod.classList.remove('active');
    }
}

// Auto-save settings on input change
function autoSaveSettings() {
    // Collect all form data
    collectFormData();

    // Save to localStorage
    localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));

    console.log('Settings auto-saved');
}

// Collect form data into settings object
function collectFormData() {
    // COD settings
    const codFee = document.getElementById('cod-fee');
    if (codFee) paymentSettings.cod.fee = parseFloat(codFee.value) || 0;

    const codMinOrder = document.getElementById('cod-min-order');
    if (codMinOrder) paymentSettings.cod.minOrder = parseFloat(codMinOrder.value) || 0;

    const codAreas = document.getElementById('cod-areas');
    if (codAreas) paymentSettings.cod.areas = codAreas.value;

    const codNotes = document.getElementById('cod-notes');
    if (codNotes) paymentSettings.cod.notes = codNotes.value;

    // Bank settings
    const bankName = document.getElementById('bank-name');
    if (bankName) paymentSettings.bank.name = bankName.value;

    const bankAccount = document.getElementById('bank-account');
    if (bankAccount) paymentSettings.bank.account = bankAccount.value;

    const bankHolder = document.getElementById('bank-holder');
    if (bankHolder) paymentSettings.bank.holder = bankHolder.value;

    const bankSwift = document.getElementById('bank-swift');
    if (bankSwift) paymentSettings.bank.swift = bankSwift.value;

    const bankInstructions = document.getElementById('bank-instructions');
    if (bankInstructions) paymentSettings.bank.instructions = bankInstructions.value;

    // CCP settings
    const ccpNumber = document.getElementById('ccp-number');
    if (ccpNumber) paymentSettings.ccp.number = ccpNumber.value;

    const ccpKey = document.getElementById('ccp-key');
    if (ccpKey) paymentSettings.ccp.key = ccpKey.value;

    const ccpHolder = document.getElementById('ccp-holder');
    if (ccpHolder) paymentSettings.ccp.holder = ccpHolder.value;

    const ccpInstructions = document.getElementById('ccp-instructions');
    if (ccpInstructions) paymentSettings.ccp.instructions = ccpInstructions.value;

    // Mobile settings
    const mobileNumber = document.getElementById('mobile-number');
    if (mobileNumber) paymentSettings.mobile.number = mobileNumber.value;

    const mobileApp = document.getElementById('mobile-app');
    if (mobileApp) paymentSettings.mobile.app = mobileApp.value;

    const mobileHolder = document.getElementById('mobile-holder');
    if (mobileHolder) paymentSettings.mobile.holder = mobileHolder.value;

    const mobileInstructions = document.getElementById('mobile-instructions');
    if (mobileInstructions) paymentSettings.mobile.instructions = mobileInstructions.value;

    // General settings
    const defaultCurrency = document.getElementById('default-currency');
    if (defaultCurrency) paymentSettings.general.currency = defaultCurrency.value;

    const paymentTimeout = document.getElementById('payment-timeout');
    if (paymentTimeout) paymentSettings.general.timeout = parseInt(paymentTimeout.value) || 3;

    const thankYouMessage = document.getElementById('thank-you-message');
    if (thankYouMessage) paymentSettings.general.thankYouMessage = thankYouMessage.value;
}

/**
 * Save all payment settings
 */
async function saveAllSettings() {
    try {
        showLoading('جاري حفظ الإعدادات...');

        // Validate all settings before saving
        const validationErrors = validateAllSettings();
        if (validationErrors.length > 0) {
            showError('يرجى تصحيح الأخطاء التالية قبل الحفظ:\n' + validationErrors.join('\n'));
            return;
        }

        // Collect form data
        collectFormData();

        // Save to API
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                settings: paymentSettings,
                timestamp: Date.now()
            })
        });

        const result = await response.json();

        if (result.success) {
            // Save to localStorage as backup
            localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
            showSuccess('تم حفظ جميع إعدادات الدفع بنجاح');
            updateAllMethodStatuses();
        } else {
            throw new Error(result.message || 'فشل في حفظ الإعدادات');
        }

    } catch (error) {
        console.error('Error saving settings:', error);

        // Attempt to save to localStorage as fallback
        try {
            localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
            showWarning('تم حفظ الإعدادات محلياً فقط. سيتم مزامنتها مع الخادم عند عودة الاتصال.');
        } catch (localError) {
            showError('فشل في حفظ الإعدادات: ' + error.message);
        }
    } finally {
        hideLoading();
    }
}

/**
 * Validate all payment settings
 */
function validateAllSettings() {
    const errors = [];

    // Validate enabled payment methods
    Object.values(PAYMENT_METHODS).forEach(method => {
        if (paymentSettings[method].enabled && !validateMethodSettings(method)) {
            errors.push(`- ${getPaymentMethodName(method)}: إعدادات غير مكتملة أو غير صحيحة`);
        }
    });

    // Validate general settings
    const general = paymentSettings.general;
    if (!general.currency) {
        errors.push('- يجب تحديد العملة الافتراضية');
    }
    if (general.timeout < 1) {
        errors.push('- مهلة الدفع يجب أن تكون دقيقة واحدة على الأقل');
    }
    if (general.taxRate < 0 || general.taxRate > 100) {
        errors.push('- نسبة الضريبة يجب أن تكون بين 0 و 100');
    }
    if (general.processingFee < 0) {
        errors.push('- رسوم المعالجة لا يمكن أن تكون سالبة');
    }

    return errors;
}

/**
 * Reset settings to defaults
 */
async function confirmResetSettings() {
    if (!confirm('هل أنت متأكد من إعادة ضبط جميع إعدادات الدفع؟ سيتم فقدان جميع التغييرات.')) {
        return;
    }

    try {
        showLoading('جاري إعادة ضبط الإعدادات...');

        // Reset to default settings
        const response = await fetch('../php/api/payment-settings.php?action=reset', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            // Clear localStorage
            localStorage.removeItem('paymentSettings');

            // Reload settings from API
            await loadPaymentSettings();

            showSuccess('تم إعادة ضبط إعدادات الدفع بنجاح');
        } else {
            throw new Error(result.message || 'فشل في إعادة ضبط الإعدادات');
        }

    } catch (error) {
        console.error('Error resetting settings:', error);
        showError('فشل في إعادة ضبط الإعدادات: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Save settings (internal function)
function saveSettings() {
    localStorage.setItem('paymentSettings', JSON.stringify(paymentSettings));
}

// Update statistics
function updateStatistics() {
    const activeMethods = Object.values(paymentSettings).filter(method => method.enabled).length - 1; // -1 for general settings
    const totalTransactions = Math.floor(Math.random() * 1000) + 100; // Mock data
    const successRate = Math.floor(Math.random() * 20) + 80; // Mock data

    const activePaymentMethodsEl = document.getElementById('activePaymentMethods');
    if (activePaymentMethodsEl) activePaymentMethodsEl.textContent = activeMethods;

    const totalTransactionsEl = document.getElementById('totalTransactions');
    if (totalTransactionsEl) totalTransactionsEl.textContent = totalTransactions;

    const successRateEl = document.getElementById('successRate');
    if (successRateEl) successRateEl.textContent = successRate + '%';
}

// Test payment settings
function testPaymentSettings() {
    showLoading('جاري اختبار الإعدادات...');

    setTimeout(() => {
        const activeMethods = Object.keys(paymentSettings).filter(key =>
            key !== 'general' && paymentSettings[key].enabled
        );

        if (activeMethods.length === 0) {
            showError('لا توجد طرق دفع مفعلة للاختبار');
            return;
        }

        let testResults = '🧪 نتائج الاختبار:\n\n';

        activeMethods.forEach(method => {
            const methodName = getMethodName(method);
            const isValid = validateMethodSettings(method);
            testResults += `${isValid ? '✅' : '❌'} ${methodName}: ${isValid ? 'جاهز' : 'يحتاج إعداد'}\n`;
        });

        alert(testResults);
        hideLoading();
    }, 2000);
}

// Get method display name
function getMethodName(method) {
    const names = {
        cod: 'الدفع عند الاستلام',
        bank: 'التحويل البنكي',
        ccp: 'الحساب الجاري البريدي',
        mobile: 'الدفع عبر الهاتف'
    };
    return names[method] || method;
}

// Validate method settings
function validateMethodSettings(method) {
    switch (method) {
        case 'cod':
            return paymentSettings.cod.areas && paymentSettings.cod.notes;
        case 'bank':
            return paymentSettings.bank.name && paymentSettings.bank.account && paymentSettings.bank.holder;
        case 'ccp':
            return paymentSettings.ccp.number && paymentSettings.ccp.key && paymentSettings.ccp.holder;
        case 'mobile':
            return paymentSettings.mobile.number && paymentSettings.mobile.holder;
        default:
            return false;
    }
}

// Export settings
function exportSettings() {
    const dataStr = JSON.stringify(paymentSettings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'payment-settings-' + new Date().toISOString().split('T')[0] + '.json';
    link.click();

    URL.revokeObjectURL(url);
    showSuccess('تم تصدير الإعدادات بنجاح');
}

// Import settings
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedSettings = JSON.parse(e.target.result);
                paymentSettings = { ...paymentSettings, ...importedSettings };
                applySettingsToUI();
                saveSettings();
                updateStatistics();
                showSuccess('تم استيراد الإعدادات بنجاح');
            } catch (error) {
                showError('خطأ في قراءة ملف الإعدادات');
            }
        };
        reader.readAsText(file);
    };

    input.click();
}

// Utility functions
function showLoading(message) {
    console.log('Loading:', message);
    // You can implement a loading spinner here
}

function hideLoading() {
    console.log('Loading finished');
}

function showSuccess(message) {
    // Create a temporary success alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-success';
    alert.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';

    document.body.appendChild(alert);

    setTimeout(() => {
        alert.remove();
    }, 3000);
}

function showError(message) {
    // Create a temporary error alert
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger';
    alert.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';

    document.body.appendChild(alert);

    setTimeout(() => {
        alert.remove();
    }, 5000);
}

// Shipping functionality
async function loadWilayas() {
    try {
        console.log('Loading wilayas from API...');
        const response = await fetch('../php/api/payment-settings.php?module=shipping&action=wilayas');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Wilayas API Response:', data);

        if (data.success && data.wilayas) {
            const wilayaSelect = document.getElementById('wilayaSelect');
            if (wilayaSelect) {
                wilayaSelect.innerHTML = '<option value="">-- اختر الولاية --</option>';

                data.wilayas.forEach(wilaya => {
                    const option = document.createElement('option');
                    option.value = wilaya.wilaya_code;
                    option.textContent = `${wilaya.wilaya_name} (${wilaya.zone_name} - ${wilaya.shipping_cost} دج)`;
                    wilayaSelect.appendChild(option);
                });

                console.log(`Loaded ${data.wilayas.length} wilayas`);
            }
        }
    } catch (error) {
        console.error('Error loading wilayas:', error);
        showError('فشل في تحميل قائمة الولايات');
    }
}

async function loadShippingZones() {
    try {
        console.log('Loading shipping zones...');
        const response = await fetch('../php/api/payment-settings.php?module=shipping&action=zones');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Zones API Response:', data);

        if (data.success && data.zones) {
            const zonesContainer = document.getElementById('shippingZones');
            if (zonesContainer) {
                zonesContainer.innerHTML = '';

                data.zones.forEach(zone => {
                    const zoneCard = document.createElement('div');
                    zoneCard.className = 'zone-card';
                    zoneCard.innerHTML = `
                        <div class="zone-title">${zone.zone_name}</div>
                        <div class="zone-details">${zone.wilaya_count} ولاية</div>
                        <div class="zone-cost">${zone.min_cost} - ${zone.max_cost} دج</div>
                        <div class="zone-details">حسب المسافة والوزن</div>
                    `;
                    zonesContainer.appendChild(zoneCard);
                });

                console.log(`Loaded ${data.zones.length} shipping zones`);
            }
        }
    } catch (error) {
        console.error('Error loading shipping zones:', error);
        showError('فشل في تحميل مناطق الشحن');
    }
}

async function calculateShippingCost() {
    const wilayaCode = document.getElementById('wilayaSelect').value;
    const weight = parseFloat(document.getElementById('packageWeight').value) || 1.0;

    if (!wilayaCode) {
        showError('يرجى اختيار الولاية أولاً');
        return;
    }

    if (weight < 0.1 || weight > 30) {
        showError('الوزن يجب أن يكون بين 0.1 و 30 كيلوغرام');
        return;
    }

    try {
        console.log(`Calculating shipping for wilaya: ${wilayaCode}, weight: ${weight}kg`);

        const response = await fetch(`../php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=${weight}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Shipping calculation result:', data);

        if (data.success) {
            displayShippingResult(data);
        } else {
            showError(data.message || 'فشل في حساب تكلفة الشحن');
        }
    } catch (error) {
        console.error('Error calculating shipping:', error);
        showError('حدث خطأ أثناء حساب تكلفة الشحن');
    }
}

function displayShippingResult(data) {
    const resultDiv = document.getElementById('shippingResult');
    const zoneInfo = data.zone_info;
    const costBreakdown = data.cost_breakdown;

    // Update zone information
    document.getElementById('zoneName').textContent = zoneInfo.zone_name;
    document.getElementById('wilayaName').textContent = zoneInfo.wilaya_name;
    document.getElementById('deliveryTime').textContent = zoneInfo.delivery_time;

    // Update cost breakdown
    document.getElementById('baseCost').textContent = costBreakdown.base_cost.toFixed(2);
    document.getElementById('weightDisplay').textContent = costBreakdown.weight.toFixed(1);
    document.getElementById('totalCost').textContent = costBreakdown.total_cost.toFixed(2);

    // Show/hide surcharge information
    const surchargeItem = document.getElementById('surchargeItem');
    if (costBreakdown.extra_weight > 0) {
        document.getElementById('surchargeAmount').textContent = costBreakdown.surcharge_total.toFixed(2);
        surchargeItem.style.display = 'block';
    } else {
        surchargeItem.style.display = 'none';
    }

    // Show the result
    resultDiv.style.display = 'block';
    resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Initialize shipping functionality
function initializeShipping() {
    // Load shipping data
    loadWilayas();
    loadShippingZones();

    // Load shipping management data
    loadShippingManagement();

    // Add shipping calculator event listener
    const calculateBtn = document.getElementById('calculateShipping');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateShippingCost);
    }

    // Add real-time calculation on weight change
    const weightInput = document.getElementById('packageWeight');
    if (weightInput) {
        weightInput.addEventListener('input', function() {
            const wilayaCode = document.getElementById('wilayaSelect').value;
            if (wilayaCode && this.value) {
                // Debounce the calculation
                clearTimeout(this.calcTimeout);
                this.calcTimeout = setTimeout(calculateShippingCost, 500);
            }
        });
    }

    // Add real-time calculation on wilaya change
    const wilayaSelect = document.getElementById('wilayaSelect');
    if (wilayaSelect) {
        wilayaSelect.addEventListener('change', function() {
            if (this.value) {
                calculateShippingCost();
            } else {
                document.getElementById('shippingResult').style.display = 'none';
            }
        });
    }

    // Initialize shipping management tabs
    initializeShippingManagement();
}

// Shipping Management Functions
async function loadShippingManagement() {
    try {
        // Load wilayas for custom rates dropdown
        const response = await fetch('../php/api/geographic-data.php?action=wilayas');
        const data = await response.json();

        if (data.success && data.wilayas) {
            const customWilayaSelect = document.getElementById('customWilayaSelect');
            if (customWilayaSelect) {
                customWilayaSelect.innerHTML = '<option value="">-- اختر الولاية --</option>';

                data.wilayas.forEach(wilaya => {
                    const option = document.createElement('option');
                    option.value = wilaya.wilaya_code;
                    option.textContent = `${wilaya.wilaya_name_ar} (المنطقة ${wilaya.zone_number})`;
                    customWilayaSelect.appendChild(option);
                });
            }

            // Load free shipping wilayas checkboxes
            loadFreeShippingWilayas(data.wilayas);
        }

        // Load existing custom rates
        loadCustomRates();

    } catch (error) {
        console.error('Error loading shipping management data:', error);
    }
}

function loadFreeShippingWilayas(wilayas) {
    const container = document.getElementById('freeShippingWilayas');
    if (!container) return;

    container.innerHTML = '';

    wilayas.forEach(wilaya => {
        const checkboxItem = document.createElement('div');
        checkboxItem.className = 'checkbox-item';

        checkboxItem.innerHTML = `
            <input type="checkbox" id="free_${wilaya.wilaya_code}" value="${wilaya.wilaya_code}">
            <label for="free_${wilaya.wilaya_code}">${wilaya.wilaya_name_ar}</label>
        `;

        container.appendChild(checkboxItem);
    });
}

async function loadCustomRates() {
    try {
        const response = await fetch('../php/api/payment-settings.php?module=shipping&action=custom_rates');
        const data = await response.json();

        if (data.success && data.custom_rates) {
            displayCustomRates(data.custom_rates);
        }
    } catch (error) {
        console.error('Error loading custom rates:', error);
    }
}

function displayCustomRates(customRates) {
    const container = document.getElementById('customRatesList');
    if (!container) return;

    if (customRates.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">لا توجد رسوم مخصصة</p>';
        return;
    }

    container.innerHTML = '';

    customRates.forEach(rate => {
        const rateItem = document.createElement('div');
        rateItem.className = 'custom-rate-item';

        rateItem.innerHTML = `
            <div class="custom-rate-info">
                <div class="custom-rate-wilaya">${rate.wilaya_name}</div>
                <div class="custom-rate-details">مدة التوصيل: ${rate.delivery_days}</div>
            </div>
            <div class="custom-rate-cost">${rate.shipping_cost} دج</div>
            <div class="custom-rate-actions">
                <button class="btn btn-sm btn-primary" onclick="editCustomRate('${rate.location_code}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteCustomRate('${rate.location_code}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        container.appendChild(rateItem);
    });
}

function initializeShippingManagement() {
    // Free shipping toggle
    const enableFreeShipping = document.getElementById('enableFreeShipping');
    if (enableFreeShipping) {
        enableFreeShipping.addEventListener('change', function() {
            const options = document.getElementById('freeShippingOptions');
            if (options) {
                options.style.display = this.checked ? 'block' : 'none';
            }
        });
    }

    // Zone selection for zone rates
    const zoneSelect = document.getElementById('zoneSelect');
    if (zoneSelect) {
        zoneSelect.addEventListener('change', function() {
            if (this.value) {
                loadZoneWilayas(this.value);
            } else {
                document.getElementById('zoneWilayas').innerHTML = '';
            }
        });
    }
}

async function loadZoneWilayas(zoneNumber) {
    try {
        const response = await fetch(`../php/api/geographic-data.php?action=wilayas`);
        const data = await response.json();

        if (data.success && data.wilayas) {
            const zoneWilayas = data.wilayas.filter(w => w.zone_number == zoneNumber);
            displayZoneWilayas(zoneWilayas, zoneNumber);
        }
    } catch (error) {
        console.error('Error loading zone wilayas:', error);
    }
}

function displayZoneWilayas(wilayas, zoneNumber) {
    const container = document.getElementById('zoneWilayas');
    if (!container) return;

    container.innerHTML = `
        <h5>ولايات المنطقة ${zoneNumber} (${wilayas.length} ولاية)</h5>
    `;

    wilayas.forEach(wilaya => {
        const wilayaItem = document.createElement('div');
        wilayaItem.className = 'wilaya-item';

        wilayaItem.innerHTML = `
            <span class="wilaya-name">${wilaya.wilaya_name_ar}</span>
            <span class="wilaya-rate" id="rate_${wilaya.wilaya_code}">-- دج</span>
        `;

        container.appendChild(wilayaItem);

        // Load current rate for this wilaya
        loadWilayaCurrentRate(wilaya.wilaya_code);
    });
}

async function loadWilayaCurrentRate(wilayaCode) {
    try {
        const response = await fetch(`../php/api/payment-settings.php?module=shipping&action=calculate&wilaya_code=${wilayaCode}&weight=1.0`);
        const data = await response.json();

        if (data.success) {
            const rateElement = document.getElementById(`rate_${wilayaCode}`);
            if (rateElement) {
                rateElement.textContent = `${data.cost_breakdown.base_cost} دج`;
            }
        }
    } catch (error) {
        console.error(`Error loading rate for wilaya ${wilayaCode}:`, error);
    }
}

// Tab management
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Add active class to clicked button
    event.target.classList.add('active');
}

async function updateZoneRate() {
    const zoneNumber = document.getElementById('zoneSelect').value;
    const newRate = document.getElementById('newZoneRate').value;

    if (!zoneNumber || !newRate) {
        showError('يرجى اختيار المنطقة وإدخال الرسوم الجديدة');
        return;
    }

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'update_zone_rate',
                zone_number: zoneNumber,
                new_rate: parseFloat(newRate)
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم تحديث رسوم المنطقة بنجاح');
            loadZoneWilayas(zoneNumber); // Reload to show updated rates
        } else {
            showError(data.message || 'فشل في تحديث رسوم المنطقة');
        }
    } catch (error) {
        console.error('Error updating zone rate:', error);
        showError('حدث خطأ أثناء تحديث رسوم المنطقة');
    }
}

async function setCustomRate() {
    const wilayaCode = document.getElementById('customWilayaSelect').value;
    const customRate = document.getElementById('customRate').value;
    const deliveryTime = document.getElementById('customDeliveryTime').value || '2-4 أيام عمل';

    if (!wilayaCode || !customRate) {
        showError('يرجى اختيار الولاية وإدخال الرسوم المخصصة');
        return;
    }

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'set_custom_rate',
                wilaya_code: wilayaCode,
                shipping_cost: parseFloat(customRate),
                delivery_days: deliveryTime
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم إضافة الرسوم المخصصة بنجاح');
            loadCustomRates(); // Reload custom rates list

            // Clear form
            document.getElementById('customWilayaSelect').value = '';
            document.getElementById('customRate').value = '';
            document.getElementById('customDeliveryTime').value = '';
        } else {
            showError(data.message || 'فشل في إضافة الرسوم المخصصة');
        }
    } catch (error) {
        console.error('Error setting custom rate:', error);
        showError('حدث خطأ أثناء إضافة الرسوم المخصصة');
    }
}

async function removeCustomRate() {
    const wilayaCode = document.getElementById('customWilayaSelect').value;

    if (!wilayaCode) {
        showError('يرجى اختيار الولاية');
        return;
    }

    if (!confirm('هل أنت متأكد من إزالة الرسوم المخصصة لهذه الولاية؟')) {
        return;
    }

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'remove_custom_rate',
                wilaya_code: wilayaCode
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم إزالة الرسوم المخصصة بنجاح');
            loadCustomRates(); // Reload custom rates list
        } else {
            showError(data.message || 'فشل في إزالة الرسوم المخصصة');
        }
    } catch (error) {
        console.error('Error removing custom rate:', error);
        showError('حدث خطأ أثناء إزالة الرسوم المخصصة');
    }
}

async function deleteCustomRate(wilayaCode) {
    if (!confirm('هل أنت متأكد من حذف هذه الرسوم المخصصة؟')) {
        return;
    }

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'remove_custom_rate',
                wilaya_code: wilayaCode
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم حذف الرسوم المخصصة بنجاح');
            loadCustomRates(); // Reload custom rates list
        } else {
            showError(data.message || 'فشل في حذف الرسوم المخصصة');
        }
    } catch (error) {
        console.error('Error deleting custom rate:', error);
        showError('حدث خطأ أثناء حذف الرسوم المخصصة');
    }
}

async function saveFreeShippingSettings() {
    const enabled = document.getElementById('enableFreeShipping').checked;
    const threshold = document.getElementById('freeShippingThreshold').value;

    const selectedWilayas = [];
    document.querySelectorAll('#freeShippingWilayas input[type="checkbox"]:checked').forEach(checkbox => {
        selectedWilayas.push(checkbox.value);
    });

    try {
        const response = await fetch('../php/api/payment-settings.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                module: 'shipping',
                action: 'save_free_shipping',
                enabled: enabled,
                threshold: parseFloat(threshold) || 0,
                wilayas: selectedWilayas
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccess('تم حفظ إعدادات الشحن المجاني بنجاح');
        } else {
            showError(data.message || 'فشل في حفظ إعدادات الشحن المجاني');
        }
    } catch (error) {
        console.error('Error saving free shipping settings:', error);
        showError('حدث خطأ أثناء حفظ إعدادات الشحن المجاني');
    }
}

/**
 * Enhanced notification system
 */
function showNotification(message, type = 'info') {
    // Use the global notification system if available
    if (typeof notificationManager !== 'undefined') {
        switch(type) {
            case 'success':
                notificationManager.showSuccess(message);
                break;
            case 'error':
                notificationManager.showError(message);
                break;
            default:
                notificationManager.showInfo(message);
        }
    } else {
        // Fallback notification
        const alertType = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
        alert(alertType + ' ' + message);
    }
}

/**
 * Enhanced statistics update
 */
function updatePaymentStatistics() {
    const enabledMethods = Object.keys(paymentSettings).filter(method =>
        method !== 'general' && paymentSettings[method].enabled
    );

    const activeMethodsEl = document.getElementById('activePaymentMethods');
    const systemStatusEl = document.querySelector('.summary-value.status-active');

    if (activeMethodsEl) {
        activeMethodsEl.textContent = enabledMethods.length;
    }

    if (systemStatusEl) {
        if (enabledMethods.length > 0) {
            systemStatusEl.textContent = 'متصل';
            systemStatusEl.className = 'summary-value status-active';
        } else {
            systemStatusEl.textContent = 'غير متصل';
            systemStatusEl.className = 'summary-value status-inactive';
        }
    }

    // Update method-specific statistics
    updateMethodStatistics();
}

function updateMethodStatistics() {
    // Update COD statistics
    const codStats = document.getElementById('codStats');
    if (codStats && paymentSettings.cod.enabled) {
        codStats.innerHTML = `
            <small>الحد الأدنى: ${paymentSettings.cod.minOrder} ${paymentSettings.general.currency}</small><br>
            <small>رسوم التوصيل: ${paymentSettings.cod.fee} ${paymentSettings.general.currency}</small>
        `;
    }

    // Update other method statistics as needed
}

/**
 * Export payment settings
 */
function exportPaymentSettings() {
    const dataStr = JSON.stringify(paymentSettings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'payment_settings_export.json';
    link.click();
    URL.revokeObjectURL(url);

    showNotification('تم تصدير إعدادات الدفع بنجاح', 'success');
}

/**
 * Import payment settings
 */
function importPaymentSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedSettings = JSON.parse(e.target.result);

                    // Validate imported settings
                    if (validateImportedSettings(importedSettings)) {
                        paymentSettings = { ...paymentSettings, ...importedSettings };
                        applySettingsToUI();
                        autoSaveSettings();
                        showNotification('تم استيراد إعدادات الدفع بنجاح', 'success');
                    } else {
                        showNotification('ملف الإعدادات غير صالح', 'error');
                    }
                } catch (error) {
                    console.error('Import error:', error);
                    showNotification('خطأ في قراءة ملف الإعدادات', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function validateImportedSettings(settings) {
    // Basic validation of imported settings structure
    const requiredMethods = ['cod', 'bank', 'ccp', 'baridimob', 'general'];
    return requiredMethods.every(method => settings.hasOwnProperty(method));
}

// Alias for savePaymentSettings (used by payment-settings.html)
function savePaymentSettings() {
    return saveAllSettings();
}

// Alias for testPaymentConnection (used by payment-settings.html)
function testPaymentConnection() {
    return testPaymentSettings();
}

// Make functions globally available
window.initializePaymentSettings = initializePaymentSettings;
window.exportPaymentSettings = exportPaymentSettings;
window.importPaymentSettings = importPaymentSettings;
window.testPaymentMethod = testPaymentMethod;
window.savePaymentSettings = savePaymentSettings;
window.testPaymentConnection = testPaymentConnection;

console.log('💳 Payment settings script loaded');

// Export functions for global access
window.initializePaymentSettings = initializePaymentSettings;
window.saveAllSettings = saveAllSettings;
window.testPaymentSettings = testPaymentSettings;
window.exportSettings = exportSettings;
window.importSettings = importSettings;
