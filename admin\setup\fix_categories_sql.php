<?php
/**
 * Fix Categories SQL Issues
 * إصلاح مشاكل SQL للفئات
 */

echo "<h2>إصلاح مشاكل SQL للفئات</h2>\n";

// Load configuration manually
$envFile = '../../.env';
if (!file_exists($envFile)) {
    die("ملف .env غير موجود في: $envFile");
}

// Load configuration
$config = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) {
        continue;
    }
    list($key, $value) = explode('=', $line, 2) + [NULL, NULL];
    if (!empty($key)) {
        $config[trim($key)] = trim($value ?? '');
    }
}

// Check required database settings
$required = ['DB_HOST', 'DB_PORT', 'DB_USERNAME', 'DB_DATABASE'];
$missing = [];
foreach ($required as $key) {
    if (empty($config[$key])) {
        $missing[] = $key;
    }
}

if (!empty($missing)) {
    die('إعدادات قاعدة البيانات المفقودة: ' . implode(', ', $missing));
}

try {
    // Connect to database
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $config['DB_HOST'],
        $config['DB_PORT'],
        $config['DB_DATABASE']
    );
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    
    $pdo = new PDO($dsn, $config['DB_USERNAME'], $config['DB_PASSWORD'] ?? '', $options);
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    
    // Fix SQL issues
    echo "<h3>إصلاح مشاكل SQL:</h3>\n";
    
    $fixes = [
        // Drop duplicate indexes if they exist
        "DROP INDEX IF EXISTS idx_categories_active_featured ON categories",
        "DROP INDEX IF EXISTS idx_categories_parent_sort ON categories",
        
        // Add indexes properly
        "CREATE INDEX idx_categories_active_featured ON categories (is_active, is_featured)",
        "CREATE INDEX idx_categories_parent_sort ON categories (parent_id, sort_order)",
        
        // Ensure category_stats table has proper structure
        "CREATE TABLE IF NOT EXISTS category_stats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            category_id INT NOT NULL,
            products_count INT DEFAULT 0,
            subcategories_count INT DEFAULT 0,
            views_count INT DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
            UNIQUE KEY unique_category_stats (category_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // Update category_stats for existing categories
        "INSERT IGNORE INTO category_stats (category_id, products_count, subcategories_count, views_count)
         SELECT 
             c.id,
             0 as products_count,
             (SELECT COUNT(*) FROM categories sub WHERE sub.parent_id = c.id) as subcategories_count,
             0 as views_count
         FROM categories c"
    ];
    
    $successCount = 0;
    foreach ($fixes as $sql) {
        try {
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ تم تنفيذ الإصلاح بنجاح</p>\n";
            $successCount++;
        } catch (Exception $e) {
            // Some errors are expected (like dropping non-existent indexes)
            if (strpos($e->getMessage(), "Can't DROP") === false && 
                strpos($e->getMessage(), "Duplicate key name") === false) {
                echo "<p style='color: orange;'>⚠️ تحذير: " . $e->getMessage() . "</p>\n";
            }
        }
    }
    
    echo "<p style='color: blue;'>✅ تم تنفيذ $successCount إصلاح بنجاح</p>\n";
    
    // Test the categories API
    echo "<h3>اختبار API الفئات:</h3>\n";
    
    // Test get all categories
    $stmt = $pdo->query("
        SELECT c.*, p.name_ar as parent_name_ar, s.subcategories_count, s.products_count
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        LEFT JOIN category_stats s ON c.id = s.category_id
        ORDER BY c.parent_id ASC, c.sort_order ASC, c.name_ar ASC
    ");
    $categories = $stmt->fetchAll();
    
    echo "<p style='color: green;'>✅ تم جلب " . count($categories) . " فئة بنجاح</p>\n";
    
    // Test get by ID
    if (!empty($categories)) {
        $firstCategory = $categories[0];
        $stmt = $pdo->prepare("
            SELECT c.*, p.name_ar as parent_name_ar, s.subcategories_count, s.products_count
            FROM categories c
            LEFT JOIN categories p ON c.parent_id = p.id
            LEFT JOIN category_stats s ON c.id = s.category_id
            WHERE c.id = ?
        ");
        $stmt->execute([$firstCategory['id']]);
        $category = $stmt->fetch();
        
        if ($category) {
            echo "<p style='color: green;'>✅ تم جلب الفئة بالمعرف " . $firstCategory['id'] . " بنجاح</p>\n";
        }
    }
    
    // Show sample data
    echo "<h3>عينة من البيانات المحدثة:</h3>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 0.9em;'>\n";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>الاسم العربي</th><th>الفئة الأب</th><th>الفئات الفرعية</th><th>نشط</th><th>مميز</th></tr>\n";
    
    foreach (array_slice($categories, 0, 10) as $category) {
        $activeIcon = $category['is_active'] ? '✅' : '❌';
        $featuredIcon = $category['is_featured'] ? '⭐' : '';
        echo "<tr>";
        echo "<td>" . $category['id'] . "</td>";
        echo "<td>" . htmlspecialchars($category['name_ar']) . "</td>";
        echo "<td>" . htmlspecialchars($category['parent_name_ar'] ?? 'رئيسية') . "</td>";
        echo "<td style='text-align: center;'>" . ($category['subcategories_count'] ?? 0) . "</td>";
        echo "<td style='text-align: center;'>" . $activeIcon . "</td>";
        echo "<td style='text-align: center;'>" . $featuredIcon . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;'>";
    echo "<h4>🎉 تم إصلاح مشاكل SQL بنجاح!</h4>";
    echo "<p>تم إصلاح جميع مشاكل SQL وتحديث البيانات.</p>";
    echo "<p><strong>الخطوات التالية:</strong></p>";
    echo "<ul>";
    echo "<li>1. اختبر إدارة الفئات في لوحة الإدارة</li>";
    echo "<li>2. اختبر إضافة وتعديل الفئات</li>";
    echo "<li>3. تحقق من عمل النماذج التفاعلية</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0; text-align: center;'>";
    echo "<a href='../index.html' style='display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>← العودة إلى لوحة الإدارة</a>";
    echo "<a href='../test-categories-api.html' style='display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>🔍 اختبار API</a>";
    echo "<a href='../test-categories-modal.html' style='display: inline-block; padding: 12px 24px; background: #17a2b8; color: white; text-decoration: none; border-radius: 8px; margin: 5px;'>📝 اختبار النماذج</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>\n";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم قاعدة البيانات</li>";
    echo "<li>صحة إعدادات الاتصال في ملف .env</li>";
    echo "<li>وجود قاعدة البيانات المحددة</li>";
    echo "</ul>";
}
?>
