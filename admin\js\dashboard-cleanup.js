/**
 * Dashboard Cleanup Script
 * تنظيف لوحة المعلومات لعرض الإحصائيات فقط
 */

(function() {
    'use strict';

    console.log('🧹 Dashboard Cleanup Script loading...');

    // Clean dashboard content to show only statistics
    function cleanDashboardContent() {
        console.log('🧹 Dashboard cleanup - checking for enhanced design...');

        const dashboard = document.getElementById('dashboard');
        if (!dashboard) {
            console.error('Dashboard section not found');
            return;
        }

        // Check if enhanced dashboard is already present
        const enhancedDashboard = dashboard.querySelector('.dashboard-header');
        const isEnhanced = dashboard.getAttribute('data-enhanced') === 'true';

        if (enhancedDashboard || isEnhanced) {
            console.log('✅ Enhanced dashboard detected - skipping cleanup');
            // Still update statistics if they exist
            loadDashboardStats();
            return;
        }

        console.log('🧹 Cleaning dashboard content...');

        // Ensure dashboard only contains statistics and summary widgets
        const cleanDashboardHTML = `
            <h2>لوحة المعلومات</h2>

            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-box"></i>
                    <div class="stat-info">
                        <h3>إجمالي المنتجات</h3>
                        <p id="totalBooks">0</p>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-shopping-cart"></i>
                    <div class="stat-info">
                        <h3>الطلبات الجديدة</h3>
                        <p id="newOrders">0</p>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-money-bill-wave"></i>
                    <div class="stat-info">
                        <h3>إجمالي المبيعات</h3>
                        <p id="totalSales">0 دج</p>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-file-alt"></i>
                    <div class="stat-info">
                        <h3>إجمالي صفحات الهبوط</h3>
                        <p id="totalLandingPages">0</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions" style="margin: 30px 0;">
                <h3>إجراءات سريعة</h3>
                <div class="actions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <button class="action-btn" onclick="adminNavigation.navigateToSection('books')" style="padding: 15px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer;">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </button>
                    <button class="action-btn" onclick="adminNavigation.navigateToSection('landingPages')" style="padding: 15px; background: #f093fb; color: white; border: none; border-radius: 8px; cursor: pointer;">
                        <i class="fas fa-bullhorn"></i> إنشاء صفحة هبوط
                    </button>
                    <button class="action-btn" onclick="adminNavigation.navigateToSection('orders')" style="padding: 15px; background: #4facfe; color: white; border: none; border-radius: 8px; cursor: pointer;">
                        <i class="fas fa-shopping-cart"></i> عرض الطلبات
                    </button>
                    <button class="action-btn" onclick="adminNavigation.navigateToSection('reports')" style="padding: 15px; background: #43e97b; color: white; border: none; border-radius: 8px; cursor: pointer;">
                        <i class="fas fa-chart-bar"></i> عرض التقارير
                    </button>
                </div>
            </div>

            <!-- Recent Orders Summary -->
            <div class="recent-orders" style="margin-top: 30px;">
                <h3>آخر الطلبات</h3>
                <div class="table-responsive">
                    <table id="recentOrdersTable" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">رقم الطلب</th>
                                <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">العميل</th>
                                <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">المبلغ</th>
                                <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">الحالة</th>
                                <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Recent orders will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <button onclick="adminNavigation.navigateToSection('orders')" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        عرض جميع الطلبات
                    </button>
                </div>
            </div>

            <!-- System Status -->
            <div class="system-status" style="margin-top: 30px;">
                <h3>حالة النظام</h3>
                <div class="status-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div class="status-card" style="background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                        <h4 style="margin: 0 0 10px 0; color: #155724;">
                            <i class="fas fa-check-circle"></i> النظام يعمل بشكل طبيعي
                        </h4>
                        <p style="margin: 0; color: #155724;">جميع الخدمات متاحة</p>
                    </div>
                    <div class="status-card" style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                        <h4 style="margin: 0 0 10px 0; color: #0c5460;">
                            <i class="fas fa-database"></i> قاعدة البيانات
                        </h4>
                        <p style="margin: 0; color: #0c5460;">متصلة وتعمل بشكل طبيعي</p>
                    </div>
                    <div class="status-card" style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <h4 style="margin: 0 0 10px 0; color: #856404;">
                            <i class="fas fa-clock"></i> آخر تحديث
                        </h4>
                        <p style="margin: 0; color: #856404;" id="lastUpdateTime">جاري التحميل...</p>
                    </div>
                </div>
            </div>
        `;

        dashboard.innerHTML = cleanDashboardHTML;

        // Update last update time
        const lastUpdateElement = document.getElementById('lastUpdateTime');
        if (lastUpdateElement) {
            lastUpdateElement.textContent = new Date().toLocaleString('ar-DZ');
        }

        console.log('✅ Dashboard content cleaned and updated');
    }

    // Load dashboard statistics
    function loadDashboardStats() {
        console.log('📊 Loading dashboard statistics...');

        // Load statistics from API
        fetch('../php/api/dashboard-stats.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateDashboardStats(data.data);
                } else {
                    console.warn('Failed to load dashboard stats:', data.message);
                    setDefaultStats();
                }
            })
            .catch(error => {
                console.error('Error loading dashboard stats:', error);
                setDefaultStats();
            });
    }

    // Update dashboard statistics
    function updateDashboardStats(stats) {
        console.log('📈 Updating dashboard statistics...');

        // Update total books
        const totalBooksElement = document.getElementById('totalBooks');
        if (totalBooksElement) {
            totalBooksElement.textContent = stats.totalBooks || 0;
        }

        // Update new orders
        const newOrdersElement = document.getElementById('newOrders');
        if (newOrdersElement) {
            newOrdersElement.textContent = stats.newOrders || 0;
        }

        // Update total sales
        const totalSalesElement = document.getElementById('totalSales');
        if (totalSalesElement) {
            totalSalesElement.textContent = `${stats.totalSales || 0} دج`;
        }

        // Update total landing pages
        const totalLandingPagesElement = document.getElementById('totalLandingPages');
        if (totalLandingPagesElement) {
            totalLandingPagesElement.textContent = stats.totalLandingPages || 0;
        }

        // Update recent orders
        if (stats.recentOrders && stats.recentOrders.length > 0) {
            updateRecentOrders(stats.recentOrders);
        }

        console.log('✅ Dashboard statistics updated');
    }

    // Set default statistics
    function setDefaultStats() {
        console.log('📊 Setting default statistics...');

        const defaultStats = {
            totalBooks: 0,
            newOrders: 0,
            totalSales: 0,
            totalLandingPages: 0,
            recentOrders: []
        };

        updateDashboardStats(defaultStats);
    }

    // Update recent orders table
    function updateRecentOrders(orders) {
        console.log('📋 Updating recent orders...');

        const tbody = document.querySelector('#recentOrdersTable tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        orders.slice(0, 5).forEach(order => {
            const row = document.createElement('tr');
            row.style.borderBottom = '1px solid #dee2e6';
            row.innerHTML = `
                <td style="padding: 12px;">#${order.id}</td>
                <td style="padding: 12px;">${order.customer_name || 'غير محدد'}</td>
                <td style="padding: 12px;">${order.total_amount || 0} دج</td>
                <td style="padding: 12px;">
                    <span style="padding: 4px 8px; border-radius: 4px; font-size: 0.875rem; background: #d4edda; color: #155724;">
                        ${getOrderStatusText(order.status)}
                    </span>
                </td>
                <td style="padding: 12px;">${new Date(order.created_at).toLocaleDateString('ar-DZ')}</td>
            `;

            // Make row clickable to navigate to orders section
            row.style.cursor = 'pointer';
            row.onclick = () => {
                if (window.adminNavigation) {
                    window.adminNavigation.navigateToSection('orders');
                }
            };

            tbody.appendChild(row);
        });

        console.log('✅ Recent orders updated');
    }

    // Get order status text in Arabic
    function getOrderStatusText(status) {
        const statusMap = {
            'pending': 'قيد الانتظار',
            'processing': 'قيد المعالجة',
            'completed': 'مكتمل',
            'cancelled': 'ملغي',
            'shipped': 'تم الشحن'
        };

        return statusMap[status] || status;
    }

    // Initialize dashboard cleanup
    function initializeDashboardCleanup() {
        console.log('🚀 Initializing dashboard cleanup...');

        // Clean dashboard content immediately
        cleanDashboardContent();

        // Load statistics
        loadDashboardStats();

        // Set up periodic refresh
        setInterval(() => {
            loadDashboardStats();
        }, 30000); // Refresh every 30 seconds

        console.log('✅ Dashboard cleanup initialized');
    }

    // Public API
    window.dashboardCleanup = {
        clean: cleanDashboardContent,
        loadStats: loadDashboardStats,
        initialize: initializeDashboardCleanup
    };

    // Auto-initialize when DOM is ready - but check for enhanced dashboard first
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a bit to let enhanced dashboard load first
            setTimeout(() => {
                const dashboard = document.getElementById('dashboard');
                if (dashboard && !dashboard.querySelector('.dashboard-header')) {
                    initializeDashboardCleanup();
                }
            }, 2000);
        });
    } else {
        // Wait a bit to let enhanced dashboard load first
        setTimeout(() => {
            const dashboard = document.getElementById('dashboard');
            if (dashboard && !dashboard.querySelector('.dashboard-header')) {
                initializeDashboardCleanup();
            }
        }, 2000);
    }

    console.log('✅ Dashboard Cleanup Script loaded successfully');

})();
