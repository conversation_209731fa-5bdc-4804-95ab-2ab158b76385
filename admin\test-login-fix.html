<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Fix</title>
</head>
<body>
    <h1>Test des corrections de connexion</h1>
    <div id="status">Chargement...</div>
    <div id="logs"></div>

    <!-- Auth Fix Script (load first) -->
    <script src="auth-fix.js"></script>

    <!-- Fallback redirect function -->
    <script>
        if (!window.safeRedirect) {
            window.safeRedirect = function (url) {
                console.log("🔄 Using fallback redirect to:", url);
                window.location.href = url;
            };
        }
    </script>

    <!-- Test Script -->
    <script>
        function addLog(message) {
            const logsDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logsDiv.appendChild(logEntry);
            console.log(message);
        }

        function testSafeRedirect() {
            addLog('Testing safeRedirect function...');
            
            if (typeof window.safeRedirect === 'function') {
                addLog('✅ safeRedirect function is available');
                
                // Test the function without actually redirecting
                try {
                    // Mock the redirect to test the function
                    const originalLocationHref = window.location.href;
                    let redirectCalled = false;
                    
                    // Temporarily override location.href to capture redirect attempts
                    Object.defineProperty(window.location, 'href', {
                        set: function(url) {
                            redirectCalled = true;
                            addLog('🔄 Redirect would go to: ' + url);
                            // Restore original behavior
                            Object.defineProperty(window.location, 'href', {
                                value: originalLocationHref,
                                writable: true
                            });
                        },
                        configurable: true
                    });
                    
                    // Test the safeRedirect function
                    window.safeRedirect('index.html');
                    
                    if (redirectCalled) {
                        addLog('✅ safeRedirect function works correctly');
                    } else {
                        addLog('⚠️ safeRedirect function did not trigger redirect');
                    }
                    
                } catch (error) {
                    addLog('❌ Error testing safeRedirect: ' + error.message);
                }
            } else {
                addLog('❌ safeRedirect function is NOT available');
                addLog('Type of window.safeRedirect: ' + typeof window.safeRedirect);
            }
        }

        function testAuthFix() {
            addLog('Testing auth-fix.js loading...');
            
            if (window.authFix) {
                addLog('✅ authFix object is available');
                addLog('authFix properties: ' + Object.keys(window.authFix).join(', '));
            } else {
                addLog('❌ authFix object is NOT available');
            }
            
            if (window.onFirebaseUserSignedIn) {
                addLog('✅ onFirebaseUserSignedIn function is available');
            } else {
                addLog('❌ onFirebaseUserSignedIn function is NOT available');
            }
        }

        // Run tests when page loads
        window.addEventListener('load', function() {
            document.getElementById('status').textContent = 'Tests en cours...';
            
            setTimeout(() => {
                testAuthFix();
                testSafeRedirect();
                document.getElementById('status').textContent = 'Tests terminés - voir les logs ci-dessous';
            }, 1000);
        });
    </script>
</body>
</html>