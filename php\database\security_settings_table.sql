-- Security Settings Database Table
-- إنشاء جدول إعدادات الأمان
CREATE TABLE IF NOT EXISTS `security_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('boolean', 'integer', 'string', 'json') DEFAULT 'string',
  `category` varchar(50) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting` (`setting_key`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Security Audit Logs Table
CREATE TABLE IF NOT EXISTS `security_audit_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text,
  `ip_address` varchar(45),
  `user_agent` text,
  `severity` enum('low', 'medium', 'high', 'critical') DEFAULT 'medium',
  `status` enum('success', 'failed', 'blocked') DEFAULT 'success',
  `metadata` json,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_severity` (`severity`),
  KEY `idx_created_at` (`created_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- IP Management Table
CREATE TABLE IF NOT EXISTS `ip_management` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `type` enum('whitelist', 'blacklist') NOT NULL,
  `reason` text,
  `added_by` int(11) DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_ip_type` (`ip_address`, `type`),
  KEY `idx_type` (`type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Active Sessions Table
CREATE TABLE IF NOT EXISTS `active_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45),
  `user_agent` text,
  `location` varchar(100),
  `device_type` varchar(50),
  `last_activity` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_session` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Insert default security settings
INSERT INTO `security_settings` (
    `setting_key`,
    `setting_value`,
    `setting_type`,
    `category`,
    `description`
  )
VALUES (
    'two_factor_enabled',
    '1',
    'boolean',
    'authentication',
    'تفعيل المصادقة الثنائية'
  ),
  (
    'password_min_length',
    '8',
    'integer',
    'authentication',
    'الحد الأدنى لطول كلمة المرور'
  ),
  (
    'password_require_uppercase',
    '1',
    'boolean',
    'authentication',
    'يجب أن تحتوي كلمة المرور على أحرف كبيرة'
  ),
  (
    'password_require_numbers',
    '1',
    'boolean',
    'authentication',
    'يجب أن تحتوي كلمة المرور على أرقام'
  ),
  (
    'password_require_symbols',
    '1',
    'boolean',
    'authentication',
    'يجب أن تحتوي كلمة المرور على رموز خاصة'
  ),
  (
    'session_timeout',
    '60',
    'integer',
    'authentication',
    'انتهاء الجلسة بالدقائق'
  ),
  (
    'max_login_attempts',
    '5',
    'integer',
    'access_control',
    'عدد محاولات تسجيل الدخول المسموحة'
  ),
  (
    'lockout_duration',
    '30',
    'integer',
    'access_control',
    'مدة الحظر بالدقائق'
  ),
  (
    'ip_whitelist_enabled',
    '0',
    'boolean',
    'access_control',
    'تفعيل قائمة IP المسموحة'
  ),
  (
    'api_access_restricted',
    '0',
    'boolean',
    'access_control',
    'تقييد الوصول لـ API'
  ),
  (
    'security_monitoring_enabled',
    '1',
    'boolean',
    'monitoring',
    'تفعيل مراقبة الأمان'
  ),
  (
    'failed_login_alerts',
    '1',
    'boolean',
    'monitoring',
    'تنبيهات محاولات الدخول الفاشلة'
  ),
  (
    'suspicious_activity_alerts',
    '1',
    'boolean',
    'monitoring',
    'تنبيهات النشاط المشبوه'
  ),
  (
    'auto_backup_enabled',
    '1',
    'boolean',
    'backup',
    'تفعيل النسخ الاحتياطي التلقائي'
  ),
  (
    'backup_frequency',
    'daily',
    'string',
    'backup',
    'تكرار النسخ الاحتياطي'
  ),
  (
    'backup_retention_days',
    '30',
    'integer',
    'backup',
    'مدة الاحتفاظ بالنسخ الاحتياطية'
  );
-- Two-Factor Authentication Table
CREATE TABLE IF NOT EXISTS `user_2fa` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `secret_key` varchar(32) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 0,
  `backup_codes` json,
  `last_used` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_2fa` (`user_id`),
  KEY `idx_is_enabled` (`is_enabled`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci;
-- Insert sample audit logs
INSERT INTO `security_audit_logs` (
    `action`,
    `description`,
    `ip_address`,
    `severity`,
    `status`
  )
VALUES (
    'admin_login',
    'تسجيل دخول المدير بنجاح',
    '*************',
    'low',
    'success'
  ),
  (
    'failed_login',
    'محاولة تسجيل دخول فاشلة',
    '*************',
    'medium',
    'failed'
  ),
  (
    'password_change',
    'تغيير كلمة المرور',
    '*************',
    'medium',
    'success'
  ),
  (
    'security_settings_update',
    'تحديث إعدادات الأمان',
    '*************',
    'high',
    'success'
  ),
  (
    'suspicious_activity',
    'نشاط مشبوه - محاولات دخول متعددة',
    '*************',
    'high',
    'blocked'
  );
