-- Security Settings Database Schema
-- إنشاء جدول إعدادات الأمان

-- Create security_settings table
CREATE TABLE IF NOT EXISTS security_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('boolean', 'integer', 'string', 'json') DEFAULT 'string',
    category ENUM('authentication', 'access_control', 'monitoring', 'backup', 'general') DEFAULT 'general',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create security_audit_logs table
CREATE TABLE IF NOT EXISTS security_audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_type <PERSON><PERSON><PERSON>('login_attempt', 'login_success', 'login_failure', 'logout', 'password_change', 'settings_change', 'suspicious_activity', 'ip_blocked', 'session_terminated') NOT NULL,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    event_details JSON,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at),
    INDEX idx_severity (severity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create active_sessions table
CREATE TABLE IF NOT EXISTS active_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(128) NOT NULL UNIQUE,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    session_data JSON,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ip_whitelist table
CREATE TABLE IF NOT EXISTS ip_whitelist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    ip_range VARCHAR(50),
    description TEXT,
    country_code VARCHAR(2),
    country_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    INDEX idx_ip_address (ip_address),
    INDEX idx_country_code (country_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ip_blacklist table
CREATE TABLE IF NOT EXISTS ip_blacklist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    ip_range VARCHAR(50),
    reason TEXT,
    blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    blocked_by VARCHAR(100),
    expires_at TIMESTAMP NULL,
    is_permanent BOOLEAN DEFAULT FALSE,
    attempt_count INT DEFAULT 1,
    INDEX idx_ip_address (ip_address),
    INDEX idx_blocked_at (blocked_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default security settings
INSERT INTO security_settings (setting_key, setting_value, setting_type, category, description) VALUES
-- Authentication Settings
('two_factor_enabled', 'true', 'boolean', 'authentication', 'تفعيل المصادقة الثنائية'),
('password_min_length', '8', 'integer', 'authentication', 'الحد الأدنى لطول كلمة المرور'),
('password_require_uppercase', 'true', 'boolean', 'authentication', 'يجب أن تحتوي كلمة المرور على أحرف كبيرة'),
('password_require_numbers', 'true', 'boolean', 'authentication', 'يجب أن تحتوي كلمة المرور على أرقام'),
('password_require_symbols', 'true', 'boolean', 'authentication', 'يجب أن تحتوي كلمة المرور على رموز خاصة'),
('session_timeout', '3600', 'integer', 'authentication', 'مدة انتهاء الجلسة بالثواني'),
('max_login_attempts', '5', 'integer', 'authentication', 'الحد الأقصى لمحاولات تسجيل الدخول'),

-- Access Control Settings
('lockout_duration', '1800', 'integer', 'access_control', 'مدة الحظر بالثواني'),
('ip_whitelist_enabled', 'false', 'boolean', 'access_control', 'تفعيل قائمة IP المسموحة'),
('api_access_restricted', 'false', 'boolean', 'access_control', 'تقييد الوصول لـ API'),
('admin_ip_restriction', 'false', 'boolean', 'access_control', 'تقييد وصول المديرين حسب IP'),

-- Monitoring Settings
('security_monitoring_enabled', 'true', 'boolean', 'monitoring', 'تفعيل مراقبة الأمان'),
('suspicious_activity_detection', 'true', 'boolean', 'monitoring', 'كشف النشاط المشبوه'),
('email_alerts_enabled', 'true', 'boolean', 'monitoring', 'تفعيل تنبيهات البريد الإلكتروني'),
('log_retention_days', '30', 'integer', 'monitoring', 'مدة حفظ السجلات بالأيام'),

-- Backup Settings
('auto_backup_enabled', 'true', 'boolean', 'backup', 'تفعيل النسخ الاحتياطي التلقائي'),
('backup_frequency', 'daily', 'string', 'backup', 'تكرار النسخ الاحتياطي'),
('backup_retention_days', '7', 'integer', 'backup', 'مدة حفظ النسخ الاحتياطية')

ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = CURRENT_TIMESTAMP;
