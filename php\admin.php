<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

class Admin
{
    private $pdo;

    public function __construct()
    {
        $this->pdo = getPDOConnection();
        if (!$this->pdo) {
            throw new Exception('Impossible de se connecter à la base de données');
        }

        // Execute store settings migration if needed
        $this->executeMigration();
    }

    private function executeMigration()
    {
        try {
            $migrationFile = __DIR__ . '/../database/migrations/add_store_settings.sql';
            if (file_exists($migrationFile)) {
                $sql = file_get_contents($migrationFile);
                $this->pdo->exec($sql);
            }
        } catch (PDOException $e) {
            error_log('Migration error: ' . $e->getMessage());
        }
    }

    // Authentification de l'administrateur
    public function login($username, $password)
    {
        try {
            $stmt = $this->pdo->prepare('SELECT * FROM admins WHERE nom_utilisateur = ?');
            $stmt->execute([sanitize($username)]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($password, $admin['mot_de_passe'])) {
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['nom_utilisateur'];
                return ['success' => true];
            }

            return ['error' => 'Nom d\'utilisateur ou mot de passe incorrect'];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Déconnexion
    public function logout()
    {
        session_destroy();
        return ['success' => true];
    }

    // Créer un nouvel administrateur
    public function createAdmin($username, $password)
    {
        try {
            // Vérifier si l'administrateur existe déjà
            $stmt = $this->pdo->prepare('SELECT id FROM admins WHERE nom_utilisateur = ?');
            $stmt->execute([sanitize($username)]);
            if ($stmt->fetch()) {
                return ['error' => 'Ce nom d\'utilisateur existe déjà'];
            }

            // Créer le nouvel administrateur
            $stmt = $this->pdo->prepare(
                'INSERT INTO admins (nom_utilisateur, mot_de_passe) VALUES (?, ?)'
            );
            $stmt->execute([
                sanitize($username),
                password_hash($password, PASSWORD_DEFAULT)
            ]);

            return ['success' => true, 'id' => $this->pdo->lastInsertId()];
        } catch (PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    // Récupérer les paramètres du magasin
    public function getStoreSettings()
    {
        try {
            // Check if store_settings table exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'store_settings'");
            $tableExists = $stmt->rowCount() > 0;

            if (!$tableExists) {
                // Create table and insert default settings
                $this->createStoreSettingsTable();
            }

            $stmt = $this->pdo->query('SELECT setting_key, setting_value FROM store_settings');
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // If no settings found, insert defaults
            if (empty($settings)) {
                $this->insertDefaultSettings();
                $stmt = $this->pdo->query('SELECT setting_key, setting_value FROM store_settings');
                $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            }

            return ['success' => true, 'settings' => $settings];
        } catch (PDOException $e) {
            error_log('Error getting store settings: ' . $e->getMessage());
            // Return default settings if database error
            return [
                'success' => true,
                'settings' => [
                    'store_name' => 'متجر مصعب',
                    'phone_number' => '+213 000000000',
                    'contact_email' => '<EMAIL>',
                    'address' => 'الجزائر'
                ]
            ];
        }
    }

    private function createStoreSettingsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS store_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(50) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $this->pdo->exec($sql);
    }

    private function insertDefaultSettings()
    {
        $defaults = [
            'store_name' => 'متجر مصعب',
            'store_description' => 'متجر إلكتروني متخصص',
            'contact_email' => '<EMAIL>',
            'phone_number' => '+213 000000000',
            'address' => 'الجزائر',
            'shipping_policy' => 'الشحن خلال 3-5 أيام عمل',
            'return_policy' => 'سياسة الإرجاع خلال 30 يوماً'
        ];

        $stmt = $this->pdo->prepare('INSERT IGNORE INTO store_settings (setting_key, setting_value) VALUES (?, ?)');
        foreach ($defaults as $key => $value) {
            $stmt->execute([$key, $value]);
        }
    }

    // Mettre à jour les paramètres du magasin
    public function updateStoreSettings($settings)
    {
        try {
            $this->pdo->beginTransaction();

            $stmt = $this->pdo->prepare(
                'UPDATE store_settings SET setting_value = ? WHERE setting_key = ?'
            );

            foreach ($settings as $key => $value) {
                $stmt->execute([sanitize($value), sanitize($key)]);
            }

            $this->pdo->commit();
            return ['success' => true];
        } catch (PDOException $e) {
            $this->pdo->rollBack();
            error_log('Error updating store settings: ' . $e->getMessage());
            return ['error' => 'Erreur lors de la mise à jour des paramètres'];
        }
    }
}

// Instancier la classe Admin
try {
    $admin = new Admin();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
    exit;
}

// Traitement des requêtes API
// Traitement des requêtes API
try {
    $rawInput = file_get_contents('php://input');
    error_log('Données brutes reçues : ' . $rawInput);
    $data = json_decode($rawInput, true);
    error_log('Données décodées : ' . print_r($data, true));

    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'login':
                // Support both POST and GET for development
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($data['username']) && isset($data['password'])) {
                    $result = $admin->login($data['username'], $data['password']);
                    if (isset($result['error'])) {
                        http_response_code(401);
                    }
                    echo json_encode($result);
                } elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['username']) && isset($_GET['password'])) {
                    // GET method support for simple servers
                    $result = $admin->login($_GET['username'], $_GET['password']);
                    if (isset($result['error'])) {
                        http_response_code(401);
                    }
                    echo json_encode($result);
                }
                break;

            case 'logout':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    echo json_encode($admin->logout());
                }
                break;

            case 'create':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    if (isAdminLoggedIn() && isset($data['username']) && isset($data['password'])) {
                        $result = $admin->createAdmin($data['username'], $data['password']);
                        if (isset($result['error'])) {
                            http_response_code(400);
                        }
                        echo json_encode($result);
                    } else {
                        http_response_code(403);
                        echo json_encode(['error' => 'Accès non autorisé']);
                    }
                }
                break;



            case 'check':
                if ($_SERVER['REQUEST_METHOD'] === 'GET') {
                    echo json_encode(['logged_in' => isAdminLoggedIn()]);
                }
                break;

            case 'get_store_settings':
                if ($_SERVER['REQUEST_METHOD'] === 'GET') {
                    try {
                        if (isAdminLoggedIn()) {
                            $result = $admin->getStoreSettings();
                            header('Content-Type: application/json; charset=utf-8');
                            echo json_encode($result, JSON_UNESCAPED_UNICODE);
                        } else {
                            http_response_code(403);
                            header('Content-Type: application/json; charset=utf-8');
                            echo json_encode(['success' => false, 'error' => 'Accès non autorisé'], JSON_UNESCAPED_UNICODE);
                        }
                    } catch (Exception $e) {
                        error_log('Settings API error: ' . $e->getMessage());
                        http_response_code(500);
                        header('Content-Type: application/json; charset=utf-8');
                        echo json_encode(['success' => false, 'error' => 'Erreur serveur'], JSON_UNESCAPED_UNICODE);
                    }
                }
                break;

            case 'update_store_settings':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    if (isAdminLoggedIn() && isset($data['settings'])) {
                        $result = $admin->updateStoreSettings($data['settings']);
                        if (isset($result['error'])) {
                            http_response_code(400);
                        }
                        echo json_encode($result);
                    } else {
                        http_response_code(403);
                        echo json_encode(['error' => 'Accès non autorisé']);
                    }
                }
                break;

            default:
                http_response_code(404);
                echo json_encode(['error' => 'Action non trouvée']);
        }
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Action non spécifiée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}
