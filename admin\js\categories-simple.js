/**
 * Categories Management - Ultra Simple Version
 * إدارة الفئات - نسخة مبسطة جداً
 */

console.log('🔧 تحميل ملف categories-simple.js...');

// Direct function without namespace to avoid conflicts
window.loadCategoriesManagementContent = async function() {
    console.log('🗂️ بدء تحميل إدارة الفئات...');

    const container = document.getElementById('categoriesManagementContent');
    if (!container) {
        console.error('❌ لم يتم العثور على حاوي إدارة الفئات');
        return;
    }

    console.log('✅ تم العثور على الحاوي');

    // Show loading state
    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
            <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
        </div>
    `;

    try {
        console.log('🌐 جاري جلب البيانات...');
        const response = await fetch('php/categories.php?action=get_all');
        console.log('📡 استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📦 البيانات المستلمة:', data);

        if (!data.success) {
            throw new Error(data.message || 'فشل في تحميل الفئات');
        }

        // Render the interface
        const categories = data.data.categories;
        const mainCategories = categories.filter(c => c.parent_id === null);
        const subCategories = categories.filter(c => c.parent_id !== null);
        const featuredCategories = categories.filter(c => c.is_featured == 1);

        container.innerHTML = `
            <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                <!-- Header -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px;">
                    <div>
                        <h2 style="margin: 0; font-size: 1.8rem;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة وتنظيم فئات المنتجات والمحتوى</p>
                    </div>
                    <div>
                        <button onclick="showAddCategoryModal()" style="margin-left: 10px; padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer;">
                            <i class="fas fa-plus"></i> إضافة فئة جديدة
                        </button>
                        <button onclick="loadCategoriesManagementContent()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer;">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>

                <!-- Statistics -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                        <div style="font-size: 2rem; color: #667eea; margin-bottom: 10px;">
                            <i class="fas fa-folder"></i>
                        </div>
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.data.total}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">إجمالي الفئات</p>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                        <div style="font-size: 2rem; color: #28a745; margin-bottom: 10px;">
                            <i class="fas fa-folder-open"></i>
                        </div>
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${mainCategories.length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات الرئيسية</p>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                        <div style="font-size: 2rem; color: #17a2b8; margin-bottom: 10px;">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${subCategories.length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات الفرعية</p>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                        <div style="font-size: 2rem; color: #ffc107; margin-bottom: 10px;">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${featuredCategories.length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات المميزة</p>
                    </div>
                </div>

                <!-- Categories List -->
                <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                    <div style="padding: 15px 20px; border-bottom: 1px solid #e0e0e0; background: #f8f9fa;">
                        <h3 style="margin: 0; color: #333;"><i class="fas fa-sitemap"></i> قائمة الفئات</h3>
                    </div>

                    <div style="padding: 20px;">
                        ${renderCategoriesList(categories)}
                    </div>
                </div>
            </div>
        `;

        console.log('✅ تم تحديث محتوى الحاوي بنجاح');

    } catch (error) {
        console.error('❌ خطأ في تحميل الفئات:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <div>
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                </div>
                <h4>خطأ في تحميل إدارة الفئات</h4>
                <p>${error.message}</p>
                <button onclick="loadCategoriesManagementContent()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
};

// Helper function to render categories list
function renderCategoriesList(categories) {
    if (!categories || categories.length === 0) {
        return '<p style="text-align: center; color: #666; padding: 40px;">لا توجد فئات للعرض</p>';
    }

    let html = '';

    // First render main categories
    const mainCategories = categories.filter(c => c.parent_id === null);

    mainCategories.forEach(mainCategory => {
        html += renderCategoryItem(mainCategory, 0);

        // Then render its subcategories
        const subCategories = categories.filter(c => c.parent_id == mainCategory.id);
        subCategories.forEach(subCategory => {
            html += renderCategoryItem(subCategory, 1);
        });
    });

    return html;
}

// Helper function to render a single category item
function renderCategoryItem(category, level) {
    const indent = level * 20;
    const bgColor = level === 0 ? '#f8f9fa' : '#ffffff';
    const borderColor = level === 0 ? '#667eea' : '#e0e0e0';
    const featuredIcon = category.is_featured == 1 ? '<span style="color: #ffc107; margin-right: 5px;">⭐</span>' : '';
    const statusIcon = category.is_active == 1 ? '<span style="color: #28a745;">✅</span>' : '<span style="color: #dc3545;">❌</span>';

    return `
        <div style="margin-right: ${indent}px; margin-bottom: 15px; padding: 15px; border: 1px solid ${borderColor}; border-radius: 8px; background: ${bgColor};">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="display: flex; align-items: center; gap: 15px; flex: 1;">
                    <div style="color: ${category.color}; font-size: 1.5rem;">
                        <i class="${category.icon || 'fas fa-folder'}"></i>
                    </div>
                    <div style="flex: 1;">
                        <h4 style="margin: 0 0 5px 0; color: #333;">
                            ${featuredIcon}${category.name_ar}
                        </h4>
                        <p style="margin: 0 0 10px 0; color: #666; font-size: 0.9em;">${category.description_ar || 'لا يوجد وصف'}</p>
                        <div style="display: flex; gap: 15px; font-size: 0.8em; color: #999;">
                            <span><i class="fas fa-layer-group"></i> ${category.subcategories_count || 0} فئة فرعية</span>
                            <span><i class="fas fa-box"></i> ${category.products_count || 0} منتج</span>
                            <span><i class="fas fa-eye"></i> ${category.views_count || 0} مشاهدة</span>
                        </div>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div style="text-align: center; font-size: 0.8em;">
                        ${statusIcon}
                    </div>
                    <div style="display: flex; gap: 5px;">
                        <button onclick="editCategory(${category.id})" title="تعديل" style="padding: 8px 12px; border: none; background: #17a2b8; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleCategoryStatus(${category.id})" title="${category.is_active == 1 ? 'إلغاء التفعيل' : 'تفعيل'}" style="padding: 8px 12px; border: none; background: ${category.is_active == 1 ? '#28a745' : '#6c757d'}; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-${category.is_active == 1 ? 'eye-slash' : 'eye'}"></i>
                        </button>
                        <button onclick="deleteCategory(${category.id})" title="حذف" style="padding: 8px 12px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Action functions
window.showAddCategoryModal = function() {
    console.log('📝 فتح نموذج إضافة فئة جديدة');
    alert('نموذج إضافة فئة جديدة - قيد التطوير');
};

window.editCategory = function(id) {
    console.log('✏️ تعديل الفئة:', id);
    alert('تعديل الفئة ' + id + ' - قيد التطوير');
};

window.toggleCategoryStatus = function(id) {
    console.log('🔄 تغيير حالة الفئة:', id);
    if (confirm('هل أنت متأكد من تغيير حالة هذه الفئة؟')) {
        alert('تغيير حالة الفئة ' + id + ' - قيد التطوير');
    }
};

window.deleteCategory = function(id) {
    console.log('🗑️ حذف الفئة:', id);
    if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
        alert('حذف الفئة ' + id + ' - قيد التطوير');
    }
};
        data: [],
        currentCategory: null,
        isLoading: false,

        // Load categories management content
        loadContent: function() {
            console.log('🗂️ بدء تحميل إدارة الفئات...');

            const container = document.getElementById('categoriesManagementContent');
            if (!container) {
                console.error('❌ لم يتم العثور على حاوي إدارة الفئات');
                return;
            }

            console.log('✅ تم العثور على الحاوي');

            // Show loading state
            this.showLoading(container);

            // Load categories from server
            this.fetchCategories()
                .then(data => {
                    console.log('📦 تم استلام البيانات:', data);

                    if (data.success) {
                        this.data = data.data.categories;
                        console.log('✅ تم حفظ بيانات الفئات:', this.data.length, 'فئة');
                        this.renderInterface(data.data);
                    } else {
                        throw new Error(data.message || 'فشل في تحميل الفئات');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في تحميل الفئات:', error);
                    this.showError(container, error.message);
                });
        },

        // Fetch categories from server
        fetchCategories: async function() {
            try {
                const url = 'php/categories.php?action=get_all';
                console.log('🌐 طلب البيانات من:', url);

                const response = await fetch(url);
                console.log('📡 استجابة الخادم:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('📋 البيانات المستلمة:', data);

                return data;
            } catch (error) {
                console.error('❌ خطأ في جلب البيانات:', error);
                throw new Error('خطأ في الاتصال بالخادم: ' + error.message);
            }
        },

        // Show loading state
        showLoading: function(container) {
            console.log('⏳ عرض حالة التحميل...');

            container.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div>
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                    </div>
                    <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
                </div>
            `;
        },

        // Show error state
        showError: function(container, message) {
            console.log('❌ عرض حالة الخطأ:', message);

            const self = this;
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #dc3545;">
                    <div>
                        <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    </div>
                    <h4>خطأ في تحميل إدارة الفئات</h4>
                    <p>${message}</p>
                    <button onclick="CategoriesManager.loadContent()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        },

        // Render categories management interface
        renderInterface: function(data) {
            console.log('🎨 بدء رسم واجهة إدارة الفئات...');

            const container = document.getElementById('categoriesManagementContent');

            if (!data || !data.categories) {
                console.error('❌ بيانات غير صحيحة للرسم');
                this.showError(container, 'بيانات الفئات غير صحيحة');
                return;
            }

            const mainCategories = data.categories.filter(c => c.parent_id === null);
            const subCategories = data.categories.filter(c => c.parent_id !== null);
            const featuredCategories = data.categories.filter(c => c.is_featured == 1);

            const html = `
                <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                    <!-- Header -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px;">
                        <div>
                            <h2 style="margin: 0; font-size: 1.8rem;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                            <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة وتنظيم فئات المنتجات والمحتوى</p>
                        </div>
                        <div>
                            <button onclick="CategoriesManager.showAddModal()" style="margin-left: 10px; padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer;">
                                <i class="fas fa-plus"></i> إضافة فئة جديدة
                            </button>
                            <button onclick="CategoriesManager.loadContent()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer;">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2rem; color: #667eea; margin-bottom: 10px;">
                                <i class="fas fa-folder"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.total}</h3>
                            <p style="margin: 5px 0 0 0; color: #666;">إجمالي الفئات</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2rem; color: #28a745; margin-bottom: 10px;">
                                <i class="fas fa-folder-open"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2rem; color: #333;">${mainCategories.length}</h3>
                            <p style="margin: 5px 0 0 0; color: #666;">الفئات الرئيسية</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2rem; color: #17a2b8; margin-bottom: 10px;">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2rem; color: #333;">${subCategories.length}</h3>
                            <p style="margin: 5px 0 0 0; color: #666;">الفئات الفرعية</p>
                        </div>
                        <div style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2rem; color: #ffc107; margin-bottom: 10px;">
                                <i class="fas fa-star"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2rem; color: #333;">${featuredCategories.length}</h3>
                            <p style="margin: 5px 0 0 0; color: #666;">الفئات المميزة</p>
                        </div>
                    </div>

                    <!-- Categories List -->
                    <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                        <div style="padding: 15px 20px; border-bottom: 1px solid #e0e0e0; background: #f8f9fa;">
                            <h3 style="margin: 0; color: #333;"><i class="fas fa-sitemap"></i> عرض هرمي للفئات</h3>
                        </div>

                        <div style="padding: 20px;">
                            ${this.renderHierarchy(data.hierarchy || [])}
                        </div>
                    </div>
                </div>
            `;

            console.log('✅ تم إنشاء HTML للواجهة');
            container.innerHTML = html;
            console.log('✅ تم تحديث محتوى الحاوي');
        },

        // Render categories hierarchy
        renderHierarchy: function(hierarchy, level = 0) {
            console.log('🌳 رسم الهيكل الهرمي، المستوى:', level, 'عدد العناصر:', hierarchy.length);

            if (!hierarchy || hierarchy.length === 0) {
                return '<p style="text-align: center; color: #666; padding: 40px;">لا توجد فئات للعرض</p>';
            }

            let html = '<div>';

            hierarchy.forEach((category, index) => {
                console.log(`📁 رسم الفئة ${index + 1}:`, category.name_ar);

                const indent = level * 20;
                const statusClass = category.is_active == 1 ? 'active' : 'inactive';
                const featuredIcon = category.is_featured == 1 ? '⭐' : '';

                html += `
                    <div style="margin-right: ${indent}px; margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; ${category.is_active == 1 ? 'background: #f8f9fa;' : 'background: #f5f5f5; opacity: 0.7;'}">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="color: ${category.color}; font-size: 1.5rem;">
                                    <i class="${category.icon || 'fas fa-folder'}"></i>
                                </div>
                                <div>
                                    <h4 style="margin: 0 0 5px 0; color: #333;">${category.name_ar} ${featuredIcon}</h4>
                                    <p style="margin: 0 0 10px 0; color: #666; font-size: 0.9em;">${category.description_ar || 'لا يوجد وصف'}</p>
                                    <div style="display: flex; gap: 15px; font-size: 0.8em; color: #999;">
                                        <span><i class="fas fa-layer-group"></i> ${category.subcategories_count || 0} فئة فرعية</span>
                                        <span><i class="fas fa-box"></i> ${category.products_count || 0} منتج</span>
                                        <span><i class="fas fa-eye"></i> ${category.views_count || 0} مشاهدة</span>
                                    </div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 5px;">
                                <button onclick="CategoriesManager.editCategory(${category.id})" title="تعديل" style="padding: 8px 12px; border: none; background: #17a2b8; color: white; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="CategoriesManager.toggleStatus(${category.id})" title="${category.is_active == 1 ? 'إلغاء التفعيل' : 'تفعيل'}" style="padding: 8px 12px; border: none; background: ${category.is_active == 1 ? '#28a745' : '#6c757d'}; color: white; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-${category.is_active == 1 ? 'eye-slash' : 'eye'}"></i>
                                </button>
                                <button onclick="CategoriesManager.deleteCategory(${category.id})" title="حذف" style="padding: 8px 12px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                // Render children
                if (category.children && category.children.length > 0) {
                    console.log(`📂 رسم ${category.children.length} فئة فرعية للفئة:`, category.name_ar);
                    html += this.renderHierarchy(category.children, level + 1);
                }
            });

            html += '</div>';
            console.log('✅ تم إنهاء رسم الهيكل الهرمي');
            return html;
        },

        // Action methods
        showAddModal: function() {
            console.log('📝 فتح نموذج إضافة فئة جديدة');
            alert('نموذج إضافة فئة جديدة - قيد التطوير');
        },

        editCategory: function(id) {
            console.log('✏️ تعديل الفئة:', id);
            alert('تعديل الفئة ' + id + ' - قيد التطوير');
        },

        toggleStatus: function(id) {
            console.log('🔄 تغيير حالة الفئة:', id);
            if (confirm('هل أنت متأكد من تغيير حالة هذه الفئة؟')) {
                alert('تغيير حالة الفئة ' + id + ' - قيد التطوير');
            }
        },

        deleteCategory: function(id) {
            console.log('🗑️ حذف الفئة:', id);
            if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
                alert('حذف الفئة ' + id + ' - قيد التطوير');
            }
        }
    };

    // Make main function globally available
    window.loadCategoriesManagementContent = function() {
        window.CategoriesManager.loadContent();
    };

    console.log('✅ تم تحميل ملف categories-simple.js بنجاح');

})();
