<?php
/**
 * Fix AI Settings Navigation Section Errors
 * Comprehensive solution for Security::init() and JSON parse failures in AI Settings
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح أخطاء إعدادات الذكاء الاصطناعي</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 12px;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .ai-provider {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .provider-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .provider-icon {
            margin-left: 10px;
            font-size: 20px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        .status-working {
            background: #d4edda;
            color: #155724;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 إصلاح أخطاء إعدادات الذكاء الاصطناعي</h1>
            <p>حل شامل لمشاكل Security::init() وJSON parse failures في قسم AI Settings</p>
        </div>

        <?php
        $allIssuesFixed = true;
        $fixedIssues = [];
        $remainingIssues = [];

        try {
            // Fix 1: Test AI API Endpoint
            echo '<div class="fix-section">';
            echo '<h3>🔍 إصلاح 1: اختبار AI API Endpoint</h3>';
            
            $aiAPIFile = '../php/api/ai.php';
            if (file_exists($aiAPIFile)) {
                echo '<div class="result pass">✅ ملف AI API موجود</div>';
                
                try {
                    // Test if file can be included without fatal errors
                    ob_start();
                    $errorOccurred = false;
                    $errorMessage = '';
                    
                    // Capture any errors
                    set_error_handler(function($severity, $message, $file, $line) use (&$errorOccurred, &$errorMessage) {
                        $errorOccurred = true;
                        $errorMessage .= "Error: $message in " . basename($file) . ":$line\n";
                    });
                    
                    // Set up clean environment for API test
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET = ['action' => 'get_settings'];
                    $_POST = [];
                    
                    // Include the AI API file
                    include $aiAPIFile;
                    
                    restore_error_handler();
                    $output = ob_get_clean();
                    
                    if (!$errorOccurred) {
                        echo '<div class="result pass">✅ AI API يتم تحميله بدون أخطاء Security::init()</div>';
                        $fixedIssues[] = 'AI API endpoint working';
                        
                        // Check if output is valid JSON
                        if (!empty($output)) {
                            $data = json_decode($output, true);
                            if ($data !== null) {
                                echo '<div class="result pass">✅ AI API يعطي استجابة JSON صالحة</div>';
                                $fixedIssues[] = 'AI API JSON response valid';
                            } else {
                                echo '<div class="result warning">⚠️ AI API استجابة ليست JSON صالحة</div>';
                                echo '<div class="code-block">Response: ' . htmlspecialchars(substr($output, 0, 200)) . '...</div>';
                            }
                        } else {
                            echo '<div class="result info">📋 AI API استجابة فارغة (طبيعي للاختبار)</div>';
                        }
                        
                    } else {
                        echo '<div class="result fail">❌ AI API أخطاء في التحميل</div>';
                        echo '<div class="code-block">' . htmlspecialchars($errorMessage) . '</div>';
                        $remainingIssues[] = 'AI API loading errors';
                        $allIssuesFixed = false;
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ AI API خطأ: ' . $e->getMessage() . '</div>';
                    $remainingIssues[] = 'AI API exception: ' . $e->getMessage();
                    $allIssuesFixed = false;
                }
                
            } else {
                echo '<div class="result fail">❌ ملف AI API غير موجود</div>';
                $remainingIssues[] = 'AI API file missing';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 2: Test Security Class Integration
            echo '<div class="fix-section">';
            echo '<h3>🔐 إصلاح 2: اختبار تكامل Security Class</h3>';
            
            try {
                require_once '../php/config.php';
                require_once '../php/security.php';
                
                if (class_exists('Security')) {
                    echo '<div class="result pass">✅ فئة Security متاحة</div>';
                    
                    if (method_exists('Security', 'init')) {
                        try {
                            Security::init();
                            echo '<div class="result pass">✅ Security::init() تعمل بنجاح</div>';
                            $fixedIssues[] = 'Security::init() working';
                        } catch (Exception $e) {
                            echo '<div class="result fail">❌ خطأ في Security::init(): ' . $e->getMessage() . '</div>';
                            $remainingIssues[] = 'Security::init() error';
                            $allIssuesFixed = false;
                        }
                    } else {
                        echo '<div class="result fail">❌ دالة Security::init() مفقودة</div>';
                        $remainingIssues[] = 'Security::init() method missing';
                        $allIssuesFixed = false;
                    }
                } else {
                    echo '<div class="result fail">❌ فئة Security غير موجودة</div>';
                    $remainingIssues[] = 'Security class missing';
                    $allIssuesFixed = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في تحميل Security: ' . $e->getMessage() . '</div>';
                $remainingIssues[] = 'Security loading error';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 3: Test AI Settings Database Table
            echo '<div class="fix-section">';
            echo '<h3>🗄️ إصلاح 3: اختبار جدول AI Settings</h3>';
            
            try {
                if (isset($conn) && $conn instanceof PDO) {
                    echo '<div class="result pass">✅ اتصال قاعدة البيانات متاح</div>';
                    
                    // Check if ai_settings table exists
                    $stmt = $conn->query("SHOW TABLES LIKE 'ai_settings'");
                    if ($stmt->rowCount() > 0) {
                        echo '<div class="result pass">✅ جدول ai_settings موجود</div>';
                        
                        // Check table structure
                        $stmt = $conn->query("DESCRIBE ai_settings");
                        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        echo '<div class="result info">📊 أعمدة الجدول: ' . implode(', ', $columns) . '</div>';
                        
                        $fixedIssues[] = 'AI settings table exists';
                    } else {
                        echo '<div class="result warning">⚠️ جدول ai_settings غير موجود - جاري الإنشاء...</div>';
                        
                        // Create ai_settings table
                        $createTableSQL = "
                        CREATE TABLE IF NOT EXISTS ai_settings (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            provider VARCHAR(50) NOT NULL,
                            api_key TEXT,
                            model VARCHAR(100),
                            max_tokens INT DEFAULT 1000,
                            temperature DECIMAL(3,2) DEFAULT 0.7,
                            is_active BOOLEAN DEFAULT TRUE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            UNIQUE KEY unique_provider (provider)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                        ";
                        
                        $conn->exec($createTableSQL);
                        echo '<div class="result pass">✅ تم إنشاء جدول ai_settings</div>';
                        
                        // Insert default settings
                        $defaultSettings = [
                            ['openai', '', 'gpt-3.5-turbo', 1000, 0.7, 1],
                            ['anthropic', '', 'claude-3-sonnet', 1000, 0.7, 0],
                            ['gemini', '', 'gemini-pro', 1000, 0.7, 0]
                        ];
                        
                        $insertStmt = $conn->prepare("
                            INSERT IGNORE INTO ai_settings (provider, api_key, model, max_tokens, temperature, is_active) 
                            VALUES (?, ?, ?, ?, ?, ?)
                        ");
                        
                        foreach ($defaultSettings as $setting) {
                            $insertStmt->execute($setting);
                        }
                        
                        echo '<div class="result pass">✅ تم إدراج الإعدادات الافتراضية</div>';
                        $fixedIssues[] = 'AI settings table created with defaults';
                    }
                    
                } else {
                    echo '<div class="result fail">❌ اتصال قاعدة البيانات غير متاح</div>';
                    $remainingIssues[] = 'Database connection unavailable';
                    $allIssuesFixed = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
                $remainingIssues[] = 'Database error: ' . $e->getMessage();
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 4: Test AI Provider Settings
            echo '<div class="fix-section">';
            echo '<h3>🤖 إصلاح 4: اختبار إعدادات مزودي الذكاء الاصطناعي</h3>';
            
            $aiProviders = [
                'openai' => [
                    'name' => 'OpenAI',
                    'icon' => '🧠',
                    'models' => ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo']
                ],
                'anthropic' => [
                    'name' => 'Anthropic Claude',
                    'icon' => '🎭',
                    'models' => ['claude-3-sonnet', 'claude-3-opus', 'claude-3-haiku']
                ],
                'gemini' => [
                    'name' => 'Google Gemini',
                    'icon' => '💎',
                    'models' => ['gemini-pro', 'gemini-pro-vision']
                ]
            ];
            
            foreach ($aiProviders as $providerId => $provider) {
                echo '<div class="ai-provider">';
                echo '<div class="provider-title">';
                echo '<span class="provider-icon">' . $provider['icon'] . '</span>';
                echo $provider['name'];
                echo '</div>';
                
                try {
                    // Test if provider settings can be retrieved
                    $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE provider = ?");
                    $stmt->execute([$providerId]);
                    $settings = $stmt->fetch();
                    
                    if ($settings) {
                        echo '<span class="status-badge status-working">✅ متاح</span>';
                        echo '<div class="result info">📋 النموذج: ' . $settings['model'] . '</div>';
                        echo '<div class="result info">📊 Max Tokens: ' . $settings['max_tokens'] . '</div>';
                        echo '<div class="result info">🌡️ Temperature: ' . $settings['temperature'] . '</div>';
                    } else {
                        echo '<span class="status-badge status-failed">❌ غير متاح</span>';
                        echo '<div class="result warning">⚠️ إعدادات المزود غير موجودة</div>';
                    }
                    
                } catch (Exception $e) {
                    echo '<span class="status-badge status-failed">❌ خطأ</span>';
                    echo '<div class="result fail">❌ خطأ في استرجاع الإعدادات: ' . $e->getMessage() . '</div>';
                }
                
                echo '</div>';
            }
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
            $allIssuesFixed = false;
        }

        // Summary
        echo '<div class="fix-section">';
        echo '<h3>📊 ملخص إصلاحات AI Settings</h3>';
        
        if ($allIssuesFixed) {
            echo '<div class="result pass">🎉 تم إصلاح جميع مشاكل AI Settings!</div>';
            echo '<div class="result pass">✅ قسم إعدادات الذكاء الاصطناعي جاهز للاستخدام</div>';
        } else {
            echo '<div class="result warning">⚠️ تم إصلاح معظم المشاكل، بعض المشاكل تحتاج تدخل يدوي</div>';
        }
        
        if (!empty($fixedIssues)) {
            echo '<h4>✅ المشاكل المُصلحة:</h4>';
            echo '<ul>';
            foreach ($fixedIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        if (!empty($remainingIssues)) {
            echo '<h4>⚠️ المشاكل المتبقية:</h4>';
            echo '<ul>';
            foreach ($remainingIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<p><a href="index.html" class="fix-button">🏠 فتح لوحة التحكم</a></p>';
        echo '<p><a href="ai-settings.html" class="fix-button">🤖 اختبار AI Settings</a></p>';
        echo '<p><a href="test-admin-sidebar-menu.php" class="fix-button">🎛️ اختبار قائمة الشريط الجانبي</a></p>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Test AI Settings API via JavaScript
        async function testAISettingsAPI() {
            console.log('🤖 اختبار AI Settings API...');
            
            try {
                // Test AI settings endpoint
                const response = await fetch('../php/api/ai.php?action=get_settings');
                console.log('AI Settings API response status:', response.status);
                
                if (response.status < 500) {
                    console.log('✅ AI Settings API: لا يوجد خطأ 500');
                    
                    try {
                        const data = await response.json();
                        console.log('✅ AI Settings API: استجابة JSON صالحة', data);
                    } catch (jsonError) {
                        console.log('⚠️ AI Settings API: استجابة ليست JSON', jsonError.message);
                    }
                } else {
                    console.log('❌ AI Settings API: خطأ 500');
                }
                
                // Test OpenAI connection testing
                const testResponse = await fetch('../php/api/ai.php?action=test_connection&provider=openai');
                console.log('OpenAI test response status:', testResponse.status);
                
                if (testResponse.status < 500) {
                    console.log('✅ OpenAI testing: لا يوجد خطأ 500');
                } else {
                    console.log('❌ OpenAI testing: خطأ 500');
                }
                
            } catch (error) {
                console.log('❌ AI Settings API test error:', error.message);
            }
        }
        
        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', testAISettingsAPI);
    </script>
</body>
</html>
