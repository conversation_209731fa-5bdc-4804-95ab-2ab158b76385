<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات المتجر</title>
    <link rel="stylesheet" href="css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .settings-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .settings-section {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .settings-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tooltip-icon {
            color: #6c757d;
            cursor: help;
        }

        .form-control {
            border-radius: 4px;
            border: 1px solid #ced4da;
            padding: 0.5rem 0.75rem;
        }

        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .form-check {
            padding-right: 1.75rem;
            padding-left: 0;
        }

        .logo-upload {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .logo-preview {
            max-width: 200px;
            margin: 10px auto;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-spinner {
            text-align: center;
        }

        .loading-spinner i {
            font-size: 3rem;
            color: #007bff;
            margin-bottom: 1rem;
        }

        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltip-text {
            visibility: hidden;
            background-color: #333;
            color: #fff;
            text-align: center;
            padding: 5px 10px;
            border-radius: 6px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
        }

        .tooltip:hover .tooltip-text {
            visibility: visible;
        }

        @media (max-width: 768px) {
            .settings-container {
                padding: 10px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .action-buttons button {
                width: 100%;
                margin-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <h2 class="mb-4">إعدادات المتجر</h2>
        
        <div id="validationErrors"></div>

        <form id="storeSettingsForm">
            <!-- General Settings -->
            <div class="settings-section">
                <h3>الإعدادات العامة</h3>
                <div class="form-group">
                    <label class="form-label" for="general.storeName">
                        اسم المتجر
                        <i class="fas fa-info-circle tooltip-icon" data-tooltip="اسم متجرك الذي سيظهر للعملاء"></i>
                    </label>
                    <input type="text" class="form-control" id="general.storeName" name="general.storeName" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="general.description">وصف المتجر</label>
                    <textarea class="form-control" id="general.description" name="general.description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" for="general.email">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="general.email" name="general.email" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="general.phone">رقم الهاتف</label>
                    <input type="tel" class="form-control" id="general.phone" name="general.phone" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="general.address">العنوان</label>
                    <textarea class="form-control" id="general.address" name="general.address" rows="2"></textarea>
                </div>
            </div>

            <!-- Payment Settings -->
            <div class="settings-section">
                <h3>إعدادات الدفع</h3>
                <div class="form-group">
                    <label class="form-label" for="payment.currency">العملة</label>
                    <select class="form-control" id="payment.currency" name="payment.currency">
                        <option value="SAR">ريال سعودي (SAR)</option>
                        <option value="USD">دولار أمريكي (USD)</option>
                        <option value="EUR">يورو (EUR)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="payment.taxRate">
                        نسبة الضريبة (%)
                        <i class="fas fa-info-circle tooltip-icon" data-tooltip="نسبة ضريبة القيمة المضافة التي سيتم تطبيقها على المبيعات"></i>
                    </label>
                    <input type="number" class="form-control" id="payment.taxRate" name="payment.taxRate" min="0" max="100">
                </div>
                <div class="form-group">
                    <label class="form-label">طرق الدفع المتاحة</label>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="payment.paymentMethods.cash" name="payment.paymentMethods.cash">
                        <label class="form-check-label" for="payment.paymentMethods.cash">الدفع عند الاستلام</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="payment.paymentMethods.bankTransfer" name="payment.paymentMethods.bankTransfer">
                        <label class="form-check-label" for="payment.paymentMethods.bankTransfer">تحويل بنكي</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="payment.paymentMethods.creditCard" name="payment.paymentMethods.creditCard">
                        <label class="form-check-label" for="payment.paymentMethods.creditCard">بطاقة ائتمان</label>
                    </div>
                </div>
            </div>

            <!-- Shipping Settings -->
            <div class="settings-section">
                <h3>إعدادات الشحن</h3>
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="shipping.enableShipping" name="shipping.enableShipping">
                        <label class="form-check-label" for="shipping.enableShipping">تفعيل الشحن</label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="shipping.defaultShippingRate">
                        تكلفة الشحن الافتراضية
                        <i class="fas fa-info-circle tooltip-icon" data-tooltip="التكلفة الأساسية للشحن"></i>
                    </label>
                    <input type="number" class="form-control" id="shipping.defaultShippingRate" name="shipping.defaultShippingRate" min="0">
                </div>
                <div class="form-group">
                    <label class="form-label" for="shipping.freeShippingThreshold">
                        حد الشحن المجاني
                        <i class="fas fa-info-circle tooltip-icon" data-tooltip="قيمة الطلب التي يصبح عندها الشحن مجانياً"></i>
                    </label>
                    <input type="number" class="form-control" id="shipping.freeShippingThreshold" name="shipping.freeShippingThreshold" min="0">
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="settings-section">
                <h3>إعدادات الإشعارات</h3>
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="notification.orderConfirmation" name="notification.orderConfirmation">
                        <label class="form-check-label" for="notification.orderConfirmation">تأكيد الطلب</label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="notification.shipmentUpdates" name="notification.shipmentUpdates">
                        <label class="form-check-label" for="notification.shipmentUpdates">تحديثات الشحن</label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="notification.stockAlerts" name="notification.stockAlerts">
                        <label class="form-check-label" for="notification.stockAlerts">تنبيهات المخزون</label>
                    </div>
                </div>
            </div>

            <!-- Display Settings -->
            <div class="settings-section">
                <h3>إعدادات العرض</h3>
                <div class="form-group">
                    <label class="form-label" for="display.theme">السمة</label>
                    <select class="form-control" id="display.theme" name="display.theme">
                        <option value="default">الافتراضية</option>
                        <option value="dark">داكنة</option>
                        <option value="light">فاتحة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="display.productsPerPage">
                        عدد المنتجات في الصفحة
                        <i class="fas fa-info-circle tooltip-icon" data-tooltip="عدد المنتجات التي ستظهر في كل صفحة"></i>
                    </label>
                    <input type="number" class="form-control" id="display.productsPerPage" name="display.productsPerPage" min="1" max="100">
                </div>
                <div class="form-group">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="display.showOutOfStock" name="display.showOutOfStock">
                        <label class="form-check-label" for="display.showOutOfStock">عرض المنتجات غير المتوفرة</label>
                    </div>
                </div>
            </div>

            <!-- Social Media Settings -->
            <div class="settings-section">
                <h3>وسائل التواصل الاجتماعي</h3>
                <div class="form-group">
                    <label class="form-label" for="social.facebook">فيسبوك</label>
                    <input type="url" class="form-control" id="social.facebook" name="social.facebook" placeholder="https://facebook.com/your-page">
                </div>
                <div class="form-group">
                    <label class="form-label" for="social.twitter">تويتر</label>
                    <input type="url" class="form-control" id="social.twitter" name="social.twitter" placeholder="https://twitter.com/your-handle">
                </div>
                <div class="form-group">
                    <label class="form-label" for="social.instagram">انستغرام</label>
                    <input type="url" class="form-control" id="social.instagram" name="social.instagram" placeholder="https://instagram.com/your-profile">
                </div>
                <div class="form-group">
                    <label class="form-label" for="social.whatsapp">واتساب</label>
                    <input type="tel" class="form-control" id="social.whatsapp" name="social.whatsapp" placeholder="+966xxxxxxxxx">
                </div>
            </div>

            <!-- Business Information -->
            <div class="settings-section">
                <h3>معلومات الشركة</h3>
                <div class="form-group">
                    <label class="form-label" for="business.companyName">اسم الشركة</label>
                    <input type="text" class="form-control" id="business.companyName" name="business.companyName">
                </div>
                <div class="form-group">
                    <label class="form-label" for="business.vatNumber">
                        رقم ضريبة القيمة المضافة
                        <i class="fas fa-info-circle tooltip-icon" data-tooltip="رقم التسجيل الضريبي المكون من 15 رقم"></i>
                    </label>
                    <input type="text" class="form-control" id="business.vatNumber" name="business.vatNumber" pattern="[0-9]{15}">
                </div>
                <div class="form-group">
                    <label class="form-label" for="business.crNumber">
                        رقم السجل التجاري
                        <i class="fas fa-info-circle tooltip-icon" data-tooltip="رقم السجل التجاري المكون من 10 أرقام"></i>
                    </label>
                    <input type="text" class="form-control" id="business.crNumber" name="business.crNumber" pattern="[0-9]{10}">
                </div>
            </div>

            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
                <button type="button" id="resetSettings" class="btn btn-outline-secondary">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
            </div>
        </form>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/settings-core.js"></script>
    <script src="js/store-settings.js"></script>
</body>
</html>