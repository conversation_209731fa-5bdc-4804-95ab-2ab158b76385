<?php
/**
 * Populate Roles Table
 * Adds default roles to the roles table
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Error handling
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    require_once '../config/database.php';
    
    // Get database connection
    $pdo = getDatabaseConnection();
    
    $result = [
        'success' => false,
        'message' => '',
        'inserted_roles' => [],
        'existing_roles' => [],
        'errors' => []
    ];
    
    // Check current roles
    $stmt = $pdo->query("SELECT name, display_name, display_name_ar FROM roles");
    $existingRoles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $existingRoleNames = array_column($existingRoles, 'name');
    
    $result['existing_roles'] = $existingRoles;
    
    // Define default roles
    $defaultRoles = [
        [
            'name' => 'admin',
            'display_name' => 'Administrator',
            'display_name_ar' => 'مدير النظام',
            'description' => 'Full system access with all permissions',
            'permissions' => json_encode([
                'users' => ['create', 'read', 'update', 'delete'],
                'products' => ['create', 'read', 'update', 'delete'],
                'orders' => ['create', 'read', 'update', 'delete'],
                'roles' => ['create', 'read', 'update', 'delete'],
                'settings' => ['read', 'update'],
                'reports' => ['read']
            ]),
            'is_active' => 1
        ],
        [
            'name' => 'seller',
            'display_name' => 'Seller',
            'display_name_ar' => 'بائع',
            'description' => 'Can manage own products and orders',
            'permissions' => json_encode([
                'products' => ['create', 'read', 'update', 'delete'],
                'orders' => ['read', 'update'],
                'reports' => ['read']
            ]),
            'is_active' => 1
        ],
        [
            'name' => 'user',
            'display_name' => 'User',
            'display_name_ar' => 'مستخدم',
            'description' => 'Basic user access for customers',
            'permissions' => json_encode([
                'products' => ['read'],
                'orders' => ['create', 'read']
            ]),
            'is_active' => 1
        ],
        [
            'name' => 'moderator',
            'display_name' => 'Moderator',
            'display_name_ar' => 'مشرف',
            'description' => 'Can moderate content and manage users',
            'permissions' => json_encode([
                'users' => ['read', 'update'],
                'products' => ['read', 'update'],
                'orders' => ['read', 'update'],
                'reports' => ['read']
            ]),
            'is_active' => 1
        ],
        [
            'name' => 'editor',
            'display_name' => 'Editor',
            'display_name_ar' => 'محرر',
            'description' => 'Can edit content and manage products',
            'permissions' => json_encode([
                'products' => ['create', 'read', 'update'],
                'orders' => ['read'],
                'reports' => ['read']
            ]),
            'is_active' => 1
        ]
    ];
    
    // Insert missing roles
    $insertStmt = $pdo->prepare("
        INSERT INTO roles (name, display_name, display_name_ar, description, permissions, is_active) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $insertedCount = 0;
    $updatedCount = 0;
    
    foreach ($defaultRoles as $role) {
        if (!in_array($role['name'], $existingRoleNames)) {
            // Insert new role
            try {
                $insertStmt->execute([
                    $role['name'],
                    $role['display_name'],
                    $role['display_name_ar'],
                    $role['description'],
                    $role['permissions'],
                    $role['is_active']
                ]);
                
                $result['inserted_roles'][] = $role['name'];
                $insertedCount++;
                
            } catch (Exception $e) {
                $result['errors'][] = "Failed to insert role '{$role['name']}': " . $e->getMessage();
            }
        } else {
            // Update existing role if it has empty fields
            try {
                $updateStmt = $pdo->prepare("
                    UPDATE roles 
                    SET display_name = COALESCE(NULLIF(display_name, ''), ?),
                        display_name_ar = COALESCE(NULLIF(display_name_ar, ''), ?),
                        description = COALESCE(NULLIF(description, ''), ?),
                        permissions = COALESCE(permissions, ?),
                        is_active = COALESCE(is_active, ?)
                    WHERE name = ?
                ");
                
                $updateStmt->execute([
                    $role['display_name'],
                    $role['display_name_ar'],
                    $role['description'],
                    $role['permissions'],
                    $role['is_active'],
                    $role['name']
                ]);
                
                if ($updateStmt->rowCount() > 0) {
                    $updatedCount++;
                }
                
            } catch (Exception $e) {
                $result['errors'][] = "Failed to update role '{$role['name']}': " . $e->getMessage();
            }
        }
    }
    
    // Get final roles count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM roles");
    $totalRoles = $stmt->fetch()['total'];
    
    // Get updated roles list
    $stmt = $pdo->query("SELECT name, display_name, display_name_ar, is_active FROM roles ORDER BY name");
    $finalRoles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result['success'] = true;
    $result['message'] = "Roles population completed. Inserted: $insertedCount, Updated: $updatedCount, Total: $totalRoles";
    $result['final_roles'] = $finalRoles;
    $result['statistics'] = [
        'inserted' => $insertedCount,
        'updated' => $updatedCount,
        'total' => $totalRoles,
        'errors' => count($result['errors'])
    ];
    
} catch (Exception $e) {
    $result = [
        'success' => false,
        'message' => 'Failed to populate roles: ' . $e->getMessage(),
        'error' => $e->getMessage(),
        'inserted_roles' => [],
        'existing_roles' => [],
        'errors' => [$e->getMessage()]
    ];
}

echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
