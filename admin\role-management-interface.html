<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأدوار والصلاحيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: #f8f9fa;
        }
        .role-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .role-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .role-header {
            border-radius: 12px 12px 0 0;
            padding: 20px;
            color: white;
            position: relative;
        }
        .role-level {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
        }
        .permission-badge {
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
            display: inline-block;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn-role {
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-user-shield text-primary"></i> إدارة الأدوار والصلاحيات</h2>
                        <p class="text-muted">إدارة أدوار المستخدمين وصلاحياتهم في النظام</p>
                    </div>
                    <div>
                        <button class="btn btn-primary btn-role" onclick="showCreateRoleModal()">
                            <i class="fas fa-plus"></i> إضافة دور جديد
                        </button>
                        <button class="btn btn-outline-secondary btn-role" onclick="refreshRoles()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5>إجمالي الأدوار</h5>
                                    <h3 id="totalRoles">--</h3>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5>إجمالي الصلاحيات</h5>
                                    <h3 id="totalPermissions">--</h3>
                                </div>
                                <i class="fas fa-key fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5>المستخدمين النشطين</h5>
                                    <h3 id="activeUsers">--</h3>
                                </div>
                                <i class="fas fa-user-check fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5>آخر تحديث</h5>
                                    <h6 id="lastUpdate">--</h6>
                                </div>
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Roles Grid -->
                <div id="rolesContainer">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-3">جاري تحميل الأدوار...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create/Edit Role Modal -->
    <div class="modal fade" id="roleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="roleModalTitle">إضافة دور جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="roleForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الدور (بالإنجليزية)</label>
                                    <input type="text" class="form-control" id="roleName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المستوى</label>
                                    <input type="number" class="form-control" id="roleLevel" min="1" max="10" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم بالعربية</label>
                                    <input type="text" class="form-control" id="roleDisplayNameAr" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم بالإنجليزية</label>
                                    <input type="text" class="form-control" id="roleDisplayNameEn" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اللون</label>
                                    <input type="color" class="form-control form-control-color" id="roleColor" value="#007bff">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الأيقونة</label>
                                    <input type="text" class="form-control" id="roleIcon" placeholder="fas fa-user">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف بالعربية</label>
                            <textarea class="form-control" id="roleDescriptionAr" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف بالإنجليزية</label>
                            <textarea class="form-control" id="roleDescriptionEn" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div id="permissionsContainer" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                <div class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل الصلاحيات...
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveRole()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Role Details Modal -->
    <div class="modal fade" id="roleDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الدور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="roleDetailsContent">
                    <!-- Role details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="editCurrentRole()">تعديل</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentRoles = [];
        let currentPermissions = [];
        let currentRoleId = null;

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            loadRoles();
            loadPermissions();
        });

        // Load all roles
        async function loadRoles() {
            try {
                const response = await fetch('php/role-management-api.php?action=roles');
                const data = await response.json();
                
                if (data.success) {
                    currentRoles = data.data;
                    displayRoles(currentRoles);
                    updateStatistics();
                } else {
                    showError('خطأ في تحميل الأدوار: ' + data.message);
                }
            } catch (error) {
                showError('خطأ في الاتصال: ' + error.message);
            }
        }

        // Load all permissions
        async function loadPermissions() {
            try {
                const response = await fetch('php/role-management-api.php?action=permissions');
                const data = await response.json();
                
                if (data.success) {
                    currentPermissions = data.data;
                } else {
                    console.error('خطأ في تحميل الصلاحيات:', data.message);
                }
            } catch (error) {
                console.error('خطأ في تحميل الصلاحيات:', error);
            }
        }

        // Display roles in grid
        function displayRoles(roles) {
            const container = document.getElementById('rolesContainer');
            
            if (!roles || roles.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أدوار</h5>
                        <p class="text-muted">ابدأ بإضافة دور جديد للنظام</p>
                        <button class="btn btn-primary" onclick="showCreateRoleModal()">
                            <i class="fas fa-plus"></i> إضافة دور جديد
                        </button>
                    </div>
                `;
                return;
            }

            const rolesHtml = roles.map(role => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card role-card h-100">
                        <div class="role-header" style="background: ${role.color}">
                            <div class="role-level">مستوى ${role.level}</div>
                            <div class="d-flex align-items-center">
                                <i class="${role.icon} fa-2x me-3"></i>
                                <div>
                                    <h5 class="mb-1">${role.display_name_ar}</h5>
                                    <small class="opacity-75">${role.display_name_en}</small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small mb-3">${role.description_ar || 'لا يوجد وصف'}</p>
                            <div class="mb-3">
                                <small class="text-muted">الصلاحيات (${role.permissions_count}):</small>
                                <div class="mt-2">
                                    ${role.permissions ? role.permissions.slice(0, 3).map(p => 
                                        `<span class="permission-badge">${p.display_name_ar}</span>`
                                    ).join('') : ''}
                                    ${role.permissions_count > 3 ? `<span class="permission-badge">+${role.permissions_count - 3} أخرى</span>` : ''}
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-users"></i> ${role.users_count} مستخدم
                                </small>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewRoleDetails(${role.id})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="editRole(${role.id})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = `<div class="row">${rolesHtml}</div>`;
        }

        // Update statistics
        function updateStatistics() {
            document.getElementById('totalRoles').textContent = currentRoles.length;
            document.getElementById('totalPermissions').textContent = Object.keys(currentPermissions).reduce((total, category) => total + currentPermissions[category].length, 0);
            document.getElementById('activeUsers').textContent = currentRoles.reduce((total, role) => total + parseInt(role.users_count), 0);
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('ar-EG');
        }

        // Show create role modal
        function showCreateRoleModal() {
            currentRoleId = null;
            document.getElementById('roleModalTitle').textContent = 'إضافة دور جديد';
            document.getElementById('roleForm').reset();
            document.getElementById('roleColor').value = '#007bff';
            loadPermissionsInModal();
            new bootstrap.Modal(document.getElementById('roleModal')).show();
        }

        // Load permissions in modal
        function loadPermissionsInModal(selectedPermissions = []) {
            const container = document.getElementById('permissionsContainer');
            
            if (!currentPermissions || Object.keys(currentPermissions).length === 0) {
                container.innerHTML = '<div class="text-muted">لا توجد صلاحيات متاحة</div>';
                return;
            }

            let html = '';
            for (const [category, permissions] of Object.entries(currentPermissions)) {
                html += `
                    <div class="mb-3">
                        <h6 class="text-primary">${category}</h6>
                        <div class="row">
                `;
                
                permissions.forEach(permission => {
                    const isChecked = selectedPermissions.includes(permission.id);
                    html += `
                        <div class="col-md-6 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="${permission.id}" 
                                       id="perm_${permission.id}" ${isChecked ? 'checked' : ''}>
                                <label class="form-check-label" for="perm_${permission.id}">
                                    ${permission.display_name_ar}
                                </label>
                            </div>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        // Refresh roles
        function refreshRoles() {
            loadRoles();
        }

        // Show error message
        function showError(message) {
            const container = document.getElementById('rolesContainer');
            container.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                    <button class="btn btn-sm btn-outline-danger ms-2" onclick="refreshRoles()">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        }

        // Show success message
        function showSuccess(message) {
            // You can implement a toast notification here
            console.log('Success:', message);
        }

        // Placeholder functions for future implementation
        function viewRoleDetails(roleId) {
            console.log('View role details:', roleId);
        }

        function editRole(roleId) {
            console.log('Edit role:', roleId);
        }

        function editCurrentRole() {
            console.log('Edit current role');
        }

        function saveRole() {
            console.log('Save role');
        }
    </script>
</body>
</html>
