/**
 * Unified Sidebar Menu Styles
 * Styles unifiés pour le menu sidebar - Résolution définitive des problèmes d'affichage
 * Version: 2.0 - Consolidation complète
 */

/* =================================================================
   SIDEBAR BASE STYLES - Styles de base du sidebar
   ================================================================= */

.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 100;
    top: 0;
    right: 0;
}

.sidebar .logo {
    padding: 30px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.sidebar .logo h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #ffffff;
    text-align: center;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* =================================================================
   NAVIGATION STYLES - Styles de navigation
   ================================================================= */

.admin-nav {
    padding: 20px 0;
}

.admin-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-nav ul li {
    margin: 8px 15px;
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 15px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    position: relative;
    font-weight: 500;
    background: transparent;
}

.admin-nav ul li:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-nav ul li.active {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.admin-nav ul li.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: #ffffff;
    border-radius: 2px 0 0 2px;
}

.admin-nav ul li i {
    font-size: 1.1rem;
    width: 24px;
    text-align: center;
    opacity: 0.9;
    flex-shrink: 0;
}

.admin-nav ul li span {
    font-size: 0.95rem;
    font-weight: 500;
    flex: 1;
    text-align: right;
}

/* =================================================================
   ADMIN SETTINGS MENU - Menu des paramètres d'administration
   ================================================================= */

.admin-settings-menu {
    margin: 8px 15px !important;
    padding: 0 !important;
    background: none !important;
    border-radius: 12px !important;
    overflow: visible !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: block !important;
    width: calc(100% - 30px) !important;
    position: relative;
}

/* HEADER DU MENU ADMIN SETTINGS */
.admin-settings-header {
    padding: 16px 20px;
    cursor: pointer;
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    color: rgba(255, 255, 255, 0.95);
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
    visibility: visible !important;
    opacity: 1 !important;
}

.admin-settings-header:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    color: #ffffff;
    transform: translateX(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.admin-settings-header-content {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 12px;
}

.admin-settings-header i:first-child {
    font-size: 1.2rem;
    width: 28px;
    margin-left: 0;
    flex-shrink: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    color: rgba(255, 255, 255, 0.95);
}

.admin-settings-header span {
    font-size: 1rem;
    font-weight: 600;
    text-align: right;
    flex: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
    color: rgba(255, 255, 255, 0.95);
}

.admin-settings-arrow {
    font-size: 0.9rem !important;
    width: 20px !important;
    margin-left: 0 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    opacity: 0.8;
    transform: rotate(180deg) !important; /* Toujours en position étendue */
    color: rgba(255, 255, 255, 0.8);
}

/* =================================================================
   SUBMENU STYLES - FORCE TOUJOURS VISIBLE
   ================================================================= */

.admin-settings-submenu {
    /* FORCE L'AFFICHAGE DU SUBMENU */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
    position: static !important;
    
    /* STYLES VISUELS */
    list-style: none !important;
    padding: 12px 0 16px 0 !important;
    margin: 0 !important;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.15) 100%) !important;
    border-radius: 0 0 12px 12px !important;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    width: 100% !important;
    
    /* RESET DE TOUS LES OVERRIDES POSSIBLES */
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    z-index: 10 !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
    transition: none !important;
}

/* ITEMS DU SUBMENU - FORCE TOUJOURS VISIBLE */
.admin-settings-submenu li {
    /* FORCE L'AFFICHAGE DES ITEMS */
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    
    /* DIMENSIONS ET ESPACEMENT */
    width: calc(100% - 24px) !important;
    height: auto !important;
    min-height: 50px !important;
    margin: 4px 12px !important;
    padding: 14px 18px !important;
    
    /* STYLES VISUELS */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 10px !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    cursor: pointer !important;
    
    /* FLEXBOX */
    align-items: center !important;
    justify-content: flex-start !important;
    flex-direction: row !important;
    gap: 12px !important;
    
    /* RESET DE TOUS LES OVERRIDES POSSIBLES */
    box-sizing: border-box !important;
    z-index: 11 !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    
    /* RESET POSITIONING */
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

/* ICÔNES DES ITEMS */
.admin-settings-submenu li i {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 1.1rem !important;
    width: 24px !important;
    height: auto !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-align: center !important;
    flex-shrink: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: static !important;
    z-index: 13 !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
}

/* TEXTE DES ITEMS */
.admin-settings-submenu li span {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-align: right !important;
    flex: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: static !important;
    width: auto !important;
    height: auto !important;
    z-index: 13 !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
    white-space: nowrap !important;
    overflow: visible !important;
    text-overflow: clip !important;
}

/* =================================================================
   ÉTATS INTERACTIFS - États hover et active
   ================================================================= */

.admin-settings-submenu li:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    color: #ffffff !important;
    transform: translateX(-4px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

.admin-settings-submenu li:hover i,
.admin-settings-submenu li:hover span {
    color: #ffffff !important;
}

.admin-settings-submenu li.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.12) 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4) !important;
    transform: translateX(-2px) !important;
}

.admin-settings-submenu li.active i,
.admin-settings-submenu li.active span {
    color: #ffffff !important;
}

.admin-settings-submenu li.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: #ffffff;
    border-radius: 2px;
}

/* =================================================================
   RESPONSIVE DESIGN - Design responsive
   ================================================================= */

@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -280px;
        width: 280px;
        height: 100vh;
        z-index: 1000;
        transition: right 0.3s ease;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar.mobile-open {
        right: 0;
    }

    .admin-settings-menu {
        margin: 8px 10px !important;
        width: calc(100% - 20px) !important;
    }

    .admin-settings-submenu li {
        margin: 3px 8px !important;
        padding: 12px 15px !important;
        min-height: 45px !important;
    }

    .admin-settings-submenu li span {
        font-size: 0.85rem !important;
    }
}

/* =================================================================
   OVERRIDES CRITIQUES - Surcharge de tous les autres CSS
   ================================================================= */

/* Surcharge de admin.css */
.admin-nav .admin-settings-submenu,
.sidebar .admin-settings-submenu,
nav .admin-settings-submenu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
    overflow: visible !important;
}

/* Surcharge de admin-settings-menu-enhanced.css */
.admin-settings-menu .admin-settings-submenu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
    overflow: visible !important;
}

/* Surcharge de final-submenu-fix.css */
.admin-settings-submenu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force l'état étendu par défaut - Le menu est toujours considéré comme étendu */

.admin-settings-menu .admin-settings-arrow {
    transform: rotate(180deg) !important;
}

/* =================================================================
   ANIMATIONS ET TRANSITIONS
   ================================================================= */

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.admin-settings-submenu li {
    animation: slideDown 0.3s ease-out;
}

.admin-settings-submenu li:nth-child(1) { animation-delay: 0.05s; }
.admin-settings-submenu li:nth-child(2) { animation-delay: 0.1s; }
.admin-settings-submenu li:nth-child(3) { animation-delay: 0.15s; }
.admin-settings-submenu li:nth-child(4) { animation-delay: 0.2s; }
.admin-settings-submenu li:nth-child(5) { animation-delay: 0.25s; }
.admin-settings-submenu li:nth-child(6) { animation-delay: 0.3s; }
.admin-settings-submenu li:nth-child(7) { animation-delay: 0.35s; }
.admin-settings-submenu li:nth-child(8) { animation-delay: 0.4s; }
.admin-settings-submenu li:nth-child(9) { animation-delay: 0.45s; }
.admin-settings-submenu li:nth-child(10) { animation-delay: 0.5s; }

/* =================================================================
   FIN DU FICHIER
   ================================================================= */