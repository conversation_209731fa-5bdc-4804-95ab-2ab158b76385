<?php
/**
 * Setup Stores Table for Store Management System
 * Creates the stores table with all necessary fields for managing user stores
 */

// Security check
define('SECURITY_CHECK', true);

require_once '../php/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد جدول المتاجر</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 5px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    </style>
</head>
<body>";

echo "<h1>🏪 إعداد نظام إدارة المتاجر</h1>";

try {
    $pdo = getPDOConnection();
    
    // Check if stores table already exists
    echo "<h2>📋 فحص الجدول الحالي</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'stores'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<div class='warning'>⚠️ جدول المتاجر موجود بالفعل</div>";
        
        // Show current structure
        $stmt = $pdo->query("DESCRIBE stores");
        $columns = $stmt->fetchAll();
        
        echo "<h3>📊 هيكل الجدول الحالي:</h3>";
        echo "<table>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='info'>ℹ️ جدول المتاجر غير موجود - سيتم إنشاؤه</div>";
    }
    
    // Create or update stores table
    echo "<h2>🔧 إنشاء/تحديث جدول المتاجر</h2>";
    
    $createStoresSQL = "
        CREATE TABLE IF NOT EXISTS stores (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            store_name VARCHAR(255) NOT NULL,
            store_slug VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            logo_url VARCHAR(500),
            status ENUM('active', 'blocked', 'pending', 'suspended') DEFAULT 'pending',
            domain VARCHAR(255),
            theme VARCHAR(100) DEFAULT 'default',
            settings JSON,
            total_products INT DEFAULT 0,
            total_orders INT DEFAULT 0,
            total_revenue DECIMAL(10,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_store_slug (store_slug),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createStoresSQL);
    echo "<div class='success'>✅ تم إنشاء/تحديث جدول المتاجر بنجاح</div>";
    
    // Add sample data if table is empty
    echo "<h2>📝 إضافة بيانات تجريبية</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM stores");
    $storeCount = $stmt->fetch()['count'];
    
    if ($storeCount == 0) {
        echo "<div class='info'>ℹ️ إضافة بيانات تجريبية للاختبار...</div>";
        
        // Get some user IDs for sample data
        $stmt = $pdo->query("SELECT id FROM users LIMIT 5");
        $users = $stmt->fetchAll();
        
        if (count($users) > 0) {
            $sampleStores = [
                [
                    'user_id' => $users[0]['id'],
                    'store_name' => 'متجر الكتب الإلكترونية',
                    'store_slug' => 'ebooks-store',
                    'description' => 'متجر متخصص في بيع الكتب الإلكترونية والمواد التعليمية',
                    'status' => 'active',
                    'theme' => 'modern',
                    'total_products' => 25,
                    'total_orders' => 150,
                    'total_revenue' => 45000.00
                ],
                [
                    'user_id' => isset($users[1]) ? $users[1]['id'] : $users[0]['id'],
                    'store_name' => 'متجر الإلكترونيات',
                    'store_slug' => 'electronics-shop',
                    'description' => 'متجر للأجهزة الإلكترونية والهواتف الذكية',
                    'status' => 'active',
                    'theme' => 'tech',
                    'total_products' => 40,
                    'total_orders' => 89,
                    'total_revenue' => 125000.00
                ],
                [
                    'user_id' => isset($users[2]) ? $users[2]['id'] : $users[0]['id'],
                    'store_name' => 'متجر الأزياء',
                    'store_slug' => 'fashion-store',
                    'description' => 'متجر للملابس والإكسسوارات العصرية',
                    'status' => 'pending',
                    'theme' => 'fashion',
                    'total_products' => 60,
                    'total_orders' => 45,
                    'total_revenue' => 32000.00
                ]
            ];
            
            $insertStmt = $pdo->prepare("
                INSERT INTO stores (user_id, store_name, store_slug, description, status, theme, total_products, total_orders, total_revenue, settings)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($sampleStores as $store) {
                $settings = json_encode([
                    'currency' => 'DZD',
                    'language' => 'ar',
                    'timezone' => 'Africa/Algiers',
                    'payment_methods' => ['cash', 'bank_transfer'],
                    'shipping_enabled' => true
                ]);
                
                $insertStmt->execute([
                    $store['user_id'],
                    $store['store_name'],
                    $store['store_slug'],
                    $store['description'],
                    $store['status'],
                    $store['theme'],
                    $store['total_products'],
                    $store['total_orders'],
                    $store['total_revenue'],
                    $settings
                ]);
                
                echo "<div class='success'>✅ تم إضافة متجر: {$store['store_name']}</div>";
            }
        } else {
            echo "<div class='warning'>⚠️ لا توجد مستخدمين في قاعدة البيانات لإضافة متاجر تجريبية</div>";
        }
    } else {
        echo "<div class='info'>ℹ️ يوجد {$storeCount} متجر في قاعدة البيانات</div>";
    }
    
    // Show final statistics
    echo "<h2>📊 إحصائيات المتاجر</h2>";
    
    $stats = $pdo->query("
        SELECT 
            status,
            COUNT(*) as count,
            SUM(total_products) as total_products,
            SUM(total_orders) as total_orders,
            SUM(total_revenue) as total_revenue
        FROM stores 
        GROUP BY status
    ")->fetchAll();
    
    echo "<table>";
    echo "<tr><th>الحالة</th><th>عدد المتاجر</th><th>إجمالي المنتجات</th><th>إجمالي الطلبات</th><th>إجمالي الإيرادات</th></tr>";
    foreach ($stats as $stat) {
        echo "<tr>";
        echo "<td>{$stat['status']}</td>";
        echo "<td>{$stat['count']}</td>";
        echo "<td>{$stat['total_products']}</td>";
        echo "<td>{$stat['total_orders']}</td>";
        echo "<td>" . number_format($stat['total_revenue'], 2) . " دج</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div class='success'>🎉 تم إعداد نظام إدارة المتاجر بنجاح!</div>";
    
    echo "<h3>🔗 الخطوات التالية:</h3>";
    echo "<p><a href='index.html' class='btn'>العودة إلى لوحة التحكم</a></p>";
    echo "<p><a href='#' class='btn' onclick='window.location.reload()'>إعادة تشغيل الإعداد</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<div class='error'>📍 التفاصيل: " . $e->getFile() . " في السطر " . $e->getLine() . "</div>";
}

echo "</body></html>";
?>
