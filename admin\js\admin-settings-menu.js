/**
 * Admin Settings Menu Controller
 * تحكم في قائمة إعدادات الإدارة القابلة للطي
 */

// Admin Settings Menu Class
class AdminSettingsMenu {
    constructor() {
        this.menu = null;
        this.submenu = null;
        this.submenuItems = [];
        this.isExpanded = false;
        this.adminSettingsSections = ['generalSettings', 'paymentSettings', 'categories', 'storeSettings', 'securitySettings'];
        
        this.init();
    }
    
    init() {
        console.log('Initializing Admin Settings Menu...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        this.menu = document.querySelector('.admin-settings-menu');
        this.submenu = document.querySelector('.admin-settings-submenu');
        this.submenuItems = document.querySelectorAll('.admin-settings-submenu li');
        
        if (!this.menu || !this.submenu) {
            console.warn('Admin settings menu elements not found');
            return;
        }
        
        this.bindEvents();
        this.checkCurrentSection();
        
        console.log('Admin Settings Menu initialized successfully');
    }
    
    bindEvents() {
        // Add click handlers for submenu items
        this.submenuItems.forEach(item => {
            item.addEventListener('click', (e) => this.handleSubmenuClick(e, item));
        });
    }
    
    toggle() {
        if (this.isExpanded) {
            this.collapse();
        } else {
            this.expand();
        }
    }
    
    expand() {
        if (!this.menu || !this.submenu) return;
        
        this.menu.classList.add('expanded');
        this.submenu.style.maxHeight = this.submenu.scrollHeight + 'px';
        this.isExpanded = true;
        
        console.log('Admin settings menu expanded');
    }
    
    collapse() {
        if (!this.menu || !this.submenu) return;
        
        this.menu.classList.remove('expanded');
        this.submenu.style.maxHeight = '0';
        this.isExpanded = false;
        
        console.log('Admin settings menu collapsed');
    }
    
    handleSubmenuClick(e, item) {
        e.preventDefault();
        e.stopPropagation();
        
        // Remove active class from all submenu items
        this.submenuItems.forEach(i => i.classList.remove('active'));
        
        // Add active class to clicked item
        item.classList.add('active');
        
        // Handle section switching
        const sectionId = item.getAttribute('data-section');
        if (sectionId) {
            this.navigateToSection(sectionId);
        }
    }
    
    navigateToSection(sectionId) {
        console.log('Navigating to admin section:', sectionId);
        
        // Remove active class from all nav items and sections
        document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
            navItem.classList.remove('active');
        });
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // Add active class to the corresponding section
        const section = document.getElementById(sectionId);
        if (section) {
            section.classList.add('active');
            
            // Update page title
            if (typeof updatePageTitle === 'function') {
                updatePageTitle(sectionId);
            }
            
            // Load section specific content
            this.loadSectionContent(sectionId);
        }
    }
    
    loadSectionContent(sectionId) {
        switch(sectionId) {
            case 'generalSettings':
                console.log('Loading general settings...');
                if (typeof loadGeneralSettingsContent === 'function') {
                    loadGeneralSettingsContent();
                }
                break;
            case 'paymentSettings':
                console.log('Loading payment settings...');
                if (typeof loadPaymentSettingsContent === 'function') {
                    loadPaymentSettingsContent();
                }
                break;
            case 'categories':
                console.log('Loading categories management...');
                if (typeof loadCategoriesContent === 'function') {
                    loadCategoriesContent();
                }
                break;
            case 'storeSettings':
                console.log('Loading store settings...');
                if (typeof loadStoreSettingsContent === 'function') {
                    loadStoreSettingsContent();
                }
                break;
            case 'securitySettings':
                console.log('Loading security settings...');
                if (typeof loadSecuritySettingsContent === 'function') {
                    loadSecuritySettingsContent();
                }
                break;
            default:
                console.log('Loading default content for:', sectionId);
        }
    }
    
    checkCurrentSection() {
        // Auto-expand admin settings if we're on one of its pages
        setTimeout(() => {
            const currentSection = document.querySelector('.content-section.active')?.id;
            
            if (this.adminSettingsSections.includes(currentSection)) {
                this.expand();
                
                // Mark the appropriate submenu item as active
                const activeSubmenuItem = document.querySelector(`.admin-settings-submenu li[data-section="${currentSection}"]`);
                if (activeSubmenuItem) {
                    activeSubmenuItem.classList.add('active');
                }
            }
        }, 100);
    }
    
    setActiveSection(sectionId) {
        // Remove active class from all submenu items
        this.submenuItems.forEach(item => item.classList.remove('active'));
        
        // Add active class to the specified section
        const activeItem = document.querySelector(`.admin-settings-submenu li[data-section="${sectionId}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
        
        // Expand menu if it's an admin settings section
        if (this.adminSettingsSections.includes(sectionId)) {
            this.expand();
        }
    }
}

// Global instance
let adminSettingsMenu = null;

// Global functions for backward compatibility
function toggleAdminSettings() {
    if (adminSettingsMenu) {
        adminSettingsMenu.toggle();
    } else {
        console.warn('Admin settings menu not initialized');
    }
}

function showAdminSection(sectionId) {
    if (adminSettingsMenu) {
        adminSettingsMenu.navigateToSection(sectionId);
    } else {
        console.warn('Admin settings menu not initialized');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    adminSettingsMenu = new AdminSettingsMenu();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminSettingsMenu;
}
