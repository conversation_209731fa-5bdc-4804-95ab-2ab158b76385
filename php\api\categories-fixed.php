<?php

/**
 * Categories Management API with Enhanced Error Handling
 * Handles CRUD operations for categories
 * Fixed version using EnvironmentConfig for database connection
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../ApiErrorHandler.php';
require_once __DIR__ . '/../../config/environment.php';

// Initialize error handler to catch any issues before JSON output
ApiErrorHandler::init();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $pdo = EnvironmentConfig::getInstance()->getDatabaseConnection();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    // Validate HTTP method
    ApiErrorHandler::checkMethod(['GET', 'POST', 'PUT', 'DELETE']);

    // Handle different HTTP methods and actions
    switch ($method) {
        case 'GET':
            if ($action === 'list' || $action === 'get_all' || empty($action)) {
                handleGetCategories($pdo);
            } elseif ($action === 'get' && isset($_GET['id'])) {
                handleGetCategory($pdo, $_GET['id']);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'POST':
            if ($action === 'create') {
                handleCreateCategory($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'PUT':
            if ($action === 'update') {
                handleUpdateCategory($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'DELETE':
            if ($action === 'delete') {
                handleDeleteCategory($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        default:
            ApiErrorHandler::sendError('طريقة الطلب غير مسموحة', 405, 'METHOD_NOT_ALLOWED');
    }
} catch (Exception $e) {
    ApiErrorHandler::sendError(
        'خطأ في API الفئات: ' . $e->getMessage(),
        500,
        'CATEGORIES_API_ERROR'
    );
}

/**
 * Get all categories
 */
function handleGetCategories($pdo)
{
    try {
        $stmt = $pdo->prepare("
            SELECT
                c.*,
                COUNT(p.id) as product_count
            FROM categories c
            LEFT JOIN produits p ON c.id = p.category_id
            GROUP BY c.id
            ORDER BY c.name_ar ASC
        ");

        $stmt->execute();
        $categories = $stmt->fetchAll();

        // Format categories for response
        $formattedCategories = array_map(function ($category) {
            return [
                'id' => (int)$category['id'],
                'name_ar' => $category['name_ar'],
                'name_en' => $category['name_en'] ?? '',
                'name_fr' => $category['name_fr'] ?? '',
                'slug' => $category['slug'],
                'description_ar' => $category['description_ar'] ?? '',
                'description_en' => $category['description_en'] ?? '',
                'description_fr' => $category['description_fr'] ?? '',
                'image' => $category['image'] ?? '',
                'icon' => $category['icon'] ?? 'fas fa-folder',
                'color' => $category['color'] ?? '#667eea',
                'sort_order' => (int)$category['sort_order'],
                'is_active' => (bool)$category['is_active'],
                'is_featured' => (bool)$category['is_featured'],
                'parent_id' => $category['parent_id'] ? (int)$category['parent_id'] : null,
                'product_count' => (int)$category['product_count'],
                'created_at' => $category['created_at'],
                'updated_at' => $category['updated_at']
            ];
        }, $categories);

        ApiErrorHandler::sendSuccess([
            'categories' => $formattedCategories,
            'total' => count($formattedCategories)
        ], 'تم تحميل الفئات بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في جلب الفئات: ' . $e->getMessage(),
            500,
            'FETCH_CATEGORIES_ERROR'
        );
    }
}

/**
 * Get single category
 */
function handleGetCategory($pdo, $categoryId)
{
    try {
        $stmt = $pdo->prepare("
            SELECT
                c.*,
                COUNT(p.id) as product_count
            FROM categories c
            LEFT JOIN produits p ON c.id = p.category_id
            WHERE c.id = ?
            GROUP BY c.id
        ");

        $stmt->execute([$categoryId]);
        $category = $stmt->fetch();

        if (!$category) {
            ApiErrorHandler::sendError('الفئة غير موجودة', 404, 'CATEGORY_NOT_FOUND');
            return;
        }

        $formattedCategory = [
            'id' => (int)$category['id'],
            'name' => $category['name'],
            'description' => $category['description'] ?? '',
            'image_url' => $category['image_url'] ?? '',
            'is_active' => (bool)$category['is_active'],
            'product_count' => (int)$category['product_count'],
            'created_at' => $category['created_at'],
            'updated_at' => $category['updated_at']
        ];

        ApiErrorHandler::sendSuccess([
            'category' => $formattedCategory
        ], 'تم تحميل الفئة بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في جلب الفئة: ' . $e->getMessage(),
            500,
            'FETCH_CATEGORY_ERROR'
        );
    }
}

/**
 * Create a new category
 */
function handleCreateCategory($pdo)
{
    try {
        $input = ApiErrorHandler::getJsonInput();
        if ($input === false) return; // Error already sent

        // Validate required fields
        $required = ['name_ar', 'slug'];
        if (!ApiErrorHandler::validateRequired($input, $required)) return;

        // Check if category slug already exists
        $stmt = $pdo->prepare("SELECT id FROM categories WHERE slug = ?");
        $stmt->execute([$input['slug']]);
        if ($stmt->fetch()) {
            ApiErrorHandler::sendError('رابط الفئة موجود بالفعل', 400, 'CATEGORY_SLUG_EXISTS');
            return;
        }

        // Insert category
        $stmt = $pdo->prepare("
            INSERT INTO categories (name_ar, name_en, name_fr, slug, description_ar, description_en, description_fr, image, icon, color, sort_order, is_active, is_featured, parent_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $input['name_ar'],
            $input['name_en'] ?? '',
            $input['name_fr'] ?? '',
            $input['slug'],
            $input['description_ar'] ?? '',
            $input['description_en'] ?? '',
            $input['description_fr'] ?? '',
            $input['image'] ?? '',
            $input['icon'] ?? 'fas fa-folder',
            $input['color'] ?? '#667eea',
            $input['sort_order'] ?? 0,
            $input['is_active'] ?? 1,
            $input['is_featured'] ?? 0,
            $input['parent_id'] ?? null
        ]);

        $categoryId = $pdo->lastInsertId();

        ApiErrorHandler::sendSuccess([
            'category_id' => (int)$categoryId
        ], 'تم إنشاء الفئة بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في إنشاء الفئة: ' . $e->getMessage(),
            500,
            'CREATE_CATEGORY_ERROR'
        );
    }
}

/**
 * Update an existing category
 */
function handleUpdateCategory($pdo)
{
    try {
        $input = ApiErrorHandler::getJsonInput();
        if ($input === false) return; // Error already sent

        if (!isset($input['id']) || empty($input['id'])) {
            ApiErrorHandler::sendError('معرف الفئة مطلوب', 400, 'MISSING_CATEGORY_ID');
            return;
        }

        $categoryId = $input['id'];
        $updates = [];
        $params = [];

        // Build dynamic update query
        $allowedFields = ['name', 'description', 'image_url', 'is_active'];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updates[] = "$field = ?";
                $params[] = $input[$field];
            }
        }

        if (empty($updates)) {
            ApiErrorHandler::sendError('لا توجد بيانات للتحديث', 400, 'NO_UPDATE_DATA');
            return;
        }

        $updates[] = "updated_at = NOW()";
        $params[] = $categoryId;

        $sql = "UPDATE categories SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        if ($stmt->rowCount() === 0) {
            ApiErrorHandler::sendError('الفئة غير موجودة', 404, 'CATEGORY_NOT_FOUND');
            return;
        }

        ApiErrorHandler::sendSuccess(null, 'تم تحديث الفئة بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في تحديث الفئة: ' . $e->getMessage(),
            500,
            'UPDATE_CATEGORY_ERROR'
        );
    }
}

/**
 * Delete a category
 */
function handleDeleteCategory($pdo)
{
    try {
        $input = ApiErrorHandler::getJsonInput();
        if ($input === false) return; // Error already sent

        if (!isset($input['id']) || empty($input['id'])) {
            ApiErrorHandler::sendError('معرف الفئة مطلوب', 400, 'MISSING_CATEGORY_ID');
            return;
        }

        $categoryId = $input['id'];

        // Check if category is in use
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM produits WHERE category_id = ?");
        $stmt->execute([$categoryId]);
        $productCount = $stmt->fetch()['count'];

        if ($productCount > 0) {
            ApiErrorHandler::sendError(
                "لا يمكن حذف الفئة لأنها تحتوي على $productCount منتج",
                400,
                'CATEGORY_IN_USE'
            );
            return;
        }

        // Delete category
        $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
        $stmt->execute([$categoryId]);

        if ($stmt->rowCount() === 0) {
            ApiErrorHandler::sendError('الفئة غير موجودة', 404, 'CATEGORY_NOT_FOUND');
            return;
        }

        ApiErrorHandler::sendSuccess(null, 'تم حذف الفئة بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في حذف الفئة: ' . $e->getMessage(),
            500,
            'DELETE_CATEGORY_ERROR'
        );
    }
}
