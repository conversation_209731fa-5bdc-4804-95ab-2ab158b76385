-- System Settings Database Schema
-- مخطط قاعدة البيانات لإعدادات النظام
-- =====================================================
-- 1. Categories Table (جدول الفئات)
-- =====================================================
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    description_ar TEXT,
    parent_id INT NULL,
    icon VARCHAR(50) DEFAULT 'fas fa-tag',
    color VARCHAR(7) DEFAULT '#3498db',
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    meta_title VARCHAR(200),
    meta_description TEXT,
    slug VARCHAR(150) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE
    SET NULL,
        INDEX idx_parent_id (parent_id),
        INDEX idx_slug (slug),
        INDEX idx_active (is_active),
        INDEX idx_sort (sort_order)
);
-- =====================================================
-- 2. Payment Methods Table (جدول طرق الدفع)
-- =====================================================
CREATE TABLE IF NOT EXISTS payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    type ENUM(
        'credit_card',
        'bank_transfer',
        'cash_on_delivery',
        'digital_wallet',
        'cryptocurrency'
    ) NOT NULL,
    provider VARCHAR(50),
    -- PayPal, Stripe, CCP, etc.
    config JSON,
    -- Configuration settings
    fees_percentage DECIMAL(5, 2) DEFAULT 0.00,
    fees_fixed DECIMAL(10, 2) DEFAULT 0.00,
    min_amount DECIMAL(10, 2) DEFAULT 0.00,
    max_amount DECIMAL(10, 2) DEFAULT NULL,
    supported_currencies JSON,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);
-- =====================================================
-- 3. General Settings Table (جدول الإعدادات العامة)
-- =====================================================
CREATE TABLE IF NOT EXISTS general_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    description VARCHAR(255),
    description_ar VARCHAR(255),
    is_public BOOLEAN DEFAULT FALSE,
    -- Can be accessed by frontend
    validation_rules JSON,
    -- Validation rules
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_public (is_public)
);
-- =====================================================
-- 4. Store Settings Table (جدول إعدادات المتاجر)
-- =====================================================
CREATE TABLE IF NOT EXISTS store_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    store_id INT NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_store_setting (store_id, setting_key),
    INDEX idx_store_id (store_id)
);
-- =====================================================
-- 5. Enhanced Users Table (جدول المستخدمين المحسن)
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    avatar VARCHAR(255),
    role_id INT NOT NULL DEFAULT 1,
    subscription_id INT,
    status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32),
    last_login TIMESTAMP NULL,
    last_ip VARCHAR(45),
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    preferences JSON,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role_id (role_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_status (status)
);
-- =====================================================
-- 6. Stores Table (جدول المتاجر)
-- =====================================================
CREATE TABLE IF NOT EXISTS stores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100),
    description TEXT,
    description_ar TEXT,
    logo VARCHAR(255),
    banner VARCHAR(255),
    domain VARCHAR(100) UNIQUE,
    subdomain VARCHAR(50) UNIQUE,
    status ENUM('active', 'inactive', 'suspended', 'pending') DEFAULT 'pending',
    subscription_id INT,
    theme VARCHAR(50) DEFAULT 'default',
    settings JSON,
    contact_info JSON,
    social_links JSON,
    seo_settings JSON,
    analytics_code TEXT,
    custom_css TEXT,
    custom_js TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_domain (domain),
    INDEX idx_subdomain (subdomain),
    INDEX idx_status (status)
);
-- =====================================================
-- 7. Roles Table (جدول الأدوار)
-- =====================================================
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    name_ar VARCHAR(50) NOT NULL,
    description TEXT,
    description_ar TEXT,
    permissions JSON,
    -- Array of permission strings
    is_system BOOLEAN DEFAULT FALSE,
    -- System roles cannot be deleted
    color VARCHAR(7) DEFAULT '#3498db',
    icon VARCHAR(50) DEFAULT 'fas fa-user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name)
);
-- =====================================================
-- 8. Subscriptions Table (جدول الاشتراكات)
-- =====================================================
CREATE TABLE IF NOT EXISTS subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    description_ar TEXT,
    price_monthly DECIMAL(10, 2) DEFAULT 0.00,
    price_yearly DECIMAL(10, 2) DEFAULT 0.00,
    features JSON,
    -- Array of features
    limits JSON,
    -- Usage limits
    is_active BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    color VARCHAR(7) DEFAULT '#3498db',
    icon VARCHAR(50) DEFAULT 'fas fa-gem',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);
-- =====================================================
-- 9. User Subscriptions Table (جدول اشتراكات المستخدمين)
-- =====================================================
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'suspended') DEFAULT 'active',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    auto_renew BOOLEAN DEFAULT TRUE,
    payment_method VARCHAR(50),
    last_payment_at TIMESTAMP NULL,
    next_payment_at TIMESTAMP NULL,
    usage_stats JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
);
-- =====================================================
-- 10. Security Settings Table (جدول إعدادات الأمان)
-- =====================================================
CREATE TABLE IF NOT EXISTS security_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description VARCHAR(255),
    description_ar VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active)
);
-- =====================================================
-- 11. System Logs Table (جدول سجلات النظام)
-- =====================================================
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL,
    message TEXT NOT NULL,
    context JSON,
    user_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE
    SET NULL,
        INDEX idx_level (level),
        INDEX idx_user_id (user_id),
        INDEX idx_created_at (created_at)
);
-- =====================================================
-- Insert Default Data
-- =====================================================
-- Default Roles
INSERT IGNORE INTO roles (
        id,
        name,
        name_ar,
        description_ar,
        permissions,
        is_system,
        color,
        icon
    )
VALUES (
        1,
        'customer',
        'عميل',
        'عميل عادي',
        '["view_products", "place_orders"]',
        TRUE,
        '#27ae60',
        'fas fa-user'
    ),
    (
        2,
        'store_owner',
        'صاحب متجر',
        'صاحب متجر إلكتروني',
        '["manage_store", "manage_products", "view_orders"]',
        TRUE,
        '#3498db',
        'fas fa-store'
    ),
    (
        3,
        'admin',
        'مدير',
        'مدير النظام',
        '["manage_users", "manage_stores", "manage_settings"]',
        TRUE,
        '#e74c3c',
        'fas fa-user-shield'
    ),
    (
        4,
        'super_admin',
        'مدير عام',
        'مدير عام للنظام',
        '["*"]',
        TRUE,
        '#9b59b6',
        'fas fa-crown'
    );
-- Default Subscriptions
INSERT IGNORE INTO subscriptions (
        id,
        name,
        name_ar,
        description_ar,
        price_monthly,
        price_yearly,
        features,
        limits,
        is_active,
        sort_order
    )
VALUES (
        1,
        'free',
        'مجاني',
        'خطة مجانية أساسية',
        0.00,
        0.00,
        '["basic_store", "5_products", "basic_support"]',
        '{"products": 5, "storage": "100MB", "bandwidth": "1GB"}',
        TRUE,
        1
    ),
    (
        2,
        'basic',
        'أساسي',
        'خطة أساسية للمتاجر الصغيرة',
        29.99,
        299.99,
        '["unlimited_products", "custom_domain", "email_support"]',
        '{"products": -1, "storage": "1GB", "bandwidth": "10GB"}',
        TRUE,
        2
    ),
    (
        3,
        'premium',
        'مميز',
        'خطة مميزة للمتاجر المتوسطة',
        59.99,
        599.99,
        '["all_basic", "advanced_analytics", "priority_support"]',
        '{"products": -1, "storage": "5GB", "bandwidth": "50GB"}',
        TRUE,
        3
    ),
    (
        4,
        'enterprise',
        'مؤسسي',
        'خطة مؤسسية للمتاجر الكبيرة',
        199.99,
        1999.99,
        '["all_premium", "white_label", "dedicated_support"]',
        '{"products": -1, "storage": "unlimited", "bandwidth": "unlimited"}',
        TRUE,
        4
    );
-- Default General Settings
INSERT IGNORE INTO general_settings (
        setting_key,
        setting_value,
        setting_type,
        category,
        description_ar
    )
VALUES (
        'site_name',
        'Mossaab Store',
        'string',
        'general',
        'اسم الموقع'
    ),
    (
        'site_description',
        'منصة التجارة الإلكترونية',
        'string',
        'general',
        'وصف الموقع'
    ),
    (
        'admin_email',
        '<EMAIL>',
        'string',
        'general',
        'بريد المدير'
    ),
    (
        'timezone',
        'Africa/Algiers',
        'string',
        'general',
        'المنطقة الزمنية'
    ),
    (
        'language',
        'ar',
        'string',
        'general',
        'لغة النظام'
    ),
    (
        'currency',
        'DZD',
        'string',
        'general',
        'العملة الافتراضية'
    ),
    (
        'maintenance_mode',
        'false',
        'boolean',
        'system',
        'وضع الصيانة'
    ),
    (
        'registration_enabled',
        'true',
        'boolean',
        'system',
        'تفعيل التسجيل'
    ),
    (
        'email_verification',
        'true',
        'boolean',
        'system',
        'تفعيل التحقق من البريد'
    ),
    (
        'max_upload_size',
        '10',
        'number',
        'system',
        'الحد الأقصى للرفع (MB)'
    );
-- Default Security Settings
INSERT IGNORE INTO security_settings (setting_key, setting_value, description_ar)
VALUES (
        'password_min_length',
        '8',
        'الحد الأدنى لطول كلمة المرور'
    ),
    (
        'password_require_uppercase',
        'true',
        'يتطلب أحرف كبيرة'
    ),
    (
        'password_require_lowercase',
        'true',
        'يتطلب أحرف صغيرة'
    ),
    (
        'password_require_numbers',
        'true',
        'يتطلب أرقام'
    ),
    (
        'password_require_symbols',
        'false',
        'يتطلب رموز'
    ),
    (
        'login_max_attempts',
        '5',
        'الحد الأقصى لمحاولات تسجيل الدخول'
    ),
    (
        'login_lockout_duration',
        '15',
        'مدة القفل بالدقائق'
    ),
    (
        'session_timeout',
        '1440',
        'انتهاء الجلسة بالدقائق'
    ),
    (
        'two_factor_enabled',
        'false',
        'تفعيل المصادقة الثنائية'
    ),
    (
        'ip_whitelist_enabled',
        'false',
        'تفعيل القائمة البيضاء للـ IP'
    );
-- Default Payment Methods
INSERT IGNORE INTO payment_methods (
        id,
        name,
        name_ar,
        type,
        provider,
        config,
        fees_percentage,
        fees_fixed,
        min_amount,
        max_amount,
        supported_currencies,
        is_active,
        sort_order
    )
VALUES (
        1,
        'Cash on Delivery',
        'الدفع عند الاستلام',
        'cash_on_delivery',
        NULL,
        '{"delivery_fee": 200, "max_distance": 50}',
        0.00,
        200.00,
        500.00,
        50000.00,
        '["DZD"]',
        TRUE,
        1
    ),
    (
        2,
        'Bank Transfer',
        'تحويل بنكي',
        'bank_transfer',
        'CCP',
        '{"bank_name": "بريد الجزائر", "account_number": "*********", "iban": "DZ21 0001 0000 0123 4567 89"}',
        0.00,
        0.00,
        1000.00,
        NULL,
        '["DZD"]',
        TRUE,
        2
    ),
    (
        3,
        'Credit Card',
        'بطاقة ائتمان',
        'credit_card',
        'Stripe',
        '{"api_key": "", "secret_key": "", "endpoint": "https://api.stripe.com", "test_mode": "true"}',
        2.90,
        30.00,
        100.00,
        NULL,
        '["DZD", "USD", "EUR"]',
        FALSE,
        3
    ),
    (
        4,
        'Digital Wallet',
        'محفظة رقمية',
        'digital_wallet',
        'PayPal',
        '{"wallet_id": "", "api_key": "", "webhook_url": ""}',
        3.40,
        0.00,
        50.00,
        NULL,
        '["USD", "EUR"]',
        FALSE,
        4
    ),
    (
        5,
        'Cryptocurrency',
        'عملة رقمية',
        'cryptocurrency',
        'Bitcoin',
        '{"wallet_address": "", "network": "bitcoin", "confirmations": 3}',
        1.00,
        0.00,
        10.00,
        NULL,
        '["BTC", "ETH"]',
        FALSE,
        5
    );
-- Default Categories
INSERT IGNORE INTO categories (
        id,
        name,
        name_ar,
        description,
        description_ar,
        parent_id,
        icon,
        color,
        sort_order,
        is_active,
        slug
    )
VALUES (
        1,
        'Electronics',
        'إلكترونيات',
        'Electronic devices and gadgets',
        'الأجهزة الإلكترونية والتقنية',
        NULL,
        'fas fa-laptop',
        '#3498db',
        1,
        TRUE,
        'electronics'
    ),
    (
        2,
        'Clothing',
        'ملابس',
        'Fashion and clothing items',
        'الأزياء والملابس',
        NULL,
        'fas fa-tshirt',
        '#e74c3c',
        2,
        TRUE,
        'clothing'
    ),
    (
        3,
        'Home & Garden',
        'منزل وحديقة',
        'Home improvement and garden supplies',
        'تحسين المنزل ومستلزمات الحديقة',
        NULL,
        'fas fa-home',
        '#27ae60',
        3,
        TRUE,
        'home-garden'
    ),
    (
        4,
        'Sports',
        'رياضة',
        'Sports equipment and accessories',
        'المعدات الرياضية والإكسسوارات',
        NULL,
        'fas fa-dumbbell',
        '#f39c12',
        4,
        TRUE,
        'sports'
    ),
    (
        5,
        'Books',
        'كتب',
        'Books and educational materials',
        'الكتب والمواد التعليمية',
        NULL,
        'fas fa-book',
        '#9b59b6',
        5,
        TRUE,
        'books'
    ),
    (
        6,
        'Smartphones',
        'هواتف ذكية',
        'Mobile phones and accessories',
        'الهواتف المحمولة والإكسسوارات',
        1,
        'fas fa-mobile-alt',
        '#3498db',
        1,
        TRUE,
        'smartphones'
    ),
    (
        7,
        'Laptops',
        'أجهزة لابتوب',
        'Laptop computers',
        'أجهزة الكمبيوتر المحمولة',
        1,
        'fas fa-laptop',
        '#2980b9',
        2,
        TRUE,
        'laptops'
    ),
    (
        8,
        'Men Clothing',
        'ملابس رجالية',
        'Clothing for men',
        'ملابس للرجال',
        2,
        'fas fa-male',
        '#34495e',
        1,
        TRUE,
        'men-clothing'
    ),
    (
        9,
        'Women Clothing',
        'ملابس نسائية',
        'Clothing for women',
        'ملابس للنساء',
        2,
        'fas fa-female',
        '#e91e63',
        2,
        TRUE,
        'women-clothing'
    );
