<?php

/**
 * Database Status Check
 * Returns detailed database connection status
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Error handling
ini_set('display_errors', 1);
error_reporting(E_ALL);

$result = [
    'success' => false,
    'message' => '',
    'connection_status' => 'failed',
    'database_info' => null,
    'tables_info' => [],
    'timestamp' => date('Y-m-d H:i:s')
];

try {
    // Try to include database config
    if (file_exists('../config/database.php')) {
        require_once '../config/database.php';
        $pdo = getDatabaseConnection();
    } elseif (file_exists('../config.php')) {
        require_once '../config.php';
        $pdo = getPDOConnection();
    } else {
        throw new Exception('Database configuration file not found');
    }

    if ($pdo) {
        $result['success'] = true;
        $result['connection_status'] = 'connected';
        $result['message'] = 'Database connection successful';

        // Get database info
        $stmt = $pdo->query("SELECT DATABASE() as db_name");
        $dbInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        $result['database_info'] = [
            'name' => $dbInfo['db_name'],
            'host' => 'localhost',
            'port' => '3307',
            'charset' => 'utf8mb4'
        ];

        // Get tables info
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($tables as $table) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            $result['tables_info'][] = [
                'name' => $table,
                'count' => (int)$count
            ];
        }

        $result['total_tables'] = count($tables);
    } else {
        throw new Exception('Failed to establish database connection');
    }
} catch (Exception $e) {
    $result['success'] = false;
    $result['connection_status'] = 'failed';
    $result['message'] = 'Database connection failed: ' . $e->getMessage();
    $result['error'] = [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => basename($e->getFile()),
        'line' => $e->getLine()
    ];

    // Log error for debugging
    error_log("Database Status API Error: " . $e->getMessage());

    // Set appropriate HTTP status code
    http_response_code(500);
}

// Ensure JSON output
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
