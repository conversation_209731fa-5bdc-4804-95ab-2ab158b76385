<?php
/**
 * Database Connection Diagnostic Tool
 * Comprehensive diagnosis of database connection issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define security check
define('SECURITY_CHECK', true);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص اتصال قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .diagnostic-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .fix-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .fix-button:hover {
            background: #0056b3;
        }
        .env-var {
            background: #e9ecef;
            padding: 3px 6px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص اتصال قاعدة البيانات</h1>
        <p>هذا السكريبت يشخص مشاكل الاتصال بقاعدة البيانات ويقدم حلول للمشاكل الشائعة.</p>

        <?php
        // Step 1: Check .env file
        echo '<div class="diagnostic-section">';
        echo '<h3>📄 فحص ملف .env</h3>';
        
        $envFile = dirname(__DIR__) . '/.env';
        if (file_exists($envFile)) {
            echo '<div class="result pass">✅ ملف .env موجود</div>';
            
            $envContent = file_get_contents($envFile);
            $envLines = explode("\n", $envContent);
            
            $dbConfig = [];
            foreach ($envLines as $line) {
                $line = trim($line);
                if (strpos($line, 'DB_') === 0 && strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $dbConfig[trim($key)] = trim($value);
                }
            }
            
            $requiredDbVars = ['DB_HOST', 'DB_PORT', 'DB_DATABASE', 'DB_USERNAME', 'DB_PASSWORD'];
            $missingVars = [];
            
            foreach ($requiredDbVars as $var) {
                if (isset($dbConfig[$var]) && !empty($dbConfig[$var])) {
                    $displayValue = ($var === 'DB_PASSWORD') ? '***' . substr($dbConfig[$var], -2) : $dbConfig[$var];
                    echo '<div class="result pass">✅ ' . $var . ': <span class="env-var">' . $displayValue . '</span></div>';
                } else {
                    echo '<div class="result fail">❌ ' . $var . ': غير محدد أو فارغ</div>';
                    $missingVars[] = $var;
                }
            }
            
            if (!empty($missingVars)) {
                echo '<div class="result warning">⚠️ متغيرات مفقودة: ' . implode(', ', $missingVars) . '</div>';
            }
            
        } else {
            echo '<div class="result fail">❌ ملف .env غير موجود في: ' . $envFile . '</div>';
        }
        echo '</div>';

        // Step 2: Load environment and test basic connection
        echo '<div class="diagnostic-section">';
        echo '<h3>🔌 اختبار الاتصال الأساسي</h3>';
        
        try {
            // Load environment manually
            if (file_exists($envFile)) {
                $envLines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($envLines as $line) {
                    if (strpos(trim($line), '#') === 0) continue;
                    if (strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $_ENV[trim($key)] = trim($value);
                    }
                }
            }
            
            $host = $_ENV['DB_HOST'] ?? 'localhost';
            $port = $_ENV['DB_PORT'] ?? '3306';
            $database = $_ENV['DB_DATABASE'] ?? '';
            $username = $_ENV['DB_USERNAME'] ?? '';
            $password = $_ENV['DB_PASSWORD'] ?? '';
            $charset = $_ENV['DB_CHARSET'] ?? 'utf8mb4';
            
            echo '<div class="result info">📋 محاولة الاتصال بـ: ' . $host . ':' . $port . '</div>';
            echo '<div class="result info">📋 قاعدة البيانات: ' . $database . '</div>';
            echo '<div class="result info">📋 المستخدم: ' . $username . '</div>';
            
            // Test 1: Check if host is reachable
            $connection = @fsockopen($host, $port, $errno, $errstr, 5);
            if ($connection) {
                echo '<div class="result pass">✅ الخادم متاح على المنفذ ' . $port . '</div>';
                fclose($connection);
            } else {
                echo '<div class="result fail">❌ لا يمكن الوصول للخادم: ' . $errstr . ' (كود الخطأ: ' . $errno . ')</div>';
                echo '<div class="result warning">💡 تأكد من أن خدمة MySQL/MariaDB تعمل</div>';
            }
            
            // Test 2: Try PDO connection
            try {
                $dsn = "mysql:host=$host;port=$port;charset=$charset";
                $pdo = new PDO($dsn, $username, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]);
                
                echo '<div class="result pass">✅ اتصال PDO ناجح (بدون قاعدة بيانات محددة)</div>';
                
                // Test 3: Check if database exists
                $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
                if ($stmt->rowCount() > 0) {
                    echo '<div class="result pass">✅ قاعدة البيانات ' . $database . ' موجودة</div>';
                    
                    // Test 4: Connect to specific database
                    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=$charset";
                    $pdo = new PDO($dsn, $username, $password, [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                    ]);
                    
                    echo '<div class="result pass">✅ اتصال ناجح بقاعدة البيانات ' . $database . '</div>';
                    
                    // Test 5: Check existing tables
                    $stmt = $pdo->query("SHOW TABLES");
                    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    echo '<div class="result info">📊 عدد الجداول الموجودة: ' . count($tables) . '</div>';
                    
                    if (count($tables) > 0) {
                        echo '<div class="result info">📋 الجداول: ' . implode(', ', array_slice($tables, 0, 10)) . (count($tables) > 10 ? '...' : '') . '</div>';
                    }
                    
                } else {
                    echo '<div class="result warning">⚠️ قاعدة البيانات ' . $database . ' غير موجودة</div>';
                    echo '<div class="result info">💡 يمكن إنشاؤها تلقائياً</div>';
                }
                
            } catch (PDOException $e) {
                echo '<div class="result fail">❌ فشل اتصال PDO: ' . $e->getMessage() . '</div>';
                
                // Analyze common errors
                $errorCode = $e->getCode();
                $errorMessage = $e->getMessage();
                
                if (strpos($errorMessage, 'Access denied') !== false) {
                    echo '<div class="result warning">💡 مشكلة في اسم المستخدم أو كلمة المرور</div>';
                } elseif (strpos($errorMessage, 'Connection refused') !== false) {
                    echo '<div class="result warning">💡 خدمة MySQL غير متاحة أو متوقفة</div>';
                } elseif (strpos($errorMessage, 'Unknown database') !== false) {
                    echo '<div class="result warning">💡 قاعدة البيانات غير موجودة</div>';
                } elseif (strpos($errorMessage, 'Unknown MySQL server host') !== false) {
                    echo '<div class="result warning">💡 عنوان الخادم غير صحيح</div>';
                }
            }
            
        } catch (Exception $e) {
            echo '<div class="result fail">❌ خطأ عام: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';

        // Step 3: Check MySQL service status (Windows)
        echo '<div class="diagnostic-section">';
        echo '<h3>🔧 فحص حالة خدمة MySQL</h3>';
        
        if (PHP_OS_FAMILY === 'Windows') {
            // Check common MySQL service names on Windows
            $services = ['MySQL', 'MySQL80', 'MySQL57', 'MariaDB', 'WAMP', 'XAMPP'];
            
            foreach ($services as $service) {
                $output = [];
                $returnVar = 0;
                exec("sc query \"$service\" 2>nul", $output, $returnVar);
                
                if ($returnVar === 0 && !empty($output)) {
                    $serviceInfo = implode("\n", $output);
                    if (strpos($serviceInfo, 'RUNNING') !== false) {
                        echo '<div class="result pass">✅ خدمة ' . $service . ' تعمل</div>';
                    } elseif (strpos($serviceInfo, 'STOPPED') !== false) {
                        echo '<div class="result warning">⚠️ خدمة ' . $service . ' متوقفة</div>';
                        echo '<div class="result info">💡 يمكن تشغيلها بالأمر: net start ' . $service . '</div>';
                    }
                }
            }
            
            // Check for XAMPP
            if (file_exists('C:\\xampp\\mysql\\bin\\mysqld.exe')) {
                echo '<div class="result info">📋 تم العثور على XAMPP MySQL</div>';
            }
            
            // Check for WAMP
            if (file_exists('C:\\wamp64\\bin\\mysql') || file_exists('C:\\wamp\\bin\\mysql')) {
                echo '<div class="result info">📋 تم العثور على WAMP MySQL</div>';
            }
            
        } else {
            echo '<div class="result info">📋 نظام التشغيل: ' . PHP_OS . '</div>';
            echo '<div class="result info">💡 للتحقق من حالة MySQL على Linux/Mac استخدم: systemctl status mysql</div>';
        }
        echo '</div>';

        // Step 4: Provide solutions
        echo '<div class="diagnostic-section">';
        echo '<h3>🛠️ الحلول المقترحة</h3>';
        
        echo '<h4>إذا كانت خدمة MySQL متوقفة:</h4>';
        echo '<div class="result info">🔧 Windows: تشغيل services.msc والبحث عن MySQL وتشغيلها</div>';
        echo '<div class="result info">🔧 XAMPP: فتح XAMPP Control Panel وتشغيل MySQL</div>';
        echo '<div class="result info">🔧 WAMP: فتح WAMP وتشغيل MySQL</div>';
        echo '<div class="result info">🔧 Command Line: net start MySQL (كمدير)</div>';
        
        echo '<h4>إذا كانت قاعدة البيانات غير موجودة:</h4>';
        echo '<div class="result info">🔧 يمكن إنشاؤها تلقائياً باستخدام سكريبت الإصلاح</div>';
        
        echo '<h4>إذا كانت بيانات الاتصال خاطئة:</h4>';
        echo '<div class="result info">🔧 تحديث ملف .env بالبيانات الصحيحة</div>';
        
        echo '</div>';

        // Step 5: Quick fixes
        echo '<div class="diagnostic-section">';
        echo '<h3>⚡ إصلاحات سريعة</h3>';
        
        echo '<a href="create-database.php" class="fix-button">إنشاء قاعدة البيانات</a>';
        echo '<a href="test-connection.php" class="fix-button">اختبار الاتصال مرة أخرى</a>';
        echo '<a href="fix-database.php" class="fix-button">تشغيل إصلاح قاعدة البيانات</a>';
        echo '<a href="index.html" class="fix-button">العودة إلى لوحة التحكم</a>';
        
        echo '</div>';
        ?>

    </div>
</body>
</html>
