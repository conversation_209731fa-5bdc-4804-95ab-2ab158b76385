<?php

/**
 * Comprehensive Demo Data Creation Script
 * Creates realistic demo data for the Mossaab Landing Page system
 */

require_once __DIR__ . '/../php/config.php';

// Set content type and charset
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء البيانات التجريبية الشاملة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .info {
            color: #17a2b8;
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .warning {
            color: #856404;
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
        }

        .progress-bar {
            background: #007bff;
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        th,
        td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: right;
        }

        th {
            background: #f8f9fa;
        }

        .btn {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎯 إنشاء البيانات التجريبية الشاملة</h1>
            <p>إنشاء بيانات تجريبية واقعية لعرض جميع ميزات النظام</p>
        </div>

        <?php
        try {
            $pdo = getPDOConnection();
            echo '<div class="success">✅ تم الاتصال بقاعدة البيانات بنجاح</div>';

            // Step 1: Create Demo User
            echo '<div class="section">';
            echo '<h2>👤 إنشاء المستخدم التجريبي</h2>';

            // Check if demo user exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'demo_user'");
            $stmt->execute();
            $demoUser = $stmt->fetch();

            if (!$demoUser) {
                // Create demo user
                $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, first_name, last_name, phone, role_id, subscription_id, status, email_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

                $hashedPassword = password_hash('demo123', PASSWORD_DEFAULT);
                $stmt->execute([
                    'demo_user',
                    '<EMAIL>',
                    $hashedPassword,
                    'مصعب',
                    'التجريبي',
                    '+213 555 123 456',
                    2, // Store owner role
                    2, // Premium subscription
                    'active',
                    1
                ]);

                $demoUserId = $pdo->lastInsertId();
                echo '<div class="success">✅ تم إنشاء المستخدم التجريبي بنجاح (ID: ' . $demoUserId . ')</div>';
                echo '<div class="info">📧 البريد الإلكتروني: <EMAIL> | كلمة المرور: demo123</div>';
            } else {
                $demoUserId = $demoUser['id'];
                echo '<div class="info">ℹ️ المستخدم التجريبي موجود بالفعل (ID: ' . $demoUserId . ')</div>';
            }
            echo '</div>';

            // Step 2: Create Categories
            echo '<div class="section">';
            echo '<h2>📂 إنشاء الفئات</h2>';

            $categories = [
                ['كتب', 'Books', 'كتب ومراجع تعليمية وثقافية', 'Educational and cultural books and references', 'fas fa-book', '#3498db'],
                ['حاسوب محمول', 'Laptops', 'أجهزة حاسوب محمولة عالية الأداء', 'High-performance laptop computers', 'fas fa-laptop', '#e74c3c'],
                ['حقائب', 'Bags', 'حقائب وأكياس متنوعة', 'Various bags and accessories', 'fas fa-shopping-bag', '#f39c12'],
                ['ألعاب', 'Games', 'ألعاب تعليمية وترفيهية', 'Educational and entertainment games', 'fas fa-gamepad', '#9b59b6'],
                ['إلكترونيات', 'Electronics', 'أجهزة إلكترونية متنوعة', 'Various electronic devices', 'fas fa-mobile-alt', '#1abc9c'],
                ['ملابس', 'Clothing', 'ملابس وأزياء', 'Clothing and fashion', 'fas fa-tshirt', '#e67e22'],
                ['رياضة', 'Sports', 'معدات رياضية ولياقة بدنية', 'Sports and fitness equipment', 'fas fa-dumbbell', '#27ae60'],
                ['منزل وحديقة', 'Home & Garden', 'أدوات منزلية ومعدات الحديقة', 'Home tools and garden equipment', 'fas fa-home', '#34495e']
            ];

            $categoryIds = [];
            foreach ($categories as $index => $category) {
                $stmt = $pdo->prepare("SELECT id FROM categories WHERE nom_ar = ?");
                $stmt->execute([$category[0]]);
                $existingCategory = $stmt->fetch();

                if (!$existingCategory) {
                    $stmt = $pdo->prepare("
                INSERT INTO categories (nom_ar, nom_en, description_ar, description_en, icone, couleur, ordre, actif)
                VALUES (?, ?, ?, ?, ?, ?, ?, 1)
            ");
                    $stmt->execute([$category[0], $category[1], $category[2], $category[3], $category[4], $category[5], $index + 1]);
                    $categoryIds[$category[1]] = $pdo->lastInsertId();
                    echo '<div class="success">✅ تم إنشاء فئة: ' . $category[0] . '</div>';
                } else {
                    $categoryIds[$category[1]] = $existingCategory['id'];
                    echo '<div class="info">ℹ️ الفئة موجودة: ' . $category[0] . '</div>';
                }
            }
            echo '</div>';

            // Step 3: Create Demo Products
            echo '<div class="section">';
            echo '<h2>📦 إنشاء المنتجات التجريبية</h2>';

            $products = [
                [
                    'titre' => 'كتاب تعلم البرمجة بلغة Python',
                    'description' => '<h3>🐍 دليل شامل لتعلم البرمجة بلغة Python</h3>
                <p>كتاب متكامل يغطي جميع أساسيات البرمجة بلغة Python من المستوى المبتدئ إلى المتقدم.</p>
                <h4>📚 محتويات الكتاب:</h4>
                <ul>
                    <li>أساسيات البرمجة والمتغيرات</li>
                    <li>الحلقات والشروط</li>
                    <li>البرمجة الكائنية</li>
                    <li>التعامل مع قواعد البيانات</li>
                    <li>تطوير تطبيقات الويب</li>
                    <li>الذكاء الاصطناعي وتعلم الآلة</li>
                </ul>
                <p><strong>🎯 مناسب للمبتدئين والمحترفين</strong></p>',
                    'prix' => 3500.00,
                    'stock' => 50,
                    'type' => 'book',
                    'category_id' => $categoryIds['Books'],
                    'auteur' => 'د. أحمد محمد الخبير',
                    'image_url' => 'https://via.placeholder.com/400x600/3498db/ffffff?text=Python+Book'
                ],
                [
                    'titre' => 'حاسوب محمول Dell XPS 13 للمطورين',
                    'description' => '<h3>💻 حاسوب محمول عالي الأداء للمطورين والمصممين</h3>
                <p>حاسوب محمول متطور مصمم خصيصاً للمطورين والمصممين المحترفين.</p>
                <h4>⚡ المواصفات التقنية:</h4>
                <ul>
                    <li>شاشة 13.3 بوصة 4K Ultra HD</li>
                    <li>معالج Intel Core i7 الجيل الحادي عشر</li>
                    <li>ذاكرة وصول عشوائي 16 جيجابايت</li>
                    <li>قرص صلب SSD بسعة 512 جيجابايت</li>
                    <li>كرت رسوميات Intel Iris Xe</li>
                    <li>بطارية تدوم حتى 12 ساعة</li>
                </ul>
                <p><strong>🚀 الأداء المثالي للعمل الاحترافي</strong></p>',
                    'prix' => 125000.00,
                    'stock' => 15,
                    'type' => 'laptop',
                    'category_id' => $categoryIds['Laptops'],
                    'processeur' => 'Intel Core i7-1165G7',
                    'ram' => '16GB DDR4',
                    'stockage' => '512GB SSD NVMe',
                    'image_url' => 'https://via.placeholder.com/400x300/e74c3c/ffffff?text=Dell+XPS+13'
                ],
                [
                    'titre' => 'حقيبة ظهر ذكية مع شاحن USB',
                    'description' => '<h3>🎒 حقيبة ظهر عصرية مع تقنيات ذكية</h3>
                <p>حقيبة ظهر متطورة مصممة للطلاب والمهنيين العصريين.</p>
                <h4>🔋 المميزات الذكية:</h4>
                <ul>
                    <li>شاحن USB مدمج للهواتف والأجهزة</li>
                    <li>مقاومة للماء والغبار</li>
                    <li>جيوب متعددة منظمة</li>
                    <li>حزام مريح للظهر</li>
                    <li>مساحة للحاسوب المحمول 15.6 بوصة</li>
                    <li>إضاءة LED للأمان الليلي</li>
                </ul>
                <p><strong>🌟 الحل الأمثل للحياة العصرية</strong></p>',
                    'prix' => 4500.00,
                    'stock' => 30,
                    'type' => 'bag',
                    'category_id' => $categoryIds['Bags'],
                    'materiel' => 'نايلون مقاوم للماء، بطانة داخلية ناعمة',
                    'capacite' => '25 لتر',
                    'image_url' => 'https://via.placeholder.com/400x400/f39c12/ffffff?text=Smart+Backpack'
                ],
                [
                    'titre' => 'لعبة تعليمية - الحروف العربية التفاعلية',
                    'description' => '<h3>🎮 لعبة تعليم الحروف العربية للأطفال</h3>
                <p>لعبة تفاعلية ممتعة لتعليم الأطفال الحروف العربية بطريقة شيقة.</p>
                <h4>🎯 المميزات التعليمية:</h4>
                <ul>
                    <li>28 حرف عربي بالأصوات</li>
                    <li>ألعاب تفاعلية متنوعة</li>
                    <li>أصوات وموسيقى جذابة</li>
                    <li>رسوم ملونة وجميلة</li>
                    <li>مناسبة للأعمار 3-8 سنوات</li>
                    <li>تطوير المهارات الحركية</li>
                </ul>
                <p><strong>📚 التعلم من خلال اللعب</strong></p>',
                    'prix' => 2800.00,
                    'stock' => 40,
                    'type' => 'game',
                    'category_id' => $categoryIds['Games'],
                    'materiel' => 'خشب طبيعي آمن، ألوان غير سامة',
                    'image_url' => 'https://via.placeholder.com/400x400/9b59b6/ffffff?text=Arabic+Letters+Game'
                ],
                [
                    'titre' => 'ساعة ذكية رياضية مقاومة للماء',
                    'description' => '<h3>⌚ ساعة ذكية متطورة للرياضيين</h3>
                <p>ساعة ذكية عالية التقنية مصممة لمتابعة الأنشطة الرياضية والصحية.</p>
                <h4>💪 المميزات الرياضية:</h4>
                <ul>
                    <li>مراقبة معدل ضربات القلب</li>
                    <li>عداد الخطوات والسعرات</li>
                    <li>مقاومة للماء حتى 50 متر</li>
                    <li>GPS مدمج لتتبع المسارات</li>
                    <li>بطارية تدوم 7 أيام</li>
                    <li>إشعارات الهاتف الذكي</li>
                </ul>
                <p><strong>🏃‍♂️ رفيقك المثالي في الرياضة</strong></p>',
                    'prix' => 15000.00,
                    'stock' => 25,
                    'type' => 'electronics',
                    'category_id' => $categoryIds['Electronics'],
                    'materiel' => 'سيليكون طبي، شاشة AMOLED',
                    'image_url' => 'https://via.placeholder.com/400x400/1abc9c/ffffff?text=Smart+Watch'
                ],
                [
                    'titre' => 'قميص قطني فاخر للرجال',
                    'description' => '<h3>👔 قميص رجالي أنيق من القطن الفاخر</h3>
                <p>قميص رجالي عالي الجودة مصنوع من أجود أنواع القطن المصري.</p>
                <h4>✨ مميزات القماش:</h4>
                <ul>
                    <li>قطن مصري 100% فاخر</li>
                    <li>تصميم كلاسيكي أنيق</li>
                    <li>مقاسات متعددة (S-XXL)</li>
                    <li>ألوان متنوعة عصرية</li>
                    <li>سهل العناية والكي</li>
                    <li>مناسب للعمل والمناسبات</li>
                </ul>
                <p><strong>🎩 الأناقة والراحة في قطعة واحدة</strong></p>',
                    'prix' => 3200.00,
                    'stock' => 60,
                    'type' => 'clothing',
                    'category_id' => $categoryIds['Clothing'],
                    'materiel' => 'قطن مصري 100%، أزرار عالية الجودة',
                    'image_url' => 'https://via.placeholder.com/400x500/e67e22/ffffff?text=Cotton+Shirt'
                ],
                [
                    'titre' => 'دمبل قابل للتعديل للياقة البدنية',
                    'description' => '<h3>🏋️‍♂️ دمبل احترافي قابل للتعديل</h3>
                <p>دمبل متطور يمكن تعديل وزنه ليناسب جميع مستويات التدريب.</p>
                <h4>💪 مميزات التدريب:</h4>
                <ul>
                    <li>وزن قابل للتعديل من 2-24 كيلو</li>
                    <li>تصميم مريح ومضاد للانزلاق</li>
                    <li>توفير المساحة في المنزل</li>
                    <li>مناسب لجميع التمارين</li>
                    <li>مواد عالية الجودة ومتينة</li>
                    <li>سهل التركيب والاستخدام</li>
                </ul>
                <p><strong>🎯 صالة رياضية في منزلك</strong></p>',
                    'prix' => 8500.00,
                    'stock' => 20,
                    'type' => 'sports',
                    'category_id' => $categoryIds['Sports'],
                    'materiel' => 'حديد مطلي، مقبض مطاطي مضاد للانزلاق',
                    'image_url' => 'https://via.placeholder.com/400x300/27ae60/ffffff?text=Adjustable+Dumbbell'
                ],
                [
                    'titre' => 'مجموعة أدوات المطبخ الذكية',
                    'description' => '<h3>🍳 مجموعة أدوات مطبخ عصرية ومتطورة</h3>
                <p>مجموعة شاملة من أدوات المطبخ الذكية لتسهيل عملية الطبخ.</p>
                <h4>🔧 محتويات المجموعة:</h4>
                <ul>
                    <li>مقلاة غير لاصقة 28 سم</li>
                    <li>طقم سكاكين احترافية</li>
                    <li>لوح تقطيع ذكي مضاد للبكتيريا</li>
                    <li>ميزان مطبخ رقمي</li>
                    <li>مجموعة ملاعق وأكواب قياس</li>
                    <li>فتاحة علب متعددة الاستخدامات</li>
                </ul>
                <p><strong>👨‍🍳 كل ما تحتاجه للطبخ الاحترافي</strong></p>',
                    'prix' => 12000.00,
                    'stock' => 18,
                    'type' => 'home',
                    'category_id' => $categoryIds['Home & Garden'],
                    'materiel' => 'ستانلس ستيل، طلاء غير لاصق، مقابض مقاومة للحرارة',
                    'image_url' => 'https://via.placeholder.com/400x400/34495e/ffffff?text=Kitchen+Tools'
                ],
                [
                    'titre' => 'كتاب تطوير الذات والنجاح',
                    'description' => '<h3>📖 دليل شامل لتطوير الذات وتحقيق النجاح</h3>
                <p>كتاب ملهم يقدم استراتيجيات عملية لتطوير الشخصية وتحقيق الأهداف.</p>
                <h4>🎯 محاور الكتاب:</h4>
                <ul>
                    <li>بناء الثقة بالنفس</li>
                    <li>إدارة الوقت بفعالية</li>
                    <li>تحديد الأهداف وتحقيقها</li>
                    <li>مهارات التواصل الفعال</li>
                    <li>التفكير الإيجابي</li>
                    <li>قصص نجاح ملهمة</li>
                </ul>
                <p><strong>🌟 استثمر في نفسك لتحقق أحلامك</strong></p>',
                    'prix' => 2200.00,
                    'stock' => 75,
                    'type' => 'book',
                    'category_id' => $categoryIds['Books'],
                    'auteur' => 'د. سارة أحمد الناجحة',
                    'image_url' => 'https://via.placeholder.com/400x600/3498db/ffffff?text=Success+Book'
                ],
                [
                    'titre' => 'حاسوب محمول للألعاب Gaming Laptop',
                    'description' => '<h3>🎮 حاسوب محمول عالي الأداء للألعاب</h3>
                <p>حاسوب محمول متخصص في الألعاب مع أحدث التقنيات والمواصفات.</p>
                <h4>🚀 مواصفات الألعاب:</h4>
                <ul>
                    <li>شاشة 15.6 بوصة 144Hz للألعاب</li>
                    <li>معالج AMD Ryzen 7 5800H</li>
                    <li>كرت رسوميات RTX 3060 6GB</li>
                    <li>ذاكرة 32 جيجابايت DDR4</li>
                    <li>قرص SSD بسعة 1 تيرابايت</li>
                    <li>نظام تبريد متطور RGB</li>
                </ul>
                <p><strong>🏆 الأداء الأقصى للألعاب الحديثة</strong></p>',
                    'prix' => 180000.00,
                    'stock' => 8,
                    'type' => 'laptop',
                    'category_id' => $categoryIds['Laptops'],
                    'processeur' => 'AMD Ryzen 7 5800H',
                    'ram' => '32GB DDR4',
                    'stockage' => '1TB SSD NVMe',
                    'image_url' => 'https://via.placeholder.com/400x300/e74c3c/ffffff?text=Gaming+Laptop'
                ]
            ];

            echo '<div class="progress"><div class="progress-bar" style="width: 0%"></div></div>';
            echo '<div id="progress-text">جاري إنشاء المنتجات...</div>';

            $productIds = [];
            foreach ($products as $index => $product) {
                // Check if product exists
                $stmt = $pdo->prepare("SELECT id FROM produits WHERE titre = ?");
                $stmt->execute([$product['titre']]);
                $existingProduct = $stmt->fetch();

                if (!$existingProduct) {
                    $stmt = $pdo->prepare("
                INSERT INTO produits (titre, description, prix, stock, type, category_id, auteur, processeur, ram, stockage, image_url, actif, has_landing_page, landing_page_enabled, slug)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 1, 1, ?)
            ");

                    $slug = strtolower(str_replace([' ', 'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر', 'ز', 'س', 'ش', 'ص', 'ض', 'ط', 'ظ', 'ع', 'غ', 'ف', 'ق', 'ك', 'ل', 'م', 'ن', 'ه', 'و', 'ي'], ['-', 'a', 'b', 't', 'th', 'j', 'h', 'kh', 'd', 'dh', 'r', 'z', 's', 'sh', 's', 'd', 't', 'z', 'a', 'gh', 'f', 'q', 'k', 'l', 'm', 'n', 'h', 'w', 'y'], $product['titre']));

                    $stmt->execute([
                        $product['titre'],
                        $product['description'],
                        $product['prix'],
                        $product['stock'],
                        $product['type'],
                        $product['category_id'],
                        $product['auteur'] ?? null,
                        $product['processeur'] ?? null,
                        $product['ram'] ?? null,
                        $product['stockage'] ?? null,
                        $product['image_url'],
                        $slug . '-' . time()
                    ]);

                    $productIds[] = $pdo->lastInsertId();
                    echo '<div class="success">✅ تم إنشاء منتج: ' . $product['titre'] . '</div>';
                } else {
                    $productIds[] = $existingProduct['id'];
                    echo '<div class="info">ℹ️ المنتج موجود: ' . $product['titre'] . '</div>';
                }

                // Update progress
                $progress = (($index + 1) / count($products)) * 100;
                echo '<script>
            document.querySelector(".progress-bar").style.width = "' . $progress . '%";
            document.getElementById("progress-text").textContent = "تم إنشاء ' . ($index + 1) . ' من ' . count($products) . ' منتجات";
        </script>';
                flush();
            }
            echo '</div>';

            // Step 4: Create Landing Pages
            echo '<div class="section">';
            echo '<h2>🌐 إنشاء صفحات الهبوط</h2>';

            if (!empty($productIds)) {
                foreach ($productIds as $index => $productId) {
                    // Get product details
                    $stmt = $pdo->prepare("SELECT titre, description, prix, type FROM produits WHERE id = ?");
                    $stmt->execute([$productId]);
                    $product = $stmt->fetch();

                    if ($product) {
                        // Check if landing page exists
                        $stmt = $pdo->prepare("SELECT id FROM landing_pages WHERE produit_id = ?");
                        $stmt->execute([$productId]);
                        $existingLandingPage = $stmt->fetch();

                        if (!$existingLandingPage) {
                            // Create landing page content based on product type
                            $landingPageContent = generateLandingPageContent($product);

                            $stmt = $pdo->prepare("
                        INSERT INTO landing_pages (titre, produit_id, template_id, contenu_droit, contenu_gauche, image_position, text_position, meta_description, actif, lien_url)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?)
                    ");

                            $stmt->execute([
                                'صفحة هبوط - ' . $product['titre'],
                                $productId,
                                'modern',
                                $landingPageContent['right'],
                                $landingPageContent['left'],
                                'center',
                                'split',
                                'صفحة هبوط مخصصة لـ ' . $product['titre'],
                                '/landing-page.php?id=' . $productId
                            ]);

                            echo '<div class="success">✅ تم إنشاء صفحة هبوط: ' . $product['titre'] . '</div>';
                        } else {
                            echo '<div class="info">ℹ️ صفحة الهبوط موجودة: ' . $product['titre'] . '</div>';
                        }
                    }
                }
            }
            echo '</div>';
        } catch (Exception $e) {
            echo '<div class="error">❌ خطأ: ' . $e->getMessage() . '</div>';
        }

        /**
         * Generate landing page content based on product type
         */
        function generateLandingPageContent($product)
        {
            $rightContent = '<div class="hero-section">
        <h1 class="hero-title">' . $product['titre'] . '</h1>
        <p class="hero-subtitle">اكتشف أفضل المنتجات بأسعار مميزة</p>
        <div class="price-section">
            <span class="price">' . number_format($product['prix'], 2) . ' دج</span>
            <span class="old-price">' . number_format($product['prix'] * 1.3, 2) . ' دج</span>
        </div>
        <button class="cta-button">اطلب الآن</button>
    </div>';

            $leftContent = '<div class="features-section">
        <h2>لماذا تختار هذا المنتج؟</h2>
        <ul class="features-list">
            <li>✅ جودة عالية مضمونة</li>
            <li>✅ ضمان شامل لمدة سنة</li>
            <li>✅ شحن مجاني لجميع أنحاء الجزائر</li>
            <li>✅ دعم فني متاح 24/7</li>
            <li>✅ إمكانية الإرجاع خلال 30 يوم</li>
        </ul>
        <div class="testimonial">
            <p>"منتج رائع وجودة ممتازة، أنصح به بشدة!"</p>
            <cite>- عميل راضي</cite>
        </div>
    </div>';

            return [
                'right' => $rightContent,
                'left' => $leftContent
            ];
        }
        ?>

        <div class="section">
            <h2>🎉 تم الانتهاء من إنشاء البيانات التجريبية الشاملة</h2>
            <div class="success">
                <h3>✅ تم إنشاء البيانات التالية بنجاح:</h3>
                <ul>
                    <li>👤 مستخدم تجريبي واحد مع صلاحيات مالك متجر</li>
                    <li>📂 8 فئات متنوعة (كتب، حاسوب، حقائب، ألعاب، إلكترونيات، ملابس، رياضة، منزل)</li>
                    <li>📦 10 منتجات متنوعة بمواصفات مفصلة وأسعار واقعية</li>
                    <li>🌐 10 صفحات هبوط مخصصة لكل منتج</li>
                    <li>🏪 متجر تجريبي شامل يعرض جميع ميزات النظام</li>
                </ul>
            </div>

            <div class="info">
                <h4>📋 معلومات الدخول التجريبية:</h4>
                <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                <p><strong>كلمة المرور:</strong> demo123</p>
                <p><strong>نوع الحساب:</strong> مالك متجر (Store Owner)</p>
                <p><strong>الاشتراك:</strong> بريميوم (Premium)</p>
            </div>

            <div class="warning">
                <h4>🔍 ميزات المتجر التجريبي:</h4>
                <ul>
                    <li>منتجات متنوعة تغطي جميع الفئات</li>
                    <li>أوصاف مفصلة باللغة العربية</li>
                    <li>صور توضيحية لكل منتج</li>
                    <li>صفحات هبوط احترافية</li>
                    <li>أسعار واقعية بالدينار الجزائري</li>
                    <li>مخزون متنوع لكل منتج</li>
                    <li>تصنيفات منظمة ومرمزة بالألوان</li>
                    <li>دعم كامل للغة العربية RTL</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="../index.html" class="btn">🏠 العودة للصفحة الرئيسية</a>
                <a href="index.html" class="btn">⚙️ لوحة التحكم</a>
                <a href="../" class="btn">🛍️ عرض المتجر التجريبي</a>
            </div>
        </div>
    </div>
</body>

</html>
