<?php

/**
 * Users Management API
 * واجهة برمجة التطبيقات لإدارة المستخدمين
 */

session_start();

// Load configuration
require_once '../../config/config.php';

// Set JSON response headers
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Connect to database
try {
    $dbConfig = Config::getDbConfig();
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $dbConfig['host'],
        $dbConfig['port'],
        $dbConfig['database']
    );

    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];

    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()]);
    exit;
}

/**
 * Users Management Class
 */
class UsersManager
{
    private $pdo;

    public function __construct($pdo)
    {
        $this->pdo = $pdo;
    }

    /**
     * Get all users with their roles and permissions (including both regular users and admins)
     */
    public function getAllUsers($page = 1, $limit = 20, $search = '', $roleFilter = '', $statusFilter = '')
    {
        try {
            $offset = ($page - 1) * $limit;

            // Build WHERE clause for search
            $searchConditions = [];
            $searchParams = [];

            if (!empty($search)) {
                $searchTerm = "%$search%";
                $searchConditions[] = "(u.username LIKE ? OR u.email LIKE ? OR CONCAT(u.first_name, ' ', u.last_name) LIKE ?)";
                $searchParams = [$searchTerm, $searchTerm, $searchTerm];
            }

            // Get regular users from users table with role information
            $usersQuery = "
                SELECT
                    u.id,
                    u.username,
                    u.email,
                    u.first_name,
                    u.last_name,
                    CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as full_name,
                    u.phone,
                    u.avatar,
                    u.status,
                    u.email_verified,
                    u.last_login,
                    u.created_at,
                    u.updated_at,
                    u.role_id,
                    COALESCE(ur.display_name_ar, 'مستخدم') as role_name,
                    COALESCE(ur.display_name_en, 'User') as role_name_en,
                    COALESCE(ur.color, '#007bff') as role_color,
                    COALESCE(ur.icon, 'fas fa-user') as role_icon,
                    COALESCE(ur.level, 4) as role_level,
                    'user' as user_type,
                    u.migrated_from
                FROM users u
                LEFT JOIN user_roles ur ON u.role_id = ur.id AND ur.is_active = 1
            ";

            if (!empty($searchConditions)) {
                $usersQuery .= " WHERE " . implode(' AND ', $searchConditions);
            }

            // Get admin users from admins table
            $adminsQuery = "
                SELECT
                    a.id,
                    a.nom_utilisateur as username,
                    a.email,
                    a.nom_complet as first_name,
                    '' as last_name,
                    a.nom_complet as full_name,
                    '' as phone,
                    '' as avatar,
                    CASE WHEN a.actif = 1 THEN 'active' ELSE 'inactive' END as status,
                    1 as email_verified,
                    a.derniere_connexion as last_login,
                    a.created_at,
                    a.updated_at,
                    999 as role_id,
                    CASE WHEN a.role = 'super_admin' THEN 'مدير عام' ELSE 'مدير' END as role_name,
                    CASE WHEN a.role = 'super_admin' THEN 'Super Admin' ELSE 'Admin' END as role_name_en,
                    CASE WHEN a.role = 'super_admin' THEN '#dc3545' ELSE '#fd7e14' END as role_color,
                    CASE WHEN a.role = 'super_admin' THEN 'fas fa-crown' ELSE 'fas fa-user-shield' END as role_icon,
                    CASE WHEN a.role = 'super_admin' THEN 1 ELSE 2 END as role_level,
                    'admin' as user_type,
                    'admin' as migrated_from
                FROM admins a
            ";

            if (!empty($searchConditions)) {
                // Adjust search conditions for admin table
                $adminSearchConditions = str_replace(
                    ['u.username', 'u.email', 'CONCAT(u.first_name, \' \', u.last_name)'],
                    ['a.nom_utilisateur', 'a.email', 'a.nom_complet'],
                    $searchConditions[0]
                );
                $adminsQuery .= " WHERE " . $adminSearchConditions;
            }

            // Combine both queries with UNION
            $combinedQuery = "
                ($usersQuery)
                UNION ALL
                ($adminsQuery)
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ";

            $allParams = array_merge($searchParams, $searchParams, [$limit, $offset]);

            $stmt = $this->pdo->prepare($combinedQuery);
            $stmt->execute($allParams);
            $users = $stmt->fetchAll();

            // Get total count for both users and admins
            $userCountQuery = "SELECT COUNT(*) as count FROM users u";
            if (!empty($searchConditions)) {
                $userCountQuery .= " WHERE " . implode(' AND ', $searchConditions);
            }

            $adminCountQuery = "SELECT COUNT(*) as count FROM admins a";
            if (!empty($searchConditions)) {
                $adminSearchConditions = str_replace(
                    ['u.username', 'u.email', 'CONCAT(u.first_name, \' \', u.last_name)'],
                    ['a.nom_utilisateur', 'a.email', 'a.nom_complet'],
                    $searchConditions[0]
                );
                $adminCountQuery .= " WHERE " . $adminSearchConditions;
            }

            $stmt = $this->pdo->prepare($userCountQuery);
            $stmt->execute($searchParams);
            $userCount = $stmt->fetch()['count'];

            $stmt = $this->pdo->prepare($adminCountQuery);
            $stmt->execute($searchParams);
            $adminCount = $stmt->fetch()['count'];

            $totalUsers = $userCount + $adminCount;

            // Get statistics
            $stats = $this->getUsersStatistics();

            // Get available roles (including admin roles)
            $roles = [
                [
                    'name' => 'admin',
                    'display_name_ar' => 'مدير',
                    'color' => '#dc3545',
                    'icon' => 'fas fa-user-shield',
                    'users_count' => $adminCount
                ]
            ];

            // Add regular user roles
            $rolesStmt = $this->pdo->query("
                SELECT ur.name, ur.display_name_ar, '#007bff' as color, 'fas fa-user' as icon,
                       COUNT(u.id) as users_count
                FROM user_roles ur
                LEFT JOIN users u ON ur.id = u.role_id
                GROUP BY ur.id
                ORDER BY ur.level
            ");
            $userRoles = $rolesStmt->fetchAll();
            $roles = array_merge($roles, $userRoles);

            return [
                'success' => true,
                'data' => [
                    'users' => $users,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total' => $totalUsers,
                        'total_pages' => ceil($totalUsers / $limit)
                    ],
                    'statistics' => $stats,
                    'roles' => $roles,
                    'filters' => [
                        'search' => $search,
                        'role' => $roleFilter,
                        'status' => $statusFilter
                    ]
                ]
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب المستخدمين: ' . $e->getMessage()];
        }
    }

    /**
     * Get user by ID with full details (supports both regular users and admins)
     */
    public function getUserById($userId, $userType = 'user')
    {
        try {
            if ($userType === 'admin') {
                // Get admin user
                $stmt = $this->pdo->prepare("
                    SELECT
                        a.id,
                        a.nom_utilisateur as username,
                        a.email,
                        a.nom_complet as first_name,
                        '' as last_name,
                        a.nom_complet as full_name,
                        '' as phone,
                        '' as avatar,
                        CASE WHEN a.actif = 1 THEN 'active' ELSE 'inactive' END as status,
                        1 as email_verified,
                        a.derniere_connexion as last_login,
                        a.created_at,
                        a.updated_at,
                        a.role,
                        'admin' as user_type
                    FROM admins a
                    WHERE a.id = ?
                ");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();

                if ($user) {
                    $user['role_name'] = $user['role'] === 'super_admin' ? 'مدير عام' : 'مدير';
                    $user['permissions'] = ['all']; // Admins have all permissions
                }
            } else {
                // Get regular user
                $stmt = $this->pdo->prepare("
                    SELECT
                        u.*,
                        ur.display_name_ar as role_name,
                        ur.display_name_en as role_name_en,
                        'user' as user_type
                    FROM users u
                    LEFT JOIN user_roles ur ON u.role_id = ur.id
                    WHERE u.id = ?
                ");
                $stmt->execute([$userId]);
                $user = $stmt->fetch();

                if ($user) {
                    // Get user permissions
                    $permissionsStmt = $this->pdo->prepare("
                        SELECT DISTINCT p.name, p.display_name_ar, p.category
                        FROM permissions p
                        INNER JOIN role_permissions rp ON p.id = rp.permission_id
                        WHERE rp.role_id = ?
                    ");
                    $permissionsStmt->execute([$user['role_id']]);
                    $user['permissions'] = $permissionsStmt->fetchAll();
                }
            }

            if (!$user) {
                return ['success' => false, 'message' => 'المستخدم غير موجود'];
            }

            // Get user permissions
            $permissionsStmt = $this->pdo->prepare("
                SELECT DISTINCT p.name, p.display_name_ar, p.category
                FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                INNER JOIN user_roles ur ON rp.role_id = ur.role_id
                WHERE ur.user_id = ?
                UNION
                SELECT DISTINCT p.name, p.display_name_ar, p.category
                FROM permissions p
                INNER JOIN user_permissions up ON p.id = up.permission_id
                WHERE up.user_id = ? AND up.granted = 1
                ORDER BY category, display_name_ar
            ");
            $permissionsStmt->execute([$userId, $userId]);
            $permissions = $permissionsStmt->fetchAll();

            // Get user sessions
            $sessionsStmt = $this->pdo->prepare("
                SELECT id, ip_address, user_agent, is_active, last_activity, created_at
                FROM user_sessions
                WHERE user_id = ?
                ORDER BY last_activity DESC
                LIMIT 10
            ");
            $sessionsStmt->execute([$userId]);
            $sessions = $sessionsStmt->fetchAll();

            $user['permissions'] = $permissions;
            $user['sessions'] = $sessions;

            return ['success' => true, 'data' => ['user' => $user]];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب بيانات المستخدم: ' . $e->getMessage()];
        }
    }

    /**
     * Create new user
     */
    public function createUser($userData)
    {
        try {
            $this->pdo->beginTransaction();

            // Validate required fields
            $required = ['username', 'email', 'password', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($userData[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }

            // Check if username or email already exists
            $checkStmt = $this->pdo->prepare("
                SELECT COUNT(*) as count FROM users
                WHERE username = ? OR email = ?
            ");
            $checkStmt->execute([$userData['username'], $userData['email']]);
            if ($checkStmt->fetch()['count'] > 0) {
                throw new Exception('اسم المستخدم أو البريد الإلكتروني موجود بالفعل');
            }

            // Hash password
            $passwordHash = password_hash($userData['password'], PASSWORD_DEFAULT);

            // Get default role ID if not specified
            $roleId = $userData['role_id'] ?? null;
            if (!$roleId) {
                $defaultRoleStmt = $this->pdo->prepare("SELECT id FROM user_roles WHERE is_default = 1 LIMIT 1");
                $defaultRoleStmt->execute();
                $defaultRole = $defaultRoleStmt->fetch();
                $roleId = $defaultRole ? $defaultRole['id'] : 4; // Fallback to ID 4 if no default found
            }

            // Insert user
            $insertStmt = $this->pdo->prepare("
                INSERT INTO users
                (username, email, password, first_name, last_name, phone, role_id, status, email_verified)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $insertStmt->execute([
                $userData['username'],
                $userData['email'],
                $passwordHash,
                $userData['first_name'],
                $userData['last_name'],
                $userData['phone'] ?? null,
                $roleId,
                $userData['status'] ?? 'active',
                $userData['email_verified'] ?? 0
            ]);

            $userId = $this->pdo->lastInsertId();

            // Create role assignment record
            $assignRoleStmt = $this->pdo->prepare("
                INSERT INTO user_role_assignments (user_id, role_id, assigned_by, assigned_at, is_active)
                VALUES (?, ?, ?, NOW(), 1)
            ");
            $assignRoleStmt->execute([$userId, $roleId, $_SESSION['user_id'] ?? 1]);

            $this->pdo->commit();

            return ['success' => true, 'message' => 'تم إنشاء المستخدم بنجاح', 'data' => ['user_id' => $userId]];
        } catch (Exception $e) {
            $this->pdo->rollback();
            return ['success' => false, 'message' => 'خطأ في إنشاء المستخدم: ' . $e->getMessage()];
        }
    }

    /**
     * Update user
     */
    public function updateUser($userId, $userData)
    {
        try {
            $this->pdo->beginTransaction();

            // Check if user exists
            $checkStmt = $this->pdo->prepare("SELECT id FROM users WHERE id = ?");
            $checkStmt->execute([$userId]);
            if (!$checkStmt->fetch()) {
                throw new Exception('المستخدم غير موجود');
            }

            // Build update query
            $updateFields = [];
            $params = [];

            $allowedFields = [
                'username',
                'email',
                'first_name',
                'last_name',
                'phone',
                'bio',
                'is_active',
                'is_verified'
            ];

            foreach ($allowedFields as $field) {
                if (isset($userData[$field])) {
                    $updateFields[] = "$field = ?";
                    $params[] = $userData[$field];
                }
            }

            // Update full_name if first_name or last_name changed
            if (isset($userData['first_name']) || isset($userData['last_name'])) {
                $updateFields[] = "full_name = ?";
                $firstName = $userData['first_name'] ?? '';
                $lastName = $userData['last_name'] ?? '';
                $params[] = trim($firstName . ' ' . $lastName);
            }

            // Handle password update
            if (!empty($userData['password'])) {
                $updateFields[] = "password_hash = ?";
                $params[] = password_hash($userData['password'], PASSWORD_DEFAULT);
            }

            if (!empty($updateFields)) {
                $updateFields[] = "updated_at = CURRENT_TIMESTAMP";
                $params[] = $userId;

                $updateQuery = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
                $updateStmt = $this->pdo->prepare($updateQuery);
                $updateStmt->execute($params);
            }

            // Update roles if provided
            if (isset($userData['roles']) && is_array($userData['roles'])) {
                // Remove existing roles
                $deleteRolesStmt = $this->pdo->prepare("DELETE FROM user_roles WHERE user_id = ?");
                $deleteRolesStmt->execute([$userId]);

                // Assign new roles
                if (!empty($userData['roles'])) {
                    $assignRoleStmt = $this->pdo->prepare("
                        INSERT INTO user_roles (user_id, role_id, assigned_by)
                        VALUES (?, ?, ?)
                    ");

                    foreach ($userData['roles'] as $roleId) {
                        $assignRoleStmt->execute([$userId, $roleId, $_SESSION['user_id'] ?? 1]);
                    }
                }
            }

            $this->pdo->commit();

            return ['success' => true, 'message' => 'تم تحديث المستخدم بنجاح'];
        } catch (Exception $e) {
            $this->pdo->rollback();
            return ['success' => false, 'message' => 'خطأ في تحديث المستخدم: ' . $e->getMessage()];
        }
    }

    /**
     * Delete user
     */
    public function deleteUser($userId)
    {
        try {
            // Check if user exists
            $checkStmt = $this->pdo->prepare("SELECT username FROM users WHERE id = ?");
            $checkStmt->execute([$userId]);
            $user = $checkStmt->fetch();

            if (!$user) {
                return ['success' => false, 'message' => 'المستخدم غير موجود'];
            }

            // Prevent deleting admin user
            if ($user['username'] === 'admin') {
                return ['success' => false, 'message' => 'لا يمكن حذف مستخدم المدير'];
            }

            $deleteStmt = $this->pdo->prepare("DELETE FROM users WHERE id = ?");
            $deleteStmt->execute([$userId]);

            return ['success' => true, 'message' => 'تم حذف المستخدم بنجاح'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في حذف المستخدم: ' . $e->getMessage()];
        }
    }

    /**
     * Get users statistics (including both regular users and admins)
     */
    public function getUsersStatistics()
    {
        try {
            $stats = [];

            // Total regular users
            $totalUsersStmt = $this->pdo->query("SELECT COUNT(*) as count FROM users");
            $totalUsers = $totalUsersStmt->fetch()['count'];

            // Total admins
            $totalAdminsStmt = $this->pdo->query("SELECT COUNT(*) as count FROM admins");
            $totalAdmins = $totalAdminsStmt->fetch()['count'];

            $stats['total_users'] = $totalUsers;
            $stats['total_admins'] = $totalAdmins;
            $stats['total_all'] = $totalUsers + $totalAdmins;

            // Active users
            $activeUsersStmt = $this->pdo->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
            $activeUsers = $activeUsersStmt->fetch()['count'];

            $activeAdminsStmt = $this->pdo->query("SELECT COUNT(*) as count FROM admins WHERE actif = 1");
            $activeAdmins = $activeAdminsStmt->fetch()['count'];

            $stats['active_users'] = $activeUsers;
            $stats['active_admins'] = $activeAdmins;
            $stats['active_all'] = $activeUsers + $activeAdmins;

            // Verified users
            $verifiedStmt = $this->pdo->query("SELECT COUNT(*) as count FROM users WHERE email_verified = 1");
            $stats['verified_users'] = $verifiedStmt->fetch()['count'];

            // Users registered today
            $todayStmt = $this->pdo->query("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()");
            $stats['today_registrations'] = $todayStmt->fetch()['count'];

            // Users registered this month
            $monthStmt = $this->pdo->query("SELECT COUNT(*) as count FROM users WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
            $stats['month_registrations'] = $monthStmt->fetch()['count'];

            // Users by role
            $roleStatsStmt = $this->pdo->query("
                SELECT
                    ur.name,
                    ur.display_name_ar,
                    ur.color,
                    ur.icon,
                    COUNT(u.id) as count
                FROM user_roles ur
                LEFT JOIN users u ON ur.id = u.role_id AND u.status = 'active'
                WHERE ur.is_active = 1
                GROUP BY ur.id
                ORDER BY ur.level ASC
            ");
            $stats['users_by_role'] = $roleStatsStmt->fetchAll();

            return $stats;
        } catch (Exception $e) {
            return ['error' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage()];
        }
    }

    /**
     * Get all roles
     */
    public function getAllRoles()
    {
        try {
            $stmt = $this->pdo->query("
                SELECT
                    ur.*,
                    COUNT(DISTINCT u.id) as users_count
                FROM user_roles ur
                LEFT JOIN users u ON ur.id = u.role_id AND u.status = 'active'
                WHERE ur.is_active = 1
                GROUP BY ur.id
                ORDER BY ur.level ASC
            ");

            return ['success' => true, 'data' => ['roles' => $stmt->fetchAll()]];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب الأدوار: ' . $e->getMessage()];
        }
    }
}

// Handle requests
$manager = new UsersManager($pdo);
$action = $_GET['action'] ?? $_POST['action'] ?? '';

// Debug logging
error_log("Users Management API - Action: $action, Method: " . $_SERVER['REQUEST_METHOD']);

switch ($action) {
    case 'get_all':
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $search = $_GET['search'] ?? '';
        $roleFilter = $_GET['role'] ?? '';
        $statusFilter = $_GET['status'] ?? '';

        $result = $manager->getAllUsers($page, $limit, $search, $roleFilter, $statusFilter);
        break;

    case 'get_by_id':
        $userId = (int)($_GET['id'] ?? 0);
        $userType = $_GET['type'] ?? 'user'; // 'user' or 'admin'
        if ($userId <= 0) {
            $result = ['success' => false, 'message' => 'معرف المستخدم مطلوب'];
        } else {
            $result = $manager->getUserById($userId, $userType);
        }
        break;

    case 'create':
        $input = json_decode(file_get_contents('php://input'), true);
        $userData = $input ?? $_POST;
        $result = $manager->createUser($userData);
        break;

    case 'update':
        $userId = (int)($_GET['id'] ?? $_POST['id'] ?? 0);
        if ($userId <= 0) {
            $result = ['success' => false, 'message' => 'معرف المستخدم مطلوب'];
        } else {
            $input = json_decode(file_get_contents('php://input'), true);
            $userData = $input ?? $_POST;
            $result = $manager->updateUser($userId, $userData);
        }
        break;

    case 'delete':
        $userId = (int)($_GET['id'] ?? $_POST['id'] ?? 0);
        if ($userId <= 0) {
            $result = ['success' => false, 'message' => 'معرف المستخدم مطلوب'];
        } else {
            $result = $manager->deleteUser($userId);
        }
        break;

    case 'get_statistics':
        $result = ['success' => true, 'data' => $manager->getUsersStatistics()];
        break;

    case 'get_roles':
        $result = $manager->getAllRoles();
        break;

    default:
        $result = ['success' => false, 'message' => 'إجراء غير صحيح'];
        break;
}

// Return JSON response
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
