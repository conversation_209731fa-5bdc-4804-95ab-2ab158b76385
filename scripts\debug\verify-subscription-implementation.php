<?php
/**
 * Verify Subscription Management Implementation
 * التحقق من تنفيذ إدارة الاشتراكات
 */

require_once 'php/config.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق من تنفيذ إدارة الاشتراكات</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        h1, h2, h3 { color: #333; }
        .test-button { display: inline-block; padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-button:hover { background: #0056b3; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .test-card { background: white; border: 1px solid #ddd; border-radius: 10px; padding: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ التحقق من تنفيذ إدارة الاشتراكات</h1>
        <p>فحص شامل لنظام إدارة الاشتراكات المُنفذ حديثاً</p>

        <?php
        $overallStatus = 'success';
        $testResults = [];
        
        try {
            $pdo = getPDOConnection();
            
            // Test 1: Database Connection
            echo "<div class='section success'>";
            echo "<h2>✅ 1. اختبار الاتصال بقاعدة البيانات</h2>";
            echo "<p>تم الاتصال بقاعدة البيانات بنجاح</p>";
            $testResults['database_connection'] = 'PASS';
            echo "</div>";
            
            // Test 2: Subscription Tables
            echo "<div class='section'>";
            echo "<h2>🗄️ 2. فحص جداول الاشتراكات</h2>";
            
            // Check subscription_plans table
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM subscription_plans");
                $plansCount = $stmt->fetch()['count'];
                echo "<div class='success'>✅ جدول subscription_plans: $plansCount خطة</div>";
                $testResults['subscription_plans_table'] = 'PASS';
            } catch (Exception $e) {
                echo "<div class='error'>❌ جدول subscription_plans: " . $e->getMessage() . "</div>";
                $testResults['subscription_plans_table'] = 'FAIL';
                $overallStatus = 'error';
            }
            
            // Check users table for subscription_id column
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE subscription_id IS NOT NULL");
                $subscribedUsers = $stmt->fetch()['count'];
                echo "<div class='success'>✅ عمود subscription_id في جدول users: $subscribedUsers مستخدم مشترك</div>";
                $testResults['users_subscription_column'] = 'PASS';
            } catch (Exception $e) {
                echo "<div class='error'>❌ عمود subscription_id في جدول users: " . $e->getMessage() . "</div>";
                $testResults['users_subscription_column'] = 'FAIL';
                $overallStatus = 'error';
            }
            echo "</div>";
            
            // Test 3: API Endpoints
            echo "<div class='section'>";
            echo "<h2>🔗 3. اختبار نقاط النهاية API</h2>";
            
            $apiEndpoints = [
                'plans' => 'جلب خطط الاشتراك',
                'stats' => 'إحصائيات الاشتراكات'
            ];
            
            foreach ($apiEndpoints as $endpoint => $description) {
                $apiUrl = 'http://localhost:8080/php/api/subscriptions.php?action=' . $endpoint;
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'method' => 'GET'
                    ]
                ]);
                
                $apiResponse = @file_get_contents($apiUrl, false, $context);
                if ($apiResponse) {
                    $apiData = json_decode($apiResponse, true);
                    if ($apiData && isset($apiData['success']) && $apiData['success']) {
                        echo "<div class='success'>✅ $description ($endpoint) يعمل بشكل صحيح</div>";
                        $testResults["api_$endpoint"] = 'PASS';
                    } else {
                        echo "<div class='error'>❌ $description ($endpoint) يعيد خطأ</div>";
                        $testResults["api_$endpoint"] = 'FAIL';
                        $overallStatus = 'error';
                    }
                } else {
                    echo "<div class='warning'>⚠️ لا يمكن الوصول إلى $description ($endpoint)</div>";
                    $testResults["api_$endpoint"] = 'WARNING';
                }
            }
            echo "</div>";
            
            // Test 4: File System
            echo "<div class='section'>";
            echo "<h2>📁 4. فحص ملفات النظام</h2>";
            
            $criticalFiles = [
                'admin/js/subscriptions-management.js' => 'JavaScript إدارة الاشتراكات',
                'php/api/subscriptions.php' => 'API إدارة الاشتراكات',
                'admin/index.html' => 'لوحة التحكم الرئيسية'
            ];
            
            foreach ($criticalFiles as $file => $description) {
                if (file_exists($file)) {
                    echo "<div class='success'>✅ $description موجود</div>";
                    $testResults["file_$file"] = 'PASS';
                } else {
                    echo "<div class='error'>❌ $description غير موجود</div>";
                    $testResults["file_$file"] = 'FAIL';
                    $overallStatus = 'error';
                }
            }
            echo "</div>";
            
            // Test 5: Admin Panel Integration
            echo "<div class='section'>";
            echo "<h2>🎛️ 5. تكامل لوحة التحكم</h2>";
            
            if (file_exists('admin/index.html')) {
                $adminContent = file_get_contents('admin/index.html');
                
                // Check for menu item
                if (strpos($adminContent, 'إدارة الاشتراكات') !== false) {
                    echo "<div class='success'>✅ عنصر قائمة 'إدارة الاشتراكات' موجود</div>";
                    $testResults['admin_menu_item'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ عنصر قائمة 'إدارة الاشتراكات' مفقود</div>";
                    $testResults['admin_menu_item'] = 'FAIL';
                    $overallStatus = 'error';
                }
                
                // Check for script inclusion
                if (strpos($adminContent, 'subscriptions-management.js') !== false) {
                    echo "<div class='success'>✅ سكريبت إدارة الاشتراكات مُحمل في لوحة التحكم</div>";
                    $testResults['admin_script_inclusion'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ سكريبت إدارة الاشتراكات غير مُحمل</div>";
                    $testResults['admin_script_inclusion'] = 'FAIL';
                    $overallStatus = 'error';
                }
                
                // Check for navigation case
                if (strpos($adminContent, 'subscriptionsManagement') !== false) {
                    echo "<div class='success'>✅ حالة التنقل لإدارة الاشتراكات موجودة</div>";
                    $testResults['admin_navigation_case'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ حالة التنقل لإدارة الاشتراكات مفقودة</div>";
                    $testResults['admin_navigation_case'] = 'FAIL';
                    $overallStatus = 'error';
                }
            }
            echo "</div>";
            
            // Test 6: JavaScript Functions
            echo "<div class='section'>";
            echo "<h2>⚙️ 6. وظائف JavaScript</h2>";
            
            if (file_exists('admin/js/subscriptions-management.js')) {
                $jsContent = file_get_contents('admin/js/subscriptions-management.js');
                
                $jsFunctions = [
                    'loadSubscriptionsManagementContent' => 'تحميل محتوى إدارة الاشتراكات',
                    'showSubscriptionsManagement' => 'عرض إدارة الاشتراكات',
                    'loadSubscriptionsManagementInterface' => 'تحميل واجهة إدارة الاشتراكات',
                    'showAddSubscriptionModal' => 'عرض نافذة إضافة اشتراك',
                    'editSubscriptionPlan' => 'تعديل خطة الاشتراك',
                    'deleteSubscriptionPlan' => 'حذف خطة الاشتراك',
                    'updateSubscriptionStats' => 'تحديث إحصائيات الاشتراكات'
                ];
                
                $functionsFound = 0;
                foreach ($jsFunctions as $func => $desc) {
                    if (strpos($jsContent, "function $func") !== false) {
                        echo "<div class='success'>✅ $desc ($func)</div>";
                        $functionsFound++;
                        $testResults["js_function_$func"] = 'PASS';
                    } else {
                        echo "<div class='error'>❌ $desc ($func) مفقودة</div>";
                        $testResults["js_function_$func"] = 'FAIL';
                        $overallStatus = 'error';
                    }
                }
                
                echo "<div class='info'>📊 الوظائف الموجودة: $functionsFound/" . count($jsFunctions) . "</div>";
            }
            echo "</div>";
            
            // Test Summary
            echo "<div class='section'>";
            echo "<h2>📊 7. ملخص النتائج</h2>";
            
            $totalTests = count($testResults);
            $passedTests = count(array_filter($testResults, function($result) { return $result === 'PASS'; }));
            $failedTests = count(array_filter($testResults, function($result) { return $result === 'FAIL'; }));
            $warningTests = count(array_filter($testResults, function($result) { return $result === 'WARNING'; }));
            
            echo "<div class='test-grid'>";
            
            echo "<div class='test-card' style='background: #d4edda; border-color: #c3e6cb;'>";
            echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ نجح</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #155724;'>$passedTests</p>";
            echo "</div>";
            
            echo "<div class='test-card' style='background: #f8d7da; border-color: #f5c6cb;'>";
            echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ فشل</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #721c24;'>$failedTests</p>";
            echo "</div>";
            
            echo "<div class='test-card' style='background: #fff3cd; border-color: #ffeaa7;'>";
            echo "<h3 style='color: #856404; margin: 0 0 10px 0;'>⚠️ تحذير</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #856404;'>$warningTests</p>";
            echo "</div>";
            
            echo "<div class='test-card' style='background: #d1ecf1; border-color: #bee5eb;'>";
            echo "<h3 style='color: #0c5460; margin: 0 0 10px 0;'>📊 المجموع</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #0c5460;'>$totalTests</p>";
            echo "</div>";
            
            echo "</div>";
            
            $successRate = round(($passedTests / $totalTests) * 100, 1);
            
            if ($successRate >= 95) {
                echo "<div class='success'>";
                echo "<h3>🎉 ممتاز! معدل النجاح: $successRate%</h3>";
                echo "<p>نظام إدارة الاشتراكات يعمل بشكل مثالي وجميع المكونات الحرجة تعمل بنجاح</p>";
                echo "<p><strong>✅ جاهز للإنتاج!</strong></p>";
            } elseif ($successRate >= 85) {
                echo "<div class='warning'>";
                echo "<h3>⚠️ جيد جداً - معدل النجاح: $successRate%</h3>";
                echo "<p>نظام إدارة الاشتراكات يعمل بشكل جيد مع بعض المشاكل البسيطة</p>";
            } else {
                echo "<div class='error'>";
                echo "<h3>❌ يحتاج إلى تحسين - معدل النجاح: $successRate%</h3>";
                echo "<p>هناك مشاكل تحتاج إلى إصلاح</p>";
            }
            echo "</div>";
            echo "</div>";
            
            // Quick Action Links
            echo "<div class='section info'>";
            echo "<h3>🔗 روابط سريعة للاختبار</h3>";
            echo "<a href='admin/index.html' class='test-button'>🏠 لوحة التحكم</a>";
            echo "<a href='test-subscription-management.html' class='test-button'>🔔 تشخيص إدارة الاشتراكات</a>";
            echo "<a href='php/api/subscriptions.php?action=plans' class='test-button'>🔗 API خطط الاشتراك</a>";
            echo "<a href='php/api/subscriptions.php?action=stats' class='test-button'>📊 API إحصائيات الاشتراكات</a>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='section error'>";
            echo "<h2>❌ خطأ في الاختبار</h2>";
            echo "<p>حدث خطأ أثناء تشغيل الاختبارات: " . $e->getMessage() . "</p>";
            echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
            echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
