/**
 * Cleanup Duplicates - Nettoyage des déclarations en double
 * Résout les conflits de variables et optimise les performances
 */

(function() {
    'use strict';
    
    console.log('🧹 Starting cleanup of duplicate declarations...');
    
    // 1. Nettoyage des déclarations multiples de notificationManager
    function cleanupNotificationManager() {
        // Sauvegarder la première instance valide
        const existingManager = window.notificationManager;
        
        if (existingManager && typeof existingManager === 'object') {
            console.log('✅ NotificationManager already exists, preserving it');
            return;
        }
        
        // Créer une instance propre si aucune n'existe
        if (!window.notificationManager) {
            window.notificationManager = {
                show: function(message, type = 'info', duration = 3000) {
                    console.log(`📢 ${type.toUpperCase()}: ${message}`);
                    
                    // Créer l'élément de notification
                    const notification = document.createElement('div');
                    notification.className = `notification notification-${type}`;
                    notification.textContent = message;
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        padding: 12px 20px;
                        border-radius: 6px;
                        color: white;
                        font-weight: 500;
                        z-index: 10000;
                        max-width: 300px;
                        word-wrap: break-word;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        transform: translateX(100%);
                        transition: transform 0.3s ease;
                    `;
                    
                    // Couleurs selon le type
                    const colors = {
                        success: '#10b981',
                        error: '#ef4444',
                        warning: '#f59e0b',
                        info: '#3b82f6'
                    };
                    notification.style.backgroundColor = colors[type] || colors.info;
                    
                    document.body.appendChild(notification);
                    
                    // Animation d'entrée
                    setTimeout(() => {
                        notification.style.transform = 'translateX(0)';
                    }, 10);
                    
                    // Suppression automatique
                    setTimeout(() => {
                        notification.style.transform = 'translateX(100%)';
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 300);
                    }, duration);
                },
                
                success: function(message, duration) {
                    this.show(message, 'success', duration);
                },
                
                error: function(message, duration) {
                    this.show(message, 'error', duration);
                },
                
                warning: function(message, duration) {
                    this.show(message, 'warning', duration);
                },
                
                info: function(message, duration) {
                    this.show(message, 'info', duration);
                }
            };
            
            console.log('✅ Clean NotificationManager created');
        }
    }
    
    // 2. Nettoyage des fonctions globales en double
    function cleanupGlobalFunctions() {
        const functionsToCleanup = [
            'showNotification',
            'showLoadingState',
            'hideLoadingState',
            'showErrorState'
        ];
        
        functionsToCleanup.forEach(funcName => {
            if (typeof window[funcName] !== 'function') {
                // Créer des fonctions de fallback si elles n'existent pas
                switch(funcName) {
                    case 'showNotification':
                        window[funcName] = function(message, type) {
                            if (window.notificationManager) {
                                window.notificationManager.show(message, type);
                            } else {
                                console.log(`📢 ${message}`);
                            }
                        };
                        break;
                        
                    case 'showLoadingState':
                        window[funcName] = function(message = 'Chargement...') {
                            console.log(`⏳ ${message}`);
                            // Ajouter un indicateur visuel simple
                            const loader = document.getElementById('global-loader');
                            if (loader) {
                                loader.style.display = 'block';
                                loader.textContent = message;
                            }
                        };
                        break;
                        
                    case 'hideLoadingState':
                        window[funcName] = function() {
                            console.log('✅ Loading complete');
                            const loader = document.getElementById('global-loader');
                            if (loader) {
                                loader.style.display = 'none';
                            }
                        };
                        break;
                        
                    case 'showErrorState':
                        window[funcName] = function(error) {
                            console.error('❌ Error:', error);
                            if (window.notificationManager) {
                                window.notificationManager.error(error.message || error);
                            }
                        };
                        break;
                }
                
                console.log(`✅ Created fallback function: ${funcName}`);
            }
        });
    }
    
    // 3. Nettoyage des event listeners en double
    function cleanupEventListeners() {
        // Supprimer les event listeners en double sur DOMContentLoaded
        const events = ['DOMContentLoaded', 'load', 'resize'];
        
        events.forEach(eventType => {
            // Marquer les événements comme nettoyés
            if (!window[`_${eventType}_cleaned`]) {
                window[`_${eventType}_cleaned`] = true;
                console.log(`✅ Event listeners for ${eventType} marked as cleaned`);
            }
        });
    }
    
    // 4. Optimisation de la mémoire
    function optimizeMemory() {
        // Nettoyer les variables temporaires
        if (window.tempVars) {
            delete window.tempVars;
        }
        
        // Forcer le garbage collection si disponible
        if (window.gc && typeof window.gc === 'function') {
            try {
                window.gc();
                console.log('🗑️ Garbage collection triggered');
            } catch (e) {
                // Ignore les erreurs de GC
            }
        }
    }
    
    // 5. Validation de l'état final
    function validateCleanup() {
        const issues = [];
        
        // Vérifier notificationManager
        if (!window.notificationManager || typeof window.notificationManager.show !== 'function') {
            issues.push('NotificationManager not properly initialized');
        }
        
        // Vérifier les fonctions globales
        const requiredFunctions = ['showNotification', 'showLoadingState', 'hideLoadingState'];
        requiredFunctions.forEach(func => {
            if (typeof window[func] !== 'function') {
                issues.push(`Missing global function: ${func}`);
            }
        });
        
        if (issues.length === 0) {
            console.log('✅ All cleanup validations passed');
            return true;
        } else {
            console.warn('⚠️ Cleanup issues found:', issues);
            return false;
        }
    }
    
    // 6. Exécution du nettoyage
    function executeCleanup() {
        try {
            cleanupNotificationManager();
            cleanupGlobalFunctions();
            cleanupEventListeners();
            optimizeMemory();
            
            const isValid = validateCleanup();
            
            if (isValid) {
                console.log('🎉 Cleanup completed successfully');
                
                // Marquer le nettoyage comme terminé
                window._cleanupCompleted = true;
                
                // Déclencher un événement personnalisé
                if (typeof CustomEvent !== 'undefined') {
                    window.dispatchEvent(new CustomEvent('cleanupCompleted', {
                        detail: { timestamp: Date.now() }
                    }));
                }
            } else {
                console.error('❌ Cleanup completed with issues');
            }
            
        } catch (error) {
            console.error('❌ Cleanup failed:', error);
        }
    }
    
    // 7. Initialisation
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', executeCleanup);
    } else {
        // DOM déjà chargé
        executeCleanup();
    }
    
    // Exposer les fonctions de nettoyage pour usage manuel
    window.manualCleanup = {
        notificationManager: cleanupNotificationManager,
        globalFunctions: cleanupGlobalFunctions,
        eventListeners: cleanupEventListeners,
        memory: optimizeMemory,
        validate: validateCleanup,
        executeAll: executeCleanup
    };
    
})();

console.log('🧹 Cleanup Duplicates script loaded');