<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفئات - صفحة مستقلة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .categories-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 25px;
        }
        
        .category-item {
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-sitemap"></i> إدارة الفئات - صفحة مستقلة</h1>
            <p>اختبار مستقل لإدارة الفئات</p>
            <button class="btn btn-primary" onclick="loadCategories()">
                <i class="fas fa-sync-alt"></i> تحميل الفئات
            </button>
            <a href="index.html" class="btn btn-success">
                <i class="fas fa-arrow-left"></i> العودة للوحة الإدارة
            </a>
        </div>
        
        <div id="categoriesContent">
            <div class="loading">
                <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <p>انقر على "تحميل الفئات" لبدء التحميل</p>
            </div>
        </div>
    </div>

    <script>
        console.log('🔧 تحميل صفحة إدارة الفئات المستقلة...');
        
        function loadCategories() {
            console.log('🗂️ بدء تحميل الفئات...');
            
            const container = document.getElementById('categoriesContent');
            
            // Show loading
            container.innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <p>جاري تحميل الفئات...</p>
                </div>
            `;
            
            // Fetch data
            fetch('php/categories.php?action=get_all')
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('📦 البيانات:', data);
                    if (data.success) {
                        renderCategories(data.data);
                    } else {
                        throw new Error(data.message || 'فشل في جلب البيانات');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ:', error);
                    showError(error.message);
                });
        }
        
        function renderCategories(data) {
            console.log('🎨 رسم الفئات...');
            
            const container = document.getElementById('categoriesContent');
            const categories = data.categories;
            const mainCategories = categories.filter(c => c.parent_id === null);
            const subCategories = categories.filter(c => c.parent_id !== null);
            const featuredCategories = categories.filter(c => c.is_featured == 1);
            
            const html = `
                <!-- Statistics -->
                <div class="stats">
                    <div class="stat-card">
                        <div style="font-size: 2rem; color: #667eea; margin-bottom: 10px;">
                            <i class="fas fa-folder"></i>
                        </div>
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.total}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">إجمالي الفئات</p>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 2rem; color: #28a745; margin-bottom: 10px;">
                            <i class="fas fa-folder-open"></i>
                        </div>
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${mainCategories.length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات الرئيسية</p>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 2rem; color: #17a2b8; margin-bottom: 10px;">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${subCategories.length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات الفرعية</p>
                    </div>
                    <div class="stat-card">
                        <div style="font-size: 2rem; color: #ffc107; margin-bottom: 10px;">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${featuredCategories.length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات المميزة</p>
                    </div>
                </div>
                
                <!-- Categories List -->
                <div class="categories-list">
                    <h3 style="margin-bottom: 20px; color: #333;">
                        <i class="fas fa-sitemap"></i> قائمة الفئات (${data.total})
                    </h3>
                    ${renderCategoriesList(categories)}
                </div>
                
                <!-- Success Message -->
                <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
                    <i class="fas fa-check-circle"></i> <strong>تم تحميل الفئات بنجاح!</strong>
                    تم عرض ${data.total} فئة.
                </div>
            `;
            
            container.innerHTML = html;
            console.log('✅ تم رسم الفئات بنجاح');
        }
        
        function renderCategoriesList(categories) {
            const mainCategories = categories.filter(c => c.parent_id === null);
            let html = '';
            
            mainCategories.forEach(mainCat => {
                const subCategories = categories.filter(c => c.parent_id == mainCat.id);
                const featuredIcon = mainCat.is_featured == 1 ? '⭐' : '';
                
                html += `
                    <div class="category-item" style="border-left-color: ${mainCat.color}; background: linear-gradient(135deg, ${mainCat.color}10 0%, #ffffff 100%);">
                        <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 10px;">
                            <div style="color: ${mainCat.color}; font-size: 1.5rem;">
                                <i class="${mainCat.icon || 'fas fa-folder'}"></i>
                            </div>
                            <div>
                                <h4 style="margin: 0; color: #333;">${mainCat.name_ar} ${featuredIcon}</h4>
                                <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9em;">${mainCat.description_ar || 'لا يوجد وصف'}</p>
                            </div>
                        </div>
                        
                        ${subCategories.length > 0 ? `
                            <div style="margin-right: 30px; margin-top: 15px;">
                                <strong style="color: #555;">الفئات الفرعية (${subCategories.length}):</strong>
                                <div style="margin-top: 10px;">
                                    ${subCategories.map(subCat => `
                                        <div style="display: inline-block; margin: 5px 10px 5px 0; padding: 5px 10px; background: white; border-radius: 15px; border: 1px solid ${subCat.color}; font-size: 0.85em;">
                                            <i class="${subCat.icon || 'fas fa-folder'}" style="color: ${subCat.color};"></i>
                                            ${subCat.name_ar} ${subCat.is_featured == 1 ? '⭐' : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : '<p style="margin-right: 30px; color: #999; font-style: italic; font-size: 0.9em;">لا توجد فئات فرعية</p>'}
                    </div>
                `;
            });
            
            return html;
        }
        
        function showError(message) {
            const container = document.getElementById('categoriesContent');
            container.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <h3>خطأ في تحميل الفئات</h3>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="loadCategories()">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        }
        
        // Auto-load on page ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 الصفحة جاهزة');
            // Uncomment to auto-load: loadCategories();
        });
        
        console.log('✅ تم تحميل الصفحة المستقلة');
    </script>
</body>
</html>
