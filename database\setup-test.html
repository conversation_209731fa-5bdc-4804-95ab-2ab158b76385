<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - إعدادات النظام</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
        
        .setup-section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #dee2e6;
        }
        
        .setup-section h3 {
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-family: inherit;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .results {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #dee2e6;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-database"></i>
                إعداد قاعدة البيانات
            </h1>
            <p>إعداد وتهيئة قاعدة البيانات لنظام إعدادات النظام</p>
        </div>

        <!-- Database Setup Section -->
        <div class="setup-section">
            <h3><i class="fas fa-server"></i> إعداد قاعدة البيانات</h3>
            <p>سيتم إنشاء جميع الجداول المطلوبة وإدراج البيانات الافتراضية</p>
            
            <div style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="setupDatabase()" id="setupBtn">
                    <i class="fas fa-play"></i>
                    بدء الإعداد
                </button>
                <button class="btn btn-warning" onclick="verifyDatabase()" id="verifyBtn">
                    <i class="fas fa-check"></i>
                    التحقق من الإعداد
                </button>
                <button class="btn btn-success" onclick="testConnection()" id="testBtn">
                    <i class="fas fa-link"></i>
                    اختبار الاتصال
                </button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري تنفيذ العملية...</p>
            </div>

            <!-- Results -->
            <div class="results" id="results" style="display: none;">
                <h4><i class="fas fa-list"></i> نتائج الإعداد</h4>
                <div id="resultsList"></div>
            </div>

            <!-- Statistics -->
            <div class="stats" id="stats" style="display: none;">
                <div class="stat-card">
                    <div class="stat-number" id="totalTables">0</div>
                    <div class="stat-label">إجمالي الجداول</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successCount">0</div>
                    <div class="stat-label">نجح</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="errorCount">0</div>
                    <div class="stat-label">فشل</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="dataRows">0</div>
                    <div class="stat-label">صفوف البيانات</div>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="setup-section" id="nextSteps" style="display: none;">
            <h3><i class="fas fa-arrow-right"></i> الخطوات التالية</h3>
            <div id="nextStepsList"></div>
        </div>
    </div>

    <script>
        async function setupDatabase() {
            const setupBtn = document.getElementById('setupBtn');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const stats = document.getElementById('stats');
            
            // Show loading
            setupBtn.disabled = true;
            loading.classList.add('show');
            results.style.display = 'none';
            stats.style.display = 'none';
            
            try {
                const response = await fetch('setup-system-settings.php');
                const data = await response.json();
                
                // Hide loading
                loading.classList.remove('show');
                setupBtn.disabled = false;
                
                // Show results
                displayResults(data);
                
                if (data.success) {
                    showNotification('تم إعداد قاعدة البيانات بنجاح!', 'success');
                } else {
                    showNotification('حدثت أخطاء أثناء الإعداد', 'error');
                }
                
            } catch (error) {
                loading.classList.remove('show');
                setupBtn.disabled = false;
                showNotification('فشل في الاتصال بالخادم: ' + error.message, 'error');
            }
        }

        async function verifyDatabase() {
            showNotification('جاري التحقق من قاعدة البيانات...', 'info');
            
            try {
                const response = await fetch('setup-system-settings.php');
                const data = await response.json();
                
                if (data.verification) {
                    displayVerification(data.verification);
                    showNotification('تم التحقق من قاعدة البيانات', 'success');
                }
                
            } catch (error) {
                showNotification('فشل في التحقق: ' + error.message, 'error');
            }
        }

        async function testConnection() {
            showNotification('جاري اختبار الاتصال...', 'info');
            
            try {
                const response = await fetch('../config/database.php');
                if (response.ok) {
                    showNotification('الاتصال بقاعدة البيانات يعمل بشكل صحيح', 'success');
                } else {
                    showNotification('فشل في الاتصال بقاعدة البيانات', 'error');
                }
            } catch (error) {
                showNotification('خطأ في الاتصال: ' + error.message, 'error');
            }
        }

        function displayResults(data) {
            const results = document.getElementById('results');
            const resultsList = document.getElementById('resultsList');
            const stats = document.getElementById('stats');
            
            results.style.display = 'block';
            stats.style.display = 'grid';
            
            // Clear previous results
            resultsList.innerHTML = '';
            
            // Display results
            if (data.results) {
                data.results.forEach(result => {
                    const item = document.createElement('div');
                    item.className = 'result-item';
                    item.innerHTML = `
                        <span>
                            <strong>${result.name}</strong> - ${result.message}
                        </span>
                        <span class="status ${result.status}">${result.status === 'success' ? 'نجح' : 'فشل'}</span>
                    `;
                    resultsList.appendChild(item);
                });
            }
            
            // Update statistics
            if (data.statistics) {
                document.getElementById('totalTables').textContent = data.verification ? data.verification.length : 0;
                document.getElementById('successCount').textContent = data.statistics.success_count || 0;
                document.getElementById('errorCount').textContent = data.statistics.error_count || 0;
            }
            
            // Show next steps
            if (data.next_steps) {
                const nextSteps = document.getElementById('nextSteps');
                const nextStepsList = document.getElementById('nextStepsList');
                
                nextSteps.style.display = 'block';
                nextStepsList.innerHTML = '';
                
                data.next_steps.forEach(step => {
                    const stepItem = document.createElement('p');
                    stepItem.innerHTML = `<i class="fas fa-arrow-left"></i> ${step}`;
                    stepItem.style.margin = '10px 0';
                    nextStepsList.appendChild(stepItem);
                });
            }
        }

        function displayVerification(verification) {
            const resultsList = document.getElementById('resultsList');
            resultsList.innerHTML = '';
            
            verification.forEach(table => {
                const item = document.createElement('div');
                item.className = 'result-item';
                item.innerHTML = `
                    <span>
                        <strong>${table.name_ar} (${table.table})</strong> - ${table.count} صف
                    </span>
                    <span class="status ${table.status}">${table.exists ? 'موجود' : 'غير موجود'}</span>
                `;
                resultsList.appendChild(item);
            });
            
            document.getElementById('results').style.display = 'block';
        }

        function showNotification(message, type) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 1000;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
