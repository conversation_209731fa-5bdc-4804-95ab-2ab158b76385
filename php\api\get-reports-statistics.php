<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Prevent any output before JSON
ob_start();

try {
    // Database connection
    require_once '../config/database.php';
    
    $response = [
        'success' => true,
        'data' => [
            'overview' => [
                'total_sales' => 125000,
                'total_orders' => 1247,
                'conversion_rate' => 8.5,
                'active_users' => 12,
                'growth_rate' => 15.3
            ],
            'sales_data' => [
                'daily' => [
                    ['date' => date('Y-m-d', strtotime('-6 days')), 'sales' => 1200],
                    ['date' => date('Y-m-d', strtotime('-5 days')), 'sales' => 1800],
                    ['date' => date('Y-m-d', strtotime('-4 days')), 'sales' => 1500],
                    ['date' => date('Y-m-d', strtotime('-3 days')), 'sales' => 2200],
                    ['date' => date('Y-m-d', strtotime('-2 days')), 'sales' => 1900],
                    ['date' => date('Y-m-d', strtotime('-1 day')), 'sales' => 2500],
                    ['date' => date('Y-m-d'), 'sales' => 1800]
                ],
                'monthly' => [
                    ['month' => date('Y-m', strtotime('-2 months')), 'sales' => 35000],
                    ['month' => date('Y-m', strtotime('-1 month')), 'sales' => 42000],
                    ['month' => date('Y-m'), 'sales' => 48000]
                ]
            ],
            'top_products' => [
                [
                    'id' => 1,
                    'name' => 'منتج رقم 1',
                    'sales' => 150,
                    'revenue' => 15000,
                    'growth' => 12.5
                ],
                [
                    'id' => 2,
                    'name' => 'منتج رقم 2',
                    'sales' => 120,
                    'revenue' => 12000,
                    'growth' => 8.3
                ],
                [
                    'id' => 3,
                    'name' => 'منتج رقم 3',
                    'sales' => 95,
                    'revenue' => 9500,
                    'growth' => -2.1
                ]
            ],
            'customer_analytics' => [
                'new_customers' => 45,
                'returning_customers' => 78,
                'customer_lifetime_value' => 850,
                'churn_rate' => 5.2
            ],
            'traffic_sources' => [
                ['source' => 'البحث المباشر', 'visitors' => 1250, 'percentage' => 35],
                ['source' => 'وسائل التواصل الاجتماعي', 'visitors' => 890, 'percentage' => 25],
                ['source' => 'الإعلانات المدفوعة', 'visitors' => 712, 'percentage' => 20],
                ['source' => 'المراجع', 'visitors' => 534, 'percentage' => 15],
                ['source' => 'أخرى', 'visitors' => 178, 'percentage' => 5]
            ]
        ],
        'message' => 'Reports and statistics retrieved successfully'
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => 'Failed to retrieve reports and statistics',
        'message' => $e->getMessage(),
        'data' => []
    ];
}

// Clear any output buffer and send JSON
ob_clean();
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;
?>
