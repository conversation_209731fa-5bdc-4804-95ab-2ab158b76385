<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - لوحة التحكم</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/system-settings.css">
</head>
<body>
    <div class="system-settings-container">
        <!-- Header -->
        <header class="system-header">
            <h1>
                <i class="fas fa-cogs"></i>
                إعدادات النظام
            </h1>
            <p>إدارة شاملة لجميع إعدادات وتكوينات النظام</p>
            
            <!-- Save Indicator -->
            <div class="save-indicator saved">محفوظ</div>
        </header>

        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <a href="../index.html"><i class="fas fa-home"></i> لوحة التحكم</a>
            <span class="separator">/</span>
            <span>إعدادات النظام</span>
        </nav>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="stat-card">
                <div class="stat-number" data-status="total_users">0</div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" data-status="active_stores">0</div>
                <div class="stat-label">المتاجر النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" data-status="total_categories">0</div>
                <div class="stat-label">الفئات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" data-status="system_health">100%</div>
                <div class="stat-label">صحة النظام</div>
            </div>
        </div>

        <!-- Settings Grid -->
        <div class="settings-grid">
            <!-- Categories Management -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="setting-title">
                        <h3>الفئات</h3>
                        <p>إدارة فئات المنتجات وتصنيفاتها</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="categories.html" class="btn btn-primary">
                        <i class="fas fa-edit"></i>
                        إدارة الفئات
                    </a>
                </div>
            </div>

            <!-- Payment Settings -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #27ae60, #229954);">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="setting-title">
                        <h3>إعدادات الدفع</h3>
                        <p>تكوين طرق الدفع والبوابات المالية</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="payment-settings.html" class="btn btn-success">
                        <i class="fas fa-cog"></i>
                        إعدادات الدفع
                    </a>
                </div>
            </div>

            <!-- General Settings -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #3498db, #2980b9);">
                        <i class="fas fa-sliders-h"></i>
                    </div>
                    <div class="setting-title">
                        <h3>إعدادات عامة</h3>
                        <p>الإعدادات الأساسية والعامة للنظام</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="general-settings.html" class="btn btn-primary">
                        <i class="fas fa-tools"></i>
                        الإعدادات العامة
                    </a>
                </div>
            </div>

            <!-- Store Settings -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="setting-title">
                        <h3>إعدادات المتجر</h3>
                        <p>تكوين إعدادات المتجر والمنتجات</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="store-settings.html" class="btn btn-warning">
                        <i class="fas fa-store-alt"></i>
                        إعدادات المتجر
                    </a>
                </div>
            </div>

            <!-- User Management -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="setting-title">
                        <h3>إدارة المستخدمين</h3>
                        <p>إدارة حسابات وبيانات المستخدمين</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="user-management.html" class="btn btn-primary">
                        <i class="fas fa-user-cog"></i>
                        إدارة المستخدمين
                    </a>
                </div>
            </div>

            <!-- Stores Management -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #1abc9c, #16a085);">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="setting-title">
                        <h3>المتاجر</h3>
                        <p>إدارة متاجر المستخدمين ومنتجاتهم</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="stores.html" class="btn btn-success">
                        <i class="fas fa-building"></i>
                        إدارة المتاجر
                    </a>
                </div>
            </div>

            <!-- Roles Management -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #34495e, #2c3e50);">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="setting-title">
                        <h3>إدارة الأدوار</h3>
                        <p>إدارة أدوار وصلاحيات المستخدمين</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="roles.html" class="btn btn-secondary">
                        <i class="fas fa-key"></i>
                        إدارة الأدوار
                    </a>
                </div>
            </div>

            <!-- Subscriptions Management -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #e67e22, #d35400);">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="setting-title">
                        <h3>إدارة الاشتراكات</h3>
                        <p>إدارة خطط وحدود الاشتراكات</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="subscriptions.html" class="btn btn-warning">
                        <i class="fas fa-gem"></i>
                        إدارة الاشتراكات
                    </a>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="setting-title">
                        <h3>الأمان</h3>
                        <p>إعدادات الأمان والحماية</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="security.html" class="btn btn-danger">
                        <i class="fas fa-lock"></i>
                        إعدادات الأمان
                    </a>
                </div>
            </div>

            <!-- System Test -->
            <div class="setting-card">
                <div class="setting-card-header">
                    <div class="setting-icon" style="background: linear-gradient(135deg, #95a5a6, #7f8c8d);">
                        <i class="fas fa-bug"></i>
                    </div>
                    <div class="setting-title">
                        <h3>اختبار النظام</h3>
                        <p>فحص شامل لصحة وأداء النظام</p>
                    </div>
                </div>
                <div class="setting-actions">
                    <a href="system-test.html" class="btn btn-secondary">
                        <i class="fas fa-stethoscope"></i>
                        اختبار النظام
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="setting-card" style="margin-top: 30px;">
            <div class="setting-card-header">
                <div class="setting-icon" style="background: linear-gradient(135deg, #3498db, #2980b9);">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="setting-title">
                    <h3>إجراءات سريعة</h3>
                    <p>أدوات وإجراءات سريعة لإدارة النظام</p>
                </div>
            </div>
            <div class="setting-actions">
                <button class="btn btn-primary" onclick="SystemSettings.saveCurrentSection()">
                    <i class="fas fa-save"></i>
                    حفظ جميع التغييرات
                </button>
                <button class="btn btn-success" onclick="refreshSystemStats()">
                    <i class="fas fa-sync"></i>
                    تحديث الإحصائيات
                </button>
                <button class="btn btn-warning" onclick="exportSystemSettings()">
                    <i class="fas fa-download"></i>
                    تصدير الإعدادات
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/core.js"></script>
    <script>
        // Page-specific functions
        async function refreshSystemStats() {
            try {
                SystemSettings.showLoading('.quick-stats', 'تحديث الإحصائيات...');
                
                const stats = await SystemSettings.apiCall('system-stats.php');
                
                // Update stat cards
                Object.keys(stats.data).forEach(key => {
                    const element = document.querySelector(`[data-status="${key}"]`);
                    if (element) {
                        element.textContent = SystemSettings.formatNumber(stats.data[key]);
                    }
                });
                
                SystemSettings.hideLoading('.quick-stats');
                SystemSettings.showNotification('تم تحديث الإحصائيات بنجاح', 'success');
                
            } catch (error) {
                SystemSettings.hideLoading('.quick-stats');
                SystemSettings.showNotification('فشل في تحديث الإحصائيات: ' + error.message, 'error');
            }
        }

        async function exportSystemSettings() {
            try {
                SystemSettings.showNotification('جاري تصدير الإعدادات...', 'info');
                
                const response = await fetch(SystemSettings.config.apiBase + 'export-settings.php');
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `system-settings-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                a.remove();
                
                SystemSettings.showNotification('تم تصدير الإعدادات بنجاح', 'success');
                
            } catch (error) {
                SystemSettings.showNotification('فشل في تصدير الإعدادات: ' + error.message, 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            SystemSettings.state.currentSection = 'SystemOverview';
            refreshSystemStats();
        });
    </script>
</body>
</html>
