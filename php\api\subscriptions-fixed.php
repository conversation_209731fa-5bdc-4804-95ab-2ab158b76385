<?php

/**
 * Subscriptions Management API with Enhanced Error Handling
 * Handles CRUD operations for subscription plans and user subscriptions
 * Addresses JSON parsing errors and implements comprehensive error handling
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../ApiErrorHandler.php';
require_once __DIR__ . '/../../config/environment.php';

// Initialize error handler to catch any issues before JSON output
ApiErrorHandler::init();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $pdo = EnvironmentConfig::getInstance()->getDatabaseConnection();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    // Validate HTTP method
    ApiErrorHandler::checkMethod(['GET', 'POST', 'PUT', 'DELETE']);

    // Handle different HTTP methods and actions
    switch ($method) {
        case 'GET':
            if ($action === 'plans' || empty($action)) {
                handleGetPlans($pdo);
            } elseif ($action === 'stats') {
                handleGetSubscriptionStats($pdo);
            } elseif ($action === 'check_limits') {
                handleCheckLimits($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'POST':
            if ($action === 'create_plan') {
                handleCreatePlan($pdo);
            } elseif ($action === 'assign') {
                handleAssignSubscription($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'PUT':
            if ($action === 'update_plan') {
                handleUpdatePlan($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        case 'DELETE':
            if ($action === 'delete_plan') {
                handleDeletePlan($pdo);
            } else {
                ApiErrorHandler::sendError('إجراء غير صالح', 400, 'INVALID_ACTION');
            }
            break;

        default:
            ApiErrorHandler::sendError('طريقة الطلب غير مسموحة', 405, 'METHOD_NOT_ALLOWED');
    }
} catch (Exception $e) {
    ApiErrorHandler::sendError(
        'خطأ في API الاشتراكات: ' . $e->getMessage(),
        500,
        'SUBSCRIPTION_API_ERROR'
    );
}

/**
 * Get all subscription plans with user counts
 */
function handleGetPlans($pdo)
{
    try {
        $stmt = $pdo->prepare("
            SELECT
                sp.*,
                COUNT(u.id) as user_count
            FROM subscription_plans sp
            LEFT JOIN users u ON sp.id = u.subscription_id
            GROUP BY sp.id
            ORDER BY sp.sort_order ASC
        ");

        $stmt->execute();
        $plans = $stmt->fetchAll();

        // Format plans for response
        $formattedPlans = array_map(function ($plan) {
            return [
                'id' => (int)$plan['id'],
                'name' => $plan['name'],
                'display_name_ar' => $plan['display_name_ar'],
                'display_name_en' => $plan['display_name_en'],
                'description_ar' => $plan['description_ar'],
                'description_en' => $plan['description_en'],
                'price' => (float)$plan['price'],
                'currency' => $plan['currency'],
                'duration_days' => (int)$plan['duration_days'],
                'max_products' => (int)$plan['max_products'],
                'max_landing_pages' => (int)$plan['max_landing_pages'],
                'max_storage_mb' => (int)$plan['max_storage_mb'],
                'max_templates' => (int)$plan['max_templates'],
                'features' => json_decode($plan['features'] ?? '[]', true),
                'is_active' => (bool)$plan['is_active'],
                'is_featured' => (bool)$plan['is_featured'],
                'sort_order' => (int)$plan['sort_order'],
                'user_count' => (int)$plan['user_count'],
                'created_at' => $plan['created_at'],
                'updated_at' => $plan['updated_at']
            ];
        }, $plans);

        ApiErrorHandler::sendSuccess([
            'plans' => $formattedPlans,
            'total' => count($formattedPlans)
        ], 'تم تحميل خطط الاشتراك بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في جلب خطط الاشتراك: ' . $e->getMessage(),
            500,
            'FETCH_PLANS_ERROR'
        );
    }
}

/**
 * Get subscription statistics
 */
function handleGetSubscriptionStats($pdo)
{
    try {
        // Total subscriptions
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM users WHERE subscription_id IS NOT NULL");
        $stmt->execute();
        $totalSubscriptions = $stmt->fetch()['total'];

        // Revenue by plan
        $stmt = $pdo->prepare("
            SELECT
                sp.display_name_ar as plan_name,
                sp.price,
                COUNT(u.id) as user_count,
                (sp.price * COUNT(u.id)) as total_revenue
            FROM subscription_plans sp
            LEFT JOIN users u ON sp.id = u.subscription_id
            GROUP BY sp.id, sp.display_name_ar, sp.price
            ORDER BY total_revenue DESC
        ");
        $stmt->execute();
        $revenueByPlan = $stmt->fetchAll();

        // Usage statistics
        $stmt = $pdo->prepare("
            SELECT
                sp.display_name_ar as plan_name,
                sp.max_products,
                sp.max_landing_pages,
                COUNT(u.id) as user_count
            FROM subscription_plans sp
            LEFT JOIN users u ON sp.id = u.subscription_id
            GROUP BY sp.id, sp.display_name_ar, sp.max_products, sp.max_landing_pages
            ORDER BY sp.sort_order ASC
        ");
        $stmt->execute();
        $usageStats = $stmt->fetchAll();

        ApiErrorHandler::sendSuccess([
            'total_subscriptions' => (int)$totalSubscriptions,
            'revenue_by_plan' => $revenueByPlan,
            'usage_stats' => $usageStats
        ], 'تم تحميل إحصائيات الاشتراكات بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في جلب إحصائيات الاشتراكات: ' . $e->getMessage(),
            500,
            'FETCH_STATS_ERROR'
        );
    }
}

/**
 * Create a new subscription plan
 */
function handleCreatePlan($pdo)
{
    try {
        $input = ApiErrorHandler::getJsonInput();
        if ($input === false) return; // Error already sent

        // Validate required fields
        $required = ['name', 'display_name_ar', 'price', 'duration_days'];
        if (!ApiErrorHandler::validateRequired($input, $required)) return;

        // Check if plan name already exists
        $stmt = $pdo->prepare("SELECT id FROM subscription_plans WHERE name = ?");
        $stmt->execute([$input['name']]);
        if ($stmt->fetch()) {
            ApiErrorHandler::sendError('اسم الخطة موجود بالفعل', 400, 'PLAN_NAME_EXISTS');
            return;
        }

        // Insert plan
        $stmt = $pdo->prepare("
            INSERT INTO subscription_plans (
                name, display_name_ar, display_name_en, description_ar, description_en,
                price, currency, duration_days, max_products, max_landing_pages,
                max_storage_mb, max_templates, features, is_active, is_featured, sort_order
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $features = json_encode($input['features'] ?? []);

        $stmt->execute([
            $input['name'],
            $input['display_name_ar'],
            $input['display_name_en'] ?? '',
            $input['description_ar'] ?? '',
            $input['description_en'] ?? '',
            $input['price'],
            $input['currency'] ?? 'USD',
            $input['duration_days'],
            $input['max_products'] ?? 0,
            $input['max_landing_pages'] ?? 0,
            $input['max_storage_mb'] ?? 0,
            $input['max_templates'] ?? 0,
            $features,
            $input['is_active'] ?? 1,
            $input['is_featured'] ?? 0,
            $input['sort_order'] ?? 0
        ]);

        $planId = $pdo->lastInsertId();

        ApiErrorHandler::sendSuccess([
            'plan_id' => (int)$planId
        ], 'تم إنشاء خطة الاشتراك بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في إنشاء خطة الاشتراك: ' . $e->getMessage(),
            500,
            'CREATE_PLAN_ERROR'
        );
    }
}

/**
 * Update an existing subscription plan
 */
function handleUpdatePlan($pdo)
{
    try {
        $input = ApiErrorHandler::getJsonInput();
        if ($input === false) return; // Error already sent

        if (!isset($input['id']) || empty($input['id'])) {
            ApiErrorHandler::sendError('معرف الخطة مطلوب', 400, 'MISSING_PLAN_ID');
            return;
        }

        $planId = $input['id'];
        $updates = [];
        $params = [];

        // Build dynamic update query
        $allowedFields = [
            'name',
            'display_name_ar',
            'display_name_en',
            'description_ar',
            'description_en',
            'price',
            'currency',
            'duration_days',
            'max_products',
            'max_landing_pages',
            'max_storage_mb',
            'max_templates',
            'is_active',
            'is_featured',
            'sort_order'
        ];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updates[] = "$field = ?";
                $params[] = $input[$field];
            }
        }

        // Handle features separately
        if (isset($input['features'])) {
            $updates[] = "features = ?";
            $params[] = json_encode($input['features']);
        }

        if (empty($updates)) {
            ApiErrorHandler::sendError('لا توجد بيانات للتحديث', 400, 'NO_UPDATE_DATA');
            return;
        }

        $updates[] = "updated_at = NOW()";
        $params[] = $planId;

        $sql = "UPDATE subscription_plans SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        if ($stmt->rowCount() === 0) {
            ApiErrorHandler::sendError('خطة الاشتراك غير موجودة', 404, 'PLAN_NOT_FOUND');
            return;
        }

        ApiErrorHandler::sendSuccess(null, 'تم تحديث خطة الاشتراك بنجاح');
    } catch (Exception $e) {
        ApiErrorHandler::sendError(
            'فشل في تحديث خطة الاشتراك: ' . $e->getMessage(),
            500,
            'UPDATE_PLAN_ERROR'
        );
    }
}
