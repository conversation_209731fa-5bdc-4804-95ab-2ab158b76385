<?php
/**
 * Create Categories Tables
 * إنشاء جداول الفئات
 */

echo "<h2>إنشاء جداول الفئات</h2>\n";

// Load configuration manually
$envFile = '../../.env';
if (!file_exists($envFile)) {
    die("ملف .env غير موجود في: $envFile");
}

// Load configuration
$config = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) {
        continue;
    }
    list($key, $value) = explode('=', $line, 2) + [NULL, NULL];
    if (!empty($key)) {
        $config[trim($key)] = trim($value ?? '');
    }
}

// Check required database settings
$required = ['DB_HOST', 'DB_PORT', 'DB_USERNAME', 'DB_DATABASE'];
$missing = [];
foreach ($required as $key) {
    if (empty($config[$key])) {
        $missing[] = $key;
    }
}

if (!empty($missing)) {
    die('إعدادات قاعدة البيانات المفقودة: ' . implode(', ', $missing));
}

try {
    // Connect to database
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $config['DB_HOST'],
        $config['DB_PORT'],
        $config['DB_DATABASE']
    );
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, $config['DB_USERNAME'], $config['DB_PASSWORD'] ?? '', $options);
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    
    // Read and execute SQL file
    $sqlFile = '../sql/categories.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }
    
    echo "<h3>تنفيذ استعلامات SQL:</h3>\n";
    
    // Split SQL into individual statements
    $statements = explode(';', $sql);
    $executedCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executedCount++;
            
            // Show what was executed
            $firstLine = strtok($statement, "\n");
            echo "<p style='color: green; margin: 2px 0;'>✅ " . substr($firstLine, 0, 60) . "...</p>\n";
        } catch (Exception $e) {
            // Skip if table already exists or other non-critical errors
            if (strpos($e->getMessage(), 'already exists') !== false) {
                echo "<p style='color: orange; margin: 2px 0;'>⚠️ " . substr($firstLine, 0, 60) . "... (موجود مسبقاً)</p>\n";
            } else {
                echo "<p style='color: red; margin: 2px 0;'>❌ خطأ: " . $e->getMessage() . "</p>\n";
            }
        }
    }
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم تنفيذ $executedCount استعلام بنجاح</p>\n";
    
    // Verify tables were created
    echo "<h3>التحقق من الجداول المنشأة:</h3>\n";
    
    $tables = ['categories', 'category_translations', 'category_hierarchy', 'category_stats'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ جدول $table موجود</p>\n";
                
                // Count records
                $countStmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $count = $countStmt->fetchColumn();
                echo "<p style='margin-right: 20px;'>عدد السجلات: $count</p>\n";
            } else {
                echo "<p style='color: red;'>❌ جدول $table غير موجود</p>\n";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في التحقق من جدول $table: " . $e->getMessage() . "</p>\n";
        }
    }
    
    // Show sample categories
    echo "<h3>عينة من الفئات المدرجة:</h3>\n";
    try {
        $stmt = $pdo->query("
            SELECT c.id, c.name_ar, c.name_en, c.slug, c.parent_id, p.name_ar as parent_name, c.is_active, c.is_featured
            FROM categories c
            LEFT JOIN categories p ON c.parent_id = p.id
            ORDER BY c.parent_id ASC, c.sort_order ASC, c.name_ar ASC
            LIMIT 15
        ");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($categories) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 0.9em;'>\n";
            echo "<tr style='background: #f0f0f0;'><th>ID</th><th>الاسم العربي</th><th>الاسم الإنجليزي</th><th>الرابط</th><th>الفئة الأب</th><th>نشط</th><th>مميز</th></tr>\n";
            foreach ($categories as $category) {
                $activeIcon = $category['is_active'] ? '✅' : '❌';
                $featuredIcon = $category['is_featured'] ? '⭐' : '';
                echo "<tr>";
                echo "<td>" . $category['id'] . "</td>";
                echo "<td>" . htmlspecialchars($category['name_ar']) . "</td>";
                echo "<td>" . htmlspecialchars($category['name_en'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($category['slug']) . "</td>";
                echo "<td>" . htmlspecialchars($category['parent_name'] ?? 'رئيسية') . "</td>";
                echo "<td style='text-align: center;'>" . $activeIcon . "</td>";
                echo "<td style='text-align: center;'>" . $featuredIcon . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في جلب الفئات: " . $e->getMessage() . "</p>\n";
    }
    
    // Show hierarchy structure
    echo "<h3>هيكل الفئات الهرمي:</h3>\n";
    try {
        $stmt = $pdo->query("
            SELECT c.id, c.name_ar, c.parent_id, c.subcategories_count
            FROM categories c
            LEFT JOIN category_stats s ON c.id = s.category_id
            WHERE c.parent_id IS NULL
            ORDER BY c.sort_order ASC, c.name_ar ASC
        ");
        $mainCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>\n";
        foreach ($mainCategories as $main) {
            echo "<div style='margin-bottom: 10px;'>\n";
            echo "<strong>📁 " . htmlspecialchars($main['name_ar']) . "</strong>\n";
            
            // Get subcategories
            $subStmt = $pdo->prepare("
                SELECT name_ar, subcategories_count
                FROM categories c
                LEFT JOIN category_stats s ON c.id = s.category_id
                WHERE c.parent_id = ?
                ORDER BY c.sort_order ASC, c.name_ar ASC
            ");
            $subStmt->execute([$main['id']]);
            $subcategories = $subStmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($subcategories) {
                foreach ($subcategories as $sub) {
                    echo "<div style='margin-right: 20px; color: #666;'>└── 📂 " . htmlspecialchars($sub['name_ar']) . "</div>\n";
                }
            }
            echo "</div>\n";
        }
        echo "</div>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في عرض الهيكل الهرمي: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<div style='color: green; font-weight: bold; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "🎉 تم إنشاء جداول الفئات بنجاح!<br>";
    echo "تم إنشاء " . count($tables) . " جداول مع البيانات الافتراضية.<br>";
    echo "يمكنك الآن استخدام قسم إدارة الفئات في لوحة الإدارة.";
    echo "</div>\n";
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 5px;'>";
    echo "<strong>الخطوات التالية:</strong><br>";
    echo "1. اختبر API الفئات: <a href='../php/categories.php?action=get_all' target='_blank'>اختبار API</a><br>";
    echo "2. تحقق من قسم إدارة الفئات في لوحة الإدارة<br>";
    echo "3. أضف المزيد من الفئات حسب الحاجة<br>";
    echo "4. اختبر البحث والفلترة<br>";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ خطأ في إنشاء الجداول: " . $e->getMessage();
    echo "</div>\n";
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #ffe7e7; border: 1px solid #ffb3b3; border-radius: 5px;'>";
    echo "<strong>نصائح لحل المشكلة:</strong><br>";
    echo "1. تأكد من صحة إعدادات قاعدة البيانات<br>";
    echo "2. تأكد من وجود صلاحيات إنشاء الجداول والفهارس<br>";
    echo "3. تحقق من إصدار MySQL/MariaDB (يجب أن يدعم FULLTEXT و FOREIGN KEY)<br>";
    echo "4. تأكد من وجود مساحة كافية في قاعدة البيانات<br>";
    echo "</div>\n";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء جداول الفئات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
            margin-top: 25px;
        }
        p {
            margin: 8px 0;
        }
        table {
            font-size: 0.9em;
        }
        th, td {
            padding: 8px 12px;
            text-align: right;
        }
        th {
            font-weight: bold;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="../index.html" class="back-link">← العودة إلى لوحة الإدارة</a>
        <a href="test_database_connection.php" class="back-link" style="background: #28a745;">🔍 اختبار الاتصال</a>
        <a href="../php/categories.php?action=get_all" class="back-link" style="background: #17a2b8;" target="_blank">📊 اختبار API</a>
    </div>
</body>
</html>
