<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل الوصول للحسابات التجريبية</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .account-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .account-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .account-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }
        
        .account-credentials {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        
        .account-role {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .account-description {
            color: #495057;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .steps {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .steps h3 {
            color: #004085;
            margin-bottom: 15px;
        }
        
        .steps ol {
            padding-right: 20px;
        }
        
        .steps li {
            margin-bottom: 10px;
            color: #004085;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 دليل الوصول للحسابات التجريبية</h1>
            <p>متجر مصعب - لوحة التحكم الإدارية</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📋 الحسابات المتاحة</h2>
                <div class="accounts-grid">
                    <div class="account-card">
                        <div class="account-title">🔑 المدير الرئيسي</div>
                        <div class="account-credentials">
                            <strong>اسم المستخدم:</strong> admin<br>
                            <strong>كلمة المرور:</strong> admin123
                        </div>
                        <div class="account-role">الدور: مدير رئيسي (Super Admin)</div>
                        <div class="account-description">
                            صلاحيات كاملة لجميع أقسام النظام، إدارة المستخدمين، والإعدادات المتقدمة.
                        </div>
                    </div>
                    
                    <div class="account-card">
                        <div class="account-title">👑 مالك المتجر</div>
                        <div class="account-credentials">
                            <strong>اسم المستخدم:</strong> mossaab<br>
                            <strong>كلمة المرور:</strong> mossaab2024
                        </div>
                        <div class="account-role">الدور: مالك (Owner)</div>
                        <div class="account-description">
                            حساب مالك المتجر مع صلاحيات إدارة المنتجات، الطلبات، والتقارير المالية.
                        </div>
                    </div>
                    
                    <div class="account-card">
                        <div class="account-title">👨‍💼 مدير المتجر</div>
                        <div class="account-credentials">
                            <strong>اسم المستخدم:</strong> manager<br>
                            <strong>كلمة المرور:</strong> manager123
                        </div>
                        <div class="account-role">الدور: مدير (Manager)</div>
                        <div class="account-description">
                            صلاحيات إدارة المنتجات والطلبات مع قيود على الإعدادات الحساسة.
                        </div>
                    </div>
                    
                    <div class="account-card">
                        <div class="account-title">🧪 حساب تجريبي</div>
                        <div class="account-credentials">
                            <strong>اسم المستخدم:</strong> demo<br>
                            <strong>كلمة المرور:</strong> demo123
                        </div>
                        <div class="account-role">الدور: تجريبي (Demo)</div>
                        <div class="account-description">
                            حساب للاستعراض فقط مع صلاحيات محدودة للقراءة والتصفح.
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <div class="steps">
                    <h3>📝 خطوات تسجيل الدخول</h3>
                    <ol>
                        <li>انقر على زر "إعداد الحسابات التجريبية" أدناه لإنشاء الحسابات</li>
                        <li>انتقل إلى صفحة تسجيل الدخول</li>
                        <li>اختر أحد الحسابات المذكورة أعلاه</li>
                        <li>أدخل اسم المستخدم وكلمة المرور</li>
                        <li>انقر على "تسجيل الدخول"</li>
                        <li>ستتم إعادة توجيهك إلى لوحة التحكم</li>
                    </ol>
                </div>
            </div>
            
            <div class="buttons">
                <a href="setup-demo-users.php" class="btn btn-success" target="_blank">
                    🚀 إعداد الحسابات التجريبية
                </a>
                <a href="login.html" class="btn btn-primary" target="_blank">
                    🔐 صفحة تسجيل الدخول
                </a>
                <a href="test-login-system.php" class="btn btn-secondary" target="_blank">
                    🧪 اختبار النظام
                </a>
            </div>
            
            <div class="warning">
                <strong>⚠️ ملاحظة مهمة:</strong>
                هذه حسابات تجريبية للاستعراض فقط. لا تستخدمها في بيئة الإنتاج.
            </div>
            
            <div class="success">
                <strong>✅ نصائح للاستخدام:</strong>
                <ul style="margin-top: 10px; padding-right: 20px;">
                    <li>جرب الحسابات المختلفة لرؤية الصلاحيات المتنوعة</li>
                    <li>استكشف جميع أقسام لوحة التحكم</li>
                    <li>اختبر إضافة وتعديل المنتجات</li>
                    <li>راجع التقارير والإحصائيات</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
