/**
 * Emergency Navigation Fix
 * Last resort fix for navigation issues
 */

console.log('🚨 Emergency Navigation Fix loading...');

// Emergency fix function
function emergencyNavigationFix() {
    console.log('🚨 Applying emergency navigation fix...');
    
    // Force add click events to all navigation items
    const selectors = [
        '.admin-nav ul li',
        '.sidebar ul li',
        'nav ul li',
        '[data-section]'
    ];
    
    selectors.forEach(selector => {
        const items = document.querySelectorAll(selector);
        console.log(`Found ${items.length} items for selector: ${selector}`);
        
        items.forEach((item, index) => {
            // Skip if already has emergency fix
            if (item.hasAttribute('data-emergency-fixed')) {
                return;
            }
            
            // Mark as fixed
            item.setAttribute('data-emergency-fixed', 'true');
            
            // Make clickable
            item.style.cursor = 'pointer';
            item.style.pointerEvents = 'auto';
            item.style.userSelect = 'none';
            
            // Add click handler
            item.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('🖱️ Emergency click handler triggered for:', this);
                
                // Get section from data-section attribute
                let sectionId = this.getAttribute('data-section');
                
                // If no data-section, try to infer from text content
                if (!sectionId) {
                    const text = this.textContent.trim();
                    if (text.includes('الرئيسية')) sectionId = 'dashboard';
                    else if (text.includes('إدارة المنتجات')) sectionId = 'books';
                    else if (text.includes('الطلبات')) sectionId = 'orders';
                    else if (text.includes('صفحات هبوط')) sectionId = 'landingPages';
                    else if (text.includes('التقارير')) sectionId = 'reports';
                    else if (text.includes('الإعدادات')) sectionId = 'settings';
                    else if (text.includes('الفئات')) sectionId = 'categories';
                    else if (text.includes('المستخدمين')) sectionId = 'usersManagement';
                    else if (text.includes('الأدوار')) sectionId = 'rolesManagement';
                    else if (text.includes('الأمان')) sectionId = 'securitySettings';
                    else if (text.includes('الاشتراكات')) sectionId = 'subscriptionsManagement';
                }
                
                if (sectionId) {
                    console.log(`🎯 Emergency navigation to: ${sectionId}`);
                    emergencyShowSection(sectionId);
                } else {
                    console.warn('⚠️ Could not determine section ID for:', this);
                }
            });
            
            console.log(`✅ Emergency fix applied to item ${index}: ${item.textContent.trim()}`);
        });
    });
}

// Emergency show section function
function emergencyShowSection(sectionId) {
    console.log(`🚨 Emergency showing section: ${sectionId}`);
    
    // Hide all possible section containers
    const hideSelectors = [
        '.content-section',
        '.admin-section',
        '[id$="Content"]',
        '[id$="Section"]',
        '#dashboard',
        '#books',
        '#orders',
        '#landingPages',
        '#reports',
        '#settings',
        '#categories',
        '#usersManagement',
        '#rolesManagement',
        '#securitySettings',
        '#subscriptionsManagement'
    ];
    
    hideSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            el.style.display = 'none';
            el.classList.remove('active');
        });
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.style.display = 'block';
        targetSection.classList.add('active');
        console.log(`✅ Section ${sectionId} shown`);
    } else {
        console.warn(`⚠️ Section ${sectionId} not found, creating emergency content`);
        emergencyCreateSection(sectionId);
    }
    
    // Update active navigation item
    document.querySelectorAll('[data-section]').forEach(item => {
        item.classList.remove('active');
        if (item.getAttribute('data-section') === sectionId) {
            item.classList.add('active');
        }
    });
    
    // Load content
    emergencyLoadContent(sectionId);
}

// Emergency create section
function emergencyCreateSection(sectionId) {
    const mainContent = document.querySelector('.main-content');
    if (!mainContent) {
        console.error('❌ Main content container not found');
        return;
    }
    
    const section = document.createElement('div');
    section.id = sectionId;
    section.className = 'content-section active';
    section.style.display = 'block';
    section.style.padding = '20px';
    
    const titles = {
        'dashboard': 'لوحة المعلومات',
        'books': 'إدارة المنتجات',
        'orders': 'الطلبات',
        'landingPages': 'صفحات الهبوط',
        'reports': 'التقارير والإحصائيات',
        'settings': 'الإعدادات',
        'categories': 'إدارة الفئات',
        'usersManagement': 'إدارة المستخدمين',
        'rolesManagement': 'إدارة الأدوار',
        'securitySettings': 'إعدادات الأمان',
        'subscriptionsManagement': 'إدارة الاشتراكات'
    };
    
    section.innerHTML = `
        <div style="background: white; border-radius: 12px; padding: 30px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
            <h2 style="color: #2d3748; margin-bottom: 20px; display: flex; align-items: center;">
                <i class="fas fa-cog" style="margin-left: 10px;"></i>
                ${titles[sectionId] || sectionId}
            </h2>
            <div id="${sectionId}Content">
                <div style="text-align: center; padding: 40px; color: #4a5568;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <p>جاري تحميل المحتوى...</p>
                    <button onclick="emergencyLoadContent('${sectionId}')" style="margin-top: 15px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                        <i class="fas fa-redo"></i> إعادة المحاولة
                    </button>
                </div>
            </div>
        </div>
    `;
    
    mainContent.appendChild(section);
    console.log(`✅ Emergency section ${sectionId} created`);
}

// Emergency load content
function emergencyLoadContent(sectionId) {
    console.log(`🚨 Emergency loading content for: ${sectionId}`);
    
    const container = document.getElementById(`${sectionId}Content`) || document.getElementById(sectionId);
    if (!container) {
        console.error(`❌ Container for ${sectionId} not found`);
        return;
    }
    
    // Show loading
    container.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #4a5568;">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i>
            <p>جاري تحميل المحتوى...</p>
        </div>
    `;
    
    // Try to load content based on section
    try {
        switch (sectionId) {
            case 'dashboard':
                if (typeof loadDashboard === 'function') {
                    loadDashboard();
                } else {
                    emergencyLoadDashboard(container);
                }
                break;
                
            case 'books':
                if (typeof loadProducts === 'function') {
                    loadProducts();
                } else if (typeof window.loadProducts === 'function') {
                    window.loadProducts();
                } else {
                    emergencyLoadProducts(container);
                }
                break;
                
            case 'orders':
                if (typeof loadOrders === 'function') {
                    loadOrders();
                } else {
                    emergencyLoadOrders(container);
                }
                break;
                
            case 'landingPages':
                if (typeof landingPagesManager !== 'undefined' && landingPagesManager.loadLandingPages) {
                    landingPagesManager.loadLandingPages();
                } else {
                    emergencyLoadLandingPages(container);
                }
                break;
                
            case 'reports':
                if (typeof loadReportsAndStatistics === 'function') {
                    loadReportsAndStatistics();
                } else {
                    emergencyLoadReports(container);
                }
                break;
                
            default:
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #4a5568;">
                        <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                        <h3>${sectionId}</h3>
                        <p>هذا القسم قيد التطوير</p>
                    </div>
                `;
        }
    } catch (error) {
        console.error(`❌ Error loading content for ${sectionId}:`, error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل المحتوى</h4>
                <p>${error.message}</p>
                <button onclick="emergencyLoadContent('${sectionId}')" style="margin-top: 15px; padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Emergency content loaders
function emergencyLoadDashboard(container) {
    container.innerHTML = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px;">
                <h3>إجمالي المنتجات</h3>
                <p style="font-size: 2rem; margin: 0;">--</p>
            </div>
            <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 12px;">
                <h3>الطلبات الجديدة</h3>
                <p style="font-size: 2rem; margin: 0;">--</p>
            </div>
            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 12px;">
                <h3>إجمالي المبيعات</h3>
                <p style="font-size: 2rem; margin: 0;">--</p>
            </div>
        </div>
        <p style="text-align: center; color: #4a5568;">لوحة المعلومات جاهزة</p>
    `;
}

function emergencyLoadProducts(container) {
    container.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3>قائمة المنتجات</h3>
            <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                <i class="fas fa-plus"></i> إضافة منتج
            </button>
        </div>
        <div style="background: white; border-radius: 8px; padding: 20px; border: 1px solid #e2e8f0;">
            <p style="text-align: center; color: #4a5568;">جاري تحميل المنتجات...</p>
        </div>
    `;
}

function emergencyLoadOrders(container) {
    container.innerHTML = `
        <h3 style="margin-bottom: 20px;">قائمة الطلبات</h3>
        <div style="background: white; border-radius: 8px; padding: 20px; border: 1px solid #e2e8f0;">
            <p style="text-align: center; color: #4a5568;">جاري تحميل الطلبات...</p>
        </div>
    `;
}

function emergencyLoadLandingPages(container) {
    container.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3>صفحات الهبوط</h3>
            <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">
                <i class="fas fa-plus"></i> إضافة صفحة
            </button>
        </div>
        <div style="background: white; border-radius: 8px; padding: 20px; border: 1px solid #e2e8f0;">
            <p style="text-align: center; color: #4a5568;">جاري تحميل صفحات الهبوط...</p>
        </div>
    `;
}

function emergencyLoadReports(container) {
    container.innerHTML = `
        <h3 style="margin-bottom: 20px;">التقارير والإحصائيات</h3>
        <div style="background: white; border-radius: 8px; padding: 20px; border: 1px solid #e2e8f0;">
            <p style="text-align: center; color: #4a5568;">جاري تحميل التقارير...</p>
        </div>
    `;
}

// Apply emergency fix when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚨 Emergency Navigation Fix - DOM Ready');
    
    // Apply fix immediately
    setTimeout(emergencyNavigationFix, 500);
    
    // Apply again after other scripts load
    setTimeout(emergencyNavigationFix, 2000);
    setTimeout(emergencyNavigationFix, 5000);
});

// Apply when window loads
window.addEventListener('load', function() {
    console.log('🚨 Emergency Navigation Fix - Window Loaded');
    setTimeout(emergencyNavigationFix, 1000);
});

// Make functions globally available
window.emergencyNavigationFix = emergencyNavigationFix;
window.emergencyShowSection = emergencyShowSection;
window.emergencyLoadContent = emergencyLoadContent;

console.log('✅ Emergency Navigation Fix loaded');
