<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Firebase System Verification</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 1rem;
        direction: rtl;
      }

      .container {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        max-width: 1000px;
        margin: 0 auto;
      }

      .header {
        text-align: center;
        margin-bottom: 2rem;
        color: #333;
      }

      .test-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
      }

      .test-card {
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        padding: 1rem;
        background: #f8f9fa;
      }

      .test-card h3 {
        margin: 0 0 1rem 0;
        color: #495057;
        font-size: 1.1rem;
      }

      .status {
        padding: 0.5rem;
        border-radius: 4px;
        margin: 0.5rem 0;
        font-size: 0.9rem;
      }

      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      .status.warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }

      .btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        margin: 0.25rem;
        font-size: 0.9rem;
      }

      .btn:hover {
        background: #5a6fd8;
      }

      .btn.success {
        background: #28a745;
      }

      .btn.danger {
        background: #dc3545;
      }

      .console-log {
        background: #2d3748;
        color: #e2e8f0;
        padding: 1rem;
        border-radius: 4px;
        font-family: "Courier New", monospace;
        font-size: 0.8rem;
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        direction: ltr;
        text-align: left;
      }

      .actions {
        text-align: center;
        margin: 2rem 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🔥 Firebase System Verification</h1>
        <p>Complete Firebase Authentication System Test</p>
      </div>

      <div class="actions">
        <button class="btn" onclick="runCompleteTest()">
          🧪 Run Complete Test
        </button>
        <button class="btn success" onclick="clearConsole()">
          🧹 Clear Console
        </button>
        <a
          href="login.html"
          class="btn"
          style="text-decoration: none; display: inline-block"
          >🔐 Go to Login</a
        >
        <a
          href="index.html"
          class="btn"
          style="text-decoration: none; display: inline-block"
          >🏠 Go to Dashboard</a
        >
      </div>

      <div class="test-grid">
        <div class="test-card">
          <h3>🔧 Firebase Initialization</h3>
          <div id="initStatus"></div>
          <button class="btn" onclick="testInitialization()">Test Init</button>
        </div>

        <div class="test-card">
          <h3>🗄️ Firestore Connection</h3>
          <div id="firestoreStatus"></div>
          <button class="btn" onclick="testFirestore()">Test Firestore</button>
        </div>

        <div class="test-card">
          <h3>🔐 Authentication Status</h3>
          <div id="authStatus"></div>
          <button class="btn" onclick="testAuth()">Test Auth</button>
        </div>

        <div class="test-card">
          <h3>👤 User Profile</h3>
          <div id="profileStatus"></div>
          <button class="btn" onclick="testProfile()">Test Profile</button>
        </div>

        <div class="test-card">
          <h3>💾 Firestore Write Test</h3>
          <div id="writeTestStatus"></div>
          <button class="btn" onclick="testFirestoreWrite()">Test Write</button>
        </div>
      </div>

      <div class="test-card">
        <h3>📋 Console Output</h3>
        <div id="consoleOutput" class="console-log"></div>
      </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module" src="js/firebase-config.js"></script>

    <script type="module">
      let consoleMessages = [];

      // Override console.log to capture messages
      const originalConsoleLog = console.log;
      const originalConsoleError = console.error;
      const originalConsoleWarn = console.warn;

      function captureConsole(message, type = "log") {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
        consoleMessages.push(logEntry);

        const consoleDiv = document.getElementById("consoleOutput");
        if (consoleDiv) {
          consoleDiv.textContent = consoleMessages.slice(-50).join("\n");
          consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }

        // Call original console method
        if (type === "error") {
          originalConsoleError(message);
        } else if (type === "warn") {
          originalConsoleWarn(message);
        } else {
          originalConsoleLog(message);
        }
      }

      console.log = (message) => captureConsole(message, "log");
      console.error = (message) => captureConsole(message, "error");
      console.warn = (message) => captureConsole(message, "warn");

      function showStatus(elementId, message, type = "info") {
        const element = document.getElementById(elementId);
        if (element) {
          element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
      }

      window.testInitialization = function () {
        console.log("Testing Firebase initialization...");

        if (
          typeof window.firebaseAuth !== "undefined" &&
          window.firebaseAuth !== null
        ) {
          if (
            typeof window.firebaseAuth.getInitializationStatus === "function"
          ) {
            const status = window.firebaseAuth.getInitializationStatus();

            let message = "";
            let type = "success";

            if (status.app && status.auth && status.db) {
              message = "✅ All Firebase services initialized successfully";
              console.log("Firebase initialization: SUCCESS");
            } else {
              message = `❌ Initialization incomplete - App: ${status.app}, Auth: ${status.auth}, DB: ${status.db}`;
              type = "error";
              console.error("Firebase initialization: FAILED");
            }

            showStatus("initStatus", message, type);
          } else {
            showStatus(
              "initStatus",
              "❌ Firebase Auth Manager incomplete",
              "error"
            );
            console.error("Firebase Auth Manager: INCOMPLETE");
          }
        } else {
          showStatus(
            "initStatus",
            "❌ Firebase Auth Manager not loaded",
            "error"
          );
          console.error("Firebase Auth Manager: NOT LOADED");
        }
      };

      window.testFirestore = async function () {
        console.log("Testing Firestore connection...");

        if (
          window.firebaseAuth &&
          typeof window.firebaseAuth.testFirestoreConnection === "function"
        ) {
          try {
            const result = await window.firebaseAuth.testFirestoreConnection();
            if (result) {
              showStatus(
                "firestoreStatus",
                "✅ Firestore connection successful (or auth working with backend issues)",
                "success"
              );
              console.log("Firestore connection: SUCCESS");
            } else {
              showStatus(
                "firestoreStatus",
                "⚠️ Firestore connection test failed",
                "warning"
              );
              console.warn("Firestore connection: FAILED");
            }
          } catch (error) {
            // Enhanced error logging with complete details
            const errorDetails = `
              Code: ${error.code || "No code"}
              Message: ${error.message || "No message"}
              Stack: ${error.stack || "No stack"}
            `;

            showStatus(
              "firestoreStatus",
              `❌ Firestore error: ${error.code || "Unknown"} - ${
                error.message || "No details"
              }`,
              "error"
            );
            console.error("Firestore error - Full Details:", errorDetails);
            console.error("Full Error Object:", error);
          }
        } else {
          showStatus(
            "firestoreStatus",
            "❌ Firestore test function not available",
            "error"
          );
          console.error("Firestore test function: NOT AVAILABLE");
        }
      };

      window.testAuth = function () {
        console.log("Testing authentication status...");

        if (
          window.firebaseAuth &&
          typeof window.firebaseAuth.isAuthenticated === "function"
        ) {
          const isAuth = window.firebaseAuth.isAuthenticated();
          const currentUser = window.firebaseAuth.getCurrentUser();

          if (isAuth && currentUser.user) {
            showStatus(
              "authStatus",
              `✅ Authenticated as: ${currentUser.user.email}`,
              "success"
            );
            console.log("Authentication: SUCCESS -", currentUser.user.email);
          } else {
            showStatus("authStatus", "⚠️ Not authenticated", "warning");
            console.log("Authentication: NOT AUTHENTICATED");
          }
        } else {
          showStatus(
            "authStatus",
            "❌ Authentication functions not available",
            "error"
          );
          console.error("Authentication functions: NOT AVAILABLE");
        }
      };

      window.testProfile = function () {
        console.log("Testing user profile...");

        if (
          window.firebaseAuth &&
          typeof window.firebaseAuth.getCurrentUser === "function"
        ) {
          const currentUser = window.firebaseAuth.getCurrentUser();

          if (currentUser.user && currentUser.profile) {
            const role = currentUser.profile.role || "No role";
            const isAdmin = currentUser.isAdmin;

            showStatus(
              "profileStatus",
              `✅ Profile loaded - Role: ${role}, Admin: ${isAdmin}`,
              "success"
            );
            console.log(
              "User profile: SUCCESS - Role:",
              role,
              "Admin:",
              isAdmin
            );
          } else if (currentUser.user) {
            showStatus(
              "profileStatus",
              "⚠️ User exists but profile not loaded",
              "warning"
            );
            console.warn("User profile: PARTIAL - User exists but no profile");
          } else {
            showStatus(
              "profileStatus",
              "⚠️ No user profile available",
              "warning"
            );
            console.log("User profile: NO USER");
          }
        } else {
          showStatus(
            "profileStatus",
            "❌ Profile functions not available",
            "error"
          );
          console.error("Profile functions: NOT AVAILABLE");
        }
      };

      window.testFirestoreWrite = async function () {
        console.log("Testing Firestore write operations...");

        if (
          window.firebaseAuth &&
          typeof window.firebaseAuth.testUserProfileCreation === "function"
        ) {
          try {
            const result = await window.firebaseAuth.testUserProfileCreation();
            if (result) {
              showStatus(
                "writeTestStatus",
                "✅ Firestore write test successful",
                "success"
              );
              console.log("Firestore write test: SUCCESS");
            } else {
              showStatus(
                "writeTestStatus",
                "⚠️ Firestore write test failed",
                "warning"
              );
              console.warn("Firestore write test: FAILED");
            }
          } catch (error) {
            showStatus(
              "writeTestStatus",
              `❌ Firestore write error: ${error.message}`,
              "error"
            );
            console.error("Firestore write error:", error.message);
          }
        } else {
          showStatus(
            "writeTestStatus",
            "⚠️ No authenticated user for write test",
            "warning"
          );
          console.warn("Firestore write test: NO AUTHENTICATED USER");
        }
      };

      window.runCompleteTest = async function () {
        console.log("=== STARTING COMPLETE FIREBASE TEST ===");

        testInitialization();
        await new Promise((resolve) => setTimeout(resolve, 500));

        await testFirestore();
        await new Promise((resolve) => setTimeout(resolve, 500));

        testAuth();
        await new Promise((resolve) => setTimeout(resolve, 500));

        testProfile();
        await new Promise((resolve) => setTimeout(resolve, 500));

        await testFirestoreWrite();

        console.log("=== COMPLETE FIREBASE TEST FINISHED ===");
      };

      window.clearConsole = function () {
        consoleMessages = [];
        document.getElementById("consoleOutput").textContent = "";
        console.log("Console cleared");
      };

      // Auto-run test when page loads
      document.addEventListener("DOMContentLoaded", function () {
        console.log("Firebase Verification page loaded");

        // Wait for Firebase to initialize
        setTimeout(() => {
          runCompleteTest();
        }, 2000);
      });
    </script>
  </body>
</html>
