<?php

/**
 * Simple Database Configuration
 * إعدادات قاعدة البيانات البسيطة
 */

// Database configuration - MySQL (from .env)
$host = 'localhost';
$dbname = 'mossab-landing-page';
$username = 'root';
$password = '';
$port = 3307;

// Create PDO connection
try {
    // First try to connect without database to create it if needed
    $pdo = new PDO("mysql:host=$host;port=$port;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

    // Now connect to the specific database
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Log error
    error_log('Database connection failed: ' . $e->getMessage());

    // Return error response for API calls
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database connection failed: ' . $e->getMessage(),
            'error_code' => 'DB_CONNECTION_ERROR',
            'details' => [
                'host' => $host,
                'port' => $port,
                'database' => $dbname,
                'username' => $username
            ]
        ]);
        exit;
    }

    die('Database connection failed: ' . $e->getMessage());
}
