<?php

/**
 * Automated Backup Manager
 * Handles database and file system backups with retention policies
 * Part of DevOps implementation for Mossaab Landing Page
 */

require_once __DIR__ . '/../config/environment.php';

class BackupManager
{
    private $env;
    private $backupPath;
    private $retentionDays;
    private $compressionEnabled;

    public function __construct()
    {
        $this->env = EnvironmentConfig::getInstance();
        $this->backupPath = __DIR__ . '/../backups/';
        $this->retentionDays = $this->env->get('backup.retention_days', 30);
        $this->compressionEnabled = $this->env->get('backup.compression', true);

        $this->ensureBackupDirectory();
    }

    /**
     * Ensure backup directory exists
     */
    private function ensureBackupDirectory()
    {
        if (!is_dir($this->backupPath)) {
            mkdir($this->backupPath, 0755, true);
        }

        // Create subdirectories
        $subdirs = ['database', 'files', 'logs'];
        foreach ($subdirs as $subdir) {
            $path = $this->backupPath . $subdir . '/';
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
        }
    }

    /**
     * Create full backup (database + files)
     */
    public function createFullBackup()
    {
        $timestamp = date('Y-m-d_H-i-s');
        $backupId = "full_backup_{$timestamp}";

        echo "🚀 Starting full backup: {$backupId}\n";

        $results = [
            'backup_id' => $backupId,
            'timestamp' => $timestamp,
            'database' => false,
            'files' => false,
            'size' => 0,
            'duration' => 0
        ];

        $startTime = microtime(true);

        try {
            // Database backup
            echo "📊 Creating database backup...\n";
            $dbBackup = $this->createDatabaseBackup($timestamp);
            $results['database'] = $dbBackup['success'];
            $results['size'] += $dbBackup['size'];

            // Files backup
            echo "📁 Creating files backup...\n";
            $filesBackup = $this->createFilesBackup($timestamp);
            $results['files'] = $filesBackup['success'];
            $results['size'] += $filesBackup['size'];

            $results['duration'] = round(microtime(true) - $startTime, 2);

            // Log backup
            $this->logBackup($results);

            // Cleanup old backups
            $this->cleanupOldBackups();

            if ($results['database'] && $results['files']) {
                echo "✅ Full backup completed successfully!\n";
                echo "📊 Size: " . $this->formatBytes($results['size']) . "\n";
                echo "⏱️  Duration: {$results['duration']} seconds\n";
                return $results;
            } else {
                throw new Exception("Backup partially failed");
            }
        } catch (Exception $e) {
            echo "❌ Backup failed: " . $e->getMessage() . "\n";
            $results['error'] = $e->getMessage();
            $this->logBackup($results);
            return $results;
        }
    }

    /**
     * Create database backup
     */
    public function createDatabaseBackup($timestamp = null)
    {
        $timestamp = $timestamp ?: date('Y-m-d_H-i-s');
        $filename = "database_backup_{$timestamp}.sql";
        $filepath = $this->backupPath . "database/{$filename}";

        try {
            $dbConfig = $this->env->get('database');

            // Build mysqldump command
            $command = sprintf(
                'mysqldump --host=%s --port=%d --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
                escapeshellarg($dbConfig['host']),
                $dbConfig['port'],
                escapeshellarg($dbConfig['username']),
                escapeshellarg($dbConfig['password']),
                escapeshellarg($dbConfig['name']),
                escapeshellarg($filepath)
            );

            // Execute backup
            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                throw new Exception("mysqldump failed: " . implode("\n", $output));
            }

            // Compress if enabled
            if ($this->compressionEnabled) {
                $compressedFile = $filepath . '.gz';
                if (gzencode(file_get_contents($filepath))) {
                    file_put_contents($compressedFile, gzencode(file_get_contents($filepath)));
                    unlink($filepath);
                    $filepath = $compressedFile;
                    $filename .= '.gz';
                }
            }

            $size = filesize($filepath);

            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => $size
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'size' => 0
            ];
        }
    }

    /**
     * Create files backup
     */
    public function createFilesBackup($timestamp = null)
    {
        $timestamp = $timestamp ?: date('Y-m-d_H-i-s');
        $filename = "files_backup_{$timestamp}.tar.gz";
        $filepath = $this->backupPath . "files/{$filename}";

        try {
            $rootPath = dirname(__DIR__);

            // Directories to backup
            $backupDirs = [
                'uploads',
                'config',
                'css',
                'js',
                'admin/css',
                'admin/js'
            ];

            // Files to backup
            $backupFiles = [
                'index.html',
                'admin/index.html',
                '.env.example'
            ];

            // Build tar command
            $items = [];
            foreach ($backupDirs as $dir) {
                if (is_dir($rootPath . '/' . $dir)) {
                    $items[] = $dir;
                }
            }
            foreach ($backupFiles as $file) {
                if (file_exists($rootPath . '/' . $file)) {
                    $items[] = $file;
                }
            }

            if (empty($items)) {
                throw new Exception("No files to backup");
            }

            $command = sprintf(
                'cd %s && tar -czf %s %s 2>&1',
                escapeshellarg($rootPath),
                escapeshellarg($filepath),
                implode(' ', array_map('escapeshellarg', $items))
            );

            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                throw new Exception("tar command failed: " . implode("\n", $output));
            }

            $size = filesize($filepath);

            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => $size
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'size' => 0
            ];
        }
    }

    /**
     * Restore database from backup
     */
    public function restoreDatabase($backupFile)
    {
        try {
            $filepath = $this->backupPath . "database/{$backupFile}";

            if (!file_exists($filepath)) {
                throw new Exception("Backup file not found: {$backupFile}");
            }

            // Handle compressed files
            if (pathinfo($filepath, PATHINFO_EXTENSION) === 'gz') {
                $tempFile = tempnam(sys_get_temp_dir(), 'db_restore_');
                file_put_contents($tempFile, gzdecode(file_get_contents($filepath)));
                $filepath = $tempFile;
            }

            $dbConfig = $this->env->get('database');

            // Build mysql command
            $command = sprintf(
                'mysql --host=%s --port=%d --user=%s --password=%s %s < %s',
                escapeshellarg($dbConfig['host']),
                $dbConfig['port'],
                escapeshellarg($dbConfig['username']),
                escapeshellarg($dbConfig['password']),
                escapeshellarg($dbConfig['name']),
                escapeshellarg($filepath)
            );

            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            // Cleanup temp file
            if (isset($tempFile) && file_exists($tempFile)) {
                unlink($tempFile);
            }

            if ($returnCode !== 0) {
                throw new Exception("mysql restore failed: " . implode("\n", $output));
            }

            echo "✅ Database restored successfully from: {$backupFile}\n";
            return true;
        } catch (Exception $e) {
            echo "❌ Database restore failed: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * List available backups
     */
    public function listBackups()
    {
        $backups = [
            'database' => [],
            'files' => [],
            'full' => []
        ];

        // Database backups
        $dbFiles = glob($this->backupPath . 'database/database_backup_*.sql*');
        foreach ($dbFiles as $file) {
            $backups['database'][] = [
                'filename' => basename($file),
                'size' => filesize($file),
                'date' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }

        // Files backups
        $fileBackups = glob($this->backupPath . 'files/files_backup_*.tar.gz');
        foreach ($fileBackups as $file) {
            $backups['files'][] = [
                'filename' => basename($file),
                'size' => filesize($file),
                'date' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }

        // Sort by date (newest first)
        foreach ($backups as &$category) {
            usort($category, function ($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });
        }

        return $backups;
    }

    /**
     * Cleanup old backups
     */
    public function cleanupOldBackups()
    {
        $cutoffTime = time() - ($this->retentionDays * 24 * 3600);
        $deletedCount = 0;
        $freedSpace = 0;

        $backupDirs = ['database', 'files'];

        foreach ($backupDirs as $dir) {
            $files = glob($this->backupPath . $dir . '/*');

            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    $size = filesize($file);
                    if (unlink($file)) {
                        $deletedCount++;
                        $freedSpace += $size;
                    }
                }
            }
        }

        if ($deletedCount > 0) {
            echo "🧹 Cleaned up {$deletedCount} old backups, freed " . $this->formatBytes($freedSpace) . "\n";
        }

        return ['deleted' => $deletedCount, 'freed_space' => $freedSpace];
    }

    /**
     * Log backup operation
     */
    private function logBackup($results)
    {
        $logFile = $this->backupPath . 'logs/backup.log';
        $logEntry = date('Y-m-d H:i:s') . " - " . json_encode($results) . "\n";
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Format bytes to human readable
     */
    public function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// CLI Interface
if (php_sapi_name() === 'cli') {
    $backup = new BackupManager();

    $command = $argv[1] ?? 'help';

    switch ($command) {
        case 'full':
            $backup->createFullBackup();
            break;

        case 'database':
            $result = $backup->createDatabaseBackup();
            if ($result['success']) {
                echo "✅ Database backup created: {$result['filename']}\n";
                echo "📊 Size: " . $backup->formatBytes($result['size']) . "\n";
            } else {
                echo "❌ Database backup failed: {$result['error']}\n";
            }
            break;

        case 'files':
            $result = $backup->createFilesBackup();
            if ($result['success']) {
                echo "✅ Files backup created: {$result['filename']}\n";
                echo "📊 Size: " . $backup->formatBytes($result['size']) . "\n";
            } else {
                echo "❌ Files backup failed: {$result['error']}\n";
            }
            break;

        case 'restore':
            if (!isset($argv[2])) {
                echo "❌ Backup filename is required.\n";
                echo "Usage: php BackupManager.php restore backup_filename.sql\n";
                exit(1);
            }
            $backup->restoreDatabase($argv[2]);
            break;

        case 'list':
            $backups = $backup->listBackups();
            echo "📋 Available Backups:\n";
            echo "====================\n\n";

            foreach ($backups as $type => $files) {
                if (!empty($files)) {
                    echo "📊 " . ucfirst($type) . " Backups:\n";
                    foreach ($files as $file) {
                        echo "  - {$file['filename']} ({$backup->formatBytes($file['size'])}) - {$file['date']}\n";
                    }
                    echo "\n";
                }
            }
            break;

        case 'cleanup':
            $backup->cleanupOldBackups();
            break;

        case 'help':
        default:
            echo "💾 Backup Manager\n";
            echo "=================\n\n";
            echo "Available commands:\n";
            echo "  full                 Create full backup (database + files)\n";
            echo "  database             Create database backup only\n";
            echo "  files                Create files backup only\n";
            echo "  restore <filename>   Restore database from backup\n";
            echo "  list                 List available backups\n";
            echo "  cleanup              Remove old backups\n";
            echo "  help                 Show this help message\n\n";
            echo "Examples:\n";
            echo "  php BackupManager.php full\n";
            echo "  php BackupManager.php restore database_backup_2025-01-22_14-30-00.sql\n";
            echo "  php BackupManager.php list\n";
            break;
    }
}
