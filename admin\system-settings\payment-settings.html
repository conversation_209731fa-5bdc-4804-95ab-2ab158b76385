<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الدفع - إعدادات النظام</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/system-settings.css">
    <style>
        /* Payment-specific styles */
        .payment-methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 25px;
        }
        
        .payment-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .payment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
        }
        
        .payment-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .payment-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .payment-icon.credit_card { background: linear-gradient(135deg, #3498db, #2980b9); }
        .payment-icon.bank_transfer { background: linear-gradient(135deg, #27ae60, #229954); }
        .payment-icon.cash_on_delivery { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .payment-icon.digital_wallet { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .payment-icon.cryptocurrency { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        
        .payment-info h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
            font-size: 1.1rem;
        }
        
        .payment-info p {
            margin: 0;
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .payment-info small {
            color: #95a5a6;
            font-size: 0.8rem;
            display: block;
        }
        
        .payment-info .provider {
            color: #3498db;
            font-weight: 500;
        }
        
        .payment-status {
            margin-right: auto;
        }
        
        .payment-fees, .payment-limits {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .fee-item, .limit-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 0.85rem;
        }
        
        .fee-label, .limit-label {
            color: #6c757d;
        }
        
        .fee-value, .limit-value {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .payment-currencies {
            margin-bottom: 20px;
        }
        
        .currencies-label {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 8px;
            display: block;
        }
        
        .currencies-list {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .currency-tag {
            background: #e9ecef;
            color: #495057;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .payment-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .config-fields {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        
        .field-error {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 5px;
        }
        
        .form-control.is-invalid {
            border-color: #e74c3c;
        }
        
        .form-control.is-valid {
            border-color: #27ae60;
        }
        
        .test-results-modal {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .test-status.success {
            background: #d4edda;
            color: #155724;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .test-status.error {
            background: #f8d7da;
            color: #721c24;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .test-section {
            margin-bottom: 20px;
        }
        
        .test-section h4 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1rem;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .test-result.passed {
            color: #27ae60;
            font-weight: bold;
        }
        
        .test-result.failed {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="system-settings-container">
        <!-- Header -->
        <header class="system-header">
            <h1>
                <i class="fas fa-credit-card"></i>
                إعدادات الدفع
            </h1>
            <p>تكوين وإدارة طرق الدفع والبوابات المالية</p>
        </header>

        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <a href="../index.html"><i class="fas fa-home"></i> لوحة التحكم</a>
            <span class="separator">/</span>
            <a href="index.html">إعدادات النظام</a>
            <span class="separator">/</span>
            <span>إعدادات الدفع</span>
        </nav>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="stat-card">
                <div class="stat-number" id="totalMethods">0</div>
                <div class="stat-label">إجمالي طرق الدفع</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeMethods">0</div>
                <div class="stat-label">الطرق النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgPercentageFee">0%</div>
                <div class="stat-label">متوسط الرسوم النسبية</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgFixedFee">0 دج</div>
                <div class="stat-label">متوسط الرسوم الثابتة</div>
            </div>
        </div>

        <!-- Payment Header -->
        <div class="categories-header">
            <div class="categories-filters">
                <div class="filter-group">
                    <label for="paymentSearch">البحث</label>
                    <input type="text" id="paymentSearch" class="enhanced-input" placeholder="البحث في طرق الدفع...">
                </div>
                
                <div class="filter-group">
                    <label for="typeFilter">النوع</label>
                    <select id="typeFilter" class="enhanced-select">
                        <option value="">جميع الأنواع</option>
                        <option value="credit_card">بطاقة ائتمان</option>
                        <option value="bank_transfer">تحويل بنكي</option>
                        <option value="cash_on_delivery">الدفع عند الاستلام</option>
                        <option value="digital_wallet">محفظة رقمية</option>
                        <option value="cryptocurrency">عملة رقمية</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="statusFilter">الحالة</label>
                    <select id="statusFilter" class="enhanced-select">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
            </div>

            <div>
                <!-- Add Button -->
                <button class="btn btn-primary" onclick="PaymentManager.showAddModal()">
                    <i class="fas fa-plus"></i>
                    إضافة طريقة دفع
                </button>
            </div>
        </div>

        <!-- Payment Methods Container -->
        <div class="payment-container">
            <div id="paymentMethodsContainer">
                <!-- Payment methods will be loaded here -->
            </div>
            
            <!-- Pagination -->
            <div id="paginationContainer"></div>
        </div>
    </div>

    <!-- Payment Method Modal -->
    <div class="modal" id="paymentModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة طريقة دفع جديدة</h3>
                <button class="modal-close" onclick="PaymentManager.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="paymentForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="name_ar">الاسم بالعربية *</label>
                            <input type="text" id="namear" name="name_ar" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="name">الاسم بالإنجليزية *</label>
                            <input type="text" id="name" name="name" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="type">نوع طريقة الدفع *</label>
                            <select id="type" name="type" class="form-control" required>
                                <option value="">اختر النوع</option>
                                <option value="credit_card">بطاقة ائتمان</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="cash_on_delivery">الدفع عند الاستلام</option>
                                <option value="digital_wallet">محفظة رقمية</option>
                                <option value="cryptocurrency">عملة رقمية</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="provider">مقدم الخدمة</label>
                            <input type="text" id="provider" name="provider" class="form-control" placeholder="مثل: PayPal, Stripe, CCP">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="feesPercentage">الرسوم النسبية (%)</label>
                            <input type="number" id="feesPercentage" name="feesPercentage" class="form-control" step="0.01" min="0" max="100" value="0">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="feesFixed">الرسوم الثابتة (دج)</label>
                            <input type="number" id="feesFixed" name="feesFixed" class="form-control" step="0.01" min="0" value="0">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="minAmount">الحد الأدنى للمبلغ (دج)</label>
                            <input type="number" id="minAmount" name="minAmount" class="form-control" step="0.01" min="0" value="0">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="maxAmount">الحد الأقصى للمبلغ (دج)</label>
                            <input type="number" id="maxAmount" name="maxAmount" class="form-control" step="0.01" min="0">
                            <small class="form-text">اتركه فارغاً لعدم وجود حد أقصى</small>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="supportedCurrencies">العملات المدعومة</label>
                            <input type="text" id="supportedCurrencies" name="supportedCurrencies" class="form-control" value="DZD" placeholder="DZD, USD, EUR">
                            <small class="form-text">افصل بين العملات بفاصلة</small>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="sortOrder">ترتيب العرض</label>
                            <input type="number" id="sortOrder" name="sortOrder" class="form-control" value="0" min="0">
                        </div>
                    </div>
                    
                    <!-- Dynamic Config Fields -->
                    <div id="configFields" class="config-fields">
                        <!-- Configuration fields will be added here based on payment type -->
                    </div>
                    
                    <!-- Active Status -->
                    <div class="form-check">
                        <input type="checkbox" id="isActive" name="isActive" checked>
                        <label for="isActive">طريقة دفع نشطة</label>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="PaymentManager.closeModal()">
                    إلغاء
                </button>
                <button type="submit" form="paymentForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/core.js"></script>
    <script src="js/payment.js"></script>
</body>
</html>
