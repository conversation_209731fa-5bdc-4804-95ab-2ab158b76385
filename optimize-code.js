#!/usr/bin/env node
/**
 * Script d'Optimisation Automatique du Code
 * Landing Pages SaaS - "صفحات هبوط للجميع"
 * 
 * Ce script applique automatiquement les optimisations recommandées
 * dans le rapport de compression pour améliorer les performances.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class CodeOptimizer {
  constructor() {
    this.projectRoot = __dirname;
    this.stats = {
      filesProcessed: 0,
      sizeReduction: 0,
      errors: []
    };
  }

  /**
   * Point d'entrée principal
   */
  async optimize() {
    console.log('🚀 Démarrage de l\'optimisation du code...');
    console.log('📁 Projet:', this.projectRoot);
    
    try {
      // Phase 1: Analyse et nettoyage
      await this.analyzeProject();
      await this.removeDeadCode();
      
      // Phase 2: Optimisation CSS
      await this.optimizeCSS();
      
      // Phase 3: Optimisation JavaScript
      await this.optimizeJavaScript();
      
      // Phase 4: Optimisation des images
      await this.optimizeImages();
      
      // Phase 5: Génération des bundles
      await this.generateOptimizedBundles();
      
      // Phase 6: Validation
      await this.validateOptimizations();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Erreur lors de l\'optimisation:', error.message);
      this.stats.errors.push(error.message);
    }
  }

  /**
   * Analyse la structure du projet
   */
  async analyzeProject() {
    console.log('\n📊 Analyse de la structure du projet...');
    
    const directories = ['css', 'js', 'admin/css', 'admin/js', 'images'];
    const analysis = {};
    
    for (const dir of directories) {
      const fullPath = path.join(this.projectRoot, dir);
      if (fs.existsSync(fullPath)) {
        analysis[dir] = this.analyzeDirectory(fullPath);
        console.log(`  ✅ ${dir}: ${analysis[dir].fileCount} fichiers, ${this.formatSize(analysis[dir].totalSize)}`);
      }
    }
    
    this.projectAnalysis = analysis;
  }

  /**
   * Analyse un répertoire
   */
  analyzeDirectory(dirPath) {
    const files = this.getFilesRecursively(dirPath);
    let totalSize = 0;
    
    files.forEach(file => {
      const stats = fs.statSync(file);
      totalSize += stats.size;
    });
    
    return {
      fileCount: files.length,
      totalSize: totalSize,
      files: files
    };
  }

  /**
   * Supprime le code mort identifié
   */
  async removeDeadCode() {
    console.log('\n🧹 Suppression du code mort...');
    
    // Fonctions JavaScript obsolètes à supprimer
    const deadFunctions = [
      'showLegacyNotification',
      'oldApiCall',
      'deprecatedCartFunctions',
      'legacyImageLoader',
      'oldValidationSystem'
    ];
    
    // Classes CSS non utilisées
    const deadCssClasses = [
      '.legacy-button',
      '.old-modal',
      '.deprecated-grid',
      '.unused-animation'
    ];
    
    // Traitement des fichiers JavaScript
    const jsFiles = this.getFilesByExtension('js');
    for (const file of jsFiles) {
      await this.removeDeadJavaScript(file, deadFunctions);
    }
    
    // Traitement des fichiers CSS
    const cssFiles = this.getFilesByExtension('css');
    for (const file of cssFiles) {
      await this.removeDeadCSS(file, deadCssClasses);
    }
    
    console.log(`  ✅ Code mort supprimé dans ${jsFiles.length + cssFiles.length} fichiers`);
  }

  /**
   * Optimise les fichiers CSS
   */
  async optimizeCSS() {
    console.log('\n🎨 Optimisation des fichiers CSS...');
    
    const cssFiles = this.getFilesByExtension('css');
    let totalReduction = 0;
    
    for (const file of cssFiles) {
      const originalSize = fs.statSync(file).size;
      
      // Lecture du contenu
      let content = fs.readFileSync(file, 'utf8');
      
      // Optimisations CSS
      content = this.optimizeCSSContent(content);
      
      // Création du fichier optimisé
      const optimizedPath = file.replace('.css', '.min.css');
      fs.writeFileSync(optimizedPath, content);
      
      const newSize = fs.statSync(optimizedPath).size;
      const reduction = originalSize - newSize;
      totalReduction += reduction;
      
      console.log(`  ✅ ${path.basename(file)}: ${this.formatSize(reduction)} économisés`);
    }
    
    console.log(`  📊 Réduction totale CSS: ${this.formatSize(totalReduction)}`);
    this.stats.sizeReduction += totalReduction;
  }

  /**
   * Optimise le contenu CSS
   */
  optimizeCSSContent(content) {
    // Suppression des commentaires
    content = content.replace(/\/\*[\s\S]*?\*\//g, '');
    
    // Suppression des espaces inutiles
    content = content.replace(/\s+/g, ' ');
    content = content.replace(/;\s*}/g, '}');
    content = content.replace(/\s*{\s*/g, '{');
    content = content.replace(/;\s*/g, ';');
    content = content.replace(/,\s*/g, ',');
    
    // Suppression des règles vides
    content = content.replace(/[^}]+{\s*}/g, '');
    
    // Optimisation des couleurs
    content = content.replace(/#([0-9a-f])\1([0-9a-f])\2([0-9a-f])\3/gi, '#$1$2$3');
    
    // Suppression des unités pour les valeurs 0
    content = content.replace(/\b0(px|em|rem|%|vh|vw|pt|pc|in|cm|mm|ex|ch|vmin|vmax)\b/g, '0');
    
    return content.trim();
  }

  /**
   * Optimise les fichiers JavaScript
   */
  async optimizeJavaScript() {
    console.log('\n⚡ Optimisation des fichiers JavaScript...');
    
    const jsFiles = this.getFilesByExtension('js');
    let totalReduction = 0;
    
    for (const file of jsFiles) {
      const originalSize = fs.statSync(file).size;
      
      // Lecture du contenu
      let content = fs.readFileSync(file, 'utf8');
      
      // Optimisations JavaScript
      content = this.optimizeJavaScriptContent(content);
      
      // Création du fichier optimisé
      const optimizedPath = file.replace('.js', '.min.js');
      fs.writeFileSync(optimizedPath, content);
      
      const newSize = fs.statSync(optimizedPath).size;
      const reduction = originalSize - newSize;
      totalReduction += reduction;
      
      console.log(`  ✅ ${path.basename(file)}: ${this.formatSize(reduction)} économisés`);
    }
    
    console.log(`  📊 Réduction totale JS: ${this.formatSize(totalReduction)}`);
    this.stats.sizeReduction += totalReduction;
  }

  /**
   * Optimise le contenu JavaScript
   */
  optimizeJavaScriptContent(content) {
    // Suppression des commentaires de ligne
    content = content.replace(/\/\/.*$/gm, '');
    
    // Suppression des commentaires de bloc
    content = content.replace(/\/\*[\s\S]*?\*\//g, '');
    
    // Suppression des console.log en production
    content = content.replace(/console\.(log|warn|info|debug)\([^)]*\);?/g, '');
    
    // Suppression des espaces inutiles
    content = content.replace(/\s+/g, ' ');
    content = content.replace(/;\s*}/g, '}');
    content = content.replace(/\s*{\s*/g, '{');
    content = content.replace(/;\s*/g, ';');
    content = content.replace(/,\s*/g, ',');
    
    // Optimisation des chaînes de caractères
    content = content.replace(/"([^"\\]*(\\.[^"\\]*)*)"/g, (match, str) => {
      return `"${str.replace(/\s+/g, ' ').trim()}"`;
    });
    
    return content.trim();
  }

  /**
   * Optimise les images
   */
  async optimizeImages() {
    console.log('\n🖼️ Optimisation des images...');
    
    const imageFiles = this.getFilesByExtension(['jpg', 'jpeg', 'png', 'svg']);
    let totalReduction = 0;
    
    for (const file of imageFiles) {
      const originalSize = fs.statSync(file).size;
      
      try {
        // Simulation d'optimisation (nécessiterait des outils comme imagemin)
        const reduction = Math.floor(originalSize * 0.3); // Estimation 30% de réduction
        totalReduction += reduction;
        
        console.log(`  ✅ ${path.basename(file)}: ${this.formatSize(reduction)} économisés (estimé)`);
      } catch (error) {
        console.log(`  ⚠️ ${path.basename(file)}: Erreur d'optimisation`);
      }
    }
    
    console.log(`  📊 Réduction totale Images: ${this.formatSize(totalReduction)} (estimée)`);
    this.stats.sizeReduction += totalReduction;
  }

  /**
   * Génère les bundles optimisés
   */
  async generateOptimizedBundles() {
    console.log('\n📦 Génération des bundles optimisés...');
    
    // Création du répertoire de sortie
    const distDir = path.join(this.projectRoot, 'dist');
    if (!fs.existsSync(distDir)) {
      fs.mkdirSync(distDir, { recursive: true });
    }
    
    // Bundle CSS principal
    await this.createCSSBundle();
    
    // Bundle JavaScript principal
    await this.createJSBundle();
    
    console.log('  ✅ Bundles générés dans le dossier dist/');
  }

  /**
   * Crée le bundle CSS optimisé
   */
  async createCSSBundle() {
    const cssFiles = [
      'css/style.css',
      'css/cart.css',
      'css/checkout.css',
      'css/auth.css'
    ];
    
    let bundleContent = '';
    
    for (const file of cssFiles) {
      const filePath = path.join(this.projectRoot, file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        bundleContent += `/* ${file} */\n${content}\n\n`;
      }
    }
    
    // Optimisation du bundle
    bundleContent = this.optimizeCSSContent(bundleContent);
    
    // Écriture du bundle
    const bundlePath = path.join(this.projectRoot, 'dist', 'core.bundle.min.css');
    fs.writeFileSync(bundlePath, bundleContent);
    
    console.log(`  ✅ Bundle CSS créé: ${this.formatSize(fs.statSync(bundlePath).size)}`);
  }

  /**
   * Crée le bundle JavaScript optimisé
   */
  async createJSBundle() {
    const jsFiles = [
      'js/utils.js',
      'js/main.js',
      'js/cart.js',
      'js/checkout.js'
    ];
    
    let bundleContent = '';
    
    for (const file of jsFiles) {
      const filePath = path.join(this.projectRoot, file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        bundleContent += `/* ${file} */\n${content}\n\n`;
      }
    }
    
    // Optimisation du bundle
    bundleContent = this.optimizeJavaScriptContent(bundleContent);
    
    // Écriture du bundle
    const bundlePath = path.join(this.projectRoot, 'dist', 'core.bundle.min.js');
    fs.writeFileSync(bundlePath, bundleContent);
    
    console.log(`  ✅ Bundle JS créé: ${this.formatSize(fs.statSync(bundlePath).size)}`);
  }

  /**
   * Valide les optimisations
   */
  async validateOptimizations() {
    console.log('\n✅ Validation des optimisations...');
    
    const distDir = path.join(this.projectRoot, 'dist');
    if (fs.existsSync(distDir)) {
      const files = fs.readdirSync(distDir);
      console.log(`  📁 ${files.length} fichiers optimisés générés`);
      
      files.forEach(file => {
        const filePath = path.join(distDir, file);
        const size = fs.statSync(filePath).size;
        console.log(`    - ${file}: ${this.formatSize(size)}`);
      });
    }
  }

  /**
   * Génère le rapport final
   */
  generateReport() {
    console.log('\n📋 Rapport d\'optimisation');
    console.log('=' .repeat(50));
    console.log(`📁 Fichiers traités: ${this.stats.filesProcessed}`);
    console.log(`💾 Réduction de taille: ${this.formatSize(this.stats.sizeReduction)}`);
    console.log(`⚠️ Erreurs: ${this.stats.errors.length}`);
    
    if (this.stats.errors.length > 0) {
      console.log('\n❌ Erreurs rencontrées:');
      this.stats.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    console.log('\n🎉 Optimisation terminée!');
    console.log('\n📝 Prochaines étapes recommandées:');
    console.log('  1. Tester les bundles optimisés');
    console.log('  2. Configurer la compression Gzip/Brotli sur le serveur');
    console.log('  3. Implémenter le Service Worker pour le cache');
    console.log('  4. Monitorer les performances avec Lighthouse');
  }

  // === MÉTHODES UTILITAIRES ===

  getFilesRecursively(dir, extensions = null) {
    let files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files = files.concat(this.getFilesRecursively(fullPath, extensions));
      } else if (!extensions || extensions.some(ext => item.endsWith(`.${ext}`))) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  getFilesByExtension(extensions) {
    if (typeof extensions === 'string') {
      extensions = [extensions];
    }
    
    const allFiles = [];
    const searchDirs = ['css', 'js', 'admin/css', 'admin/js', 'images'];
    
    for (const dir of searchDirs) {
      const fullPath = path.join(this.projectRoot, dir);
      if (fs.existsSync(fullPath)) {
        const files = this.getFilesRecursively(fullPath, extensions);
        allFiles.push(...files);
      }
    }
    
    return allFiles;
  }

  async removeDeadJavaScript(file, deadFunctions) {
    let content = fs.readFileSync(file, 'utf8');
    let modified = false;
    
    for (const func of deadFunctions) {
      const regex = new RegExp(`function\s+${func}\s*\([^)]*\)\s*{[^}]*}`, 'g');
      if (regex.test(content)) {
        content = content.replace(regex, '');
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(file, content);
      this.stats.filesProcessed++;
    }
  }

  async removeDeadCSS(file, deadClasses) {
    let content = fs.readFileSync(file, 'utf8');
    let modified = false;
    
    for (const className of deadClasses) {
      const regex = new RegExp(`\\${className}\s*{[^}]*}`, 'g');
      if (regex.test(content)) {
        content = content.replace(regex, '');
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(file, content);
      this.stats.filesProcessed++;
    }
  }

  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Exécution du script
if (require.main === module) {
  const optimizer = new CodeOptimizer();
  optimizer.optimize().catch(console.error);
}

module.exports = CodeOptimizer;