-- =====================================================
-- Database Consolidation Migration: livres -> produits
-- This migration consolidates all references from 'livres' table to 'produits' table
-- =====================================================
-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;
-- Step 1: Check if livres table exists and migrate data if it does
-- This approach is more compatible with MariaDB
INSERT IGNORE INTO produits (
        type,
        titre,
        auteur,
        description,
        prix,
        stock,
        image_url,
        has_landing_page,
        landing_page_enabled,
        slug,
        created_at,
        updated_at
    )
SELECT "book",
    titre,
    auteur,
    description,
    prix,
    stock,
    image_url,
    COALESCE(has_landing_page, 0),
    COALESCE(landing_page_enabled, 0),
    slug,
    COALESCE(date_ajout, NOW()),
    NOW()
FROM livres
WHERE EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
            AND table_name = 'livres'
    )
    AND NOT EXISTS (
        SELECT 1
        FROM produits p
        WHERE p.titre = livres.titre
            AND p.auteur = livres.auteur
    );
-- Step 3: Update foreign key references in related tables
-- Update product_images table references
UPDATE product_images pi
    JOIN produits p ON pi.product_id = p.id
SET pi.product_id = p.id
WHERE EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
            AND table_name = 'livres'
    );
-- Update product_content_blocks table references
UPDATE product_content_blocks pcb
    JOIN produits p ON pcb.product_id = p.id
SET pcb.product_id = p.id
WHERE EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
            AND table_name = 'livres'
    );
-- Step 4: Update foreign key constraints to reference produits table
-- Drop existing foreign key constraints that reference livres
ALTER TABLE details_commande DROP FOREIGN KEY IF EXISTS details_commande_ibfk_2;
ALTER TABLE panier DROP FOREIGN KEY IF EXISTS panier_ibfk_1;
ALTER TABLE product_content_blocks DROP FOREIGN KEY IF EXISTS product_content_blocks_ibfk_1;
-- Add new foreign key constraints that reference produits
ALTER TABLE details_commande
ADD CONSTRAINT details_commande_ibfk_2 FOREIGN KEY (livre_id) REFERENCES produits(id) ON DELETE RESTRICT;
ALTER TABLE panier
ADD CONSTRAINT panier_ibfk_1 FOREIGN KEY (livre_id) REFERENCES produits(id) ON DELETE CASCADE;
ALTER TABLE product_content_blocks
ADD CONSTRAINT product_content_blocks_ibfk_1 FOREIGN KEY (product_id) REFERENCES produits(id) ON DELETE CASCADE;
-- Step 5: Ensure produits table has all necessary columns
-- Add missing columns if they don't exist
ALTER TABLE produits
ADD COLUMN IF NOT EXISTS has_landing_page TINYINT(1) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS landing_page_enabled TINYINT(1) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS slug VARCHAR(255) DEFAULT NULL,
    ADD COLUMN IF NOT EXISTS actif TINYINT(1) DEFAULT 1;
-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_produits_slug ON produits(slug);
CREATE INDEX IF NOT EXISTS idx_produits_landing ON produits(has_landing_page, landing_page_enabled);
CREATE INDEX IF NOT EXISTS idx_produits_type ON produits(type);
CREATE INDEX IF NOT EXISTS idx_produits_actif ON produits(actif);
-- Step 6: Drop livres table if it exists (only after successful migration)
-- This is commented out for safety - uncomment only after verifying migration
-- DROP TABLE IF EXISTS livres;
-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
-- Step 7: Verification queries (optional - for manual verification)
-- SELECT 'Migration completed successfully' as status;
-- SELECT COUNT(*) as total_products FROM produits;
-- SELECT type, COUNT(*) as count FROM produits GROUP BY type;
