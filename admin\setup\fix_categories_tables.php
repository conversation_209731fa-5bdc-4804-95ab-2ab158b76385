<?php
/**
 * Fix Categories Tables
 * إصلاح جداول الفئات
 */

echo "<h2>إصلاح جداول الفئات</h2>\n";

// Load configuration manually
$envFile = '../../.env';
if (!file_exists($envFile)) {
    die("ملف .env غير موجود في: $envFile");
}

// Load configuration
$config = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) {
        continue;
    }
    list($key, $value) = explode('=', $line, 2) + [NULL, NULL];
    if (!empty($key)) {
        $config[trim($key)] = trim($value ?? '');
    }
}

// Check required database settings
$required = ['DB_HOST', 'DB_PORT', 'DB_USERNAME', 'DB_DATABASE'];
$missing = [];
foreach ($required as $key) {
    if (empty($config[$key])) {
        $missing[] = $key;
    }
}

if (!empty($missing)) {
    die('إعدادات قاعدة البيانات المفقودة: ' . implode(', ', $missing));
}

try {
    // Connect to database
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $config['DB_HOST'],
        $config['DB_PORT'],
        $config['DB_DATABASE']
    );
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, $config['DB_USERNAME'], $config['DB_PASSWORD'] ?? '', $options);
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    
    // Drop existing tables if they exist
    echo "<h3>حذف الجداول الموجودة (إن وجدت):</h3>\n";
    
    $dropTables = [
        "DROP TABLE IF EXISTS `category_stats`",
        "DROP TABLE IF EXISTS `category_hierarchy`", 
        "DROP TABLE IF EXISTS `category_translations`",
        "DROP TABLE IF EXISTS `categories`"
    ];
    
    foreach ($dropTables as $dropSQL) {
        try {
            $pdo->exec($dropSQL);
            echo "<p style='color: orange;'>🗑️ تم حذف الجدول</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في حذف الجدول: " . $e->getMessage() . "</p>\n";
        }
    }
    
    // Create categories table
    echo "<h3>إنشاء جدول categories الجديد:</h3>\n";
    $createCategories = "
    CREATE TABLE `categories` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `name_ar` varchar(255) NOT NULL,
      `name_en` varchar(255) DEFAULT NULL,
      `name_fr` varchar(255) DEFAULT NULL,
      `slug` varchar(255) NOT NULL,
      `description_ar` text DEFAULT NULL,
      `description_en` text DEFAULT NULL,
      `description_fr` text DEFAULT NULL,
      `parent_id` int(11) DEFAULT NULL,
      `image` varchar(500) DEFAULT NULL,
      `icon` varchar(100) DEFAULT 'fas fa-folder',
      `color` varchar(7) DEFAULT '#667eea',
      `sort_order` int(11) DEFAULT 0,
      `is_active` tinyint(1) DEFAULT 1,
      `is_featured` tinyint(1) DEFAULT 0,
      `meta_title_ar` varchar(255) DEFAULT NULL,
      `meta_title_en` varchar(255) DEFAULT NULL,
      `meta_title_fr` varchar(255) DEFAULT NULL,
      `meta_description_ar` text DEFAULT NULL,
      `meta_description_en` text DEFAULT NULL,
      `meta_description_fr` text DEFAULT NULL,
      `meta_keywords_ar` text DEFAULT NULL,
      `meta_keywords_en` text DEFAULT NULL,
      `meta_keywords_fr` text DEFAULT NULL,
      `created_by` int(11) DEFAULT NULL,
      `updated_by` int(11) DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `slug` (`slug`),
      KEY `idx_parent_id` (`parent_id`),
      KEY `idx_is_active` (`is_active`),
      KEY `idx_is_featured` (`is_featured`),
      KEY `idx_sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createCategories);
    echo "<p style='color: green;'>✅ تم إنشاء جدول categories بنجاح</p>\n";
    
    // Create category_stats table
    echo "<h3>إنشاء جدول category_stats:</h3>\n";
    $createCategoryStats = "
    CREATE TABLE `category_stats` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `category_id` int(11) NOT NULL,
      `products_count` int(11) DEFAULT 0,
      `subcategories_count` int(11) DEFAULT 0,
      `views_count` int(11) DEFAULT 0,
      `last_product_added` timestamp NULL DEFAULT NULL,
      `last_viewed` timestamp NULL DEFAULT NULL,
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_category_stats` (`category_id`),
      KEY `idx_category_id` (`category_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createCategoryStats);
    echo "<p style='color: green;'>✅ تم إنشاء جدول category_stats بنجاح</p>\n";
    
    // Insert default categories
    echo "<h3>إدراج الفئات الافتراضية:</h3>\n";
    
    $defaultCategories = [
        // Main categories
        ['الكتب الإلكترونية', 'E-Books', 'Livres Électroniques', 'e-books', 'مجموعة شاملة من الكتب الإلكترونية في مختلف المجالات', 'Comprehensive collection of e-books in various fields', 'Collection complète de livres électroniques dans divers domaines', NULL, NULL, 'fas fa-book', '#667eea', 1, 1, 1],
        ['المنتجات الرقمية', 'Digital Products', 'Produits Numériques', 'digital-products', 'منتجات رقمية متنوعة', 'Various digital products', 'Divers produits numériques', NULL, NULL, 'fas fa-laptop', '#6f42c1', 2, 1, 1],
        ['الدورات التدريبية', 'Training Courses', 'Cours de Formation', 'training-courses', 'دورات تدريبية أونلاين', 'Online training courses', 'Cours de formation en ligne', NULL, NULL, 'fas fa-chalkboard-teacher', '#20c997', 3, 1, 1],
        
        // Sub-categories for books
        ['الكتب التعليمية', 'Educational Books', 'Livres Éducatifs', 'educational-books', 'كتب تعليمية ومناهج دراسية', 'Educational books and curricula', 'Livres éducatifs et programmes scolaires', 1, NULL, 'fas fa-graduation-cap', '#28a745', 1, 1, 1],
        ['الكتب الأدبية', 'Literature Books', 'Livres Littéraires', 'literature-books', 'روايات وقصص وشعر', 'Novels, stories and poetry', 'Romans, histoires et poésie', 1, NULL, 'fas fa-feather-alt', '#dc3545', 2, 1, 1],
        ['الكتب العلمية', 'Scientific Books', 'Livres Scientifiques', 'scientific-books', 'كتب في العلوم والتكنولوجيا', 'Books in science and technology', 'Livres de science et technologie', 1, NULL, 'fas fa-flask', '#17a2b8', 3, 1, 1],
        ['كتب الأطفال', 'Children Books', 'Livres pour Enfants', 'children-books', 'كتب مخصصة للأطفال', 'Books designed for children', 'Livres conçus pour les enfants', 1, NULL, 'fas fa-child', '#ffc107', 4, 1, 1],
        
        // Sub-categories for digital products
        ['البرمجيات', 'Software', 'Logiciels', 'software', 'برامج وتطبيقات', 'Programs and applications', 'Programmes et applications', 2, NULL, 'fas fa-code', '#fd7e14', 1, 1, 0],
        ['القوالب والتصاميم', 'Templates & Designs', 'Modèles et Designs', 'templates-designs', 'قوالب مواقع وتصاميم جرافيك', 'Website templates and graphic designs', 'Modèles de sites web et designs graphiques', 2, NULL, 'fas fa-paint-brush', '#e83e8c', 2, 1, 0],
        
        // Sub-categories for courses
        ['دورات البرمجة', 'Programming Courses', 'Cours de Programmation', 'programming-courses', 'دورات تعلم البرمجة', 'Programming learning courses', 'Cours d\'apprentissage de la programmation', 3, NULL, 'fas fa-laptop-code', '#6610f2', 1, 1, 1],
        ['دورات التصميم', 'Design Courses', 'Cours de Design', 'design-courses', 'دورات تعلم التصميم الجرافيكي', 'Graphic design learning courses', 'Cours d\'apprentissage du design graphique', 3, NULL, 'fas fa-palette', '#fd7e14', 2, 1, 1],
        ['دورات التسويق', 'Marketing Courses', 'Cours de Marketing', 'marketing-courses', 'دورات التسويق الرقمي', 'Digital marketing courses', 'Cours de marketing numérique', 3, NULL, 'fas fa-bullhorn', '#dc3545', 3, 1, 1]
    ];
    
    $insertStmt = $pdo->prepare("
        INSERT INTO categories 
        (name_ar, name_en, name_fr, slug, description_ar, description_en, description_fr, parent_id, image, icon, color, sort_order, is_active, is_featured) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $insertedCount = 0;
    foreach ($defaultCategories as $category) {
        try {
            $insertStmt->execute($category);
            $insertedCount++;
            echo "<p style='color: green; margin: 2px 0;'>✅ " . $category[0] . "</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: red; margin: 2px 0;'>❌ خطأ في إدراج " . $category[0] . ": " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم إدراج $insertedCount فئة من أصل " . count($defaultCategories) . "</p>\n";
    
    // Initialize category stats
    echo "<h3>تهيئة إحصائيات الفئات:</h3>\n";
    
    $statsStmt = $pdo->prepare("
        INSERT INTO category_stats (category_id, products_count, subcategories_count, views_count)
        SELECT 
            c.id,
            0 as products_count,
            (SELECT COUNT(*) FROM categories sub WHERE sub.parent_id = c.id) as subcategories_count,
            0 as views_count
        FROM categories c
    ");
    
    $statsStmt->execute();
    echo "<p style='color: green;'>✅ تم تهيئة إحصائيات الفئات</p>\n";
    
    // Verify tables and data
    echo "<h3>التحقق من النتائج النهائية:</h3>\n";
    
    $tables = ['categories', 'category_stats'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
        $count = $stmt->fetchColumn();
        echo "<p style='color: green;'>✅ جدول $table: $count سجل</p>\n";
    }
    
    // Show sample categories
    echo "<h3>عينة من الفئات المدرجة:</h3>\n";
    $stmt = $pdo->query("
        SELECT c.id, c.name_ar, c.name_en, c.slug, c.parent_id, p.name_ar as parent_name, c.is_active, c.is_featured
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        ORDER BY c.parent_id ASC, c.sort_order ASC, c.name_ar ASC
    ");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($categories) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 0.9em;'>\n";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>الاسم العربي</th><th>الاسم الإنجليزي</th><th>الرابط</th><th>الفئة الأب</th><th>نشط</th><th>مميز</th></tr>\n";
        foreach ($categories as $category) {
            $activeIcon = $category['is_active'] ? '✅' : '❌';
            $featuredIcon = $category['is_featured'] ? '⭐' : '';
            echo "<tr>";
            echo "<td>" . $category['id'] . "</td>";
            echo "<td>" . htmlspecialchars($category['name_ar']) . "</td>";
            echo "<td>" . htmlspecialchars($category['name_en'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($category['slug']) . "</td>";
            echo "<td>" . htmlspecialchars($category['parent_name'] ?? 'رئيسية') . "</td>";
            echo "<td style='text-align: center;'>" . $activeIcon . "</td>";
            echo "<td style='text-align: center;'>" . $featuredIcon . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // Show hierarchy structure
    echo "<h3>هيكل الفئات الهرمي:</h3>\n";
    $stmt = $pdo->query("
        SELECT c.id, c.name_ar, c.parent_id, s.subcategories_count
        FROM categories c
        LEFT JOIN category_stats s ON c.id = s.category_id
        WHERE c.parent_id IS NULL
        ORDER BY c.sort_order ASC, c.name_ar ASC
    ");
    $mainCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>\n";
    foreach ($mainCategories as $main) {
        echo "<div style='margin-bottom: 10px;'>\n";
        echo "<strong>📁 " . htmlspecialchars($main['name_ar']) . "</strong>\n";
        
        // Get subcategories
        $subStmt = $pdo->prepare("
            SELECT name_ar, s.subcategories_count
            FROM categories c
            LEFT JOIN category_stats s ON c.id = s.category_id
            WHERE c.parent_id = ?
            ORDER BY c.sort_order ASC, c.name_ar ASC
        ");
        $subStmt->execute([$main['id']]);
        $subcategories = $subStmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($subcategories) {
            foreach ($subcategories as $sub) {
                echo "<div style='margin-right: 20px; color: #666;'>└── 📂 " . htmlspecialchars($sub['name_ar']) . "</div>\n";
            }
        }
        echo "</div>\n";
    }
    echo "</div>\n";
    
    echo "<div style='color: green; font-weight: bold; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "🎉 تم إصلاح وإنشاء جداول الفئات بنجاح!<br>";
    echo "تم إنشاء " . count($tables) . " جداول مع " . count($defaultCategories) . " فئة افتراضية.<br>";
    echo "يمكنك الآن استخدام قسم إدارة الفئات في لوحة الإدارة بدون مشاكل.";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ خطأ في إصلاح الجداول: " . $e->getMessage();
    echo "</div>\n";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح جداول الفئات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
            margin-top: 25px;
        }
        p {
            margin: 8px 0;
        }
        table {
            font-size: 0.9em;
        }
        th, td {
            padding: 8px 12px;
            text-align: right;
        }
        th {
            font-weight: bold;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="../index.html" class="back-link">← العودة إلى لوحة الإدارة</a>
        <a href="test_database_connection.php" class="back-link" style="background: #28a745;">🔍 اختبار الاتصال</a>
        <a href="../php/categories.php?action=get_all" class="back-link" style="background: #17a2b8;" target="_blank">📊 اختبار API</a>
    </div>
</body>
</html>
