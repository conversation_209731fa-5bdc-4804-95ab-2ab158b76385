<?php
require_once 'php/config.php';

try {
    // Create a landing page for "فن اللامبالاة" (ID: 9)
    $stmt = $conn->prepare("
        INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $contenu_droit = '
    <h3>🎯 لماذا هذا الكتاب مميز؟</h3>
    <ul>
        <li><strong>نهج جديد في التفكير:</strong> يقدم مارك مانسون طريقة مختلفة تماماً للنظر إلى الحياة</li>
        <li><strong>أسلوب مباشر وصريح:</strong> بدون تعقيدات أو كلام منمق، فقط الحقائق</li>
        <li><strong>نصائح عملية:</strong> يمكنك تطبيقها في حياتك اليومية فوراً</li>
        <li><strong>مترجم بعناية:</strong> ترجمة احترافية تحافظ على روح النص الأصلي</li>
    </ul>
    
    <h3>💡 ما ستتعلمه من هذا الكتاب:</h3>
    <p>• كيف تختار معاركك بحكمة<br>
    • فن قول "لا" بثقة<br>
    • التخلص من القلق غير المبرر<br>
    • بناء حياة أكثر معنى وأقل توتراً</p>
    ';
    
    $contenu_gauche = '
    <h3>📚 عن المؤلف مارك مانسون</h3>
    <p>مارك مانسون كاتب أمريكي ومدون مشهور، اشتهر بأسلوبه المباشر والصريح في الكتابة. يركز في أعماله على علم النفس الإيجابي والتطوير الذاتي بطريقة عملية وواقعية.</p>
    
    <h3>🌟 آراء القراء</h3>
    <blockquote>
        "كتاب غيّر نظرتي للحياة تماماً. أسلوب مانسون المباشر يجعلك تواجه الحقائق بشجاعة."
        <cite>- أحمد محمد، قارئ</cite>
    </blockquote>
    
    <blockquote>
        "أخيراً كتاب تطوير ذات بدون كلام منمق! نصائح عملية يمكن تطبيقها فعلاً."
        <cite>- فاطمة علي، طالبة جامعية</cite>
    </blockquote>
    
    <h3>🎁 عرض خاص</h3>
    <p><strong>احصل على الكتاب الآن بسعر مخفض!</strong><br>
    السعر العادي: 2500 دج<br>
    <span style="color: #e74c3c; font-size: 1.2em;">السعر الحالي: 1800 دج فقط!</span></p>
    ';
    
    $stmt->execute([
        9, // produit_id for "فن اللامبالاة"
        'فن اللامبالاة - نهج معاكس للعيش حياة جيدة',
        $contenu_droit,
        $contenu_gauche,
        '/landing-page-template.php?id='
    ]);
    
    $landing_page_id = $conn->lastInsertId();
    
    // Add sample images for the landing page
    $images = [
        'images/book3.svg', // Main product image
        'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&h=600&fit=crop', // Book reading scene
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop', // Author workspace
        'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=800&h=600&fit=crop', // Book collection
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO landing_page_images (landing_page_id, image_url, ordre) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($images as $index => $image_url) {
        $stmt->execute([$landing_page_id, $image_url, $index]);
    }
    
    // Update the lien_url with the actual landing page ID
    $stmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
    $stmt->execute(['/landing-page-template.php?id=' . $landing_page_id, $landing_page_id]);
    
    echo "✅ Landing page créée avec succès!<br>";
    echo "ID de la landing page: $landing_page_id<br>";
    echo "URL: <a href='/landing-page-template.php?id=$landing_page_id' target='_blank'>Voir la landing page</a><br>";
    echo "Images ajoutées: " . count($images) . "<br>";
    
    // Display the created landing page info
    echo "<br><h3>📋 Détails de la landing page créée:</h3>";
    echo "<strong>Produit:</strong> فن اللامبالاة<br>";
    echo "<strong>Prix:</strong> 1800 دج<br>";
    echo "<strong>Auteur:</strong> مارك مانسون<br>";
    echo "<strong>Images:</strong> " . count($images) . " images dans le carousel<br>";
    echo "<strong>Contenu:</strong> Blocs de texte à droite et à gauche<br>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la création de la landing page: " . $e->getMessage();
}
?>
