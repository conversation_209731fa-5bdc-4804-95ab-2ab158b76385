<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-User-ID, X-User-Role');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    require_once __DIR__ . '/../config/database.php';

    $conn = getDatabaseConnection();
    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    // Get user information from query parameters
    $currentUserId = $_GET['user_id'] ?? 1;
    $currentUserRole = $_GET['user_role'] ?? 'admin';

    // Simple user validation
    $stmt = $conn->prepare("SELECT id, role, store_id FROM users WHERE id = ?");
    $stmt->execute([$currentUserId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        // Use default user info
        $user = [
            'id' => $currentUserId,
            'role' => $currentUserRole,
            'store_id' => 1
        ];
    }

    // Determine if user is admin
    $isAdmin = in_array($user['role'], ['admin', 'super_admin']);

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGetProducts($conn, $currentUserId, $user, $isAdmin);
            break;
        case 'POST':
            handleCreateProduct($conn, $currentUserId, $user);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed'], JSON_UNESCAPED_UNICODE);
    }
} catch (Exception $e) {
    error_log('Error in products-multi-user.php: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}

function handleGetProducts($conn, $currentUserId, $user, $isAdmin)
{
    try {
        if ($isAdmin) {
            // Admin sees ALL products with user info
            $sql = "
                SELECT p.*, u.username as owner_username, u.email as owner_email
                FROM produits p
                LEFT JOIN users u ON p.user_id = u.id
                ORDER BY p.created_at DESC
            ";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
        } else {
            // Regular users see only their own products
            if (isset($user['store_id']) && $user['store_id']) {
                $sql = "SELECT * FROM produits WHERE store_id = ? ORDER BY created_at DESC";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$user['store_id']]);
            } else {
                $sql = "SELECT * FROM produits WHERE user_id = ? ORDER BY created_at DESC";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$currentUserId]);
            }
        }

        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Add metadata for each product
        $stats = [
            'total_products' => count($products),
            'active_products' => 0,
            'owned_products' => 0
        ];

        foreach ($products as &$product) {
            // Check if product has landing page
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM landing_pages WHERE produit_id = ?");
            $stmt->execute([$product['id']]);
            $product['has_landing_page'] = $stmt->fetch()['count'] > 0;

            // Format price
            $product['formatted_price'] = number_format($product['prix'] ?? 0, 2) . ' دج';

            // Add URLs
            $product['url'] = '/product.php?id=' . $product['id'];
            if ($product['has_landing_page']) {
                $product['landing_url'] = '/landing-page.php?id=' . $product['id'];
            }

            // Count active products
            if (isset($product['actif']) && $product['actif']) {
                $stats['active_products']++;
            }

            // Calculate ownership
            $isOwner = ($product['user_id'] == $currentUserId) || ($product['store_id'] == $user['store_id']);
            if ($isOwner) {
                $stats['owned_products']++;
            }

            // Add ownership info
            $product['ownership_info'] = [
                'is_owner' => $isOwner,
                'owner_name' => $product['owner_username'] ?? 'Unknown',
                'can_edit' => $isOwner || $isAdmin,
                'can_delete' => $isOwner || $isAdmin
            ];

            // Add admin-specific info
            if ($isAdmin && isset($product['owner_username'])) {
                $product['admin_info'] = [
                    'username' => $product['owner_username'],
                    'email' => $product['owner_email'],
                    'user_id' => $product['user_id'],
                    'store_id' => $product['store_id']
                ];
            }
        }

        $response = [
            'success' => true,
            'products' => $products,
            'user_info' => [
                'id' => $user['id'],
                'role' => $user['role'],
                'store_id' => $user['store_id'],
                'is_admin' => $isAdmin
            ],
            'stats' => $stats,
            'message' => $isAdmin ?
                'Admin view: All products loaded with ownership info' :
                'User products loaded successfully'
        ];

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        throw new Exception("Error loading products: " . $e->getMessage());
    }
}

function handleCreateProduct($conn, $currentUserId, $user)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON input'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Required fields
        $requiredFields = ['titre', 'description', 'prix', 'type'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => "Missing required field: $field"], JSON_UNESCAPED_UNICODE);
                return;
            }
        }

        // Insert new product
        $sql = "
            INSERT INTO produits (
                store_id, user_id, titre, description, prix, type,
                stock, image_url, actif,
                created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?,
                ?, ?, 1,
                NOW(), NOW()
            )
        ";

        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $user['store_id'] ?? 1,
            $currentUserId,
            $input['titre'],
            $input['description'],
            $input['prix'],
            $input['type'],
            $input['stock'] ?? 0,
            $input['image_url'] ?? null
        ]);

        $productId = $conn->lastInsertId();

        // Get the created product
        $stmt = $conn->prepare("SELECT * FROM produits WHERE id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'product' => $product,
            'product_id' => $productId,
            'message' => 'Product created successfully'
        ], JSON_UNESCAPED_UNICODE);
    } catch (Exception $e) {
        error_log('Error creating product: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Error creating product: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}
