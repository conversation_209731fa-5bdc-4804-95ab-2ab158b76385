<?php

/**
 * Safe Database Migration Runner
 * Fixed version that handles MariaDB syntax properly
 */

require_once __DIR__ . '/../php/config.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل ترحيل قاعدة البيانات الآمن</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }

        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }

        h1,
        h2,
        h3 {
            color: #333;
        }

        .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }

        .button:hover {
            background: #0056b3;
        }

        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔧 تشغيل ترحيل قاعدة البيانات الآمن</h1>
        <p>إصدار محسن يتعامل مع أخطاء MariaDB بشكل صحيح</p>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_migration'])) {
            try {
                echo "<div class='info'>🚀 بدء تشغيل الترحيل...</div>";

                $pdo = getPDOConnection();

                // Test connection
                echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

                // Check current state
                echo "<h3>📊 فحص الحالة الحالية</h3>";

                // Check if livres table exists
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables
                                    WHERE table_schema = DATABASE() AND table_name = 'livres'");
                $livresExists = $stmt->fetch()['count'] > 0;

                if ($livresExists) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM livres");
                    $livresCount = $stmt->fetch()['count'];
                    echo "<div class='info'>📚 جدول livres موجود مع $livresCount عنصر</div>";
                } else {
                    echo "<div class='warning'>⚠️ جدول livres غير موجود</div>";
                }

                // Check produits table
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables
                                    WHERE table_schema = DATABASE() AND table_name = 'produits'");
                $produitsExists = $stmt->fetch()['count'] > 0;

                if ($produitsExists) {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
                    $produitsCount = $stmt->fetch()['count'];
                    echo "<div class='info'>📦 جدول produits موجود مع $produitsCount عنصر</div>";
                } else {
                    echo "<div class='error'>❌ جدول produits غير موجود! يجب إنشاؤه أولاً</div>";
                    exit;
                }

                echo "<h3>🔄 تنفيذ الترحيل</h3>";

                // Start transaction
                $transactionStarted = false;
                try {
                    $pdo->beginTransaction();
                    $transactionStarted = true;
                    // Step 1: Disable foreign key checks
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
                    echo "<div class='success'>✅ تم تعطيل فحص المفاتيح الخارجية</div>";

                    // Step 2: Migrate data if livres table exists
                    if ($livresExists) {
                        $migrateSQL = "
                        INSERT IGNORE INTO produits (type, titre, auteur, description, prix, stock, image_url, has_landing_page, landing_page_enabled, slug, created_at, updated_at)
                        SELECT 'book', titre, auteur, description, prix, stock, image_url,
                               COALESCE(has_landing_page, 0), COALESCE(landing_page_enabled, 0), slug,
                               COALESCE(date_ajout, NOW()), NOW()
                        FROM livres
                        WHERE NOT EXISTS (SELECT 1 FROM produits p WHERE p.titre = livres.titre AND p.auteur = livres.auteur)
                        ";

                        $stmt = $pdo->prepare($migrateSQL);
                        $stmt->execute();
                        $migratedCount = $stmt->rowCount();
                        echo "<div class='success'>✅ تم ترحيل $migratedCount عنصر من livres إلى produits</div>";
                    }

                    // Step 3: Update foreign key constraints
                    echo "<div class='info'>🔗 تحديث المفاتيح الخارجية...</div>";

                    // Drop existing constraints safely
                    $constraints = [
                        "ALTER TABLE details_commande DROP FOREIGN KEY IF EXISTS details_commande_ibfk_2",
                        "ALTER TABLE panier DROP FOREIGN KEY IF EXISTS panier_ibfk_1",
                        "ALTER TABLE product_content_blocks DROP FOREIGN KEY IF EXISTS product_content_blocks_ibfk_1"
                    ];

                    foreach ($constraints as $sql) {
                        try {
                            $pdo->exec($sql);
                            echo "<div class='success'>✅ " . substr($sql, 0, 50) . "...</div>";
                        } catch (Exception $e) {
                            echo "<div class='warning'>⚠️ " . $e->getMessage() . "</div>";
                        }
                    }

                    // Add new constraints
                    $newConstraints = [
                        "ALTER TABLE details_commande ADD CONSTRAINT details_commande_ibfk_2 FOREIGN KEY (livre_id) REFERENCES produits(id) ON DELETE RESTRICT",
                        "ALTER TABLE panier ADD CONSTRAINT panier_ibfk_1 FOREIGN KEY (livre_id) REFERENCES produits(id) ON DELETE CASCADE",
                        "ALTER TABLE product_content_blocks ADD CONSTRAINT product_content_blocks_ibfk_1 FOREIGN KEY (product_id) REFERENCES produits(id) ON DELETE CASCADE"
                    ];

                    foreach ($newConstraints as $sql) {
                        try {
                            $pdo->exec($sql);
                            echo "<div class='success'>✅ " . substr($sql, 0, 50) . "...</div>";
                        } catch (Exception $e) {
                            echo "<div class='warning'>⚠️ " . $e->getMessage() . "</div>";
                        }
                    }

                    // Step 4: Add missing columns
                    $columnUpdates = [
                        "ALTER TABLE produits ADD COLUMN IF NOT EXISTS has_landing_page TINYINT(1) DEFAULT 0",
                        "ALTER TABLE produits ADD COLUMN IF NOT EXISTS landing_page_enabled TINYINT(1) DEFAULT 0",
                        "ALTER TABLE produits ADD COLUMN IF NOT EXISTS slug VARCHAR(255) DEFAULT NULL",
                        "ALTER TABLE produits ADD COLUMN IF NOT EXISTS actif TINYINT(1) DEFAULT 1"
                    ];

                    foreach ($columnUpdates as $sql) {
                        try {
                            $pdo->exec($sql);
                            echo "<div class='success'>✅ " . substr($sql, 0, 50) . "...</div>";
                        } catch (Exception $e) {
                            echo "<div class='warning'>⚠️ " . $e->getMessage() . "</div>";
                        }
                    }

                    // Step 5: Add indexes
                    $indexes = [
                        "CREATE INDEX IF NOT EXISTS idx_produits_slug ON produits(slug)",
                        "CREATE INDEX IF NOT EXISTS idx_produits_landing ON produits(has_landing_page, landing_page_enabled)",
                        "CREATE INDEX IF NOT EXISTS idx_produits_type ON produits(type)",
                        "CREATE INDEX IF NOT EXISTS idx_produits_actif ON produits(actif)"
                    ];

                    foreach ($indexes as $sql) {
                        try {
                            $pdo->exec($sql);
                            echo "<div class='success'>✅ " . substr($sql, 0, 50) . "...</div>";
                        } catch (Exception $e) {
                            echo "<div class='warning'>⚠️ " . $e->getMessage() . "</div>";
                        }
                    }

                    // Re-enable foreign key checks
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
                    echo "<div class='success'>✅ تم تفعيل فحص المفاتيح الخارجية</div>";

                    // Commit transaction
                    if ($transactionStarted && $pdo->inTransaction()) {
                        $pdo->commit();
                        $transactionStarted = false;
                        echo "<div class='success'>🎉 تم الترحيل بنجاح!</div>";
                    }
                } catch (Exception $e) {
                    // Rollback only if transaction is active
                    if ($transactionStarted && $pdo->inTransaction()) {
                        try {
                            $pdo->rollback();
                            $transactionStarted = false;
                        } catch (Exception $rollbackError) {
                            echo "<div class='warning'>⚠️ خطأ في التراجع: " . $rollbackError->getMessage() . "</div>";
                        }
                    }
                    throw $e;
                }

                // Final verification (outside transaction)
                try {
                    echo "<h3>✅ التحقق النهائي</h3>";
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
                    $finalCount = $stmt->fetch()['count'];
                    echo "<div class='info'>📊 العدد النهائي للمنتجات: $finalCount</div>";
                } catch (Exception $e) {
                    echo "<div class='warning'>⚠️ تحذير في التحقق النهائي: " . $e->getMessage() . "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ خطأ في الترحيل: " . $e->getMessage() . "</div>";
                echo "<div class='error'>📁 الملف: " . $e->getFile() . "</div>";
                echo "<div class='error'>📍 السطر: " . $e->getLine() . "</div>";
            }
        } else {
        ?>
            <div class="warning">
                <h3>⚠️ تحذير مهم</h3>
                <p>هذا الترحيل سيقوم بتحديث قاعدة البيانات. تأكد من عمل نسخة احتياطية قبل المتابعة.</p>
            </div>

            <form method="POST">
                <button type="submit" name="run_migration" class="button">🚀 تشغيل الترحيل</button>
            </form>

            <div class="info">
                <h3>📋 ما سيتم تنفيذه:</h3>
                <ul>
                    <li>ترحيل البيانات من جدول livres إلى produits</li>
                    <li>تحديث المفاتيح الخارجية</li>
                    <li>إضافة الأعمدة المفقودة</li>
                    <li>إنشاء الفهارس المطلوبة</li>
                </ul>
            </div>
        <?php
        }
        ?>
    </div>
</body>

</html>
