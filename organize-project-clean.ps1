# Script d'Organisation et de Nettoyage du Projet
# Landing Pages SaaS - "صفحات هبوط للجميع"

Write-Host "🧹 Démarrage du nettoyage et organisation du projet..." -ForegroundColor Green

# Définir le répertoire racine du projet
$ProjectRoot = Get-Location
Write-Host "📁 Répertoire du projet: $ProjectRoot" -ForegroundColor Cyan

# Créer la structure de dossiers pour l'organisation
$FoldersToCreate = @(
    "scripts\tests",
    "scripts\debug",
    "scripts\setup",
    "scripts\migration",
    "scripts\maintenance",
    "documentation\summaries",
    "documentation\guides",
    "temp\backup",
    "temp\logs"
)

Write-Host "`n📂 Création de la structure de dossiers..." -ForegroundColor Yellow
foreach ($folder in $FoldersToCreate) {
    $fullPath = Join-Path $ProjectRoot $folder
    if (-not (Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "  ✅ Créé: $folder" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ Existe déjà: $folder" -ForegroundColor Yellow
    }
}

# Fonction pour déplacer les fichiers
function Move-FilesWithPattern {
    param(
        [string]$SourceDir,
        [string]$TargetDir,
        [string[]]$Patterns
    )
    
    $movedCount = 0
    foreach ($pattern in $Patterns) {
        $files = Get-ChildItem -Path $SourceDir -Name $pattern -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            $sourcePath = Join-Path $SourceDir $file
            $targetPath = Join-Path $TargetDir $file
            
            if (Test-Path $sourcePath) {
                try {
                    Move-Item -Path $sourcePath -Destination $targetPath -Force
                    Write-Host "    📦 Déplacé: $file" -ForegroundColor Green
                    $movedCount++
                } catch {
                    Write-Host "    ❌ Erreur lors du déplacement de $file : $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    }
    return $movedCount
}

# Définir les patterns de fichiers à déplacer depuis la racine
$RootFilePatterns = @{
    "scripts\tests" = @(
        "test-*.html",
        "test-*.php",
        "*-test.html",
        "*-test.php",
        "debug-*.html",
        "debug-*.php",
        "admin-test*.html",
        "admin-fixes-test.html",
        "admin-critical-fixes-test.html"
    )
    "scripts\debug" = @(
        "debug*.php",
        "diagnose*.php",
        "check-*.php",
        "verify-*.php",
        "comprehensive-*.php",
        "final-*-test.php",
        "analyze*.php",
        "quick-*.php"
    )
    "scripts\setup" = @(
        "setup-*.php",
        "create-*.php",
        "populate-*.php",
        "add-*.php",
        "implement-*.php",
        "migration-*.php",
        "import*.php"
    )
    "scripts\maintenance" = @(
        "fix-*.php",
        "update-*.php",
        "run-*.php",
        "master-fix-*.php",
        "complete-*.php",
        "integrate-*.php"
    )
    "documentation\summaries" = @(
        "*_SUMMARY.md",
        "*_FIXES_SUMMARY.md",
        "*_COMPLETE_SUMMARY.md",
        "*_IMPLEMENTATION_SUMMARY.md",
        "FIXES_SUMMARY.md",
        "CRITICAL_*.md",
        "ADMIN_*.md",
        "CHANGELOG.md"
    )
    "documentation\guides" = @(
        "*_GUIDE.md",
        "*_DOCUMENTATION.md",
        "DEPLOYMENT_GUIDE.md",
        "TECHNICAL_*.md",
        "GUIDE_*.md",
        "CODE_*.md"
    )
    "temp\logs" = @(
        "*.err",
        "*.log",
        "query-result.txt",
        "console-errors*.err"
    )
}

# Déplacer les fichiers depuis la racine
Write-Host "`n🚚 Déplacement des fichiers depuis la racine..." -ForegroundColor Yellow
$totalMoved = 0
foreach ($targetFolder in $RootFilePatterns.Keys) {
    $patterns = $RootFilePatterns[$targetFolder]
    $targetPath = Join-Path $ProjectRoot $targetFolder
    
    Write-Host "  📁 Vers $targetFolder :" -ForegroundColor Cyan
    $moved = Move-FilesWithPattern -SourceDir $ProjectRoot -TargetDir $targetPath -Patterns $patterns
    $totalMoved += $moved
    
    if ($moved -eq 0) {
        Write-Host "    ℹ️ Aucun fichier trouvé" -ForegroundColor Gray
    }
}

# Déplacer les fichiers depuis le dossier admin/
$adminDir = Join-Path $ProjectRoot "admin"
if (Test-Path $adminDir) {
    Write-Host "`n🚚 Déplacement des fichiers depuis admin/..." -ForegroundColor Yellow
    
    # Patterns spécifiques pour admin/
    $adminPatterns = @{
        "scripts\tests" = @(
            "test-*.html",
            "test-*.php",
            "*-test.html",
            "*-test.php",
            "debug-*.html",
            "firefox-test*.html",
            "admin-*-test.html"
        )
        "scripts\debug" = @(
            "debug-*.php",
            "diagnose-*.php",
            "check-*.php",
            "verify-*.php",
            "comprehensive-*.php",
            "server-*.php",
            "quick-*.php"
        )
        "scripts\setup" = @(
            "setup-*.php",
            "create-*.php",
            "populate-*.php",
            "add-*.php",
            "run-*.php"
        )
        "scripts\maintenance" = @(
            "fix-*.php",
            "master-fix-*.php",
            "final-*.php"
        )
        "documentation\summaries" = @(
            "*_SUMMARY.md",
            "*_FIXES_SUMMARY.md",
            "*_DOCUMENTATION.md",
            "ADMIN_*.md",
            "CRITICAL*.md",
            "HTTP-*.md",
            "JAVASCRIPT-*.md",
            "OPENAI-*.md"
        )
    }
    
    foreach ($targetFolder in $adminPatterns.Keys) {
        $patterns = $adminPatterns[$targetFolder]
        $targetPath = Join-Path $ProjectRoot $targetFolder
        
        Write-Host "  📁 Admin vers $targetFolder :" -ForegroundColor Cyan
        $moved = Move-FilesWithPattern -SourceDir $adminDir -TargetDir $targetPath -Patterns $patterns
        $totalMoved += $moved
        
        if ($moved -eq 0) {
            Write-Host "    ℹ️ Aucun fichier trouvé" -ForegroundColor Gray
        }
    }
}

# Nettoyer les fichiers temporaires et de sauvegarde
Write-Host "`n🗑️ Nettoyage des fichiers temporaires..." -ForegroundColor Yellow
$tempPatterns = @(
    "*.tmp",
    "*.bak",
    "*~",
    "*.orig",
    "Thumbs.db",
    ".DS_Store"
)

$cleanedCount = 0
foreach ($pattern in $tempPatterns) {
    $tempFiles = Get-ChildItem -Path $ProjectRoot -Name $pattern -Recurse -ErrorAction SilentlyContinue
    foreach ($file in $tempFiles) {
        $filePath = Join-Path $ProjectRoot $file
        try {
            Remove-Item -Path $filePath -Force
            Write-Host "  🗑️ Supprimé: $file" -ForegroundColor Red
            $cleanedCount++
        } catch {
            Write-Host "  ❌ Erreur lors de la suppression de $file" -ForegroundColor Red
        }
    }
}

# Créer un fichier d'index pour chaque dossier
Write-Host "`n📋 Création des fichiers d'index..." -ForegroundColor Yellow
$indexContent = @{
    "scripts\tests" = "# Fichiers de Tests`n`nCe dossier contient tous les fichiers de test et de validation du système.`n`n## Types de tests`n- Tests unitaires`n- Tests d'intégration`n- Tests de l'interface admin`n- Tests de débogage"
    "scripts\debug" = "# Fichiers de Debug`n`nCe dossier contient les outils de diagnostic et de débogage.`n`n## Outils disponibles`n- Scripts de diagnostic`n- Vérifications de base de données`n- Tests de connectivité`n- Analyses système"
    "scripts\setup" = "# Scripts d'Installation`n`nCe dossier contient les scripts d'installation et de configuration.`n`n## Scripts disponibles`n- Configuration initiale`n- Création de données de test`n- Migration de base de données`n- Peuplement des données"
    "scripts\maintenance" = "# Scripts de Maintenance`n`nCe dossier contient les scripts de maintenance et de réparation.`n`n## Scripts disponibles`n- Corrections automatiques`n- Mises à jour système`n- Optimisations`n- Réparations critiques"
    "documentation\summaries" = "# Résumés et Rapports`n`nCe dossier contient tous les résumés de corrections et rapports d'implémentation.`n`n## Types de documents`n- Résumés de corrections`n- Rapports d'implémentation`n- Documentation critique`n- Guides d'administration"
    "documentation\guides" = "# Guides et Documentation`n`nCe dossier contient les guides techniques et la documentation.`n`n## Types de guides`n- Guides de déploiement`n- Documentation technique`n- Guides d'optimisation`n- Manuels d'utilisation"
}

foreach ($folder in $indexContent.Keys) {
    $indexPath = Join-Path $ProjectRoot "$folder\README.md"
    $content = $indexContent[$folder]
    
    if (-not (Test-Path $indexPath)) {
        Set-Content -Path $indexPath -Value $content -Encoding UTF8
        Write-Host "  📋 Créé: $folder\README.md" -ForegroundColor Green
    }
}

# Générer un rapport final
Write-Host "`n📊 Génération du rapport final..." -ForegroundColor Yellow

$reportContent = @"
# Rapport d'Organisation du Projet
Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

## Résumé
- **Fichiers déplacés**: $totalMoved
- **Fichiers nettoyés**: $cleanedCount
- **Dossiers créés**: $($FoldersToCreate.Count)

## Structure organisée

### Scripts
- scripts/tests/ - Fichiers de test et validation
- scripts/debug/ - Outils de diagnostic
- scripts/setup/ - Scripts d'installation
- scripts/maintenance/ - Scripts de maintenance

### Documentation
- documentation/summaries/ - Résumés et rapports
- documentation/guides/ - Guides techniques

### Temporaire
- temp/backup/ - Sauvegardes temporaires
- temp/logs/ - Fichiers de logs

## Fichiers principaux conservés à la racine
- index.html - Page d'accueil
- landing-page-template.php - Template principal
- router.php - Routeur principal
- config/ - Configuration
- css/, js/, php/ - Ressources principales
- admin/ - Interface d'administration (nettoyée)

## Prochaines étapes recommandées
1. Vérifier que tous les liens fonctionnent
2. Mettre à jour les chemins dans les fichiers de configuration
3. Tester l'application après réorganisation
4. Archiver les anciens fichiers de test si nécessaire

---
*Rapport généré automatiquement par le script d'organisation*
"@

$reportPath = Join-Path $ProjectRoot "ORGANIZATION_REPORT.md"
Set-Content -Path $reportPath -Value $reportContent -Encoding UTF8

# Afficher le résumé final
Write-Host "`n" -NoNewline
Write-Host "🎉 ORGANISATION TERMINÉE !" -ForegroundColor Green -BackgroundColor Black
Write-Host "`n📊 Résumé:" -ForegroundColor Cyan
Write-Host "  📦 Fichiers déplacés: $totalMoved" -ForegroundColor White
Write-Host "  🗑️ Fichiers nettoyés: $cleanedCount" -ForegroundColor White
Write-Host "  📁 Dossiers créés: $($FoldersToCreate.Count)" -ForegroundColor White
Write-Host "  📋 Rapport généré: ORGANIZATION_REPORT.md" -ForegroundColor White

Write-Host "`n✅ Le projet est maintenant organisé et nettoyé !" -ForegroundColor Green
Write-Host "`n📝 Prochaines étapes:" -ForegroundColor Yellow
Write-Host "  1. Vérifier les liens et chemins" -ForegroundColor White
Write-Host "  2. Tester l'application" -ForegroundColor White
Write-Host "  3. Consulter le rapport: ORGANIZATION_REPORT.md" -ForegroundColor White

Write-Host "`n🚀 Projet prêt pour la production !" -ForegroundColor Green