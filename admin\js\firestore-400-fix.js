/**
 * Firestore 400 Error Fix
 * Résolution des erreurs Firestore 400 (Bad Request) et des problèmes de mode hors ligne
 * 
 * Erreurs ciblées :
 * - GET .../Listen/channel?gsessionid=... 400 (Bad Request)
 * - FirebaseError: [code=unavailable] Connection failed
 * - Failed to get document because client is offline
 */

import { getAuth, signOut, signInAnonymously } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
import { getFirestore, getDoc, getDocFromCache, enableIndexedDbPersistence, connectFirestoreEmulator } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
import { onLog } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";

// Configuration pour la gestion des erreurs Firestore
const FIRESTORE_CONFIG = {
    MAX_RETRIES: 3,
    RETRY_DELAY_BASE: 1000, // 1 seconde
    SESSION_RESET_THRESHOLD: 5, // Nombre d'erreurs 400 avant reset de session
    OFFLINE_CACHE_ENABLED: true,
    FORCE_LONG_POLLING: true
};

// Compteur d'erreurs pour déclencher le reset de session
let error400Count = 0;
let lastSessionReset = 0;
const SESSION_RESET_COOLDOWN = 60000; // 1 minute

/**
 * Gestionnaire principal des erreurs Firestore 400
 */
class Firestore400ErrorHandler {
    constructor() {
        this.isOnline = navigator.onLine;
        this.pendingOperations = new Map();
        this.initializeErrorHandling();
        this.setupNetworkMonitoring();
        this.enableOfflineSupport();
    }

    /**
     * Initialise la gestion globale des erreurs
     */
    initializeErrorHandling() {
        // Intercepter les erreurs de réseau Firestore
        this.interceptFirestoreRequests();
        
        // Gérer les erreurs non capturées
        window.addEventListener('unhandledrejection', (event) => {
            this.handleUnhandledRejection(event);
        });

        // Monitoring des logs Firebase
        try {
            onLog((log) => {
                if (log.level === 'error' && log.message.includes('400')) {
                    this.handle400Error(log);
                }
            });
        } catch (error) {
            console.debug('Firebase logging not available:', error.message);
        }

        console.log('🔧 Firestore 400 Error Handler initialisé');
    }

    /**
     * Intercepte les requêtes Firestore pour gérer les erreurs 400
     */
    interceptFirestoreRequests() {
        const originalFetch = window.fetch;
        
        window.fetch = async (...args) => {
            const [url, options] = args;
            
            // Vérifier si c'est une requête Firestore
            if (typeof url === 'string' && url.includes('firestore.googleapis.com')) {
                try {
                    const response = await originalFetch.apply(window, args);
                    
                    // Gérer les erreurs 400
                    if (response.status === 400) {
                        console.warn('🚨 Firestore 400 Error détectée:', url);
                        await this.handle400Response(response, url);
                        
                        // Retourner une réponse factice pour éviter les erreurs en cascade
                        return new Response('{}', { status: 200 });
                    }
                    
                    // Reset du compteur d'erreurs en cas de succès
                    if (response.ok) {
                        error400Count = 0;
                    }
                    
                    return response;
                } catch (error) {
                    return this.handleFetchError(error, url, args);
                }
            }
            
            return originalFetch.apply(window, args);
        };
    }

    /**
     * Gère les erreurs 400 spécifiques
     */
    async handle400Error(log) {
        error400Count++;
        console.warn(`🚨 Erreur Firestore 400 #${error400Count}:`, log.message);
        
        // Déclencher un reset de session si nécessaire
        if (error400Count >= FIRESTORE_CONFIG.SESSION_RESET_THRESHOLD) {
            await this.resetFirebaseSession();
        }
    }

    /**
     * Gère les réponses 400
     */
    async handle400Response(response, url) {
        try {
            const responseText = await response.text();
            console.warn('🔍 Détails erreur 400:', {
                url,
                status: response.status,
                statusText: response.statusText,
                response: responseText
            });
            
            // Analyser la cause de l'erreur
            if (responseText.includes('Invalid session ID') || responseText.includes('Token expired')) {
                await this.resetFirebaseSession();
            }
        } catch (error) {
            console.debug('Impossible de lire la réponse 400:', error.message);
        }
    }

    /**
     * Reset de la session Firebase
     */
    async resetFirebaseSession() {
        const now = Date.now();
        
        // Vérifier le cooldown
        if (now - lastSessionReset < SESSION_RESET_COOLDOWN) {
            console.debug('⏳ Reset de session en cooldown');
            return;
        }
        
        try {
            console.log('🔄 Reset de la session Firebase...');
            
            const auth = getAuth();
            
            // Déconnecter l'utilisateur actuel
            await signOut(auth);
            
            // Attendre un peu
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Reconnecter anonymement pour obtenir une nouvelle session
            await signInAnonymously(auth);
            
            // Reset des compteurs
            error400Count = 0;
            lastSessionReset = now;
            
            console.log('✅ Session Firebase réinitialisée');
            
            // Notifier l'application
            if (typeof window.onFirebaseSessionReset === 'function') {
                window.onFirebaseSessionReset();
            }
        } catch (error) {
            console.error('❌ Erreur lors du reset de session:', error);
        }
    }

    /**
     * Gère les erreurs de fetch
     */
    async handleFetchError(error, url, args) {
        if (url.includes('firestore.googleapis.com')) {
            console.debug('🔄 Erreur Firestore, tentative de retry:', error.message);
            
            // Retry avec backoff exponentiel
            return this.retryWithBackoff(() => fetch(...args), FIRESTORE_CONFIG.MAX_RETRIES);
        }
        
        throw error;
    }

    /**
     * Retry avec backoff exponentiel
     */
    async retryWithBackoff(operation, maxRetries, attempt = 1) {
        try {
            return await operation();
        } catch (error) {
            if (attempt >= maxRetries) {
                console.warn(`❌ Échec après ${maxRetries} tentatives:`, error.message);
                throw error;
            }
            
            const delay = FIRESTORE_CONFIG.RETRY_DELAY_BASE * Math.pow(2, attempt - 1);
            console.debug(`⏳ Retry ${attempt}/${maxRetries} dans ${delay}ms`);
            
            await new Promise(resolve => setTimeout(resolve, delay));
            return this.retryWithBackoff(operation, maxRetries, attempt + 1);
        }
    }

    /**
     * Gère les rejections non capturées
     */
    handleUnhandledRejection(event) {
        const error = event.reason;
        
        if (error && error.code === 'unavailable') {
            console.debug('🔄 Firestore unavailable, utilisation du cache');
            event.preventDefault();
            return;
        }
        
        if (error && error.message && error.message.includes('Failed to get document because client is offline')) {
            console.debug('📱 Client hors ligne, utilisation du cache');
            event.preventDefault();
            return;
        }
    }

    /**
     * Active le support hors ligne
     */
    async enableOfflineSupport() {
        if (!FIRESTORE_CONFIG.OFFLINE_CACHE_ENABLED) return;
        
        try {
            const db = getFirestore();
            await enableIndexedDbPersistence(db);
            console.log('✅ Support hors ligne Firestore activé');
        } catch (error) {
            if (error.code === 'failed-precondition') {
                console.debug('⚠️ Persistence déjà activée dans un autre onglet');
            } else if (error.code === 'unimplemented') {
                console.debug('⚠️ Persistence non supportée par ce navigateur');
            } else {
                console.warn('⚠️ Erreur activation persistence:', error);
            }
        }
    }

    /**
     * Surveille l'état du réseau
     */
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            console.log('🌐 Connexion réseau rétablie');
            this.isOnline = true;
            this.processPendingOperations();
        });
        
        window.addEventListener('offline', () => {
            console.log('📱 Connexion réseau perdue');
            this.isOnline = false;
        });
    }

    /**
     * Traite les opérations en attente
     */
    async processPendingOperations() {
        if (this.pendingOperations.size === 0) return;
        
        console.log(`🔄 Traitement de ${this.pendingOperations.size} opérations en attente`);
        
        for (const [id, operation] of this.pendingOperations) {
            try {
                await operation();
                this.pendingOperations.delete(id);
            } catch (error) {
                console.warn(`❌ Échec opération ${id}:`, error.message);
            }
        }
    }

    /**
     * Charge un document avec fallback cache
     */
    async getDocumentWithFallback(docRef) {
        try {
            // Essayer de récupérer le document en ligne
            const doc = await getDoc(docRef);
            return doc;
        } catch (error) {
            if (error.code === 'unavailable' || !this.isOnline) {
                console.debug('🔄 Utilisation du cache pour le document');
                try {
                    const cachedDoc = await getDocFromCache(docRef);
                    return cachedDoc;
                } catch (cacheError) {
                    console.warn('❌ Document non disponible en cache:', cacheError.message);
                    return null;
                }
            }
            throw error;
        }
    }
}

// Initialisation automatique
let firestoreErrorHandler;

// Initialiser quand le DOM est prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        firestoreErrorHandler = new Firestore400ErrorHandler();
    });
} else {
    firestoreErrorHandler = new Firestore400ErrorHandler();
}

// Export pour utilisation globale
window.Firestore400ErrorHandler = Firestore400ErrorHandler;
window.firestoreErrorHandler = firestoreErrorHandler;

// Fonction utilitaire pour charger un document avec retry
window.getDocumentWithRetry = async (docRef, maxRetries = FIRESTORE_CONFIG.MAX_RETRIES) => {
    if (firestoreErrorHandler) {
        return firestoreErrorHandler.getDocumentWithFallback(docRef);
    }
    
    // Fallback si le handler n'est pas initialisé
    return getDoc(docRef);
};

console.log('🔧 Firestore 400 Error Fix chargé');