// Settings Core Module
const settingsCore = (() => {
    // Private variables
    let isLoading = false;
    let hasUnsavedChanges = false;
    let validationRules = {};

    // Loading state management
    const showLoadingOverlay = () => {
        isLoading = true;
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>جاري التحميل...</p>
            </div>
        `;
        document.body.appendChild(overlay);
    };

    const hideLoadingOverlay = () => {
        isLoading = false;
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    };

    // Form change tracking
    const initializeChangeTracking = (form) => {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                hasUnsavedChanges = true;
                updateSaveButton();
            });
        });
    };

    // Save button state management
    const updateSaveButton = () => {
        const saveButton = document.querySelector('button[type="submit"]');
        if (saveButton) {
            saveButton.disabled = !hasUnsavedChanges;
        }
    };

    // Navigation warning
    const initializeNavigationWarning = () => {
        window.addEventListener('beforeunload', (e) => {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    };

    // Save settings to API
    const saveToAPI = async (endpoint, data, options = {}) => {
        const {
            successMessage = 'تم الحفظ بنجاح',
            errorMessage = 'فشل الحفظ',
            loadingMessage = 'جاري الحفظ...'
        } = options;

        try {
            showLoadingOverlay();
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            showNotification('success', successMessage);
            hasUnsavedChanges = false;
            updateSaveButton();
            return result;

        } catch (error) {
            console.error('API Error:', error);
            showNotification('error', errorMessage);
            throw error;
        } finally {
            hideLoadingOverlay();
        }
    };

    // Validation
    const validateSettings = (data, rules) => {
        const errors = [];

        for (const [path, rule] of Object.entries(rules)) {
            const value = getNestedValue(data, path);

            // Required field validation
            if (rule.required && !value) {
                errors.push(`${rule.label} مطلوب`);
                continue;
            }

            // Skip further validation if value is empty and not required
            if (!value && !rule.required) continue;

            // Type validation
            switch (rule.type) {
                case 'email':
                    if (!isValidEmail(value)) {
                        errors.push(`${rule.label} غير صالح`);
                    }
                    break;

                case 'number':
                    const num = parseFloat(value);
                    if (isNaN(num)) {
                        errors.push(`${rule.label} يجب أن يكون رقماً`);
                    } else {
                        if (rule.min !== undefined && num < rule.min) {
                            errors.push(`${rule.label} يجب أن يكون ${rule.min} أو أكثر`);
                        }
                        if (rule.max !== undefined && num > rule.max) {
                            errors.push(`${rule.label} يجب أن يكون ${rule.max} أو أقل`);
                        }
                    }
                    break;

                case 'url':
                    if (!isValidURL(value)) {
                        errors.push(`${rule.label} غير صالح`);
                    }
                    break;

                case 'pattern':
                    if (rule.pattern && !rule.pattern.test(value)) {
                        errors.push(`${rule.label} غير صالح`);
                    }
                    break;
            }
        }

        return errors;
    };

    // Helper functions
    const getNestedValue = (obj, path) => {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    };

    const isValidEmail = (email) => {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    };

    const isValidURL = (url) => {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    };

    const showNotification = (type, message) => {
        if (window.NotificationManager) {
            NotificationManager.show(type, message);
        } else {
            alert(message);
        }
    };

    // Initialize
const initialize = (formId, rules = {}) => {
    const form = document.getElementById(formId);
    if (form) {
        validationRules = rules;
        initializeChangeTracking(form);
        updateSaveButton();
        initializeNavigationWarning();
        initializeTooltips();
    }
};

// Initialize tooltips
const initializeTooltips = () => {
    const tooltips = document.querySelectorAll('.tooltip-icon');
    tooltips.forEach(tooltip => {
        const tooltipText = tooltip.getAttribute('data-tooltip');
        if (tooltipText) {
            tooltip.addEventListener('mouseenter', (e) => {
                const tip = document.createElement('div');
                tip.className = 'tooltip-text';
                tip.textContent = tooltipText;
                tooltip.appendChild(tip);
            });

            tooltip.addEventListener('mouseleave', () => {
                const tip = tooltip.querySelector('.tooltip-text');
                if (tip) tip.remove();
            });
        }
    });
};

    // Public API
return {
    initialize,
    saveToAPI,
    validateSettings,
    showNotification,
    showLoadingOverlay,
    hideLoadingOverlay,
    initializeTooltips
};
})();

// Initialize on DOM load
document.addEventListener('DOMContentLoaded', settingsCore.initialize);
