/**
 * Payment Settings Management JavaScript
 * إدارة إعدادات الدفع
 */

const PaymentManager = {
    // State
    state: {
        methods: [],
        currentPage: 1,
        totalPages: 1,
        isLoading: false,
        selectedMethod: null,
        testResults: null
    },

    // Configuration
    config: {
        itemsPerPage: 20,
        apiEndpoint: '../../api/system/payment-settings.php'
    },

    // Payment method types with Arabic names
    paymentTypes: {
        'credit_card': 'بطاقة ائتمان',
        'bank_transfer': 'تحويل بنكي',
        'cash_on_delivery': 'الدفع عند الاستلام',
        'digital_wallet': 'محفظة رقمية',
        'cryptocurrency': 'عملة رقمية'
    },

    // Initialize
    async init() {
        console.log('💳 Initializing Payment Manager...');
        
        try {
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial data
            await this.loadPaymentMethods();
            
            // Load stats
            await this.loadStats();
            
            // Setup form validation
            this.setupFormValidation();
            
            console.log('✅ Payment Manager initialized');
            SystemSettings.state.currentSection = 'Payment';
            
        } catch (error) {
            console.error('❌ Failed to initialize Payment Manager:', error);
            SystemSettings.showNotification('فشل في تحميل إدارة الدفع: ' + error.message, 'error');
        }
    },

    // Setup Event Listeners
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('paymentSearch');
        if (searchInput) {
            searchInput.addEventListener('input', SystemSettings.debounce(() => {
                this.state.currentPage = 1;
                this.loadPaymentMethods();
            }, 300));
        }

        // Type filter
        const typeFilter = document.getElementById('typeFilter');
        if (typeFilter) {
            typeFilter.addEventListener('change', () => {
                this.state.currentPage = 1;
                this.loadPaymentMethods();
            });
        }

        // Status filter
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.state.currentPage = 1;
                this.loadPaymentMethods();
            });
        }

        // Form submission
        const paymentForm = document.getElementById('paymentForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.savePaymentMethod();
            });
        }

        // Type change in form
        const typeSelect = document.getElementById('type');
        if (typeSelect) {
            typeSelect.addEventListener('change', (e) => {
                this.updateConfigFields(e.target.value);
            });
        }

        // Modal close
        const modal = document.getElementById('paymentModal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal();
                }
            });
        }
    },

    // Load Payment Methods
    async loadPaymentMethods() {
        if (this.state.isLoading) return;
        
        this.state.isLoading = true;
        SystemSettings.showLoading('.payment-container', 'تحميل طرق الدفع...');

        try {
            const params = new URLSearchParams({
                page: this.state.currentPage,
                limit: this.config.itemsPerPage,
                search: document.getElementById('paymentSearch')?.value || '',
                type: document.getElementById('typeFilter')?.value || '',
                status: document.getElementById('statusFilter')?.value || ''
            });

            const response = await fetch(`${this.config.apiEndpoint}?${params}`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'فشل في تحميل طرق الدفع');
            }

            this.state.methods = data.data;
            this.state.totalPages = data.pagination.total_pages;

            this.renderPaymentMethods();
            this.renderPagination(data.pagination);

        } catch (error) {
            console.error('Error loading payment methods:', error);
            SystemSettings.showNotification('فشل في تحميل طرق الدفع: ' + error.message, 'error');
        } finally {
            this.state.isLoading = false;
            SystemSettings.hideLoading('.payment-container');
        }
    },

    // Render Payment Methods
    renderPaymentMethods() {
        const container = document.getElementById('paymentMethodsContainer');
        if (!container) return;

        if (this.state.methods.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-credit-card"></i>
                    <h3>لا توجد طرق دفع</h3>
                    <p>لم يتم العثور على أي طرق دفع</p>
                    <button class="btn btn-primary" onclick="PaymentManager.showAddModal()">
                        <i class="fas fa-plus"></i>
                        إضافة طريقة دفع
                    </button>
                </div>
            `;
            return;
        }

        const html = `
            <div class="payment-methods-grid">
                ${this.state.methods.map(method => this.renderPaymentCard(method)).join('')}
            </div>
        `;

        container.innerHTML = html;
    },

    // Render Payment Card
    renderPaymentCard(method) {
        const statusClass = method.is_active ? 'status-active' : 'status-inactive';
        const statusText = method.is_active ? 'نشط' : 'غير نشط';
        const typeText = this.paymentTypes[method.type] || method.type;

        return `
            <div class="payment-card" data-id="${method.id}">
                <div class="payment-header">
                    <div class="payment-icon ${method.type}">
                        <i class="${this.getPaymentIcon(method.type)}"></i>
                    </div>
                    <div class="payment-info">
                        <h4>${method.name_ar}</h4>
                        <p>${method.name}</p>
                        <small>${typeText}</small>
                        ${method.provider ? `<small class="provider">مقدم الخدمة: ${method.provider}</small>` : ''}
                    </div>
                    <div class="payment-status">
                        <span class="status-indicator ${statusClass}">${statusText}</span>
                    </div>
                </div>
                
                <div class="payment-body">
                    <div class="payment-fees">
                        <div class="fee-item">
                            <span class="fee-label">رسوم نسبية:</span>
                            <span class="fee-value">${method.fees_percentage}%</span>
                        </div>
                        <div class="fee-item">
                            <span class="fee-label">رسوم ثابتة:</span>
                            <span class="fee-value">${method.fees_fixed} دج</span>
                        </div>
                    </div>
                    
                    <div class="payment-limits">
                        <div class="limit-item">
                            <span class="limit-label">الحد الأدنى:</span>
                            <span class="limit-value">${method.min_amount} دج</span>
                        </div>
                        ${method.max_amount ? `
                            <div class="limit-item">
                                <span class="limit-label">الحد الأقصى:</span>
                                <span class="limit-value">${method.max_amount} دج</span>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="payment-currencies">
                        <span class="currencies-label">العملات المدعومة:</span>
                        <div class="currencies-list">
                            ${method.supported_currencies.map(currency => 
                                `<span class="currency-tag">${currency}</span>`
                            ).join('')}
                        </div>
                    </div>
                </div>
                
                <div class="payment-actions">
                    <button class="btn btn-sm btn-primary" onclick="PaymentManager.editPaymentMethod(${method.id})">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-success" onclick="PaymentManager.testPaymentMethod(${method.id})">
                        <i class="fas fa-vial"></i>
                        اختبار
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="PaymentManager.toggleStatus(${method.id}, ${!method.is_active})">
                        <i class="fas fa-${method.is_active ? 'pause' : 'play'}"></i>
                        ${method.is_active ? 'إيقاف' : 'تفعيل'}
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="PaymentManager.deletePaymentMethod(${method.id})">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            </div>
        `;
    },

    // Get Payment Icon
    getPaymentIcon(type) {
        const icons = {
            'credit_card': 'fas fa-credit-card',
            'bank_transfer': 'fas fa-university',
            'cash_on_delivery': 'fas fa-money-bill-wave',
            'digital_wallet': 'fas fa-wallet',
            'cryptocurrency': 'fab fa-bitcoin'
        };
        return icons[type] || 'fas fa-credit-card';
    },

    // Show Add Modal
    showAddModal() {
        this.state.selectedMethod = null;
        this.showModal('إضافة طريقة دفع جديدة');
        this.resetForm();
        this.updateConfigFields('credit_card'); // Default type
    },

    // Edit Payment Method
    async editPaymentMethod(id) {
        try {
            SystemSettings.showLoading('.modal-content', 'تحميل بيانات طريقة الدفع...');
            
            const response = await fetch(`${this.config.apiEndpoint}?id=${id}`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'فشل في تحميل بيانات طريقة الدفع');
            }

            this.state.selectedMethod = data.data;
            this.showModal('تعديل طريقة الدفع');
            this.populateForm(data.data);
            this.updateConfigFields(data.data.type);

        } catch (error) {
            console.error('Error loading payment method:', error);
            SystemSettings.showNotification('فشل في تحميل بيانات طريقة الدفع: ' + error.message, 'error');
        } finally {
            SystemSettings.hideLoading('.modal-content');
        }
    },

    // Show Modal
    showModal(title) {
        const modal = document.getElementById('paymentModal');
        const modalTitle = document.getElementById('modalTitle');
        
        if (modal && modalTitle) {
            modalTitle.textContent = title;
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    },

    // Close Modal
    closeModal() {
        const modal = document.getElementById('paymentModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        this.state.selectedMethod = null;
        this.state.testResults = null;
    },

    // Reset Form
    resetForm() {
        const form = document.getElementById('paymentForm');
        if (form) {
            form.reset();
            
            // Reset to defaults
            document.getElementById('type').value = 'credit_card';
            document.getElementById('feesPercentage').value = '0';
            document.getElementById('feesFixed').value = '0';
            document.getElementById('minAmount').value = '0';
            document.getElementById('supportedCurrencies').value = 'DZD';
        }
    },

    // Populate Form
    populateForm(method) {
        const fields = [
            'name', 'name_ar', 'type', 'provider', 'fees_percentage', 'fees_fixed',
            'min_amount', 'max_amount', 'sort_order'
        ];

        fields.forEach(field => {
            const element = document.getElementById(field.replace(/_/g, ''));
            if (element) {
                let value = method[field];
                if (field === 'fees_percentage') value = method.fees_percentage;
                if (field === 'fees_fixed') value = method.fees_fixed;
                if (field === 'min_amount') value = method.min_amount;
                if (field === 'max_amount') value = method.max_amount;
                if (field === 'sort_order') value = method.sort_order;
                
                element.value = value || '';
            }
        });

        // Set supported currencies
        const currenciesElement = document.getElementById('supportedCurrencies');
        if (currenciesElement && method.supported_currencies) {
            currenciesElement.value = method.supported_currencies.join(', ');
        }

        // Set active status
        const isActiveCheckbox = document.getElementById('isActive');
        if (isActiveCheckbox) {
            isActiveCheckbox.checked = method.is_active;
        }

        // Set config fields
        if (method.config) {
            this.populateConfigFields(method.config);
        }
    },

    // Update Config Fields
    updateConfigFields(type) {
        const configContainer = document.getElementById('configFields');
        if (!configContainer) return;

        let configHTML = '';

        switch (type) {
            case 'credit_card':
                configHTML = `
                    <div class="form-group">
                        <label class="form-label" for="apiKey">مفتاح API</label>
                        <input type="text" id="apiKey" name="config[api_key]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="secretKey">المفتاح السري</label>
                        <input type="password" id="secretKey" name="config[secret_key]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="endpoint">نقطة النهاية</label>
                        <input type="url" id="endpoint" name="config[endpoint]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="testMode">وضع الاختبار</label>
                        <select id="testMode" name="config[test_mode]" class="form-control">
                            <option value="true">مفعل</option>
                            <option value="false">معطل</option>
                        </select>
                    </div>
                `;
                break;

            case 'bank_transfer':
                configHTML = `
                    <div class="form-group">
                        <label class="form-label" for="bankName">اسم البنك</label>
                        <input type="text" id="bankName" name="config[bank_name]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="accountNumber">رقم الحساب</label>
                        <input type="text" id="accountNumber" name="config[account_number]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="iban">IBAN</label>
                        <input type="text" id="iban" name="config[iban]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="swiftCode">رمز SWIFT</label>
                        <input type="text" id="swiftCode" name="config[swift_code]" class="form-control">
                    </div>
                `;
                break;

            case 'digital_wallet':
                configHTML = `
                    <div class="form-group">
                        <label class="form-label" for="walletId">معرف المحفظة</label>
                        <input type="text" id="walletId" name="config[wallet_id]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="apiKey">مفتاح API</label>
                        <input type="text" id="apiKey" name="config[api_key]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="webhookUrl">رابط Webhook</label>
                        <input type="url" id="webhookUrl" name="config[webhook_url]" class="form-control">
                    </div>
                `;
                break;

            case 'cryptocurrency':
                configHTML = `
                    <div class="form-group">
                        <label class="form-label" for="walletAddress">عنوان المحفظة</label>
                        <input type="text" id="walletAddress" name="config[wallet_address]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="network">الشبكة</label>
                        <select id="network" name="config[network]" class="form-control">
                            <option value="bitcoin">Bitcoin</option>
                            <option value="ethereum">Ethereum</option>
                            <option value="litecoin">Litecoin</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="confirmations">عدد التأكيدات المطلوبة</label>
                        <input type="number" id="confirmations" name="config[confirmations]" class="form-control" value="3" min="1">
                    </div>
                `;
                break;

            case 'cash_on_delivery':
                configHTML = `
                    <div class="form-group">
                        <label class="form-label" for="deliveryFee">رسوم التوصيل</label>
                        <input type="number" id="deliveryFee" name="config[delivery_fee]" class="form-control" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="maxDistance">أقصى مسافة توصيل (كم)</label>
                        <input type="number" id="maxDistance" name="config[max_distance]" class="form-control" min="1">
                    </div>
                `;
                break;
        }

        configContainer.innerHTML = configHTML;
    },

    // Populate Config Fields
    populateConfigFields(config) {
        Object.keys(config).forEach(key => {
            const element = document.querySelector(`[name="config[${key}]"]`);
            if (element) {
                element.value = config[key];
            }
        });
    },

    // Setup Form Validation
    setupFormValidation() {
        const form = document.getElementById('paymentForm');
        if (!form) return;

        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });
    },

    // Validate Field
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'هذا الحقل مطلوب';
        }

        // Email validation
        if (field.type === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            isValid = false;
            message = 'يرجى إدخال بريد إلكتروني صحيح';
        }

        // URL validation
        if (field.type === 'url' && value && !/^https?:\/\/.+/.test(value)) {
            isValid = false;
            message = 'يرجى إدخال رابط صحيح';
        }

        // Number validation
        if (field.type === 'number' && value) {
            const num = parseFloat(value);
            const min = parseFloat(field.min);
            const max = parseFloat(field.max);
            
            if (isNaN(num)) {
                isValid = false;
                message = 'يرجى إدخال رقم صحيح';
            } else if (!isNaN(min) && num < min) {
                isValid = false;
                message = `القيمة يجب أن تكون أكبر من أو تساوي ${min}`;
            } else if (!isNaN(max) && num > max) {
                isValid = false;
                message = `القيمة يجب أن تكون أصغر من أو تساوي ${max}`;
            }
        }

        // Update field appearance
        field.classList.toggle('is-invalid', !isValid);
        field.classList.toggle('is-valid', isValid && value);

        // Show/hide error message
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!isValid && message) {
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'field-error';
                field.parentNode.appendChild(errorElement);
            }
            errorElement.textContent = message;
        } else if (errorElement) {
            errorElement.remove();
        }

        return isValid;
    },

    // Save Payment Method
    async savePaymentMethod() {
        const form = document.getElementById('paymentForm');
        if (!form) return;

        // Validate all fields
        const inputs = form.querySelectorAll('input, select, textarea');
        let isFormValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            SystemSettings.showNotification('يرجى تصحيح الأخطاء في النموذج', 'error');
            return;
        }

        const formData = new FormData(form);
        const data = {};
        
        // Process form data
        for (let [key, value] of formData.entries()) {
            if (key.startsWith('config[')) {
                if (!data.config) data.config = {};
                const configKey = key.match(/config\[(.+)\]/)[1];
                data.config[configKey] = value;
            } else {
                data[key] = value;
            }
        }
        
        // Convert checkbox
        data.is_active = document.getElementById('isActive').checked;
        
        // Convert numeric fields
        data.fees_percentage = parseFloat(data.feesPercentage) || 0;
        data.fees_fixed = parseFloat(data.feesFixed) || 0;
        data.min_amount = parseFloat(data.minAmount) || 0;
        data.max_amount = data.maxAmount ? parseFloat(data.maxAmount) : null;
        data.sort_order = parseInt(data.sortOrder) || 0;
        
        // Process supported currencies
        data.supported_currencies = data.supportedCurrencies
            .split(',')
            .map(c => c.trim())
            .filter(c => c);

        try {
            SystemSettings.showLoading('.modal-content', 'حفظ طريقة الدفع...');

            const url = this.config.apiEndpoint;
            const method = this.state.selectedMethod ? 'PUT' : 'POST';
            
            if (this.state.selectedMethod) {
                data.id = this.state.selectedMethod.id;
            }

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في حفظ طريقة الدفع');
            }

            SystemSettings.showNotification(
                this.state.selectedMethod ? 'تم تحديث طريقة الدفع بنجاح' : 'تم إضافة طريقة الدفع بنجاح',
                'success'
            );

            this.closeModal();
            await this.loadPaymentMethods();
            await this.loadStats();

        } catch (error) {
            console.error('Error saving payment method:', error);
            SystemSettings.showNotification('فشل في حفظ طريقة الدفع: ' + error.message, 'error');
        } finally {
            SystemSettings.hideLoading('.modal-content');
        }
    },

    // Test Payment Method
    async testPaymentMethod(id) {
        try {
            SystemSettings.showNotification('جاري اختبار طريقة الدفع...', 'info');
            
            const response = await fetch(`${this.config.apiEndpoint}?action=test&method_id=${id}`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'فشل في اختبار طريقة الدفع');
            }

            this.state.testResults = data.data;
            this.showTestResults();

        } catch (error) {
            console.error('Error testing payment method:', error);
            SystemSettings.showNotification('فشل في اختبار طريقة الدفع: ' + error.message, 'error');
        }
    },

    // Show Test Results
    showTestResults() {
        if (!this.state.testResults) return;

        const results = this.state.testResults;
        const status = results.test_results.overall.status;
        const statusText = status === 'passed' ? 'نجح' : 'فشل';
        const statusClass = status === 'passed' ? 'success' : 'error';

        let html = `
            <div class="test-results-modal">
                <div class="test-header">
                    <h3>نتائج اختبار ${results.method}</h3>
                    <div class="test-status ${statusClass}">
                        ${statusText} (${results.test_results.overall.percentage}%)
                    </div>
                </div>
                
                <div class="test-details">
                    <div class="test-section">
                        <h4>التحقق الأساسي</h4>
                        ${Object.entries(results.test_results.basic_validation).map(([key, value]) => `
                            <div class="test-item">
                                <span>${this.getTestLabel(key)}</span>
                                <span class="test-result ${value ? 'passed' : 'failed'}">
                                    ${value ? '✓' : '✗'}
                                </span>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="test-section">
                        <h4>تحقق الإعدادات</h4>
                        ${Object.entries(results.test_results.config_validation).map(([key, value]) => `
                            <div class="test-item">
                                <span>${this.getTestLabel(key)}</span>
                                <span class="test-result ${value ? 'passed' : 'failed'}">
                                    ${value ? '✓' : '✗'}
                                </span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div class="test-actions">
                    <button class="btn btn-secondary" onclick="PaymentManager.closeTestResults()">
                        إغلاق
                    </button>
                </div>
            </div>
        `;

        // Show in modal or notification
        SystemSettings.showNotification(
            `اختبار ${results.method}: ${statusText} (${results.test_results.overall.passed}/${results.test_results.overall.total})`,
            statusClass
        );
    },

    // Get Test Label
    getTestLabel(key) {
        const labels = {
            'name_exists': 'وجود الاسم',
            'type_valid': 'نوع صحيح',
            'currencies_configured': 'العملات مكونة',
            'api_key_exists': 'وجود مفتاح API',
            'secret_key_exists': 'وجود المفتاح السري',
            'endpoint_configured': 'نقطة النهاية مكونة',
            'bank_name_exists': 'وجود اسم البنك',
            'account_number_exists': 'وجود رقم الحساب',
            'iban_exists': 'وجود IBAN',
            'wallet_id_exists': 'وجود معرف المحفظة',
            'basic_config': 'الإعدادات الأساسية'
        };
        return labels[key] || key;
    },

    // Toggle Status
    async toggleStatus(id, newStatus) {
        try {
            const response = await fetch(this.config.apiEndpoint, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: id,
                    is_active: newStatus
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في تغيير حالة طريقة الدفع');
            }

            SystemSettings.showNotification(
                newStatus ? 'تم تفعيل طريقة الدفع' : 'تم إيقاف طريقة الدفع',
                'success'
            );

            await this.loadPaymentMethods();

        } catch (error) {
            console.error('Error toggling payment method status:', error);
            SystemSettings.showNotification('فشل في تغيير حالة طريقة الدفع: ' + error.message, 'error');
        }
    },

    // Delete Payment Method
    async deletePaymentMethod(id) {
        if (!confirm('هل أنت متأكد من حذف طريقة الدفع هذه؟\nقد يؤثر هذا على الطلبات الموجودة.')) {
            return;
        }

        try {
            const response = await fetch(`${this.config.apiEndpoint}?id=${id}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في حذف طريقة الدفع');
            }

            SystemSettings.showNotification('تم حذف طريقة الدفع بنجاح', 'success');
            await this.loadPaymentMethods();
            await this.loadStats();

        } catch (error) {
            console.error('Error deleting payment method:', error);
            SystemSettings.showNotification('فشل في حذف طريقة الدفع: ' + error.message, 'error');
        }
    },

    // Load Stats
    async loadStats() {
        try {
            const response = await fetch(`${this.config.apiEndpoint}?action=stats`);
            const data = await response.json();

            if (data.success) {
                this.updateStats(data.data);
            }

        } catch (error) {
            console.warn('Failed to load payment stats:', error);
        }
    },

    // Update Stats
    updateStats(stats) {
        const elements = {
            totalMethods: document.getElementById('totalMethods'),
            activeMethods: document.getElementById('activeMethods'),
            avgPercentageFee: document.getElementById('avgPercentageFee'),
            avgFixedFee: document.getElementById('avgFixedFee')
        };

        if (elements.totalMethods) elements.totalMethods.textContent = stats.total || 0;
        if (elements.activeMethods) elements.activeMethods.textContent = stats.active || 0;
        if (elements.avgPercentageFee) elements.avgPercentageFee.textContent = (stats.average_fees?.percentage || 0) + '%';
        if (elements.avgFixedFee) elements.avgFixedFee.textContent = (stats.average_fees?.fixed || 0) + ' دج';
    },

    // Render Pagination
    renderPagination(pagination) {
        const container = document.getElementById('paginationContainer');
        if (!container) return;

        if (pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = `
            <div class="pagination">
                <button class="btn btn-sm btn-secondary" 
                        onclick="PaymentManager.goToPage(${pagination.current_page - 1})"
                        ${pagination.current_page === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                    السابق
                </button>
                
                <span class="pagination-info">
                    صفحة ${pagination.current_page} من ${pagination.total_pages}
                </span>
                
                <button class="btn btn-sm btn-secondary"
                        onclick="PaymentManager.goToPage(${pagination.current_page + 1})"
                        ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}>
                    التالي
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        `;

        container.innerHTML = html;
    },

    // Go to Page
    goToPage(page) {
        if (page < 1 || page > this.state.totalPages) return;
        
        this.state.currentPage = page;
        this.loadPaymentMethods();
    }
};

// Global save function for SystemSettings
window.savePayment = async function(isAutoSave = false) {
    // Payment settings don't have a global save, but we can refresh data
    if (!isAutoSave) {
        await PaymentManager.loadPaymentMethods();
        SystemSettings.showNotification('تم تحديث بيانات طرق الدفع', 'success');
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('payment-settings.html')) {
        PaymentManager.init();
    }
});

// Make globally available
window.PaymentManager = PaymentManager;
