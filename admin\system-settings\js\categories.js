/**
 * Categories Management JavaScript
 * إدارة الفئات
 */

const CategoriesManager = {
    // State
    state: {
        categories: [],
        currentPage: 1,
        totalPages: 1,
        isLoading: false,
        selectedCategory: null,
        viewMode: 'list' // 'list' or 'tree'
    },

    // Configuration
    config: {
        itemsPerPage: 20,
        apiEndpoint: '../../api/system/categories.php'
    },

    // Initialize
    async init() {
        console.log('🏷️ Initializing Categories Manager...');
        
        try {
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial data
            await this.loadCategories();
            
            // Load stats
            await this.loadStats();
            
            console.log('✅ Categories Manager initialized');
            SystemSettings.state.currentSection = 'Categories';
            
        } catch (error) {
            console.error('❌ Failed to initialize Categories Manager:', error);
            SystemSettings.showNotification('فشل في تحميل إدارة الفئات: ' + error.message, 'error');
        }
    },

    // Setup Event Listeners
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('categorySearch');
        if (searchInput) {
            searchInput.addEventListener('input', SystemSettings.debounce(() => {
                this.state.currentPage = 1;
                this.loadCategories();
            }, 300));
        }

        // Status filter
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.state.currentPage = 1;
                this.loadCategories();
            });
        }

        // Parent filter
        const parentFilter = document.getElementById('parentFilter');
        if (parentFilter) {
            parentFilter.addEventListener('change', () => {
                this.state.currentPage = 1;
                this.loadCategories();
            });
        }

        // View mode toggle
        const viewModeButtons = document.querySelectorAll('.view-mode-btn');
        viewModeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mode = e.target.dataset.mode;
                this.setViewMode(mode);
            });
        });

        // Form submission
        const categoryForm = document.getElementById('categoryForm');
        if (categoryForm) {
            categoryForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveCategory();
            });
        }

        // Modal close
        const modal = document.getElementById('categoryModal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal();
                }
            });
        }
    },

    // Load Categories
    async loadCategories() {
        if (this.state.isLoading) return;
        
        this.state.isLoading = true;
        SystemSettings.showLoading('.categories-container', 'تحميل الفئات...');

        try {
            const params = new URLSearchParams({
                page: this.state.currentPage,
                limit: this.config.itemsPerPage,
                search: document.getElementById('categorySearch')?.value || '',
                status: document.getElementById('statusFilter')?.value || '',
                parent_id: document.getElementById('parentFilter')?.value || ''
            });

            const response = await fetch(`${this.config.apiEndpoint}?${params}`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'فشل في تحميل الفئات');
            }

            this.state.categories = data.data;
            this.state.totalPages = data.pagination.total_pages;

            // Render based on view mode
            if (this.state.viewMode === 'tree') {
                await this.loadCategoriesTree();
            } else {
                this.renderCategoriesList();
            }

            this.renderPagination(data.pagination);

        } catch (error) {
            console.error('Error loading categories:', error);
            SystemSettings.showNotification('فشل في تحميل الفئات: ' + error.message, 'error');
        } finally {
            this.state.isLoading = false;
            SystemSettings.hideLoading('.categories-container');
        }
    },

    // Load Categories Tree
    async loadCategoriesTree() {
        try {
            const response = await fetch(`${this.config.apiEndpoint}?action=tree`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'فشل في تحميل شجرة الفئات');
            }

            this.renderCategoriesTree(data.data);

        } catch (error) {
            console.error('Error loading categories tree:', error);
            SystemSettings.showNotification('فشل في تحميل شجرة الفئات: ' + error.message, 'error');
        }
    },

    // Render Categories List
    renderCategoriesList() {
        const container = document.getElementById('categoriesContainer');
        if (!container) return;

        if (this.state.categories.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-tags"></i>
                    <h3>لا توجد فئات</h3>
                    <p>لم يتم العثور على أي فئات</p>
                    <button class="btn btn-primary" onclick="CategoriesManager.showAddModal()">
                        <i class="fas fa-plus"></i>
                        إضافة فئة جديدة
                    </button>
                </div>
            `;
            return;
        }

        const html = `
            <div class="categories-grid">
                ${this.state.categories.map(category => this.renderCategoryCard(category)).join('')}
            </div>
        `;

        container.innerHTML = html;
    },

    // Render Category Card
    renderCategoryCard(category) {
        const statusClass = category.is_active ? 'status-active' : 'status-inactive';
        const statusText = category.is_active ? 'نشط' : 'غير نشط';

        return `
            <div class="category-card" data-id="${category.id}">
                <div class="category-header">
                    <div class="category-icon" style="background: ${category.color}">
                        <i class="${category.icon}"></i>
                    </div>
                    <div class="category-info">
                        <h4>${category.name_ar}</h4>
                        <p>${category.name}</p>
                        ${category.parent_name_ar ? `<small>تحت: ${category.parent_name_ar}</small>` : ''}
                    </div>
                    <div class="category-status">
                        <span class="status-indicator ${statusClass}">${statusText}</span>
                    </div>
                </div>
                
                <div class="category-body">
                    ${category.description_ar ? `<p class="category-description">${category.description_ar}</p>` : ''}
                    
                    <div class="category-stats">
                        <div class="stat-item">
                            <i class="fas fa-layer-group"></i>
                            <span>${category.children_count} فئة فرعية</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-sort"></i>
                            <span>ترتيب: ${category.sort_order}</span>
                        </div>
                    </div>
                </div>
                
                <div class="category-actions">
                    <button class="btn btn-sm btn-primary" onclick="CategoriesManager.editCategory(${category.id})">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-success" onclick="CategoriesManager.addSubcategory(${category.id})">
                        <i class="fas fa-plus"></i>
                        فئة فرعية
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="CategoriesManager.deleteCategory(${category.id})">
                        <i class="fas fa-trash"></i>
                        حذف
                    </button>
                </div>
            </div>
        `;
    },

    // Render Categories Tree
    renderCategoriesTree(categories) {
        const container = document.getElementById('categoriesContainer');
        if (!container) return;

        const html = `
            <div class="categories-tree">
                ${categories.map(category => this.renderTreeNode(category)).join('')}
            </div>
        `;

        container.innerHTML = html;
    },

    // Render Tree Node
    renderTreeNode(category, level = 0) {
        const hasChildren = category.children && category.children.length > 0;
        const statusClass = category.is_active ? 'status-active' : 'status-inactive';
        const indent = level * 20;

        let html = `
            <div class="tree-node" style="margin-right: ${indent}px" data-id="${category.id}">
                <div class="tree-node-content">
                    ${hasChildren ? `
                        <button class="tree-toggle" onclick="CategoriesManager.toggleTreeNode(${category.id})">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    ` : '<span class="tree-spacer"></span>'}
                    
                    <div class="tree-icon" style="background: ${category.color}">
                        <i class="${category.icon}"></i>
                    </div>
                    
                    <div class="tree-info">
                        <span class="tree-name">${category.name_ar}</span>
                        <span class="tree-status ${statusClass}"></span>
                    </div>
                    
                    <div class="tree-actions">
                        <button class="btn btn-xs btn-primary" onclick="CategoriesManager.editCategory(${category.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-xs btn-success" onclick="CategoriesManager.addSubcategory(${category.id})">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="btn btn-xs btn-danger" onclick="CategoriesManager.deleteCategory(${category.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                ${hasChildren ? `
                    <div class="tree-children" id="tree-children-${category.id}">
                        ${category.children.map(child => this.renderTreeNode(child, level + 1)).join('')}
                    </div>
                ` : ''}
            </div>
        `;

        return html;
    },

    // Set View Mode
    setViewMode(mode) {
        this.state.viewMode = mode;
        
        // Update button states
        document.querySelectorAll('.view-mode-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.mode === mode);
        });

        // Reload data
        this.loadCategories();
    },

    // Show Add Modal
    showAddModal() {
        this.state.selectedCategory = null;
        this.showModal('إضافة فئة جديدة');
        this.resetForm();
    },

    // Add Subcategory
    addSubcategory(parentId) {
        this.state.selectedCategory = null;
        this.showModal('إضافة فئة فرعية');
        this.resetForm();
        
        // Set parent
        const parentSelect = document.getElementById('parentId');
        if (parentSelect) {
            parentSelect.value = parentId;
        }
    },

    // Edit Category
    async editCategory(id) {
        try {
            SystemSettings.showLoading('.modal-content', 'تحميل بيانات الفئة...');
            
            const response = await fetch(`${this.config.apiEndpoint}?id=${id}`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'فشل في تحميل بيانات الفئة');
            }

            this.state.selectedCategory = data.data;
            this.showModal('تعديل الفئة');
            this.populateForm(data.data);

        } catch (error) {
            console.error('Error loading category:', error);
            SystemSettings.showNotification('فشل في تحميل بيانات الفئة: ' + error.message, 'error');
        } finally {
            SystemSettings.hideLoading('.modal-content');
        }
    },

    // Show Modal
    showModal(title) {
        const modal = document.getElementById('categoryModal');
        const modalTitle = document.getElementById('modalTitle');
        
        if (modal && modalTitle) {
            modalTitle.textContent = title;
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    },

    // Close Modal
    closeModal() {
        const modal = document.getElementById('categoryModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        this.state.selectedCategory = null;
    },

    // Reset Form
    resetForm() {
        const form = document.getElementById('categoryForm');
        if (form) {
            form.reset();
            
            // Reset color and icon to defaults
            document.getElementById('color').value = '#3498db';
            document.getElementById('icon').value = 'fas fa-tag';
        }
    },

    // Populate Form
    populateForm(category) {
        const fields = [
            'name', 'name_ar', 'description', 'description_ar',
            'parentId', 'icon', 'color', 'sortOrder', 'metaTitle', 'metaDescription'
        ];

        fields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                let value = category[field.replace(/([A-Z])/g, '_$1').toLowerCase()];
                if (field === 'parentId') value = category.parent_id;
                if (field === 'sortOrder') value = category.sort_order;
                if (field === 'metaTitle') value = category.meta_title;
                if (field === 'metaDescription') value = category.meta_description;
                
                element.value = value || '';
            }
        });

        // Set active status
        const isActiveCheckbox = document.getElementById('isActive');
        if (isActiveCheckbox) {
            isActiveCheckbox.checked = category.is_active;
        }
    },

    // Save Category
    async saveCategory() {
        const form = document.getElementById('categoryForm');
        if (!form) return;

        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Convert checkbox
        data.is_active = document.getElementById('isActive').checked;
        
        // Convert numeric fields
        data.sort_order = parseInt(data.sortOrder) || 0;
        data.parent_id = data.parentId || null;

        try {
            SystemSettings.showLoading('.modal-content', 'حفظ الفئة...');

            const url = this.config.apiEndpoint;
            const method = this.state.selectedCategory ? 'PUT' : 'POST';
            
            if (this.state.selectedCategory) {
                data.id = this.state.selectedCategory.id;
            }

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في حفظ الفئة');
            }

            SystemSettings.showNotification(
                this.state.selectedCategory ? 'تم تحديث الفئة بنجاح' : 'تم إضافة الفئة بنجاح',
                'success'
            );

            this.closeModal();
            await this.loadCategories();
            await this.loadStats();

        } catch (error) {
            console.error('Error saving category:', error);
            SystemSettings.showNotification('فشل في حفظ الفئة: ' + error.message, 'error');
        } finally {
            SystemSettings.hideLoading('.modal-content');
        }
    },

    // Delete Category
    async deleteCategory(id) {
        if (!confirm('هل أنت متأكد من حذف هذه الفئة؟\nسيتم حذف جميع الفئات الفرعية أيضاً.')) {
            return;
        }

        try {
            const response = await fetch(`${this.config.apiEndpoint}?id=${id}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في حذف الفئة');
            }

            SystemSettings.showNotification('تم حذف الفئة بنجاح', 'success');
            await this.loadCategories();
            await this.loadStats();

        } catch (error) {
            console.error('Error deleting category:', error);
            SystemSettings.showNotification('فشل في حذف الفئة: ' + error.message, 'error');
        }
    },

    // Load Stats
    async loadStats() {
        try {
            const response = await fetch(`${this.config.apiEndpoint}?action=stats`);
            const data = await response.json();

            if (data.success) {
                this.updateStats(data.data);
            }

        } catch (error) {
            console.warn('Failed to load stats:', error);
        }
    },

    // Update Stats
    updateStats(stats) {
        const elements = {
            totalCategories: document.getElementById('totalCategories'),
            activeCategories: document.getElementById('activeCategories'),
            parentCategories: document.getElementById('parentCategories'),
            childCategories: document.getElementById('childCategories')
        };

        if (elements.totalCategories) elements.totalCategories.textContent = stats.total || 0;
        if (elements.activeCategories) elements.activeCategories.textContent = stats.active || 0;
        if (elements.parentCategories) elements.parentCategories.textContent = stats.parents || 0;
        if (elements.childCategories) elements.childCategories.textContent = stats.children || 0;
    },

    // Render Pagination
    renderPagination(pagination) {
        const container = document.getElementById('paginationContainer');
        if (!container) return;

        if (pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = `
            <div class="pagination">
                <button class="btn btn-sm btn-secondary" 
                        onclick="CategoriesManager.goToPage(${pagination.current_page - 1})"
                        ${pagination.current_page === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                    السابق
                </button>
                
                <span class="pagination-info">
                    صفحة ${pagination.current_page} من ${pagination.total_pages}
                </span>
                
                <button class="btn btn-sm btn-secondary"
                        onclick="CategoriesManager.goToPage(${pagination.current_page + 1})"
                        ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}>
                    التالي
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
        `;

        container.innerHTML = html;
    },

    // Go to Page
    goToPage(page) {
        if (page < 1 || page > this.state.totalPages) return;
        
        this.state.currentPage = page;
        this.loadCategories();
    },

    // Toggle Tree Node
    toggleTreeNode(categoryId) {
        const children = document.getElementById(`tree-children-${categoryId}`);
        const toggle = document.querySelector(`[onclick="CategoriesManager.toggleTreeNode(${categoryId})"] i`);
        
        if (children && toggle) {
            const isExpanded = children.style.display !== 'none';
            children.style.display = isExpanded ? 'none' : 'block';
            toggle.className = isExpanded ? 'fas fa-chevron-right' : 'fas fa-chevron-down';
        }
    }
};

// Global save function for SystemSettings
window.saveCategories = async function(isAutoSave = false) {
    // Categories don't have a global save, but we can refresh data
    if (!isAutoSave) {
        await CategoriesManager.loadCategories();
        SystemSettings.showNotification('تم تحديث بيانات الفئات', 'success');
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('categories.html')) {
        CategoriesManager.init();
    }
});

// Make globally available
window.CategoriesManager = CategoriesManager;
