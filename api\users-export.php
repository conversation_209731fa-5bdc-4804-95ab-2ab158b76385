<?php
/**
 * Users Export API
 * API لتصدير بيانات المستخدمين
 */

require_once '../config/database.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit;
}

try {
    // Get all users
    $stmt = $pdo->query("
        SELECT 
            id,
            name,
            email,
            role,
            status,
            created_at,
            last_login,
            updated_at
        FROM users 
        ORDER BY created_at DESC
    ");
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Set headers for CSV download
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="users_export_' . date('Y-m-d_H-i-s') . '.csv"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // Create file pointer connected to the output stream
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Add CSV headers
    fputcsv($output, [
        'ID',
        'الاسم',
        'البريد الإلكتروني',
        'الدور',
        'الحالة',
        'تاريخ التسجيل',
        'آخر تسجيل دخول',
        'تاريخ التحديث'
    ]);
    
    // Add user data
    foreach ($users as $user) {
        // Convert role to Arabic
        $roleMap = [
            'super_admin' => 'مدير النظام',
            'admin' => 'مدير',
            'editor' => 'محرر',
            'customer' => 'عميل'
        ];
        
        // Convert status to Arabic
        $statusMap = [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ];
        
        fputcsv($output, [
            $user['id'],
            $user['name'],
            $user['email'],
            $roleMap[$user['role']] ?? $user['role'],
            $statusMap[$user['status']] ?? $user['status'],
            $user['created_at'] ? date('Y-m-d H:i:s', strtotime($user['created_at'])) : '',
            $user['last_login'] ? date('Y-m-d H:i:s', strtotime($user['last_login'])) : '',
            $user['updated_at'] ? date('Y-m-d H:i:s', strtotime($user['updated_at'])) : ''
        ]);
    }
    
    fclose($output);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
