<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Google Sign-In - Firebase</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
        }
        
        .test-section h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-family: inherit;
            font-weight: 500;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-google {
            background: #4285f4;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .config-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .checklist li:before {
            content: "❌ ";
            margin-left: 10px;
        }
        
        .checklist li.done:before {
            content: "✅ ";
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 اختبار Google Sign-In</h1>
            <p>تشخيص وإصلاح مشاكل تسجيل الدخول بـ Google</p>
        </div>

        <!-- Configuration Checklist -->
        <div class="test-section">
            <h3>📋 قائمة التحقق من الإعداد</h3>
            <div class="status warning">
                <strong>⚠️ تأكد من إكمال هذه الخطوات في Firebase Console:</strong>
            </div>
            
            <ul class="checklist">
                <li id="check-auth">تفعيل Google Sign-In في Authentication > Sign-in method</li>
                <li id="check-domains">إضافة localhost إلى Authorized domains</li>
                <li id="check-oauth">تكوين OAuth في Google Cloud Console</li>
                <li id="check-origins">إضافة http://localhost:8000 إلى JavaScript origins</li>
                <li id="check-redirects">إضافة redirect URIs المناسبة</li>
            </ul>
            
            <button class="btn btn-primary" onclick="checkConfiguration()">فحص الإعداد</button>
        </div>

        <!-- Current Configuration -->
        <div class="test-section">
            <h3>⚙️ الإعداد الحالي</h3>
            <div class="config-info" id="currentConfig">جاري تحميل الإعداد...</div>
        </div>

        <!-- Google Sign-In Test -->
        <div class="test-section">
            <h3>🧪 اختبار Google Sign-In</h3>
            <div id="googleStatus" class="status info">جاهز للاختبار</div>
            
            <button class="btn btn-google" onclick="testGoogleSignIn()">
                <svg width="18" height="18" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                اختبار Google Sign-In
            </button>
            
            <button class="btn btn-danger" onclick="testSignOut()">تسجيل خروج</button>
        </div>

        <!-- Error Details -->
        <div class="test-section">
            <h3>🔍 تفاصيل الأخطاء</h3>
            <div id="errorDetails" class="config-info">لا توجد أخطاء حالياً</div>
        </div>

        <!-- Manual Configuration -->
        <div class="test-section">
            <h3>🛠️ إعداد يدوي</h3>
            <div class="status info">
                <strong>إذا كان Google Sign-In لا يعمل، اتبع هذه الخطوات:</strong>
            </div>
            
            <div class="config-info">
                <strong>1. Firebase Console:</strong><br>
                - اذهب إلى https://console.firebase.google.com/<br>
                - اختر مشروع landingpage-a7491<br>
                - Authentication > Sign-in method<br>
                - فعّل Google provider<br>
                - أضف support email<br><br>
                
                <strong>2. Google Cloud Console:</strong><br>
                - اذهب إلى https://console.cloud.google.com/<br>
                - APIs & Services > Credentials<br>
                - اختر Web client OAuth 2.0<br>
                - أضف إلى Authorized JavaScript origins:<br>
                &nbsp;&nbsp;http://localhost:8000<br>
                &nbsp;&nbsp;http://127.0.0.1:8000<br><br>
                
                <strong>3. Authorized redirect URIs:</strong><br>
                &nbsp;&nbsp;http://localhost:8000/__/auth/handler<br>
                &nbsp;&nbsp;https://landingpage-a7491.firebaseapp.com/__/auth/handler
            </div>
            
            <button class="btn btn-primary" onclick="copyConfig()">نسخ الإعداد</button>
        </div>

        <!-- Navigation -->
        <div class="test-section">
            <h3>🔗 روابط مفيدة</h3>
            <button class="btn btn-primary" onclick="window.open('https://console.firebase.google.com/', '_blank')">Firebase Console</button>
            <button class="btn btn-primary" onclick="window.open('https://console.cloud.google.com/', '_blank')">Google Cloud Console</button>
            <button class="btn btn-success" onclick="window.location.href='login.html'">صفحة تسجيل الدخول</button>
        </div>
    </div>

    <!-- Use fixed Firebase config -->
    <script type="module" src="js/firebase-config-fixed.js"></script>

    <script type="module">
        let errorLog = [];

        // Check configuration
        window.checkConfiguration = function() {
            const checks = [
                { id: 'check-auth', test: () => window.firebaseAuth !== undefined },
                { id: 'check-domains', test: () => window.location.hostname === 'localhost' },
                { id: 'check-oauth', test: () => true }, // Can't check this programmatically
                { id: 'check-origins', test: () => window.location.origin === 'http://localhost:8000' },
                { id: 'check-redirects', test: () => true } // Can't check this programmatically
            ];

            checks.forEach(check => {
                const element = document.getElementById(check.id);
                if (check.test()) {
                    element.classList.add('done');
                } else {
                    element.classList.remove('done');
                }
            });
        };

        // Test Google Sign-In
        window.testGoogleSignIn = async function() {
            const statusDiv = document.getElementById('googleStatus');
            const errorDiv = document.getElementById('errorDetails');
            
            statusDiv.className = 'status info';
            statusDiv.textContent = '🔄 جاري اختبار Google Sign-In...';
            
            try {
                if (!window.firebaseAuth) {
                    throw new Error('Firebase Auth not initialized');
                }

                const result = await window.firebaseAuth.signInWithGoogle();
                
                if (result.success) {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `✅ نجح تسجيل الدخول بـ Google!<br>المستخدم: ${result.user.email}`;
                    errorDiv.textContent = 'لا توجد أخطاء - Google Sign-In يعمل بشكل صحيح!';
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Google Sign-In Test Error:', error);
                
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ فشل في تسجيل الدخول: ${error.message}`;
                
                // Log detailed error
                errorLog.push({
                    timestamp: new Date().toISOString(),
                    error: error.message,
                    code: error.code,
                    stack: error.stack
                });
                
                updateErrorDisplay();
                
                // Provide specific solutions
                if (error.message.includes('popup')) {
                    statusDiv.innerHTML += '<br><br>💡 <strong>الحل:</strong> تأكد من السماح بالنوافذ المنبثقة في المتصفح';
                } else if (error.message.includes('CORS') || error.message.includes('Cross-Origin')) {
                    statusDiv.innerHTML += '<br><br>💡 <strong>الحل:</strong> تحقق من إعداد Authorized JavaScript origins في Google Cloud Console';
                } else if (error.message.includes('offline')) {
                    statusDiv.innerHTML += '<br><br>💡 <strong>الحل:</strong> تحقق من اتصال الإنترنت';
                }
            }
        };

        // Test sign out
        window.testSignOut = async function() {
            try {
                if (window.firebaseAuth) {
                    await window.firebaseAuth.signOutUser();
                    document.getElementById('googleStatus').className = 'status success';
                    document.getElementById('googleStatus').textContent = '✅ تم تسجيل الخروج بنجاح';
                }
            } catch (error) {
                console.error('Sign out error:', error);
            }
        };

        // Update error display
        function updateErrorDisplay() {
            const errorDiv = document.getElementById('errorDetails');
            if (errorLog.length === 0) {
                errorDiv.textContent = 'لا توجد أخطاء حالياً';
                return;
            }

            const latestErrors = errorLog.slice(-3); // Show last 3 errors
            errorDiv.innerHTML = latestErrors.map(err => 
                `[${err.timestamp}] ${err.error}\nCode: ${err.code || 'N/A'}`
            ).join('\n\n');
        }

        // Copy configuration
        window.copyConfig = function() {
            const config = `
Firebase Project ID: landingpage-a7491
Auth Domain: landingpage-a7491.firebaseapp.com

Google Cloud Console - Authorized JavaScript origins:
http://localhost:8000
http://127.0.0.1:8000

Google Cloud Console - Authorized redirect URIs:
http://localhost:8000/__/auth/handler
https://landingpage-a7491.firebaseapp.com/__/auth/handler
            `;
            
            navigator.clipboard.writeText(config).then(() => {
                alert('تم نسخ الإعداد إلى الحافظة!');
            }).catch(() => {
                alert('فشل في النسخ. يرجى النسخ يدوياً.');
            });
        };

        // Show current configuration
        function showCurrentConfig() {
            const configDiv = document.getElementById('currentConfig');
            const config = {
                hostname: window.location.hostname,
                origin: window.location.origin,
                userAgent: navigator.userAgent.substring(0, 50) + '...',
                firebaseLoaded: !!window.firebaseAuth,
                online: navigator.onLine
            };
            
            configDiv.innerHTML = Object.entries(config)
                .map(([key, value]) => `${key}: ${value}`)
                .join('<br>');
        }

        // Initialize
        setTimeout(() => {
            checkConfiguration();
            showCurrentConfig();
        }, 1000);

        // Listen for auth state changes
        window.onFirebaseUserSignedIn = function(user, profile) {
            const statusDiv = document.getElementById('googleStatus');
            statusDiv.className = 'status success';
            statusDiv.innerHTML = `✅ مستخدم مسجل: ${user.email}<br>الدور: ${profile?.role || 'غير محدد'}`;
        };

        window.onFirebaseUserSignedOut = function() {
            const statusDiv = document.getElementById('googleStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = 'جاهز للاختبار';
        };
    </script>
</body>
</html>
