<?php
/**
 * Demo Installation Script
 * سكريبت تثبيت البيانات التجريبية
 */

require_once __DIR__ . '/config.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $pdo = getPDOConnection();
    
    // Start transaction
    $pdo->beginTransaction();
    
    echo "بدء تثبيت البيانات التجريبية...\n";
    
    // 1. Install Security Settings Tables
    echo "تثبيت جداول إعدادات الأمان...\n";
    $securitySQL = file_get_contents(__DIR__ . '/database/security_settings_table.sql');
    $pdo->exec($securitySQL);
    echo "✓ تم تثبيت جداول إعدادات الأمان\n";
    
    // 2. Install Demo Tables
    echo "تثبيت جداول البيانات التجريبية...\n";
    $demoTablesSQL = file_get_contents(__DIR__ . '/database/demo_tables.sql');
    $pdo->exec($demoTablesSQL);
    echo "✓ تم تثبيت جداول البيانات التجريبية\n";
    
    // 3. Insert Demo Data
    echo "إدراج البيانات التجريبية...\n";
    $demoDataSQL = file_get_contents(__DIR__ . '/database/demo_data.sql');
    $pdo->exec($demoDataSQL);
    echo "✓ تم إدراج البيانات التجريبية\n";
    
    // 4. Create sample customers
    echo "إنشاء عملاء تجريبيين...\n";
    $customersSQL = "
    INSERT INTO `customers` (`name`, `email`, `phone`, `address`, `city`, `country`) VALUES
    ('محمد أحمد الخليل', '<EMAIL>', '+213 555 111 222', 'شارع الاستقلال 123', 'الجزائر', 'الجزائر'),
    ('فاطمة علي بن سالم', '<EMAIL>', '+213 555 333 444', 'حي البدر، شارع النصر 45', 'وهران', 'الجزائر'),
    ('عبد الرحمن خالد', '<EMAIL>', '+213 555 555 666', 'المدينة الجديدة، شارع السلام 78', 'قسنطينة', 'الجزائر'),
    ('سارة محمود حسن', '<EMAIL>', '+213 555 777 888', 'حي الزيتون، شارع الحرية 12', 'عنابة', 'الجزائر'),
    ('أحمد حسن محمد', '<EMAIL>', '+213 555 999 000', 'شارع الثورة 34', 'سطيف', 'الجزائر');
    ";
    $pdo->exec($customersSQL);
    echo "✓ تم إنشاء العملاء التجريبيين\n";
    
    // 5. Create sample orders
    echo "إنشاء طلبات تجريبية...\n";
    $ordersSQL = "
    INSERT INTO `orders` (`store_id`, `customer_name`, `customer_email`, `customer_phone`, `total_amount`, `status`, `payment_status`, `shipping_address`, `order_items`) VALUES
    (1, 'محمد أحمد', '<EMAIL>', '+213 555 111 222', 165000.00, 'delivered', 'paid', 'شارع الاستقلال 123، الجزائر', '[{\"product_id\": 1, \"name\": \"هاتف سامسونج جالاكسي S24 Ultra\", \"price\": 165000, \"quantity\": 1}]'),
    (1, 'فاطمة علي', '<EMAIL>', '+213 555 333 444', 135000.00, 'shipped', 'paid', 'حي البدر، شارع النصر 45، وهران', '[{\"product_id\": 3, \"name\": \"لابتوب ديل XPS 13\", \"price\": 135000, \"quantity\": 1}]'),
    (1, 'عبد الرحمن خالد', '<EMAIL>', '+213 555 555 666', 42000.00, 'confirmed', 'paid', 'المدينة الجديدة، شارع السلام 78، قسنطينة', '[{\"product_id\": 4, \"name\": \"سماعات سوني WH-1000XM5\", \"price\": 42000, \"quantity\": 1}]'),
    (1, 'سارة محمود', '<EMAIL>', '+213 555 777 888', 88000.00, 'pending', 'pending', 'حي الزيتون، شارع الحرية 12، عنابة', '[{\"product_id\": 5, \"name\": \"تلفزيون سامسونج 55 بوصة 4K\", \"price\": 88000, \"quantity\": 1}]'),
    (1, 'أحمد حسن', '<EMAIL>', '+213 555 999 000', 72000.00, 'delivered', 'paid', 'شارع الثورة 34، سطيف', '[{\"product_id\": 6, \"name\": \"بلايستيشن 5 ديجيتال\", \"price\": 72000, \"quantity\": 1}]');
    ";
    $pdo->exec($ordersSQL);
    echo "✓ تم إنشاء الطلبات التجريبية\n";
    
    // 6. Create newsletter subscriptions
    echo "إنشاء اشتراكات النشرة الإخبارية...\n";
    $newsletterSQL = "
    INSERT INTO `newsletter_subscriptions` (`email`, `name`, `source`, `interests`) VALUES
    ('<EMAIL>', 'أحمد محمد', 'landing_page', '[\"smartphones\", \"laptops\"]'),
    ('<EMAIL>', 'فاطمة علي', 'homepage', '[\"accessories\", \"smart_home\"]'),
    ('<EMAIL>', 'محمد خالد', 'product_page', '[\"gaming\", \"computers\"]'),
    ('<EMAIL>', 'سارة أحمد', 'social_media', '[\"smartphones\", \"accessories\"]'),
    ('<EMAIL>', 'عبد الله حسن', 'referral', '[\"all_categories\"]');
    ";
    $pdo->exec($newsletterSQL);
    echo "✓ تم إنشاء اشتراكات النشرة الإخبارية\n";
    
    // 7. Create landing page analytics
    echo "إنشاء تحليلات صفحات الهبوط...\n";
    $analyticsSQL = "
    INSERT INTO `landing_page_analytics` (`landing_page_id`, `visitor_ip`, `action_type`, `action_data`) VALUES
    (1, '*************', 'view', '{\"page\": \"smartphones-sale\", \"duration\": 45}'),
    (1, '*************', 'click', '{\"element\": \"cta_button\", \"text\": \"تسوق الآن\"}'),
    (2, '*************', 'view', '{\"page\": \"laptops-professionals\", \"duration\": 67}'),
    (3, '*************', 'conversion', '{\"product_id\": 4, \"value\": 42000}'),
    (4, '*************', 'view', '{\"page\": \"smart-home-devices\", \"duration\": 89}'),
    (5, '*************', 'click', '{\"element\": \"product_link\", \"product_id\": 6}');
    ";
    $pdo->exec($analyticsSQL);
    echo "✓ تم إنشاء تحليلات صفحات الهبوط\n";
    
    // 8. Update landing page statistics
    echo "تحديث إحصائيات صفحات الهبوط...\n";
    $updateStatsSQL = "
    UPDATE `landing_pages` SET 
        `page_views` = FLOOR(RAND() * 1000) + 100,
        `conversion_rate` = ROUND(RAND() * 10 + 2, 2)
    WHERE `id` BETWEEN 1 AND 10;
    ";
    $pdo->exec($updateStatsSQL);
    echo "✓ تم تحديث إحصائيات صفحات الهبوط\n";
    
    // Commit transaction
    $pdo->commit();
    
    echo "\n🎉 تم تثبيت جميع البيانات التجريبية بنجاح!\n\n";
    echo "📊 ملخص البيانات المثبتة:\n";
    echo "- 1 بائع تجريبي (أحمد محمد الخليل)\n";
    echo "- 1 متجر إلكتروني (متجر الخليل الإلكتروني)\n";
    echo "- 5 فئات منتجات\n";
    echo "- 10 منتجات متنوعة\n";
    echo "- 10 صفحات هبوط احترافية\n";
    echo "- 5 عملاء تجريبيين\n";
    echo "- 5 طلبات تجريبية\n";
    echo "- إعدادات أمان شاملة\n";
    echo "- تحليلات وإحصائيات\n\n";
    echo "✅ يمكنك الآن استخدام النظام مع البيانات التجريبية!\n";
    
    // Return success response
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => 'تم تثبيت البيانات التجريبية بنجاح',
        'data' => [
            'sellers' => 1,
            'stores' => 1,
            'categories' => 5,
            'products' => 10,
            'landing_pages' => 10,
            'customers' => 5,
            'orders' => 5
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo "\n❌ خطأ في تثبيت البيانات التجريبية:\n";
    echo $e->getMessage() . "\n";
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'فشل في تثبيت البيانات التجريبية: ' . $e->getMessage()
    ]);
}
?>
