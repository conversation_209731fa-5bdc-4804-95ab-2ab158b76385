<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simulation de Connexion</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .simulation-box { border: 2px solid #007bff; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test de Simulation de Connexion</h1>
    <p>Cette page simule le processus de connexion pour vérifier que les corrections fonctionnent.</p>
    
    <div class="simulation-box">
        <h3>Simulation du processus de connexion</h3>
        <button onclick="simulateLogin()">🔐 Simuler une connexion réussie</button>
        <button onclick="simulateLoginPageMode()">📱 Simuler connexion en mode "login page"</button>
        <button onclick="simulateAdminLogin()">👑 Simuler connexion admin</button>
    </div>
    
    <div id="results"></div>

    <!-- Auth Fix Script -->
    <script src="auth-fix.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = new Date().toLocaleTimeString() + ': ' + message;
            resultsDiv.appendChild(resultDiv);
            console.log(message);
        }

        function simulateRedirect(url) {
            addResult(`🔄 Tentative de redirection vers: ${url}`, 'info');
            
            if (typeof window.safeRedirect === 'function') {
                addResult('✅ Utilisation de window.safeRedirect', 'success');
                
                // Intercepter la redirection pour la simulation
                const originalLocationHref = window.location.href;
                let redirectCalled = false;
                
                // Temporairement override window.location.href
                const originalSetter = Object.getOwnPropertyDescriptor(window.location, 'href')?.set ||
                                     Object.getOwnPropertyDescriptor(Location.prototype, 'href')?.set;
                
                if (originalSetter) {
                    Object.defineProperty(window.location, 'href', {
                        set: function(newUrl) {
                            redirectCalled = true;
                            addResult(`✅ Redirection simulée vers: ${newUrl}`, 'success');
                            addResult('🎉 Test réussi - la redirection aurait eu lieu', 'success');
                            
                            // Restaurer le comportement original après un délai
                            setTimeout(() => {
                                Object.defineProperty(window.location, 'href', {
                                    set: originalSetter,
                                    get: Object.getOwnPropertyDescriptor(window.location, 'href')?.get ||
                                         Object.getOwnPropertyDescriptor(Location.prototype, 'href')?.get,
                                    configurable: true
                                });
                            }, 100);
                        },
                        get: function() {
                            return originalLocationHref;
                        },
                        configurable: true
                    });
                }
                
                // Appeler safeRedirect
                try {
                    window.safeRedirect(url);
                    if (!redirectCalled) {
                        addResult('⚠️ safeRedirect appelée mais redirection non détectée', 'warning');
                    }
                } catch (error) {
                    addResult(`❌ Erreur lors de l'appel à safeRedirect: ${error.message}`, 'error');
                }
                
            } else {
                addResult('❌ window.safeRedirect non disponible - utilisation du fallback', 'warning');
                addResult(`🔄 Fallback: window.location.href = '${url}'`, 'info');
            }
        }

        function simulateLogin() {
            addResult('=== SIMULATION DE CONNEXION STANDARD ===', 'info');
            
            // Simuler les données utilisateur
            const mockUser = {
                email: '<EMAIL>',
                uid: 'test-uid-123'
            };
            
            const mockProfile = {
                role: 'admin',
                loginPageMode: false
            };
            
            addResult('👤 Utilisateur simulé: ' + mockUser.email, 'info');
            addResult('🔐 Profil: ' + JSON.stringify(mockProfile), 'info');
            
            // Marquer comme connexion manuelle
            window.loginFormSubmitted = true;
            
            // Simuler l'appel à onFirebaseUserSignedIn
            if (typeof window.onFirebaseUserSignedIn === 'function') {
                addResult('✅ Appel de onFirebaseUserSignedIn...', 'info');
                
                // Simuler que nous sommes sur la page de connexion
                Object.defineProperty(window.location, 'pathname', {
                    value: '/admin/login.html',
                    configurable: true
                });
                
                try {
                    window.onFirebaseUserSignedIn(mockUser, mockProfile);
                    addResult('✅ onFirebaseUserSignedIn exécutée sans erreur', 'success');
                } catch (error) {
                    addResult(`❌ Erreur dans onFirebaseUserSignedIn: ${error.message}`, 'error');
                }
            } else {
                addResult('❌ onFirebaseUserSignedIn non disponible', 'error');
            }
        }

        function simulateLoginPageMode() {
            addResult('=== SIMULATION CONNEXION MODE "LOGIN PAGE" ===', 'info');
            
            const mockUser = {
                email: '<EMAIL>',
                uid: 'loginpage-uid-456'
            };
            
            const mockProfile = {
                loginPageMode: true,
                role: 'user'
            };
            
            addResult('👤 Utilisateur en mode login page: ' + mockUser.email, 'info');
            addResult('📱 Profil minimal: ' + JSON.stringify(mockProfile), 'info');
            
            window.loginFormSubmitted = true;
            
            if (typeof window.onFirebaseUserSignedIn === 'function') {
                Object.defineProperty(window.location, 'pathname', {
                    value: '/admin/login.html',
                    configurable: true
                });
                
                try {
                    window.onFirebaseUserSignedIn(mockUser, mockProfile);
                    addResult('✅ Mode login page traité correctement', 'success');
                } catch (error) {
                    addResult(`❌ Erreur en mode login page: ${error.message}`, 'error');
                }
            }
        }

        function simulateAdminLogin() {
            addResult('=== SIMULATION CONNEXION ADMIN ===', 'info');
            
            const mockUser = {
                email: '<EMAIL>',
                uid: 'admin-uid-789'
            };
            
            const mockProfile = {
                role: 'super_admin',
                loginPageMode: false,
                permissions: ['all']
            };
            
            addResult('👑 Administrateur: ' + mockUser.email, 'info');
            addResult('🛡️ Profil admin: ' + JSON.stringify(mockProfile), 'info');
            
            window.loginFormSubmitted = true;
            
            if (typeof window.onFirebaseUserSignedIn === 'function') {
                Object.defineProperty(window.location, 'pathname', {
                    value: '/admin/login.html',
                    configurable: true
                });
                
                try {
                    window.onFirebaseUserSignedIn(mockUser, mockProfile);
                    addResult('✅ Connexion admin traitée correctement', 'success');
                } catch (error) {
                    addResult(`❌ Erreur connexion admin: ${error.message}`, 'error');
                }
            }
        }

        // Initialisation
        window.addEventListener('load', function() {
            addResult('🚀 Page de test chargée', 'info');
            addResult('📋 Fonctions disponibles:', 'info');
            
            const functions = ['safeRedirect', 'onFirebaseUserSignedIn', 'onFirebaseUserSignedOut'];
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    addResult(`  ✅ ${func}`, 'success');
                } else {
                    addResult(`  ❌ ${func}`, 'error');
                }
            });
            
            addResult('🧪 Prêt pour les tests de simulation', 'info');
        });
    </script>
</body>
</html>