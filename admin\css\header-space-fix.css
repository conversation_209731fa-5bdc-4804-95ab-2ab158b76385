/**
 * Header Space Fix CSS
 * Fixes empty space issues at the top of the admin interface
 */

/* Ensure no extra margins or padding on body and html */
html, body {
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: hidden;
}

/* Fix admin container positioning */
.admin-container {
    margin: 0 !important;
    padding: 0 !important;
    min-height: 100vh;
    display: flex;
}

/* Ensure main content starts at the top */
.main-content {
    padding-top: 20px !important; /* Reduced from 40px */
    margin-top: 0 !important;
}

/* Fix content header positioning */
.content-header {
    margin-top: 0 !important;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Ensure sidebar doesn't create extra space */
.sidebar {
    margin: 0 !important;
    padding-top: 0 !important;
}

/* Fix any potential loading indicator issues */
#loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
}

/* Ensure notifications don't create space */
.notifications-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    pointer-events: none;
}

/* Fix mobile menu toggle positioning */
.mobile-menu-toggle {
    position: fixed;
    top: 15px;
    right: 15px;
    z-index: 1001;
}

/* Ensure dashboard content starts properly */
.dashboard-content,
.section-content {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Fix any potential issues with the first child elements */
.main-content > *:first-child {
    margin-top: 0 !important;
}

.content-header + * {
    margin-top: 0 !important;
}

/* Ensure proper spacing for different sections */
#dashboardContent,
#booksContent,
#ordersContent,
#landingPagesContent,
#reportsContent,
#settingsContent {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Fix any issues with enhanced dashboard */
.enhanced-dashboard {
    margin-top: 0 !important;
    padding-top: 20px;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .main-content {
        padding-top: 80px !important; /* Account for mobile menu toggle */
        margin-top: 0 !important;
    }
    
    .content-header {
        margin-top: 0 !important;
    }
}

/* Fix for any potential CSS conflicts */
.admin-container * {
    box-sizing: border-box;
}

/* Ensure visibility is properly handled */
body.content-loaded {
    visibility: visible !important;
}

/* Fix any potential issues with flexbox */
.admin-container {
    align-items: stretch;
    justify-content: flex-start;
}

/* Ensure main content takes full available space */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Fix content wrapper if it exists */
.content-wrapper {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Emergency fix for any remaining space issues */
.main-content::before {
    content: none !important;
    display: none !important;
}

/* Ensure proper header hierarchy */
.content-header h1 {
    margin: 0;
    padding: 0;
    line-height: 1.2;
}

/* Fix any potential issues with page title */
#pageTitle {
    margin: 0 !important;
    padding: 0 !important;
}

/* Ensure refresh button doesn't create space */
#refreshPageBtn {
    margin: 0;
    padding: 8px 16px;
}

/* Fix for any hidden elements that might create space */
[style*="display: none"],
[style*="visibility: hidden"] {
    margin: 0 !important;
    padding: 0 !important;
    height: 0 !important;
}

/* Final emergency fix */
.main-content > .content-header:first-child {
    margin-top: 0 !important;
}

/* Debug helper - remove in production */
/*
.main-content {
    border: 2px solid red !important;
}

.content-header {
    border: 2px solid blue !important;
}

.admin-container {
    border: 2px solid green !important;
}
*/
