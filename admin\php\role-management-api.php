<?php

/**
 * Role Management API
 * واجهة برمجة تطبيقات إدارة الأدوار
 *
 * Handles CRUD operations for roles, permissions, and role assignments
 */

session_start();
require_once '../../config/config.php';

// Set JSON response headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

class RoleManagementAPI
{
    private $pdo;

    public function __construct()
    {
        try {
            $dbConfig = Config::getDbConfig();
            $dsn = sprintf(
                "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                $dbConfig['host'],
                $dbConfig['port'],
                $dbConfig['database']
            );

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ];

            $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
        } catch (Exception $e) {
            $this->sendError('Database connection failed: ' . $e->getMessage(), 500);
        }
    }

    private function sendResponse($data, $success = true, $message = '', $code = 200)
    {
        http_response_code($code);
        echo json_encode([
            'success' => $success,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    private function sendError($message, $code = 400)
    {
        $this->sendResponse(null, false, $message, $code);
    }

    /**
     * Get all roles with their permissions
     */
    public function getAllRoles()
    {
        try {
            $stmt = $this->pdo->query("
                SELECT
                    r.*,
                    COUNT(DISTINCT rp.permission_id) as permissions_count,
                    COUNT(DISTINCT ura.user_id) as users_count
                FROM user_roles r
                LEFT JOIN role_permissions rp ON r.id = rp.role_id AND rp.granted = 1
                LEFT JOIN user_role_assignments ura ON r.id = ura.role_id AND ura.is_active = 1
                WHERE r.is_active = 1
                GROUP BY r.id
                ORDER BY r.level ASC
            ");

            $roles = $stmt->fetchAll();

            // Get permissions for each role
            foreach ($roles as &$role) {
                $permStmt = $this->pdo->prepare("
                    SELECT p.id, p.name, p.display_name_ar, p.display_name_en, p.category
                    FROM permissions p
                    INNER JOIN role_permissions rp ON p.id = rp.permission_id
                    WHERE rp.role_id = ? AND rp.granted = 1 AND p.is_active = 1
                    ORDER BY p.category, p.display_name_ar
                ");
                $permStmt->execute([$role['id']]);
                $role['permissions'] = $permStmt->fetchAll();
            }

            $this->sendResponse($roles, true, 'تم جلب الأدوار بنجاح');
        } catch (Exception $e) {
            $this->sendError('خطأ في جلب الأدوار: ' . $e->getMessage());
        }
    }

    /**
     * Get all permissions grouped by category
     */
    public function getAllPermissions()
    {
        try {
            $stmt = $this->pdo->query("
                SELECT *
                FROM permissions
                WHERE is_active = 1
                ORDER BY category, display_name_ar
            ");

            $permissions = $stmt->fetchAll();

            // Group by category
            $grouped = [];
            foreach ($permissions as $permission) {
                $category = $permission['category'];
                if (!isset($grouped[$category])) {
                    $grouped[$category] = [];
                }
                $grouped[$category][] = $permission;
            }

            $this->sendResponse($grouped, true, 'تم جلب الصلاحيات بنجاح');
        } catch (Exception $e) {
            $this->sendError('خطأ في جلب الصلاحيات: ' . $e->getMessage());
        }
    }

    /**
     * Get role by ID with full details
     */
    public function getRoleById($roleId)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT *
                FROM user_roles
                WHERE id = ? AND is_active = 1
            ");
            $stmt->execute([$roleId]);
            $role = $stmt->fetch();

            if (!$role) {
                $this->sendError('الدور غير موجود', 404);
            }

            // Get role permissions
            $permStmt = $this->pdo->prepare("
                SELECT p.id, p.name, p.display_name_ar, p.display_name_en, p.category
                FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                WHERE rp.role_id = ? AND rp.granted = 1 AND p.is_active = 1
                ORDER BY p.category, p.display_name_ar
            ");
            $permStmt->execute([$roleId]);
            $role['permissions'] = $permStmt->fetchAll();

            // Get users with this role
            $userStmt = $this->pdo->prepare("
                SELECT u.id, u.username, u.email, u.first_name, u.last_name
                FROM users u
                INNER JOIN user_role_assignments ura ON u.id = ura.user_id
                WHERE ura.role_id = ? AND ura.is_active = 1 AND u.status = 'active'
                ORDER BY u.first_name, u.last_name
            ");
            $userStmt->execute([$roleId]);
            $role['users'] = $userStmt->fetchAll();

            $this->sendResponse($role, true, 'تم جلب تفاصيل الدور بنجاح');
        } catch (Exception $e) {
            $this->sendError('خطأ في جلب تفاصيل الدور: ' . $e->getMessage());
        }
    }

    /**
     * Create a new role
     */
    public function createRole($data)
    {
        try {
            // Validate required fields
            $required = ['name', 'display_name_ar', 'display_name_en', 'level'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    $this->sendError("الحقل $field مطلوب");
                }
            }

            // Check if role name already exists
            $stmt = $this->pdo->prepare("SELECT id FROM user_roles WHERE name = ?");
            $stmt->execute([$data['name']]);
            if ($stmt->fetch()) {
                $this->sendError('اسم الدور موجود بالفعل');
            }

            // Insert new role
            $insertStmt = $this->pdo->prepare("
                INSERT INTO user_roles (name, display_name_ar, display_name_en, description_ar, description_en, level, color, icon, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
            ");

            $insertStmt->execute([
                $data['name'],
                $data['display_name_ar'],
                $data['display_name_en'],
                $data['description_ar'] ?? '',
                $data['description_en'] ?? '',
                $data['level'],
                $data['color'] ?? '#007bff',
                $data['icon'] ?? 'fas fa-user'
            ]);

            $roleId = $this->pdo->lastInsertId();

            // Assign permissions if provided
            if (!empty($data['permissions']) && is_array($data['permissions'])) {
                $this->assignPermissionsToRole($roleId, $data['permissions']);
            }

            $this->sendResponse(['id' => $roleId], true, 'تم إنشاء الدور بنجاح');
        } catch (Exception $e) {
            $this->sendError('خطأ في إنشاء الدور: ' . $e->getMessage());
        }
    }

    /**
     * Update an existing role
     */
    public function updateRole($roleId, $data)
    {
        try {
            // Check if role exists
            $stmt = $this->pdo->prepare("SELECT id FROM user_roles WHERE id = ? AND is_active = 1");
            $stmt->execute([$roleId]);
            if (!$stmt->fetch()) {
                $this->sendError('الدور غير موجود', 404);
            }

            // Build update query dynamically
            $updateFields = [];
            $updateValues = [];

            $allowedFields = ['display_name_ar', 'display_name_en', 'description_ar', 'description_en', 'level', 'color', 'icon'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $data[$field];
                }
            }

            if (!empty($updateFields)) {
                $updateValues[] = $roleId;
                $updateStmt = $this->pdo->prepare("
                    UPDATE user_roles
                    SET " . implode(', ', $updateFields) . ", updated_at = NOW()
                    WHERE id = ?
                ");
                $updateStmt->execute($updateValues);
            }

            // Update permissions if provided
            if (isset($data['permissions']) && is_array($data['permissions'])) {
                $this->assignPermissionsToRole($roleId, $data['permissions']);
            }

            $this->sendResponse(['id' => $roleId], true, 'تم تحديث الدور بنجاح');
        } catch (Exception $e) {
            $this->sendError('خطأ في تحديث الدور: ' . $e->getMessage());
        }
    }

    /**
     * Assign permissions to a role
     */
    private function assignPermissionsToRole($roleId, $permissionIds)
    {
        try {
            // Remove existing permissions
            $deleteStmt = $this->pdo->prepare("DELETE FROM role_permissions WHERE role_id = ?");
            $deleteStmt->execute([$roleId]);

            // Add new permissions
            if (!empty($permissionIds)) {
                $insertStmt = $this->pdo->prepare("
                    INSERT INTO role_permissions (role_id, permission_id, granted)
                    VALUES (?, ?, 1)
                ");

                foreach ($permissionIds as $permissionId) {
                    $insertStmt->execute([$roleId, $permissionId]);
                }
            }
        } catch (Exception $e) {
            throw new Exception('خطأ في تعيين الصلاحيات: ' . $e->getMessage());
        }
    }

    /**
     * Assign role to user
     */
    public function assignRoleToUser($userId, $roleId)
    {
        try {
            // Check if user exists
            $userStmt = $this->pdo->prepare("SELECT id FROM users WHERE id = ? AND status = 'active'");
            $userStmt->execute([$userId]);
            if (!$userStmt->fetch()) {
                $this->sendError('المستخدم غير موجود', 404);
            }

            // Check if role exists
            $roleStmt = $this->pdo->prepare("SELECT id FROM user_roles WHERE id = ? AND is_active = 1");
            $roleStmt->execute([$roleId]);
            if (!$roleStmt->fetch()) {
                $this->sendError('الدور غير موجود', 404);
            }

            // Update user's role_id
            $updateUserStmt = $this->pdo->prepare("UPDATE users SET role_id = ? WHERE id = ?");
            $updateUserStmt->execute([$roleId, $userId]);

            // Insert or update role assignment
            $assignStmt = $this->pdo->prepare("
                INSERT INTO user_role_assignments (user_id, role_id, assigned_at, is_active)
                VALUES (?, ?, NOW(), 1)
                ON DUPLICATE KEY UPDATE
                role_id = VALUES(role_id),
                assigned_at = NOW(),
                is_active = 1
            ");
            $assignStmt->execute([$userId, $roleId]);

            $this->sendResponse(null, true, 'تم تعيين الدور للمستخدم بنجاح');
        } catch (Exception $e) {
            $this->sendError('خطأ في تعيين الدور: ' . $e->getMessage());
        }
    }

    /**
     * Get user permissions (combined from role and direct assignments)
     */
    public function getUserPermissions($userId)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT p.name, p.display_name_ar, p.display_name_en, p.category
                FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                INNER JOIN user_role_assignments ura ON rp.role_id = ura.role_id
                WHERE ura.user_id = ? AND ura.is_active = 1 AND rp.granted = 1 AND p.is_active = 1
                ORDER BY p.category, p.display_name_ar
            ");
            $stmt->execute([$userId]);
            $permissions = $stmt->fetchAll();

            $this->sendResponse($permissions, true, 'تم جلب صلاحيات المستخدم بنجاح');
        } catch (Exception $e) {
            $this->sendError('خطأ في جلب صلاحيات المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Check if user has specific permission
     */
    public function checkUserPermission($userId, $permission)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as has_permission
                FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                INNER JOIN user_role_assignments ura ON rp.role_id = ura.role_id
                WHERE ura.user_id = ? AND p.name = ? AND ura.is_active = 1 AND rp.granted = 1 AND p.is_active = 1
            ");
            $stmt->execute([$userId, $permission]);
            $result = $stmt->fetch();

            $hasPermission = $result['has_permission'] > 0;

            $this->sendResponse([
                'has_permission' => $hasPermission,
                'permission' => $permission,
                'user_id' => $userId
            ], true, $hasPermission ? 'المستخدم لديه الصلاحية' : 'المستخدم ليس لديه الصلاحية');
        } catch (Exception $e) {
            $this->sendError('خطأ في فحص الصلاحية: ' . $e->getMessage());
        }
    }
}

// Handle API requests
try {
    $api = new RoleManagementAPI();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'roles':
                    $api->getAllRoles();
                    break;

                case 'permissions':
                    $api->getAllPermissions();
                    break;

                case 'role':
                    $roleId = $_GET['id'] ?? null;
                    if (!$roleId) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'معرف الدور مطلوب']);
                        exit;
                    }
                    $api->getRoleById($roleId);
                    break;

                case 'user_permissions':
                    $userId = $_GET['user_id'] ?? null;
                    if (!$userId) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'معرف المستخدم مطلوب']);
                        exit;
                    }
                    $api->getUserPermissions($userId);
                    break;

                case 'check_permission':
                    $userId = $_GET['user_id'] ?? null;
                    $permission = $_GET['permission'] ?? null;
                    if (!$userId || !$permission) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'معرف المستخدم والصلاحية مطلوبان']);
                        exit;
                    }
                    $api->checkUserPermission($userId, $permission);
                    break;

                default:
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
            }
            break;

        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);

            switch ($action) {
                case 'create_role':
                    $api->createRole($input);
                    break;

                case 'assign_role':
                    $userId = $input['user_id'] ?? null;
                    $roleId = $input['role_id'] ?? null;
                    if (!$userId || !$roleId) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'معرف المستخدم والدور مطلوبان']);
                        exit;
                    }
                    $api->assignRoleToUser($userId, $roleId);
                    break;

                default:
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
            }
            break;

        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            $roleId = $_GET['id'] ?? null;

            if ($action === 'update_role' && $roleId) {
                $api->updateRole($roleId, $input);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'معرف الدور مطلوب']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
