<?php
/**
 * Debug Stores API Script
 * Diagnoses and fixes the 500 Internal Server Error in stores API
 */

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "🔍 Debugging Stores API...\n\n";

// Test 1: Check if config file loads
echo "📋 Test 1: Loading configuration...\n";
try {
    require_once '../php/config.php';
    echo "✅ Config loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Check database connection
echo "\n📋 Test 2: Testing database connection...\n";
try {
    $pdo = getPDOConnection();
    echo "✅ Database connection successful\n";
    
    // Test basic query
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetchColumn();
    echo "✅ Database version: {$version}\n";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 3: Check if stores table exists and has data
echo "\n📋 Test 3: Checking stores table...\n";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM stores");
    $count = $stmt->fetchColumn();
    echo "✅ Stores table exists with {$count} records\n";
    
    if ($count > 0) {
        $stmt = $pdo->query("SELECT id, store_name, status FROM stores LIMIT 3");
        $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "📊 Sample stores:\n";
        foreach ($samples as $store) {
            echo "   - {$store['store_name']} (ID: {$store['id']}, Status: {$store['status']})\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Stores table error: " . $e->getMessage() . "\n";
}

// Test 4: Check users table
echo "\n📋 Test 4: Checking users table...\n";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $count = $stmt->fetchColumn();
    echo "✅ Users table exists with {$count} records\n";
} catch (Exception $e) {
    echo "❌ Users table error: " . $e->getMessage() . "\n";
}

// Test 5: Test the exact query from stores API
echo "\n📋 Test 5: Testing stores API query...\n";
try {
    $sql = "
        SELECT 
            s.*,
            u.nom as owner_name,
            u.email as owner_email,
            u.telephone as owner_phone,
            u.created_at as user_created_at
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        ORDER BY s.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ Query executed successfully, found " . count($stores) . " stores\n";
    
    // Test JSON processing
    foreach ($stores as &$store) {
        if ($store['settings']) {
            $settings = json_decode($store['settings'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                echo "⚠️ JSON decode error for store {$store['id']}: " . json_last_error_msg() . "\n";
            }
        }
    }
    echo "✅ JSON processing completed\n";
    
} catch (Exception $e) {
    echo "❌ Query error: " . $e->getMessage() . "\n";
}

// Test 6: Test complete API function
echo "\n📋 Test 6: Testing handleGetStores function...\n";
try {
    // Capture output
    ob_start();
    
    // Include the stores API file and test the function
    $apiFile = '../php/api/stores.php';
    if (file_exists($apiFile)) {
        // Read the file content
        $content = file_get_contents($apiFile);
        
        // Extract just the handleGetStores function
        if (strpos($content, 'function handleGetStores') !== false) {
            echo "✅ handleGetStores function found in API file\n";
            
            // Test by calling the API directly
            echo "🔗 Testing API endpoint directly...\n";
            
            // Use curl to test the API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_NOBODY, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                echo "❌ cURL error: {$error}\n";
            } else {
                echo "📊 HTTP Response Code: {$httpCode}\n";
                
                // Split headers and body
                $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
                $headers = substr($response, 0, $headerSize);
                $body = substr($response, $headerSize);
                
                echo "📋 Response Headers:\n{$headers}\n";
                echo "📋 Response Body:\n{$body}\n";
                
                if ($httpCode === 200) {
                    $data = json_decode($body, true);
                    if ($data) {
                        echo "✅ API returned valid JSON\n";
                        echo "📊 Success: " . ($data['success'] ? 'true' : 'false') . "\n";
                        echo "📊 Total stores: " . ($data['total'] ?? 'unknown') . "\n";
                    } else {
                        echo "❌ API returned invalid JSON: " . json_last_error_msg() . "\n";
                    }
                } else {
                    echo "❌ API returned HTTP {$httpCode}\n";
                }
            }
        } else {
            echo "❌ handleGetStores function not found in API file\n";
        }
    } else {
        echo "❌ API file not found: {$apiFile}\n";
    }
    
    $output = ob_get_clean();
    echo $output;
    
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Function test error: " . $e->getMessage() . "\n";
}

// Test 7: Check PHP error log
echo "\n📋 Test 7: Checking for PHP errors...\n";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    $errors = file_get_contents($errorLog);
    $recentErrors = array_slice(explode("\n", $errors), -10);
    echo "📋 Recent PHP errors:\n";
    foreach ($recentErrors as $error) {
        if (trim($error)) {
            echo "   {$error}\n";
        }
    }
} else {
    echo "ℹ️ No error log file found or configured\n";
}

echo "\n🎉 Debugging completed!\n";
echo "\n💡 Next steps:\n";
echo "   1. Check the API response above\n";
echo "   2. Look for any error messages\n";
echo "   3. Fix any identified issues\n";
echo "   4. Test the API again\n";
?>
