<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            margin: 20px;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 1.8rem;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 1rem;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2d3748;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            direction: rtl;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .demo-credentials {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .demo-credentials h4 {
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .demo-credentials p {
            color: #718096;
            margin: 4px 0;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .back-link a:hover {
            color: #5a67d8;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-message {
            background: #f0fff4;
            border: 1px solid #c6f6d5;
            color: #22543d;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-shield-alt"></i> لوحة التحكم</h1>
            <p>تسجيل الدخول إلى نظام الإدارة</p>
        </div>

        <div class="login-form">
            <div class="success-message" id="successMessage">
                <i class="fas fa-check-circle"></i>
                <strong>تم تسجيل الخروج بنجاح!</strong>
                <br>يمكنك الآن تسجيل الدخول مرة أخرى.
            </div>

            <div class="demo-credentials">
                <h4><i class="fas fa-info-circle"></i> بيانات تجريبية:</h4>
                <p><strong>المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" value="admin" required>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" value="admin123" required>
                </div>

                <button type="submit" class="login-button" id="loginButton">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </button>
            </form>

            <div class="loading" id="loadingDiv">
                <div class="spinner"></div>
                <p>جاري تسجيل الدخول...</p>
            </div>

            <div class="back-link">
                <a href="index.html">
                    <i class="fas fa-arrow-right"></i> العودة إلى الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <script>
        // Check if user was redirected after logout
        if (window.location.search.includes('logout=success')) {
            document.getElementById('successMessage').style.display = 'block';
        }

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginButton = document.getElementById('loginButton');
            const loadingDiv = document.getElementById('loadingDiv');
            
            // Simple validation (in real app, this would be server-side)
            if (username === 'admin' && password === 'admin123') {
                // Show loading
                loginButton.style.display = 'none';
                loadingDiv.style.display = 'block';
                
                // Store session (in real app, this would be a secure token)
                localStorage.setItem('adminSession', 'active');
                localStorage.setItem('adminToken', 'demo-token-' + Date.now());
                
                // Redirect after delay
                setTimeout(() => {
                    window.location.href = 'admin/index.html';
                }, 2000);
            } else {
                alert('بيانات تسجيل الدخول غير صحيحة!\n\nيرجى استخدام:\nالمستخدم: admin\nكلمة المرور: admin123');
            }
        });

        // Auto-focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
