Je vais organiser le projet en créant une structure de dossiers dédiée pour les fichiers non-production et en déplaçant les fichiers appropriés.

## 1. Créer la structure de dossiers scripts/

## 2. Identifier et déplacer les fichiers de test et debug du répertoire admin/

Il semble que les fichiers existent déjà. Supprimons d'abord les fichiers existants et utilisons une approche différente :

Utilisons une approche plus systématique. Créons d'abord un script PowerShell pour organiser les fichiers :

## 3. Exécuter le script d'organisation

Il y a une erreur de syntaxe dans le script PowerShell. Créons une version corrigée :

_You are out of user messages <NAME_EMAIL>. Please update your account [here](https://app.augmentcode.com/account) to continue using Augment._
