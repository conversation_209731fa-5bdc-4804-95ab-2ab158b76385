/* Security Settings Dashboard Layout */
.dashboard-layout {
    padding: 20px;
}

/* Security Score Card */
.security-score {
    text-align: center;
    padding: 15px;
}

.security-score h3 {
    margin: 10px 0;
    font-size: 24px;
    font-weight: bold;
}

/* Password Strength Meter */
.strength-meter {
    width: 100%;
    height: 8px;
    margin: 10px 0;
    border-radius: 4px;
}

.strength-meter.weak { accent-color: #dc3545; }
.strength-meter.medium { accent-color: #ffc107; }
.strength-meter.strong { accent-color: #28a745; }
.strength-meter.very-strong { accent-color: #198754; }

/* Password Requirements List */
#passwordRequirements {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

#passwordRequirements li {
    margin: 5px 0;
    color: #6c757d;
}

#passwordRequirements li.met {
    color: #28a745;
}

#passwordRequirements li i {
    margin-left: 5px;
}

/* Security Recommendations */
.recommendation-item {
    display: flex;
    margin: 10px 0;
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.recommendation-item.high {
    border-right: 4px solid #dc3545;
}

.recommendation-item.medium {
    border-right: 4px solid #ffc107;
}

.recommendation-item.low {
    border-right: 4px solid #28a745;
}

.recommendation-icon {
    margin-left: 15px;
    font-size: 24px;
}

.recommendation-item.high .recommendation-icon { color: #dc3545; }
.recommendation-item.medium .recommendation-icon { color: #ffc107; }
.recommendation-item.low .recommendation-icon { color: #28a745; }

.recommendation-content {
    flex: 1;
}

.recommendation-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: bold;
}

.recommendation-content p {
    margin: 0;
    color: #6c757d;
}

/* Toast Notifications */
#toastContainer {
    z-index: 1050;
}

.toast {
    min-width: 300px;
    margin: 10px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.toast-success .toast-header { color: #28a745; }
.toast-error .toast-header { color: #dc3545; }
.toast-warning .toast-header { color: #ffc107; }
.toast-info .toast-header { color: #17a2b8; }

.toast-header {
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
}

.toast-header i {
    margin-left: 10px;
}

.toast-body {
    padding: 10px 15px;
}

/* Active Sessions Table */
.table-responsive {
    margin: 15px 0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Security Charts */
.card {
    margin-bottom: 20px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-title {
    margin-bottom: 20px;
    font-weight: 600;
}

/* Security Stats Cards */
.security-stats-card {
    text-align: center;
    padding: 20px;
}

.security-stats-card h3 {
    font-size: 28px;
    font-weight: bold;
    margin: 10px 0;
}

.security-stats-card p {
    color: #6c757d;
    margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .recommendation-item {
        flex-direction: column;
    }

    .recommendation-icon {
        margin: 0 0 10px 0;
    }

    .toast {
        min-width: auto;
        width: 100%;
    }
}

/* RTL Specific Adjustments */
.nav-tabs {
    padding-right: 0;
}

.me-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

.ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

.me-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

.ms-2 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
}