<?php

/**
 * Categories API
 * API إدارة الفئات
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../config/db_env.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                getCategory($_GET['id']);
            } elseif ($action === 'tree') {
                getCategoriesTree();
            } elseif ($action === 'stats') {
                getCategoriesStats();
            } else {
                getCategories();
            }
            break;
        case 'POST':
            createCategory();
            break;
        case 'PUT':
            updateCategory();
            break;
        case 'DELETE':
            if (isset($_GET['id'])) {
                deleteCategory($_GET['id']);
            }
            break;
        default:
            throw new Exception('Method not allowed');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getCategories()
{
    global $pdo;

    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $search = $_GET['search'] ?? '';
    $parent_id = $_GET['parent_id'] ?? null;
    $status = $_GET['status'] ?? '';

    $offset = ($page - 1) * $limit;

    // Build WHERE clause
    $where = [];
    $params = [];

    if ($search) {
        $where[] = "(name LIKE ? OR name_ar LIKE ? OR description LIKE ? OR description_ar LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if ($parent_id !== null) {
        if ($parent_id === '0' || $parent_id === 'null') {
            $where[] = "parent_id IS NULL";
        } else {
            $where[] = "parent_id = ?";
            $params[] = $parent_id;
        }
    }

    if ($status !== '') {
        $where[] = "is_active = ?";
        $params[] = $status === 'active' ? 1 : 0;
    }

    $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

    // Get total count
    $countSql = "SELECT COUNT(*) FROM categories $whereClause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetchColumn();

    // Get categories with parent info
    $sql = "
        SELECT
            c.*,
            p.name as parent_name,
            p.name_ar as parent_name_ar,
            (SELECT COUNT(*) FROM categories WHERE parent_id = c.id) as children_count
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        $whereClause
        ORDER BY c.sort_order ASC, c.name ASC
        LIMIT ? OFFSET ?
    ";

    $params[] = $limit;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format categories
    foreach ($categories as &$category) {
        $category['is_active'] = (bool) $category['is_active'];
        $category['children_count'] = (int) $category['children_count'];
        $category['sort_order'] = (int) $category['sort_order'];
    }

    echo json_encode([
        'success' => true,
        'data' => $categories,
        'pagination' => [
            'current_page' => (int) $page,
            'per_page' => (int) $limit,
            'total' => (int) $total,
            'total_pages' => ceil($total / $limit)
        ]
    ]);
}

function getCategoriesTree()
{
    global $pdo;

    $stmt = $pdo->query("
        SELECT
            id, name, name_ar, parent_id, icon, color, is_active, sort_order,
            (SELECT COUNT(*) FROM categories WHERE parent_id = c.id) as children_count
        FROM categories c
        ORDER BY sort_order ASC, name ASC
    ");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Build tree structure
    $tree = buildCategoryTree($categories);

    echo json_encode([
        'success' => true,
        'data' => $tree
    ]);
}

function buildCategoryTree($categories, $parentId = null)
{
    $tree = [];

    foreach ($categories as $category) {
        if ($category['parent_id'] == $parentId) {
            $category['children'] = buildCategoryTree($categories, $category['id']);
            $category['is_active'] = (bool) $category['is_active'];
            $category['children_count'] = (int) $category['children_count'];
            $tree[] = $category;
        }
    }

    return $tree;
}

function getCategory($id)
{
    global $pdo;

    $stmt = $pdo->prepare("
        SELECT
            c.*,
            p.name as parent_name,
            p.name_ar as parent_name_ar
        FROM categories c
        LEFT JOIN categories p ON c.parent_id = p.id
        WHERE c.id = ?
    ");
    $stmt->execute([$id]);
    $category = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$category) {
        throw new Exception('Category not found');
    }

    $category['is_active'] = (bool) $category['is_active'];

    echo json_encode([
        'success' => true,
        'data' => $category
    ]);
}

function createCategory()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid input data');
    }

    // Validate required fields
    $required = ['name', 'name_ar'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }

    // Check if slug exists
    $slug = generateSlug($input['name']);
    $stmt = $pdo->prepare("SELECT id FROM categories WHERE slug = ?");
    $stmt->execute([$slug]);
    if ($stmt->fetch()) {
        $slug .= '-' . time();
    }

    $stmt = $pdo->prepare("
        INSERT INTO categories (
            name, name_ar, description, description_ar, parent_id,
            icon, color, sort_order, is_active, meta_title,
            meta_description, slug
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $input['name'],
        $input['name_ar'],
        $input['description'] ?? null,
        $input['description_ar'] ?? null,
        $input['parent_id'] ?? null,
        $input['icon'] ?? 'fas fa-tag',
        $input['color'] ?? '#3498db',
        $input['sort_order'] ?? 0,
        $input['is_active'] ?? true,
        $input['meta_title'] ?? null,
        $input['meta_description'] ?? null,
        $slug
    ]);

    $categoryId = $pdo->lastInsertId();

    echo json_encode([
        'success' => true,
        'message' => 'Category created successfully',
        'data' => ['id' => $categoryId]
    ]);
}

function updateCategory()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['id'])) {
        throw new Exception('Invalid input data');
    }

    $id = $input['id'];

    // Check if category exists
    $stmt = $pdo->prepare("SELECT id FROM categories WHERE id = ?");
    $stmt->execute([$id]);
    if (!$stmt->fetch()) {
        throw new Exception('Category not found');
    }

    // Update slug if name changed
    if (isset($input['name'])) {
        $slug = generateSlug($input['name']);
        $stmt = $pdo->prepare("SELECT id FROM categories WHERE slug = ? AND id != ?");
        $stmt->execute([$slug, $id]);
        if ($stmt->fetch()) {
            $slug .= '-' . time();
        }
        $input['slug'] = $slug;
    }

    // Build update query
    $fields = [];
    $params = [];

    $allowedFields = [
        'name',
        'name_ar',
        'description',
        'description_ar',
        'parent_id',
        'icon',
        'color',
        'sort_order',
        'is_active',
        'meta_title',
        'meta_description',
        'slug'
    ];

    foreach ($allowedFields as $field) {
        if (array_key_exists($field, $input)) {
            $fields[] = "$field = ?";
            $params[] = $input[$field];
        }
    }

    if (empty($fields)) {
        throw new Exception('No fields to update');
    }

    $params[] = $id;

    $stmt = $pdo->prepare("
        UPDATE categories
        SET " . implode(', ', $fields) . "
        WHERE id = ?
    ");

    $stmt->execute($params);

    echo json_encode([
        'success' => true,
        'message' => 'Category updated successfully'
    ]);
}

function deleteCategory($id)
{
    global $pdo;

    // Check if category has children
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories WHERE parent_id = ?");
    $stmt->execute([$id]);
    $childrenCount = $stmt->fetchColumn();

    if ($childrenCount > 0) {
        throw new Exception('Cannot delete category with subcategories');
    }

    // Check if category is used by products (if products table exists)
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
        $stmt->execute([$id]);
        $productsCount = $stmt->fetchColumn();

        if ($productsCount > 0) {
            throw new Exception('Cannot delete category with products');
        }
    } catch (PDOException $e) {
        // Products table doesn't exist, continue
    }

    $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
    $stmt->execute([$id]);

    echo json_encode([
        'success' => true,
        'message' => 'Category deleted successfully'
    ]);
}

function getCategoriesStats()
{
    global $pdo;

    $stats = [];

    // Total categories
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $stats['total'] = $stmt->fetchColumn();

    // Active categories
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories WHERE is_active = 1");
    $stats['active'] = $stmt->fetchColumn();

    // Parent categories
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories WHERE parent_id IS NULL");
    $stats['parents'] = $stmt->fetchColumn();

    // Child categories
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories WHERE parent_id IS NOT NULL");
    $stats['children'] = $stmt->fetchColumn();

    echo json_encode([
        'success' => true,
        'data' => $stats
    ]);
}

function generateSlug($text)
{
    // Simple slug generation
    $slug = strtolower(trim($text));
    $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    return trim($slug, '-');
}
