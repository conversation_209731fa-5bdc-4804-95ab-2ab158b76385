<?php

/**
 * Users API
 * Handles all user-related API requests
 */

// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = 'localhost';
$port = '3307';
$dbname = 'mossab-landing-page';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get the action parameter
$action = $_GET['action'] ?? $_POST['action'] ?? 'list';

try {
    switch ($action) {
        case 'list':
            handleListUsers($pdo);
            break;
        case 'create':
            handleCreateUser($pdo);
            break;
        case 'update':
            handleUpdateUser($pdo);
            break;
        case 'delete':
            handleDeleteUser($pdo);
            break;
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action: ' . $action
            ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleListUsers($pdo)
{
    try {
        // Create users table if it doesn't exist
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(255) NOT NULL UNIQUE,
                email VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(255),
                role VARCHAR(100) DEFAULT 'user',
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($createTableSQL);

        // Insert sample data if table is empty
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $count = $countStmt->fetch()['count'];

        if ($count == 0) {
            $sampleUsers = [
                [
                    'username' => 'admin',
                    'email' => '<EMAIL>',
                    'password' => password_hash('admin123', PASSWORD_DEFAULT),
                    'full_name' => 'مدير النظام',
                    'role' => 'admin'
                ],
                [
                    'username' => 'ahmed_store',
                    'email' => '<EMAIL>',
                    'password' => password_hash('password123', PASSWORD_DEFAULT),
                    'full_name' => 'أحمد محمد',
                    'role' => 'store_owner'
                ],
                [
                    'username' => 'fatima_user',
                    'email' => '<EMAIL>',
                    'password' => password_hash('password123', PASSWORD_DEFAULT),
                    'full_name' => 'فاطمة أحمد',
                    'role' => 'user'
                ]
            ];

            $insertStmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
            foreach ($sampleUsers as $user) {
                $insertStmt->execute([$user['username'], $user['email'], $user['password'], $user['full_name'], $user['role']]);
            }
        }

        // Fetch all users (excluding passwords)
        $stmt = $pdo->query("SELECT id, username, email, full_name, role, status, last_login, created_at FROM users ORDER BY created_at DESC");
        $users = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'data' => [
                'users' => $users,
                'total' => count($users)
            ],
            'message' => 'Users retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve users: ' . $e->getMessage());
    }
}

function handleCreateUser($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['username']) || !isset($input['email']) || !isset($input['password'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Username, email and password are required'
        ]);
        return;
    }

    try {
        $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name, role, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $input['username'],
            $input['email'],
            $hashedPassword,
            $input['full_name'] ?? '',
            $input['role'] ?? 'user',
            $input['status'] ?? 'active'
        ]);

        $userId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $userId,
                'username' => $input['username']
            ],
            'message' => 'User created successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to create user: ' . $e->getMessage());
    }
}

function handleUpdateUser($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'User ID is required'
        ]);
        return;
    }

    try {
        $updateFields = [];
        $params = [];

        if (isset($input['username'])) {
            $updateFields[] = 'username = ?';
            $params[] = $input['username'];
        }
        if (isset($input['email'])) {
            $updateFields[] = 'email = ?';
            $params[] = $input['email'];
        }
        if (isset($input['full_name'])) {
            $updateFields[] = 'full_name = ?';
            $params[] = $input['full_name'];
        }
        if (isset($input['role'])) {
            $updateFields[] = 'role = ?';
            $params[] = $input['role'];
        }
        if (isset($input['status'])) {
            $updateFields[] = 'status = ?';
            $params[] = $input['status'];
        }
        if (isset($input['password'])) {
            $updateFields[] = 'password = ?';
            $params[] = password_hash($input['password'], PASSWORD_DEFAULT);
        }

        $params[] = $input['id'];

        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $input['id']
            ],
            'message' => 'User updated successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to update user: ' . $e->getMessage());
    }
}

function handleDeleteUser($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'User ID is required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$input['id']]);

        echo json_encode([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to delete user: ' . $e->getMessage());
    }
}
