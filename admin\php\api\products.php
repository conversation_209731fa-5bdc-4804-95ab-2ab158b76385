<?php
/**
 * Products API
 * Handles all product-related API requests
 */

// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = 'localhost';
$port = '3307';
$dbname = 'mossab-landing-page';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get the action parameter
$action = $_GET['action'] ?? $_POST['action'] ?? 'list';

try {
    switch ($action) {
        case 'list':
            handleListProducts($pdo);
            break;
        case 'create':
            handleCreateProduct($pdo);
            break;
        case 'update':
            handleUpdateProduct($pdo);
            break;
        case 'delete':
            handleDeleteProduct($pdo);
            break;
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action: ' . $action
            ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleListProducts($pdo)
{
    try {
        // Create products table if it doesn't exist
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                category_id INT,
                user_id INT,
                status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
                stock_quantity INT DEFAULT 0,
                image_url VARCHAR(500),
                actif BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_category_id (category_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($createTableSQL);

        // Insert sample data if table is empty
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM products");
        $count = $countStmt->fetch()['count'];
        
        if ($count == 0) {
            $sampleProducts = [
                [
                    'name' => 'هاتف ذكي متطور',
                    'description' => 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
                    'price' => 299.99,
                    'category_id' => 1,
                    'user_id' => 1,
                    'stock_quantity' => 50,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Phone',
                    'actif' => true
                ],
                [
                    'name' => 'لابتوب للألعاب',
                    'description' => 'لابتوب قوي مخصص للألعاب والتصميم',
                    'price' => 899.99,
                    'category_id' => 1,
                    'user_id' => 1,
                    'stock_quantity' => 25,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Laptop',
                    'actif' => true
                ],
                [
                    'name' => 'قميص قطني',
                    'description' => 'قميص قطني عالي الجودة ومريح',
                    'price' => 29.99,
                    'category_id' => 2,
                    'user_id' => 2,
                    'stock_quantity' => 100,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Shirt',
                    'actif' => true
                ],
                [
                    'name' => 'كتاب البرمجة',
                    'description' => 'دليل شامل لتعلم البرمجة من الصفر',
                    'price' => 19.99,
                    'category_id' => 3,
                    'user_id' => 1,
                    'stock_quantity' => 200,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Book',
                    'actif' => true
                ],
                [
                    'name' => 'ساعة ذكية',
                    'description' => 'ساعة ذكية مع مراقب اللياقة البدنية',
                    'price' => 199.99,
                    'category_id' => 1,
                    'user_id' => 2,
                    'stock_quantity' => 75,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Watch',
                    'actif' => true
                ]
            ];
            
            $insertStmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id, user_id, stock_quantity, image_url, actif) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            foreach ($sampleProducts as $product) {
                $insertStmt->execute([
                    $product['name'],
                    $product['description'],
                    $product['price'],
                    $product['category_id'],
                    $product['user_id'],
                    $product['stock_quantity'],
                    $product['image_url'],
                    $product['actif']
                ]);
            }
        }

        // Fetch all products
        $stmt = $pdo->query("SELECT *, stock_quantity as stock FROM products ORDER BY created_at DESC");
        $products = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'data' => $products,
            'message' => 'Products retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve products: ' . $e->getMessage());
    }
}

function handleCreateProduct($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['name'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Product name is required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id, user_id, stock_quantity, image_url, status, actif) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            $input['price'] ?? 0,
            $input['category_id'] ?? null,
            $input['user_id'] ?? 1,
            $input['stock_quantity'] ?? 0,
            $input['image_url'] ?? '',
            $input['status'] ?? 'active',
            $input['actif'] ?? true
        ]);

        $productId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $productId,
                'name' => $input['name']
            ],
            'message' => 'Product created successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to create product: ' . $e->getMessage());
    }
}

function handleUpdateProduct($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id']) || !isset($input['name'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Product ID and name are required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, price = ?, category_id = ?, stock_quantity = ?, image_url = ?, status = ?, actif = ? WHERE id = ?");
        $stmt->execute([
            $input['name'],
            $input['description'] ?? '',
            $input['price'] ?? 0,
            $input['category_id'] ?? null,
            $input['stock_quantity'] ?? 0,
            $input['image_url'] ?? '',
            $input['status'] ?? 'active',
            $input['actif'] ?? true,
            $input['id']
        ]);

        echo json_encode([
            'success' => true,
            'data' => [
                'id' => $input['id'],
                'name' => $input['name']
            ],
            'message' => 'Product updated successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to update product: ' . $e->getMessage());
    }
}

function handleDeleteProduct($pdo)
{
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Product ID is required'
        ]);
        return;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
        $stmt->execute([$input['id']]);

        echo json_encode([
            'success' => true,
            'message' => 'Product deleted successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to delete product: ' . $e->getMessage());
    }
}
?>
