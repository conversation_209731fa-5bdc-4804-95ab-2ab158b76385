<?php

/**
 * Router for PHP Built-in Server
 * Handles URL rewriting since .htaccess is not supported by php -S
 */

$uri = $_SERVER['REQUEST_URI'];
$path = parse_url($uri, PHP_URL_PATH);

// Remove query string for path matching
$cleanPath = strtok($path, '?');

// Store URLs: /store/{slug}
if (preg_match('#^/store/([a-zA-Z0-9\-]+)/?$#', $cleanPath, $matches)) {
    $_GET['store'] = $matches[1];
    require 'store.php';
    return;
}

// Product URLs: /product/{slug}
if (preg_match('#^/product/([^/]+)/?$#', $cleanPath, $matches)) {
    $_GET['slug'] = $matches[1];
    require 'product-landing.php';
    return;
}

// Landing page URLs: /landing/product-{id}-{slug}
if (preg_match('#^/landing/product-([0-9]+)-([a-zA-Z0-9]+)/?$#', $cleanPath, $matches)) {
    $_GET['url'] = $matches[1] . '-' . $matches[2];
    require 'landing-page-template.php';
    return;
}

// Admin URLs: /admin/
if (preg_match('#^/admin/?$#', $cleanPath)) {
    require 'admin/index.html';
    return;
}

// Admin PHP files: /admin/*.php
if (preg_match('#^/admin/(.+\.php)$#', $cleanPath, $matches)) {
    $adminFile = 'admin/' . $matches[1];
    if (file_exists($adminFile)) {
        require $adminFile;
        return;
    }
}

// Admin HTML files: /admin/*.html
if (preg_match('#^/admin/(.+\.html)$#', $cleanPath, $matches)) {
    $adminFile = 'admin/' . $matches[1];
    if (file_exists($adminFile)) {
        require $adminFile;
        return;
    }
}

// API URLs: /php/api/*
if (preg_match('#^/php/api/(.+)$#', $cleanPath, $matches)) {
    $apiFile = 'php/api/' . $matches[1];
    if (file_exists($apiFile)) {
        require $apiFile;
        return;
    }
}

// Static files - let the server handle them
if (file_exists('.' . $cleanPath)) {
    return false; // Let the built-in server serve the file
}

// Default fallback to index.html
require 'index.html';
