<?php
/**
 * Test Database Connection
 * Simple test to verify database connectivity
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Error handling
ini_set('display_errors', 1);
error_reporting(E_ALL);

$result = [
    'success' => false,
    'message' => '',
    'details' => [],
    'timestamp' => date('Y-m-d H:i:s')
];

try {
    // Try to load configuration
    if (file_exists('../config/database.php')) {
        require_once '../config/database.php';
        $result['details'][] = 'Database configuration loaded successfully';
        
        // Test connection
        $connectionTest = testDatabaseConnection();
        $result['details'][] = 'Connection test executed';
        
        if ($connectionTest['success']) {
            $result['success'] = true;
            $result['message'] = 'Database connection successful';
            $result['connection_info'] = [
                'host' => $connectionTest['host'],
                'port' => $connectionTest['port'],
                'database' => $connectionTest['database']
            ];
            
            // Try to get database connection and test a simple query
            try {
                $pdo = getDatabaseConnection();
                $stmt = $pdo->query("SELECT 1 as test");
                $testResult = $stmt->fetch();
                
                if ($testResult && $testResult['test'] == 1) {
                    $result['details'][] = 'Simple query test passed';
                    
                    // Test if we can see tables
                    $stmt = $pdo->query("SHOW TABLES");
                    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    $result['tables_count'] = count($tables);
                    $result['details'][] = "Found {$result['tables_count']} tables in database";
                    
                    if (count($tables) > 0) {
                        $result['sample_tables'] = array_slice($tables, 0, 5);
                    }
                } else {
                    $result['details'][] = 'Simple query test failed';
                }
                
            } catch (Exception $e) {
                $result['details'][] = 'Query test failed: ' . $e->getMessage();
            }
            
        } else {
            $result['message'] = $connectionTest['message'];
            $result['details'][] = 'Connection test failed: ' . $connectionTest['message'];
        }
        
    } else {
        $result['message'] = 'Database configuration file not found';
        $result['details'][] = 'Configuration file ../config/database.php not found';
        
        // Try direct connection with fallback values
        $result['details'][] = 'Attempting direct connection with fallback values';
        
        $host = 'localhost';
        $port = '3307';
        $dbname = 'poultraydz';
        $username = 'postgres';
        $password = 'root';
        
        try {
            $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            
            $result['success'] = true;
            $result['message'] = 'Direct connection successful with fallback values';
            $result['connection_info'] = [
                'host' => $host,
                'port' => $port,
                'database' => $dbname
            ];
            $result['details'][] = 'Direct connection established successfully';
            
        } catch (PDOException $e) {
            $result['message'] = 'Direct connection failed: ' . $e->getMessage();
            $result['details'][] = 'Direct connection failed: ' . $e->getMessage();
        }
    }
    
} catch (Exception $e) {
    $result['message'] = 'Test failed with exception: ' . $e->getMessage();
    $result['details'][] = 'Exception occurred: ' . $e->getMessage();
}

// Add PHP and system information
$result['system_info'] = [
    'php_version' => PHP_VERSION,
    'pdo_available' => extension_loaded('pdo'),
    'pdo_mysql_available' => extension_loaded('pdo_mysql'),
    'current_directory' => getcwd(),
    'script_path' => __FILE__
];

echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
