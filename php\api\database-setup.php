<?php

/**
 * Database Setup API
 * Handles database table creation and setup
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$results = [
    'success' => false,
    'message' => '',
    'fixes' => [],
    'errors' => [],
    'warnings' => []
];

try {
    // Load environment configuration
    require_once __DIR__ . '/../config/env-loader.php';

    // Create PDO connection using .env settings
    $pdo = EnvLoader::createDatabaseConnection();

    $results['fixes'][] = "Database connection established successfully";

    // Handle different actions
    $action = $_GET['action'] ?? ($_POST['action'] ?? 'setup_roles');

    switch ($action) {
        case 'setup_roles':
            setupRolesTable($pdo, $results);
            break;

        case 'test_connection':
            testDatabaseConnection($pdo, $results);
            break;

        case 'check_tables':
            checkTables($pdo, $results);
            break;

        default:
            setupRolesTable($pdo, $results);
    }

    $results['success'] = true;
    $results['message'] = 'Database setup completed successfully';
} catch (Exception $e) {
    $results['success'] = false;
    $results['message'] = 'Database setup failed: ' . $e->getMessage();
    $results['errors'][] = $e->getMessage();
}

echo json_encode($results, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

/**
 * Setup roles table
 */
function setupRolesTable($pdo, &$results)
{
    try {
        // Check if roles table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
        if ($stmt->rowCount() == 0) {
            // Create roles table
            $createTableSQL = "
                CREATE TABLE roles (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) UNIQUE NOT NULL,
                    display_name VARCHAR(100) NOT NULL DEFAULT '',
                    display_name_ar VARCHAR(100) NOT NULL DEFAULT '',
                    description TEXT DEFAULT NULL,
                    permissions JSON DEFAULT NULL,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";

            $pdo->exec($createTableSQL);
            $results['fixes'][] = "Created roles table with proper structure";
        } else {
            $results['fixes'][] = "Roles table already exists";
        }

        // Check if table has data
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
        $count = $countStmt->fetch()['count'];

        if ($count == 0) {
            // Insert default roles
            $defaultRoles = [
                ['admin', 'Administrator', 'مدير النظام', 'Full system access'],
                ['seller', 'Seller', 'بائع', 'Can manage own products and orders'],
                ['user', 'User', 'مستخدم', 'Basic user access'],
                ['moderator', 'Moderator', 'مشرف', 'Can moderate content'],
                ['editor', 'Editor', 'محرر', 'Can edit content']
            ];

            $insertStmt = $pdo->prepare("
                INSERT INTO roles (name, display_name, display_name_ar, description)
                VALUES (?, ?, ?, ?)
            ");

            foreach ($defaultRoles as $role) {
                $insertStmt->execute($role);
            }

            $results['fixes'][] = "Inserted " . count($defaultRoles) . " default roles";
        } else {
            $results['fixes'][] = "Roles table already has $count roles";
        }

        // Verify the table works
        $testStmt = $pdo->query("SELECT id, name, display_name, display_name_ar FROM roles ORDER BY id LIMIT 3");
        $roles = $testStmt->fetchAll();

        $results['fixes'][] = "Verification successful - found " . count($roles) . " roles";
        $results['sample_roles'] = $roles;
    } catch (Exception $e) {
        $results['errors'][] = "Failed to setup roles table: " . $e->getMessage();
        throw $e;
    }
}

/**
 * Test database connection
 */
function testDatabaseConnection($pdo, &$results)
{
    try {
        // Test basic query
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();

        if ($result['test'] == 1) {
            $results['fixes'][] = "Database connection test successful";
        }

        // Get database info
        $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as version");
        $dbInfo = $stmt->fetch();

        $results['database_info'] = [
            'name' => $dbInfo['db_name'],
            'version' => $dbInfo['version'],
            'charset' => 'utf8mb4'
        ];

        $results['fixes'][] = "Connected to database: " . $dbInfo['db_name'];
    } catch (Exception $e) {
        $results['errors'][] = "Database connection test failed: " . $e->getMessage();
        throw $e;
    }
}

/**
 * Check existing tables
 */
function checkTables($pdo, &$results)
{
    try {
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $results['tables'] = $tables;
        $results['fixes'][] = "Found " . count($tables) . " tables in database";

        // Check specific tables
        $requiredTables = ['roles', 'users', 'products'];
        $existingTables = [];
        $missingTables = [];

        foreach ($requiredTables as $table) {
            if (in_array($table, $tables)) {
                $existingTables[] = $table;
            } else {
                $missingTables[] = $table;
            }
        }

        if (!empty($existingTables)) {
            $results['fixes'][] = "Existing required tables: " . implode(', ', $existingTables);
        }

        if (!empty($missingTables)) {
            $results['warnings'][] = "Missing tables: " . implode(', ', $missingTables);
        }
    } catch (Exception $e) {
        $results['errors'][] = "Failed to check tables: " . $e->getMessage();
        throw $e;
    }
}
