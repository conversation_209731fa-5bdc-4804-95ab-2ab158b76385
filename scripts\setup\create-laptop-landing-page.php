<?php
require_once 'php/config.php';

try {
    // Create a landing page for "حاسوب محمول للمطورين" (ID: 2)
    $stmt = $conn->prepare("
        INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $contenu_droit = '
    <h3>💻 لماذا هذا الحاسوب مثالي للمطورين؟</h3>
    <ul>
        <li><strong>معالج Intel Core i7:</strong> أداء فائق للبرمجة والتطوير</li>
        <li><strong>ذاكرة 16GB RAM:</strong> تشغيل متعدد المهام بسلاسة</li>
        <li><strong>تخزين 512GB SSD:</strong> سرعة في التشغيل والوصول للملفات</li>
        <li><strong>شاشة عالية الدقة:</strong> مثالية للبرمجة لساعات طويلة</li>
        <li><strong>لوحة مفاتيح مريحة:</strong> تجربة كتابة ممتازة للكود</li>
    </ul>
    
    <h3>🚀 المواصفات التقنية:</h3>
    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr style="background: #f8f9fa;">
            <td style="padding: 10px; border: 1px solid #ddd;"><strong>المعالج</strong></td>
            <td style="padding: 10px; border: 1px solid #ddd;">Intel Core i7 الجيل الأحدث</td>
        </tr>
        <tr>
            <td style="padding: 10px; border: 1px solid #ddd;"><strong>الذاكرة</strong></td>
            <td style="padding: 10px; border: 1px solid #ddd;">16GB DDR4 RAM</td>
        </tr>
        <tr style="background: #f8f9fa;">
            <td style="padding: 10px; border: 1px solid #ddd;"><strong>التخزين</strong></td>
            <td style="padding: 10px; border: 1px solid #ddd;">512GB NVMe SSD</td>
        </tr>
        <tr>
            <td style="padding: 10px; border: 1px solid #ddd;"><strong>الشاشة</strong></td>
            <td style="padding: 10px; border: 1px solid #ddd;">15.6" Full HD IPS</td>
        </tr>
    </table>
    
    <h3>⚡ مثالي للتطبيقات التالية:</h3>
    <p>• تطوير تطبيقات الويب والموبايل<br>
    • البرمجة بلغات متعددة (Python, Java, C++)<br>
    • تشغيل بيئات التطوير المتكاملة (IDE)<br>
    • تصميم قواعد البيانات<br>
    • اختبار وتشغيل الخوادم المحلية</p>
    ';
    
    $contenu_gauche = '
    <h3>🏢 عن الشركة المصنعة</h3>
    <p>نحن نقدم أجهزة حاسوب عالية الجودة مصممة خصيصاً لتلبية احتياجات المطورين والمبرمجين. جميع أجهزتنا تأتي مع ضمان شامل وخدمة ما بعد البيع المتميزة.</p>
    
    <h3>🌟 آراء المطورين</h3>
    <blockquote style="border-left: 3px solid #667eea; padding-left: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">
        "أفضل حاسوب استخدمته للبرمجة! الأداء ممتاز والسرعة لا تصدق. أنصح به كل مطور."
        <cite style="display: block; margin-top: 10px; font-weight: bold;">- سارة أحمد، مطورة Full Stack</cite>
    </blockquote>
    
    <blockquote style="border-left: 3px solid #667eea; padding-left: 15px; margin: 20px 0; font-style: italic; background: #f8f9fa; padding: 15px;">
        "يتعامل مع أثقل بيئات التطوير بسهولة. Docker, VS Code, وعدة متصفحات في نفس الوقت بدون أي مشاكل."
        <cite style="display: block; margin-top: 10px; font-weight: bold;">- محمد علي، مطور Backend</cite>
    </blockquote>
    
    <h3>🛡️ الضمان والخدمات</h3>
    <ul>
        <li><strong>ضمان شامل:</strong> سنتان كاملتان</li>
        <li><strong>خدمة الصيانة:</strong> مجانية لمدة 6 أشهر</li>
        <li><strong>الدعم التقني:</strong> 24/7 عبر الهاتف والواتساب</li>
        <li><strong>التوصيل:</strong> مجاني لجميع أنحاء الجزائر</li>
    </ul>
    
    <h3>🎁 عرض خاص للمطورين</h3>
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center;">
        <h4 style="margin: 0 0 10px 0;">عرض محدود!</h4>
        <p style="margin: 0 0 10px 0;">السعر العادي: <s>10,999 دج</s></p>
        <p style="font-size: 1.5em; font-weight: bold; margin: 0;">السعر الحالي: 8,999 دج فقط!</p>
        <small>وفر 2,000 دج - العرض ساري حتى نفاد الكمية</small>
    </div>
    ';
    
    $stmt->execute([
        2, // produit_id for "حاسوب محمول للمطورين"
        'حاسوب محمول للمطورين - أداء استثنائي للبرمجة',
        $contenu_droit,
        $contenu_gauche,
        '/landing-page-template.php?id='
    ]);
    
    $landing_page_id = $conn->lastInsertId();
    
    // Add sample images for the laptop landing page
    $images = [
        '/images/default-laptop.jpg', // Main product image
        'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=800&h=600&fit=crop', // Laptop coding scene
        'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=800&h=600&fit=crop', // Developer workspace
        'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&h=600&fit=crop', // Code on screen
        'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=600&fit=crop', // Modern laptop setup
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO landing_page_images (landing_page_id, image_url, ordre) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($images as $index => $image_url) {
        $stmt->execute([$landing_page_id, $image_url, $index]);
    }
    
    // Update the lien_url with the actual landing page ID
    $stmt = $conn->prepare("UPDATE landing_pages SET lien_url = ? WHERE id = ?");
    $stmt->execute(['/landing-page-template.php?id=' . $landing_page_id, $landing_page_id]);
    
    echo "✅ Landing page pour laptop créée avec succès!<br>";
    echo "ID de la landing page: $landing_page_id<br>";
    echo "URL: <a href='/landing-page-template.php?id=$landing_page_id' target='_blank' style='color: #667eea; font-weight: bold;'>🚀 Voir la landing page</a><br>";
    echo "Images ajoutées: " . count($images) . "<br>";
    
    // Display the created landing page info
    echo "<br><div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📋 Détails de la landing page créée:</h3>";
    echo "<strong>Produit:</strong> حاسوب محمول للمطورين<br>";
    echo "<strong>Prix:</strong> 8,999 دج (au lieu de 10,999 دج)<br>";
    echo "<strong>Processeur:</strong> Intel Core i7<br>";
    echo "<strong>RAM:</strong> 16GB<br>";
    echo "<strong>Stockage:</strong> 512GB SSD<br>";
    echo "<strong>Images:</strong> " . count($images) . " images dans le carousel<br>";
    echo "<strong>Contenu:</strong> Spécifications techniques, témoignages, garanties<br>";
    echo "<strong>Fonctionnalités:</strong> Carousel automatique, design responsive, CTA optimisés<br>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la création de la landing page: " . $e->getMessage();
}
?>
