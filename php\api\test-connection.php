<?php

/**
 * Simple Connection Test API
 * Tests database connection and returns basic info
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Error handling
ini_set('display_errors', 0); // Don't display errors in JSON response
error_reporting(E_ALL);

try {
    // Simple database configuration (fallback if env-loader fails)
    $dbConfig = [
        'host' => 'localhost',
        'port' => '3307',
        'database' => 'mossab-landing-page',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4'
    ];

    // Try to load from .env if possible
    try {
        require_once __DIR__ . '/../config/env-loader.php';
        $dbConfig = EnvLoader::getDatabaseConfig();
    } catch (Exception $envError) {
        // Use fallback config
    }

    // Create PDO connection
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$dbConfig['charset']}"
    ]);

    // Test basic query (using server_time instead of current_time to avoid keyword conflict)
    $stmt = $pdo->query("SELECT 1 as test, NOW() as server_time");
    $result = $stmt->fetch();

    // Get database info
    $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as version");
    $dbInfo = $stmt->fetch();

    // Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    $rolesTableExists = $stmt->rowCount() > 0;

    $rolesCount = 0;
    if ($rolesTableExists) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM roles");
        $rolesCount = $stmt->fetch()['count'];
    }

    // Success response
    $response = [
        'success' => true,
        'message' => 'Database connection successful',
        'timestamp' => date('Y-m-d H:i:s'),
        'database_info' => [
            'name' => $dbInfo['db_name'],
            'version' => $dbInfo['version'],
            'host' => $dbConfig['host'],
            'port' => $dbConfig['port'],
            'configured_database' => $dbConfig['database']
        ],
        'test_query' => $result,
        // Note: 'current_time' alias changed to 'server_time' to avoid MariaDB keyword conflict
        'roles_table' => [
            'exists' => $rolesTableExists,
            'count' => $rolesCount
        ]
    ];

    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} catch (PDOException $e) {
    // Database connection error
    $response = [
        'success' => false,
        'message' => 'Database connection failed',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ];

    http_response_code(500);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} catch (Exception $e) {
    // General error
    $response = [
        'success' => false,
        'message' => 'An error occurred',
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ];

    http_response_code(500);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
