/**
 * Outil de Diagnostic Firestore 400 - Version Complète
 * Diagnostic et résolution automatique des erreurs Firestore
 * 
 * Fonctionnalités :
 * - Diagnostic en temps réel des erreurs 400
 * - Analyse des sessions WebChannel
 * - Validation de la configuration Firebase
 * - Tests de connectivité réseau
 * - Récupération automatique des erreurs
 * - Rapport détaillé des problèmes
 */

// Configuration du diagnostic
const DIAGNOSTIC_CONFIG = {
    ENABLE_REAL_TIME_MONITORING: true,
    ENABLE_AUTO_RECOVERY: true,
    ENABLE_DETAILED_LOGGING: true,
    MAX_RECOVERY_ATTEMPTS: 3,
    RECOVERY_DELAY: 2000, // 2 secondes
    HEALTH_CHECK_INTERVAL: 30000, // 30 secondes
    SESSION_TIMEOUT: 1800000 // 30 minutes
};

/**
 * Outil de diagnostic Firestore complet
 */
class FirestoreDiagnosticTool {
    constructor() {
        this.isInitialized = false;
        this.diagnosticResults = new Map();
        this.errorHistory = [];
        this.recoveryAttempts = 0;
        this.lastHealthCheck = null;
        this.sessionStartTime = Date.now();
        
        this.initializeDiagnostic();
    }

    /**
     * Initialise l'outil de diagnostic
     */
    async initializeDiagnostic() {
        console.log('🔍 Initialisation de l\'outil de diagnostic Firestore...');
        
        try {
            // Démarrer le monitoring en temps réel
            if (DIAGNOSTIC_CONFIG.ENABLE_REAL_TIME_MONITORING) {
                this.startRealTimeMonitoring();
            }
            
            // Effectuer un diagnostic initial
            await this.runCompleteDiagnostic();
            
            // Démarrer les vérifications de santé périodiques
            this.startHealthChecks();
            
            // Configurer la récupération automatique
            if (DIAGNOSTIC_CONFIG.ENABLE_AUTO_RECOVERY) {
                this.setupAutoRecovery();
            }
            
            this.isInitialized = true;
            console.log('✅ Outil de diagnostic Firestore initialisé avec succès');
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation du diagnostic:', error);
        }
    }

    /**
     * Démarre le monitoring en temps réel
     */
    startRealTimeMonitoring() {
        console.log('📡 Démarrage du monitoring en temps réel...');
        
        // Intercepter les requêtes fetch
        this.interceptFetchRequests();
        
        // Surveiller les erreurs WebSocket
        this.monitorWebSocketErrors();
        
        // Surveiller les erreurs de console
        this.monitorConsoleErrors();
        
        // Surveiller l'état du réseau
        this.monitorNetworkStatus();
    }

    /**
     * Intercepte les requêtes fetch pour détecter les erreurs 400
     */
    interceptFetchRequests() {
        const originalFetch = window.fetch;
        const self = this;
        
        window.fetch = function(url, options) {
            const startTime = performance.now();
            
            return originalFetch(url, options)
                .then(response => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    // Analyser les requêtes Firestore
                    if (typeof url === 'string' && url.includes('firestore.googleapis.com')) {
                        self.analyzeFirestoreRequest(url, response, duration, options);
                    }
                    
                    return response;
                })
                .catch(error => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    // Enregistrer l'erreur
                    if (typeof url === 'string' && url.includes('firestore.googleapis.com')) {
                        self.recordFirestoreError(url, error, duration, options);
                    }
                    
                    throw error;
                });
        };
    }

    /**
     * Analyse une requête Firestore
     */
    analyzeFirestoreRequest(url, response, duration, options) {
        const analysis = {
            timestamp: new Date().toISOString(),
            url: url,
            status: response.status,
            statusText: response.statusText,
            duration: duration,
            headers: this.extractHeaders(response.headers),
            method: options?.method || 'GET'
        };
        
        // Détecter les erreurs 400
        if (response.status === 400) {
            console.warn('🚨 Erreur 400 détectée:', analysis);
            this.handle400Error(analysis);
        }
        
        // Enregistrer l'analyse
        this.recordAnalysis('firestore_request', analysis);
        
        if (DIAGNOSTIC_CONFIG.ENABLE_DETAILED_LOGGING) {
            console.debug('📊 Analyse requête Firestore:', analysis);
        }
    }

    /**
     * Gère une erreur 400 spécifique
     */
    async handle400Error(analysis) {
        console.log('🔧 Traitement de l\'erreur 400...');
        
        // Analyser l'URL pour identifier le type d'erreur
        const errorType = this.identify400ErrorType(analysis.url);
        
        // Enregistrer l'erreur
        this.errorHistory.push({
            ...analysis,
            errorType: errorType,
            timestamp: Date.now()
        });
        
        // Déclencher la récupération automatique
        if (DIAGNOSTIC_CONFIG.ENABLE_AUTO_RECOVERY && this.recoveryAttempts < DIAGNOSTIC_CONFIG.MAX_RECOVERY_ATTEMPTS) {
            await this.attemptAutoRecovery(errorType, analysis);
        }
        
        // Générer un rapport d'erreur
        this.generateErrorReport(analysis, errorType);
    }

    /**
     * Identifie le type d'erreur 400
     */
    identify400ErrorType(url) {
        if (url.includes('/Listen/channel')) {
            return 'webchannel_session';
        } else if (url.includes('gsessionid=')) {
            return 'session_expired';
        } else if (url.includes('/documents/')) {
            return 'document_access';
        } else if (url.includes('/databases/')) {
            return 'database_config';
        } else {
            return 'unknown';
        }
    }

    /**
     * Tente une récupération automatique
     */
    async attemptAutoRecovery(errorType, analysis) {
        this.recoveryAttempts++;
        console.log(`🔄 Tentative de récupération automatique ${this.recoveryAttempts}/${DIAGNOSTIC_CONFIG.MAX_RECOVERY_ATTEMPTS} pour: ${errorType}`);
        
        try {
            switch (errorType) {
                case 'webchannel_session':
                case 'session_expired':
                    await this.resetFirebaseSession();
                    break;
                    
                case 'document_access':
                    await this.refreshDocumentAccess();
                    break;
                    
                case 'database_config':
                    await this.validateDatabaseConfig();
                    break;
                    
                default:
                    await this.performGeneralRecovery();
                    break;
            }
            
            // Attendre avant de continuer
            await new Promise(resolve => setTimeout(resolve, DIAGNOSTIC_CONFIG.RECOVERY_DELAY));
            
            console.log('✅ Récupération automatique terminée');
            
        } catch (error) {
            console.error('❌ Échec de la récupération automatique:', error);
        }
    }

    /**
     * Réinitialise la session Firebase
     */
    async resetFirebaseSession() {
        console.log('🔄 Réinitialisation de la session Firebase...');
        
        try {
            // Utiliser le gestionnaire d'erreurs existant si disponible
            if (window.firestoreErrorHandler) {
                await window.firestoreErrorHandler.resetFirebaseSession();
            } else {
                // Fallback : réinitialisation manuelle
                if (window.firebaseAuth && typeof window.firebaseAuth.signOut === 'function') {
                    await window.firebaseAuth.signOut();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // Reconnexion anonyme si possible
                    if (typeof window.firebaseAuth.signInAnonymously === 'function') {
                        await window.firebaseAuth.signInAnonymously();
                    }
                }
            }
            
            console.log('✅ Session Firebase réinitialisée');
            
        } catch (error) {
            console.error('❌ Erreur lors de la réinitialisation de session:', error);
            throw error;
        }
    }

    /**
     * Actualise l'accès aux documents
     */
    async refreshDocumentAccess() {
        console.log('🔄 Actualisation de l\'accès aux documents...');
        
        try {
            // Vider le cache Firestore si possible
            if (window.clearFirestoreCache && typeof window.clearFirestoreCache === 'function') {
                await window.clearFirestoreCache();
            }
            
            // Recharger le profil utilisateur
            if (window.firebaseAuth && window.firebaseAuth.currentUser) {
                const user = window.firebaseAuth.currentUser;
                if (typeof window.firebaseAuth.loadUserProfile === 'function') {
                    await window.firebaseAuth.loadUserProfile(user.uid);
                }
            }
            
            console.log('✅ Accès aux documents actualisé');
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'actualisation d\'accès:', error);
            throw error;
        }
    }

    /**
     * Valide la configuration de la base de données
     */
    async validateDatabaseConfig() {
        console.log('🔍 Validation de la configuration de la base de données...');
        
        try {
            // Vérifier la configuration Firebase
            const config = this.getFirebaseConfig();
            
            if (!config || !config.projectId) {
                throw new Error('Configuration Firebase manquante ou invalide');
            }
            
            console.log('📋 Configuration Firebase:', {
                projectId: config.projectId,
                authDomain: config.authDomain,
                databaseURL: config.databaseURL
            });
            
            // Tester la connectivité
            await this.testFirestoreConnectivity();
            
            console.log('✅ Configuration de la base de données validée');
            
        } catch (error) {
            console.error('❌ Erreur de validation de configuration:', error);
            throw error;
        }
    }

    /**
     * Effectue une récupération générale
     */
    async performGeneralRecovery() {
        console.log('🔄 Récupération générale en cours...');
        
        try {
            // Nettoyer le cache du navigateur
            await this.clearBrowserCache();
            
            // Réinitialiser les connexions réseau
            await this.resetNetworkConnections();
            
            // Recharger les scripts critiques
            await this.reloadCriticalScripts();
            
            console.log('✅ Récupération générale terminée');
            
        } catch (error) {
            console.error('❌ Erreur lors de la récupération générale:', error);
            throw error;
        }
    }

    /**
     * Surveille les erreurs WebSocket
     */
    monitorWebSocketErrors() {
        const originalWebSocket = window.WebSocket;
        const self = this;
        
        window.WebSocket = function(url, protocols) {
            const ws = new originalWebSocket(url, protocols);
            
            ws.addEventListener('error', (event) => {
                if (url.includes('firestore.googleapis.com')) {
                    console.warn('🔌 Erreur WebSocket Firestore détectée:', event);
                    self.recordAnalysis('websocket_error', {
                        timestamp: new Date().toISOString(),
                        url: url,
                        error: event.type,
                        readyState: ws.readyState
                    });
                }
            });
            
            ws.addEventListener('close', (event) => {
                if (url.includes('firestore.googleapis.com') && event.code !== 1000) {
                    console.warn('🔌 Fermeture WebSocket Firestore anormale:', event);
                    self.recordAnalysis('websocket_close', {
                        timestamp: new Date().toISOString(),
                        url: url,
                        code: event.code,
                        reason: event.reason
                    });
                }
            });
            
            return ws;
        };
    }

    /**
     * Surveille les erreurs de console
     */
    monitorConsoleErrors() {
        const self = this;
        
        window.addEventListener('error', (event) => {
            if (event.error && event.error.message.toLowerCase().includes('firestore')) {
                self.recordAnalysis('console_error', {
                    timestamp: new Date().toISOString(),
                    message: event.error.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error.stack
                });
            }
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            const error = event.reason;
            if (error && (error.code || error.message?.toLowerCase().includes('firestore'))) {
                self.recordAnalysis('unhandled_rejection', {
                    timestamp: new Date().toISOString(),
                    code: error.code,
                    message: error.message,
                    stack: error.stack
                });
            }
        });
    }

    /**
     * Surveille l'état du réseau
     */
    monitorNetworkStatus() {
        const self = this;
        
        window.addEventListener('online', () => {
            console.log('🌐 Connexion réseau rétablie');
            self.recordAnalysis('network_status', {
                timestamp: new Date().toISOString(),
                status: 'online',
                connectionType: navigator.connection?.effectiveType || 'unknown'
            });
            
            // Déclencher une vérification de santé
            setTimeout(() => self.runHealthCheck(), 2000);
        });
        
        window.addEventListener('offline', () => {
            console.warn('🌐 Connexion réseau perdue');
            self.recordAnalysis('network_status', {
                timestamp: new Date().toISOString(),
                status: 'offline'
            });
        });
        
        // Surveiller les changements de connexion
        if ('connection' in navigator) {
            navigator.connection.addEventListener('change', () => {
                const connection = navigator.connection;
                self.recordAnalysis('connection_change', {
                    timestamp: new Date().toISOString(),
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt,
                    saveData: connection.saveData
                });
            });
        }
    }

    /**
     * Démarre les vérifications de santé périodiques
     */
    startHealthChecks() {
        console.log('💓 Démarrage des vérifications de santé périodiques...');
        
        setInterval(() => {
            this.runHealthCheck();
        }, DIAGNOSTIC_CONFIG.HEALTH_CHECK_INTERVAL);
        
        // Première vérification immédiate
        setTimeout(() => this.runHealthCheck(), 5000);
    }

    /**
     * Effectue une vérification de santé
     */
    async runHealthCheck() {
        console.log('💓 Vérification de santé Firestore...');
        
        try {
            const healthStatus = {
                timestamp: new Date().toISOString(),
                sessionAge: Date.now() - this.sessionStartTime,
                errorCount: this.errorHistory.length,
                recoveryAttempts: this.recoveryAttempts,
                networkStatus: navigator.onLine,
                memoryUsage: this.getMemoryUsage()
            };
            
            // Tester la connectivité Firestore
            const connectivityTest = await this.testFirestoreConnectivity();
            healthStatus.firestoreConnectivity = connectivityTest;
            
            // Vérifier l'âge de la session
            if (healthStatus.sessionAge > DIAGNOSTIC_CONFIG.SESSION_TIMEOUT) {
                console.warn('⏰ Session Firebase expirée, réinitialisation recommandée');
                healthStatus.sessionExpired = true;
                
                if (DIAGNOSTIC_CONFIG.ENABLE_AUTO_RECOVERY) {
                    await this.resetFirebaseSession();
                }
            }
            
            this.lastHealthCheck = healthStatus;
            this.recordAnalysis('health_check', healthStatus);
            
            console.log('💓 Vérification de santé terminée:', healthStatus);
            
        } catch (error) {
            console.error('❌ Erreur lors de la vérification de santé:', error);
        }
    }

    /**
     * Teste la connectivité Firestore
     */
    async testFirestoreConnectivity() {
        try {
            // Test simple de connectivité
            const testUrl = 'https://firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel';
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);
            
            const response = await fetch(testUrl, {
                method: 'HEAD',
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            return {
                success: true,
                status: response.status,
                responseTime: performance.now()
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                type: error.name
            };
        }
    }

    /**
     * Effectue un diagnostic complet
     */
    async runCompleteDiagnostic() {
        console.log('🔍 Exécution du diagnostic complet...');
        
        const diagnostic = {
            timestamp: new Date().toISOString(),
            browser: this.getBrowserInfo(),
            network: this.getNetworkInfo(),
            firebase: await this.getFirebaseInfo(),
            performance: this.getPerformanceInfo(),
            errors: this.getErrorSummary()
        };
        
        this.recordAnalysis('complete_diagnostic', diagnostic);
        
        console.log('📊 Diagnostic complet terminé:', diagnostic);
        return diagnostic;
    }

    /**
     * Obtient les informations du navigateur
     */
    getBrowserInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            hardwareConcurrency: navigator.hardwareConcurrency,
            deviceMemory: navigator.deviceMemory || 'unknown'
        };
    }

    /**
     * Obtient les informations réseau
     */
    getNetworkInfo() {
        const info = {
            onLine: navigator.onLine,
            connectionType: 'unknown',
            effectiveType: 'unknown',
            downlink: 'unknown',
            rtt: 'unknown',
            saveData: false
        };
        
        if ('connection' in navigator) {
            const connection = navigator.connection;
            info.connectionType = connection.type || 'unknown';
            info.effectiveType = connection.effectiveType || 'unknown';
            info.downlink = connection.downlink || 'unknown';
            info.rtt = connection.rtt || 'unknown';
            info.saveData = connection.saveData || false;
        }
        
        return info;
    }

    /**
     * Obtient les informations Firebase
     */
    async getFirebaseInfo() {
        const info = {
            configLoaded: false,
            authInitialized: false,
            firestoreInitialized: false,
            currentUser: null,
            projectId: null
        };
        
        try {
            // Vérifier la configuration
            const config = this.getFirebaseConfig();
            if (config) {
                info.configLoaded = true;
                info.projectId = config.projectId;
            }
            
            // Vérifier l'authentification
            if (window.firebaseAuth) {
                info.authInitialized = true;
                if (window.firebaseAuth.currentUser) {
                    info.currentUser = {
                        uid: window.firebaseAuth.currentUser.uid,
                        email: window.firebaseAuth.currentUser.email,
                        isAnonymous: window.firebaseAuth.currentUser.isAnonymous
                    };
                }
            }
            
            // Vérifier Firestore
            if (window.db || window.firestore) {
                info.firestoreInitialized = true;
            }
            
        } catch (error) {
            info.error = error.message;
        }
        
        return info;
    }

    /**
     * Obtient les informations de performance
     */
    getPerformanceInfo() {
        const info = {
            memoryUsage: this.getMemoryUsage(),
            timing: {},
            navigation: {}
        };
        
        if ('performance' in window) {
            const perf = performance;
            
            if (perf.timing) {
                info.timing = {
                    domContentLoaded: perf.timing.domContentLoadedEventEnd - perf.timing.navigationStart,
                    loadComplete: perf.timing.loadEventEnd - perf.timing.navigationStart,
                    domInteractive: perf.timing.domInteractive - perf.timing.navigationStart
                };
            }
            
            if (perf.navigation) {
                info.navigation = {
                    type: perf.navigation.type,
                    redirectCount: perf.navigation.redirectCount
                };
            }
        }
        
        return info;
    }

    /**
     * Obtient l'utilisation mémoire
     */
    getMemoryUsage() {
        if ('memory' in performance) {
            return {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            };
        }
        return null;
    }

    /**
     * Obtient un résumé des erreurs
     */
    getErrorSummary() {
        const summary = {
            totalErrors: this.errorHistory.length,
            errorTypes: {},
            recentErrors: this.errorHistory.slice(-5),
            recoveryAttempts: this.recoveryAttempts
        };
        
        // Compter les types d'erreurs
        this.errorHistory.forEach(error => {
            const type = error.errorType || 'unknown';
            summary.errorTypes[type] = (summary.errorTypes[type] || 0) + 1;
        });
        
        return summary;
    }

    /**
     * Obtient la configuration Firebase
     */
    getFirebaseConfig() {
        // Essayer différentes méthodes pour obtenir la config
        if (window.firebaseConfig) {
            return window.firebaseConfig;
        }
        
        if (window.firebase && window.firebase.app) {
            try {
                return window.firebase.app().options;
            } catch (error) {
                console.debug('Impossible d\'obtenir la config via firebase.app():', error.message);
            }
        }
        
        return null;
    }

    /**
     * Extrait les en-têtes de réponse
     */
    extractHeaders(headers) {
        const extracted = {};
        
        if (headers && typeof headers.forEach === 'function') {
            headers.forEach((value, key) => {
                extracted[key] = value;
            });
        }
        
        return extracted;
    }

    /**
     * Enregistre une analyse
     */
    recordAnalysis(type, data) {
        if (!this.diagnosticResults.has(type)) {
            this.diagnosticResults.set(type, []);
        }
        
        const results = this.diagnosticResults.get(type);
        results.push(data);
        
        // Garder seulement les 50 derniers résultats
        if (results.length > 50) {
            results.splice(0, results.length - 50);
        }
    }

    /**
     * Génère un rapport d'erreur
     */
    generateErrorReport(analysis, errorType) {
        const report = {
            timestamp: new Date().toISOString(),
            errorType: errorType,
            analysis: analysis,
            context: {
                sessionAge: Date.now() - this.sessionStartTime,
                totalErrors: this.errorHistory.length,
                recoveryAttempts: this.recoveryAttempts,
                networkStatus: navigator.onLine
            },
            recommendations: this.getRecommendations(errorType)
        };
        
        console.warn('📋 Rapport d\'erreur Firestore 400:', report);
        
        // Envoyer le rapport si un gestionnaire est défini
        if (typeof window.onFirestoreErrorReport === 'function') {
            window.onFirestoreErrorReport(report);
        }
        
        return report;
    }

    /**
     * Obtient des recommandations basées sur le type d'erreur
     */
    getRecommendations(errorType) {
        const recommendations = {
            webchannel_session: [
                'Vérifier la stabilité de la connexion réseau',
                'Réinitialiser la session Firebase',
                'Activer le mode long polling',
                'Vérifier les règles de sécurité Firestore'
            ],
            session_expired: [
                'Renouveler le token d\'authentification',
                'Vérifier la configuration de l\'authentification',
                'Implémenter un rafraîchissement automatique des tokens'
            ],
            document_access: [
                'Vérifier les permissions d\'accès aux documents',
                'Valider les règles de sécurité Firestore',
                'Vérifier l\'authentification de l\'utilisateur'
            ],
            database_config: [
                'Valider la configuration Firebase',
                'Vérifier l\'ID du projet',
                'Contrôler les paramètres de la base de données'
            ],
            unknown: [
                'Analyser les logs détaillés',
                'Vérifier la connectivité réseau',
                'Redémarrer l\'application'
            ]
        };
        
        return recommendations[errorType] || recommendations.unknown;
    }

    /**
     * Configure la récupération automatique
     */
    setupAutoRecovery() {
        console.log('🔧 Configuration de la récupération automatique...');
        
        // Réinitialiser le compteur de tentatives périodiquement
        setInterval(() => {
            if (this.recoveryAttempts > 0) {
                this.recoveryAttempts = Math.max(0, this.recoveryAttempts - 1);
                console.log(`🔄 Compteur de récupération réduit: ${this.recoveryAttempts}`);
            }
        }, 60000); // Toutes les minutes
    }

    /**
     * Nettoie le cache du navigateur
     */
    async clearBrowserCache() {
        try {
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                await Promise.all(
                    cacheNames.map(name => caches.delete(name))
                );
                console.log('🧹 Cache du navigateur nettoyé');
            }
        } catch (error) {
            console.debug('Erreur lors du nettoyage du cache:', error.message);
        }
    }

    /**
     * Réinitialise les connexions réseau
     */
    async resetNetworkConnections() {
        try {
            // Fermer toutes les connexions WebSocket ouvertes
            if (window.openWebSockets) {
                window.openWebSockets.forEach(ws => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.close();
                    }
                });
            }
            
            console.log('🔌 Connexions réseau réinitialisées');
        } catch (error) {
            console.debug('Erreur lors de la réinitialisation réseau:', error.message);
        }
    }

    /**
     * Recharge les scripts critiques
     */
    async reloadCriticalScripts() {
        try {
            // Liste des scripts critiques à recharger
            const criticalScripts = [
                'firebase-config.js',
                'firestore-400-fix.js'
            ];
            
            for (const scriptName of criticalScripts) {
                const existingScript = document.querySelector(`script[src*="${scriptName}"]`);
                if (existingScript) {
                    const newScript = document.createElement('script');
                    newScript.src = existingScript.src + '?t=' + Date.now();
                    newScript.type = existingScript.type || 'text/javascript';
                    
                    document.head.appendChild(newScript);
                    console.log(`🔄 Script rechargé: ${scriptName}`);
                }
            }
        } catch (error) {
            console.debug('Erreur lors du rechargement des scripts:', error.message);
        }
    }

    /**
     * Obtient un rapport de diagnostic complet
     */
    getFullDiagnosticReport() {
        return {
            timestamp: new Date().toISOString(),
            isInitialized: this.isInitialized,
            sessionAge: Date.now() - this.sessionStartTime,
            errorHistory: this.errorHistory,
            recoveryAttempts: this.recoveryAttempts,
            lastHealthCheck: this.lastHealthCheck,
            diagnosticResults: Object.fromEntries(this.diagnosticResults),
            recommendations: this.getGeneralRecommendations()
        };
    }

    /**
     * Obtient des recommandations générales
     */
    getGeneralRecommendations() {
        const recommendations = [];
        
        if (this.errorHistory.length > 10) {
            recommendations.push('Nombre élevé d\'erreurs détecté - vérifier la configuration');
        }
        
        if (this.recoveryAttempts > 5) {
            recommendations.push('Nombreuses tentatives de récupération - problème persistant');
        }
        
        if (Date.now() - this.sessionStartTime > DIAGNOSTIC_CONFIG.SESSION_TIMEOUT) {
            recommendations.push('Session longue - redémarrage recommandé');
        }
        
        if (!navigator.onLine) {
            recommendations.push('Connexion réseau hors ligne');
        }
        
        return recommendations;
    }
}

// Initialisation automatique
let firestoreDiagnosticTool;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        firestoreDiagnosticTool = new FirestoreDiagnosticTool();
    });
} else {
    firestoreDiagnosticTool = new FirestoreDiagnosticTool();
}

// Export global
window.FirestoreDiagnosticTool = FirestoreDiagnosticTool;
window.firestoreDiagnosticTool = firestoreDiagnosticTool;

// Fonctions utilitaires globales
window.getFirestoreDiagnostic = () => {
    return firestoreDiagnosticTool ? firestoreDiagnosticTool.getFullDiagnosticReport() : null;
};

window.runFirestoreHealthCheck = () => {
    return firestoreDiagnosticTool ? firestoreDiagnosticTool.runHealthCheck() : null;
};

window.resetFirestoreSession = () => {
    return firestoreDiagnosticTool ? firestoreDiagnosticTool.resetFirebaseSession() : null;
};

console.log('🔍 Outil de diagnostic Firestore 400 chargé');