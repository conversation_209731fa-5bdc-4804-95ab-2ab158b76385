/**
 * Error Monitoring - Surveillance et rapport d'erreurs
 * Monitore l'état de l'application après les corrections
 */

(function() {
    'use strict';
    
    class ErrorMonitor {
        constructor() {
            this.errors = [];
            this.warnings = [];
            this.fixes = [];
            this.startTime = Date.now();
            this.isMonitoring = false;
            
            this.init();
        }
        
        init() {
            this.setupErrorCapture();
            this.setupPerformanceMonitoring();
            this.setupHealthChecks();
            this.startMonitoring();
            
            console.log('📊 Error Monitor initialized');
        }
        
        // 1. Capture des erreurs
        setupErrorCapture() {
            // Erreurs JavaScript non capturées
            window.addEventListener('error', (event) => {
                this.logError({
                    type: 'javascript',
                    message: event.message,
                    filename: event.filename,
                    line: event.lineno,
                    column: event.colno,
                    stack: event.error ? event.error.stack : null,
                    timestamp: Date.now()
                });
            });
            
            // Promesses rejetées non capturées
            window.addEventListener('unhandledrejection', (event) => {
                const error = event.reason;
                
                // Filtrer les erreurs Firebase connues
                if (this.isKnownFirebaseError(error)) {
                    this.logFix({
                        type: 'firebase_handled',
                        message: `Firebase error handled: ${error.message}`,
                        timestamp: Date.now()
                    });
                    event.preventDefault();
                    return;
                }
                
                this.logError({
                    type: 'promise_rejection',
                    message: error.message || error.toString(),
                    stack: error.stack,
                    timestamp: Date.now()
                });
            });
            
            // Erreurs de ressources (images, scripts, etc.)
            window.addEventListener('error', (event) => {
                if (event.target !== window) {
                    this.logError({
                        type: 'resource',
                        message: `Failed to load: ${event.target.src || event.target.href}`,
                        element: event.target.tagName,
                        timestamp: Date.now()
                    });
                }
            }, true);
        }
        
        // 2. Surveillance des performances
        setupPerformanceMonitoring() {
            // Observer les métriques de performance
            if ('PerformanceObserver' in window) {
                try {
                    const observer = new PerformanceObserver((list) => {
                        for (const entry of list.getEntries()) {
                            if (entry.entryType === 'navigation') {
                                this.logPerformance({
                                    type: 'navigation',
                                    loadTime: entry.loadEventEnd - entry.loadEventStart,
                                    domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                                    timestamp: Date.now()
                                });
                            }
                        }
                    });
                    
                    observer.observe({ entryTypes: ['navigation', 'resource'] });
                } catch (error) {
                    console.warn('Performance monitoring not available:', error);
                }
            }
        }
        
        // 3. Vérifications de santé
        setupHealthChecks() {
            setInterval(() => {
                this.performHealthCheck();
            }, 60000); // Toutes les minutes
        }
        
        performHealthCheck() {
            const health = {
                timestamp: Date.now(),
                memory: this.getMemoryUsage(),
                firebase: this.checkFirebaseHealth(),
                dom: this.checkDOMHealth(),
                network: navigator.onLine
            };
            
            this.logHealth(health);
        }
        
        getMemoryUsage() {
            if ('memory' in performance) {
                return {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };
            }
            return null;
        }
        
        checkFirebaseHealth() {
            const status = {
                config: !!window.firebase,
                auth: !!window.auth,
                firestore: !!window.db,
                enhancedManager: !!window.firebaseEnhancedManager
            };
            
            if (window.firebaseEnhancedManager) {
                Object.assign(status, window.firebaseEnhancedManager.getStatus());
            }
            
            return status;
        }
        
        checkDOMHealth() {
            return {
                elements: document.querySelectorAll('*').length,
                scripts: document.querySelectorAll('script').length,
                stylesheets: document.querySelectorAll('link[rel="stylesheet"]').length,
                images: document.querySelectorAll('img').length
            };
        }
        
        // 4. Filtrage des erreurs connues
        isKnownFirebaseError(error) {
            if (!error || !error.message) return false;
            
            const knownErrors = [
                'Failed to fetch',
                'NetworkError',
                'ERR_ABORTED',
                'Could not reach Cloud Firestore backend',
                'Failed to get document from cache',
                'quota exceeded',
                'permission-denied'
            ];
            
            return knownErrors.some(known => 
                error.message.toLowerCase().includes(known.toLowerCase())
            );
        }
        
        // 5. Logging des événements
        logError(error) {
            this.errors.push(error);
            console.error('🚨 Error captured:', error);
            
            // Limiter le nombre d'erreurs stockées
            if (this.errors.length > 100) {
                this.errors = this.errors.slice(-50);
            }
        }
        
        logWarning(warning) {
            this.warnings.push(warning);
            console.warn('⚠️ Warning captured:', warning);
            
            if (this.warnings.length > 50) {
                this.warnings = this.warnings.slice(-25);
            }
        }
        
        logFix(fix) {
            this.fixes.push(fix);
            console.log('✅ Fix applied:', fix);
            
            if (this.fixes.length > 50) {
                this.fixes = this.fixes.slice(-25);
            }
        }
        
        logPerformance(perf) {
            console.log('📈 Performance:', perf);
        }
        
        logHealth(health) {
            console.log('💚 Health check:', health);
        }
        
        // 6. Surveillance active
        startMonitoring() {
            this.isMonitoring = true;
            
            // Rapport initial après 5 secondes
            setTimeout(() => {
                this.generateReport();
            }, 5000);
            
            // Rapports périodiques toutes les 5 minutes
            setInterval(() => {
                this.generateReport();
            }, 300000);
        }
        
        // 7. Génération de rapports
        generateReport() {
            const uptime = Date.now() - this.startTime;
            const report = {
                timestamp: new Date().toISOString(),
                uptime: Math.round(uptime / 1000),
                errors: this.errors.length,
                warnings: this.warnings.length,
                fixes: this.fixes.length,
                recentErrors: this.errors.slice(-5),
                recentFixes: this.fixes.slice(-5),
                firebase: this.checkFirebaseHealth(),
                memory: this.getMemoryUsage()
            };
            
            console.group('📊 Error Monitor Report');
            console.log('⏱️ Uptime:', Math.round(uptime / 1000), 'seconds');
            console.log('🚨 Total Errors:', this.errors.length);
            console.log('⚠️ Total Warnings:', this.warnings.length);
            console.log('✅ Total Fixes:', this.fixes.length);
            
            if (this.errors.length > 0) {
                console.log('🔍 Recent Errors:', this.errors.slice(-3));
            }
            
            if (this.fixes.length > 0) {
                console.log('🛠️ Recent Fixes:', this.fixes.slice(-3));
            }
            
            console.log('🔥 Firebase Status:', report.firebase);
            
            if (report.memory) {
                console.log('💾 Memory Usage:', `${report.memory.used}MB / ${report.memory.total}MB`);
            }
            
            console.groupEnd();
            
            // Stocker le rapport pour accès externe
            window._errorMonitorReport = report;
            
            return report;
        }
        
        // 8. API publique
        getErrors() {
            return [...this.errors];
        }
        
        getWarnings() {
            return [...this.warnings];
        }
        
        getFixes() {
            return [...this.fixes];
        }
        
        clearLogs() {
            this.errors = [];
            this.warnings = [];
            this.fixes = [];
            console.log('🗑️ Error logs cleared');
        }
        
        getStatus() {
            return {
                isMonitoring: this.isMonitoring,
                uptime: Date.now() - this.startTime,
                errorCount: this.errors.length,
                warningCount: this.warnings.length,
                fixCount: this.fixes.length
            };
        }
    }
    
    // Initialisation
    if (!window.errorMonitor) {
        window.errorMonitor = new ErrorMonitor();
    }
    
    // API globale
    window.getErrorReport = () => {
        return window.errorMonitor.generateReport();
    };
    
    window.clearErrorLogs = () => {
        window.errorMonitor.clearLogs();
    };
    
    window.getErrorStatus = () => {
        return window.errorMonitor.getStatus();
    };
    
})();

console.log('📊 Error Monitoring system loaded');