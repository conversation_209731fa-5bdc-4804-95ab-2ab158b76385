<?php

/**
 * Environment Configuration Management
 * Handles different environments (development, staging, production)
 * Part of DevOps implementation for Mossaab Landing Page
 */

class EnvironmentConfig
{
    private static $instance = null;
    private $environment;
    private $config;

    private function __construct()
    {
        $this->detectEnvironment();
        $this->loadConfiguration();
        $this->validateConfiguration();
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Detect current environment
     */
    private function detectEnvironment()
    {
        // Check environment variable first
        $env = $_ENV['APP_ENV'] ?? $_SERVER['APP_ENV'] ?? null;

        if ($env) {
            $this->environment = $env;
            return;
        }

        // Auto-detect based on domain/server characteristics
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';

        if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
            $this->environment = 'development';
        } elseif (strpos($host, 'staging') !== false || strpos($host, 'test') !== false) {
            $this->environment = 'staging';
        } else {
            $this->environment = 'production';
        }
    }

    /**
     * Load configuration based on environment
     */
    private function loadConfiguration()
    {
        $baseConfig = [
            'app' => [
                'name' => 'Mossaab Landing Page',
                'version' => '1.2.0',
                'timezone' => 'Africa/Algiers',
                'locale' => 'ar_DZ',
                'charset' => 'utf8mb4'
            ],
            'security' => [
                'session_lifetime' => 3600, // 1 hour
                'csrf_protection' => true,
                'xss_protection' => true,
                'rate_limiting' => true
            ],
            'features' => [
                'debug_mode' => false,
                'maintenance_mode' => false,
                'api_logging' => true,
                'error_reporting' => false
            ]
        ];

        // Environment-specific configurations
        switch ($this->environment) {
            case 'development':
                $this->config = array_merge_recursive($baseConfig, [
                    'database' => [
                        'host' => 'localhost',
                        'port' => 3307,
                        'name' => 'mossab-landing-page',
                        'username' => 'root',
                        'password' => '',
                        'charset' => 'utf8mb4',
                        'options' => [
                            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                            PDO::ATTR_EMULATE_PREPARES => false
                        ]
                    ],
                    'features' => [
                        'debug_mode' => true,
                        'error_reporting' => true,
                        'api_logging' => true,
                        'detailed_errors' => true
                    ],
                    'logging' => [
                        'level' => 'DEBUG',
                        'file' => 'logs/app-dev.log',
                        'max_files' => 10
                    ],
                    'cache' => [
                        'enabled' => false,
                        'ttl' => 300
                    ]
                ]);
                break;

            case 'staging':
                $this->config = array_merge_recursive($baseConfig, [
                    'database' => [
                        'host' => $_ENV['DB_HOST'] ?? 'localhost',
                        'port' => $_ENV['DB_PORT'] ?? 3306,
                        'name' => $_ENV['DB_NAME'] ?? 'poultraydz_staging',
                        'username' => $_ENV['DB_USER'] ?? 'staging_user',
                        'password' => $_ENV['DB_PASS'] ?? '',
                        'charset' => 'utf8mb4',
                        'options' => [
                            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                            PDO::ATTR_EMULATE_PREPARES => false
                        ]
                    ],
                    'features' => [
                        'debug_mode' => false,
                        'error_reporting' => false,
                        'api_logging' => true,
                        'detailed_errors' => false
                    ],
                    'logging' => [
                        'level' => 'INFO',
                        'file' => 'logs/app-staging.log',
                        'max_files' => 30
                    ],
                    'cache' => [
                        'enabled' => true,
                        'ttl' => 600
                    ]
                ]);
                break;

            case 'production':
                $this->config = array_merge_recursive($baseConfig, [
                    'database' => [
                        'host' => $_ENV['DB_HOST'] ?? 'localhost',
                        'port' => $_ENV['DB_PORT'] ?? 3306,
                        'name' => $_ENV['DB_NAME'] ?? 'poultraydz_prod',
                        'username' => $_ENV['DB_USER'] ?? 'prod_user',
                        'password' => $_ENV['DB_PASS'] ?? '',
                        'charset' => 'utf8mb4',
                        'options' => [
                            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                            PDO::ATTR_EMULATE_PREPARES => false
                        ]
                    ],
                    'features' => [
                        'debug_mode' => false,
                        'error_reporting' => false,
                        'api_logging' => false,
                        'detailed_errors' => false
                    ],
                    'logging' => [
                        'level' => 'ERROR',
                        'file' => 'logs/app-prod.log',
                        'max_files' => 90
                    ],
                    'cache' => [
                        'enabled' => true,
                        'ttl' => 3600
                    ],
                    'security' => [
                        'session_lifetime' => 1800, // 30 minutes
                        'rate_limiting' => true,
                        'strict_mode' => true
                    ]
                ]);
                break;
        }
    }

    /**
     * Validate configuration
     */
    private function validateConfiguration()
    {
        $required = [
            'database.host',
            'database.name',
            'database.username',
            'app.name'
        ];

        foreach ($required as $key) {
            if (!$this->get($key)) {
                throw new Exception("Required configuration key '$key' is missing");
            }
        }

        // Validate database connection in non-production environments
        if ($this->environment !== 'production') {
            $this->validateDatabaseConnection();
        }
    }

    /**
     * Validate database connection
     */
    private function validateDatabaseConnection()
    {
        try {
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                $this->get('database.host'),
                $this->get('database.port'),
                $this->get('database.name'),
                $this->get('database.charset')
            );

            $pdo = new PDO(
                $dsn,
                $this->get('database.username'),
                $this->get('database.password'),
                $this->get('database.options')
            );

            // Test connection
            $pdo->query('SELECT 1');
        } catch (Exception $e) {
            if ($this->get('features.debug_mode')) {
                throw new Exception("Database connection failed: " . $e->getMessage());
            } else {
                error_log("Database connection validation failed: " . $e->getMessage());
            }
        }
    }

    /**
     * Get configuration value using dot notation
     */
    public function get($key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Get current environment
     */
    public function getEnvironment()
    {
        return $this->environment;
    }

    /**
     * Check if in development environment
     */
    public function isDevelopment()
    {
        return $this->environment === 'development';
    }

    /**
     * Check if in staging environment
     */
    public function isStaging()
    {
        return $this->environment === 'staging';
    }

    /**
     * Check if in production environment
     */
    public function isProduction()
    {
        return $this->environment === 'production';
    }

    /**
     * Get all configuration
     */
    public function getAll()
    {
        return $this->config;
    }

    /**
     * Set timezone
     */
    public function setTimezone()
    {
        date_default_timezone_set($this->get('app.timezone'));
    }

    /**
     * Configure error reporting
     */
    public function configureErrorReporting()
    {
        if ($this->get('features.error_reporting')) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
            ini_set('display_startup_errors', 1);
        } else {
            error_reporting(0);
            ini_set('display_errors', 0);
            ini_set('display_startup_errors', 0);
        }
    }

    /**
     * Get database PDO connection
     */
    public function getDatabaseConnection()
    {
        static $pdo = null;

        if ($pdo === null) {
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                $this->get('database.host'),
                $this->get('database.port'),
                $this->get('database.name'),
                $this->get('database.charset')
            );

            $pdo = new PDO(
                $dsn,
                $this->get('database.username'),
                $this->get('database.password'),
                $this->get('database.options')
            );
        }

        return $pdo;
    }
}

// Initialize environment configuration
$env = EnvironmentConfig::getInstance();
$env->setTimezone();
$env->configureErrorReporting();

// Make available globally
function env($key, $default = null)
{
    return EnvironmentConfig::getInstance()->get($key, $default);
}

function isDevelopment()
{
    return EnvironmentConfig::getInstance()->isDevelopment();
}

function isProduction()
{
    return EnvironmentConfig::getInstance()->isProduction();
}

function getEnvironment()
{
    return EnvironmentConfig::getInstance()->getEnvironment();
}
