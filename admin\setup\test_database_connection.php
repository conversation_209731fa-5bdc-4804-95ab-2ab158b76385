<?php
/**
 * Test Database Connection
 * اختبار الاتصال بقاعدة البيانات
 */

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>\n";

try {
    // Test configuration loading
    echo "<h3>1. اختبار تحميل الإعدادات:</h3>\n";
    
    $envFile = '../../.env';
    if (!file_exists($envFile)) {
        throw new Exception("ملف .env غير موجود في: $envFile");
    }
    echo "<p style='color: green;'>✅ ملف .env موجود</p>\n";
    
    // Load configuration manually
    $config = [];
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($key, $value) = explode('=', $line, 2) + [NULL, NULL];
        if (!empty($key)) {
            $config[trim($key)] = trim($value ?? '');
        }
    }
    
    echo "<p style='color: green;'>✅ تم تحميل الإعدادات</p>\n";
    
    // Check required database settings
    $required = ['DB_HOST', 'DB_PORT', 'DB_USERNAME', 'DB_DATABASE'];
    $missing = [];
    foreach ($required as $key) {
        if (empty($config[$key])) {
            $missing[] = $key;
        }
    }
    
    if (!empty($missing)) {
        throw new Exception('إعدادات قاعدة البيانات المفقودة: ' . implode(', ', $missing));
    }
    
    echo "<p style='color: green;'>✅ جميع إعدادات قاعدة البيانات المطلوبة موجودة</p>\n";
    
    // Display database configuration (without password)
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>إعدادات قاعدة البيانات:</strong><br>";
    echo "Host: " . $config['DB_HOST'] . "<br>";
    echo "Port: " . $config['DB_PORT'] . "<br>";
    echo "Database: " . $config['DB_DATABASE'] . "<br>";
    echo "Username: " . $config['DB_USERNAME'] . "<br>";
    echo "Password: " . (empty($config['DB_PASSWORD']) ? 'غير محدد' : '***') . "<br>";
    echo "</div>\n";
    
    // Test database connection
    echo "<h3>2. اختبار الاتصال بقاعدة البيانات:</h3>\n";
    
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $config['DB_HOST'],
        $config['DB_PORT'],
        $config['DB_DATABASE']
    );
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, $config['DB_USERNAME'], $config['DB_PASSWORD'] ?? '', $options);
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    
    // Test database operations
    echo "<h3>3. اختبار عمليات قاعدة البيانات:</h3>\n";
    
    // Test SELECT
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch();
    echo "<p style='color: green;'>✅ إصدار قاعدة البيانات: " . $version['version'] . "</p>\n";
    
    // Test SHOW TABLES
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p style='color: green;'>✅ عدد الجداول الموجودة: " . count($tables) . "</p>\n";
    
    if (!empty($tables)) {
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>الجداول الموجودة:</strong><br>";
        foreach ($tables as $table) {
            echo "- $table<br>";
        }
        echo "</div>\n";
    }
    
    // Check if our tables exist
    echo "<h3>4. التحقق من جداول الإعدادات العامة:</h3>\n";
    
    $ourTables = ['general_settings', 'settings_history'];
    $existingTables = [];
    $missingTables = [];
    
    foreach ($ourTables as $table) {
        if (in_array($table, $tables)) {
            $existingTables[] = $table;
            echo "<p style='color: green;'>✅ جدول $table موجود</p>\n";
        } else {
            $missingTables[] = $table;
            echo "<p style='color: orange;'>⚠️ جدول $table غير موجود</p>\n";
        }
    }
    
    if (!empty($missingTables)) {
        echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 5px;'>";
        echo "<strong>الجداول المفقودة:</strong><br>";
        foreach ($missingTables as $table) {
            echo "- $table<br>";
        }
        echo "<br><strong>الحل:</strong> قم بتشغيل سكريپت إنشاء الجداول<br>";
        echo "<a href='create_general_settings_tables.php' style='background: #667eea; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>إنشاء الجداول</a>";
        echo "</div>\n";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<strong>✅ جميع الجداول المطلوبة موجودة!</strong><br>";
        echo "يمكنك الآن استخدام قسم الإعدادات العامة في لوحة الإدارة.";
        echo "</div>\n";
    }
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 5px;'>";
    echo "<strong>✅ اختبار الاتصال مكتمل بنجاح!</strong><br>";
    echo "قاعدة البيانات تعمل بشكل صحيح ويمكن الوصول إليها.";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0;'>";
    echo "❌ خطأ في الاتصال: " . $e->getMessage();
    echo "</div>\n";
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #ffe7e7; border: 1px solid #ffb3b3; border-radius: 5px;'>";
    echo "<strong>خطوات حل المشكلة:</strong><br>";
    echo "1. تأكد من تشغيل خادم قاعدة البيانات (MySQL/MariaDB)<br>";
    echo "2. تأكد من صحة إعدادات قاعدة البيانات في ملف .env<br>";
    echo "3. تأكد من وجود قاعدة البيانات المحددة<br>";
    echo "4. تأكد من صحة اسم المستخدم وكلمة المرور<br>";
    echo "5. تأكد من أن المنفذ صحيح (عادة 3306)<br>";
    echo "</div>\n";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بقاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
            margin-top: 25px;
        }
        p {
            margin: 8px 0;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="../index.html" class="back-link">← العودة إلى لوحة الإدارة</a>
    </div>
</body>
</html>
