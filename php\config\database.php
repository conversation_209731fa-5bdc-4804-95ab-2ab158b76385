<?php

/**
 * Database Configuration
 * Uses the centralized config system
 */

require_once __DIR__ . '/config.php';

try {
    $config = Config::getInstance();
    $dbConfig = $config->getDatabaseConfig();

    // Extract database configuration
    $host = $dbConfig['host'];
    $port = $dbConfig['port'];
    $dbname = $dbConfig['name'];
    $username = $dbConfig['user'];
    $password = $dbConfig['pass'];
    $charset = $dbConfig['charset'];

    // Fallback to direct environment variables if config fails
    if (empty($dbname)) {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3307';
        $dbname = $_ENV['DB_DATABASE'] ?? 'mossab-landing-page';
        $username = $_ENV['DB_USERNAME'] ?? 'root';
        $password = $_ENV['DB_PASSWORD'] ?? '';
        $charset = $_ENV['DB_CHARSET'] ?? 'utf8mb4';
    }
} catch (Exception $e) {
    // Emergency fallback configuration
    $host = 'localhost';
    $port = '3307';
    $dbname = 'mossab-landing-page';
    $username = 'root';
    $password = '';
    $charset = 'utf8mb4';

    error_log("Database config error: " . $e->getMessage());
}

// Validate required values
if (empty($dbname) || empty($username)) {
    throw new Exception('Database configuration is incomplete. Please check your .env file.');
}

// Create DSN
$dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset={$charset}";

// PDO options
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$charset} COLLATE {$charset}_unicode_ci"
];

// Test connection function
function testDatabaseConnection()
{
    global $dsn, $username, $password, $options;

    try {
        $pdo = new PDO($dsn, $username, $password, $options);
        return [
            'success' => true,
            'message' => 'Database connection successful',
            'host' => $GLOBALS['host'],
            'port' => $GLOBALS['port'],
            'database' => $GLOBALS['dbname']
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Database connection failed: ' . $e->getMessage(),
            'host' => $GLOBALS['host'],
            'port' => $GLOBALS['port'],
            'database' => $GLOBALS['dbname']
        ];
    }
}

// Create connection function
function getDatabaseConnection()
{
    global $dsn, $username, $password, $options;

    try {
        $pdo = new PDO($dsn, $username, $password, $options);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

// Alias for backward compatibility
function getPDOConnection()
{
    return getDatabaseConnection();
}
