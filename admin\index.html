<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <!-- Critical CSS for initial render -->
    <style>
      body {
        visibility: hidden;
      }
      .content-loaded {
        visibility: visible;
      }
      #loading-indicator {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        text-align: center;
        visibility: visible;
      }
      #loading-indicator .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Enhanced Admin Profile Dropdown Styles */
      .admin-profile-button {
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        padding: 12px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
      }

      .admin-profile-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .admin-profile-button:hover {
        background: linear-gradient(135deg, #f0f4ff 0%, #faf5ff 100%);
        border-color: #667eea;
        box-shadow: 0 8px 16px rgba(102, 126, 234, 0.2);
        transform: translateY(-2px);
      }

      .admin-profile-button:hover::before {
        opacity: 1;
      }

      .admin-profile-button:active {
        transform: translateY(0);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
      }

      .admin-profile-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        min-width: 250px;
        z-index: 1000;
        margin-top: 8px;
        overflow: hidden;
      }

      .profile-menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        color: #4a5568;
        text-decoration: none;

      /* Theme Toggle Button Styles */
      .theme-toggle-button {
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 10px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 0.9rem;
        font-weight: 600;
        color: #4a5568;
        position: relative;
        overflow: hidden;
      }

      .theme-toggle-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .theme-toggle-button:hover {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border-color: #f59e0b;
        box-shadow: 0 8px 16px rgba(245, 158, 11, 0.2);
        transform: translateY(-2px);
      }

      .theme-toggle-button:hover::before {
        opacity: 1;
      }

      .theme-toggle-icon {
        position: relative;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .theme-toggle-text {
        position: relative;
        z-index: 2;
      }

      /* Dark theme styles */
      [data-theme="dark"] {
        --bg-primary: #1a202c;
        --bg-secondary: #2d3748;
        --text-primary: #f7fafc;
        --text-secondary: #e2e8f0;
        --border-color: #4a5568;
      }

      [data-theme="dark"] body {
        background-color: var(--bg-primary);
        color: var(--text-primary);
      }

      [data-theme="dark"] .admin-container {
        background-color: var(--bg-primary);
      }

      [data-theme="dark"] .sidebar {
        background-color: var(--bg-secondary);
        border-color: var(--border-color);
      }

      [data-theme="dark"] .main-content {
        background-color: var(--bg-primary);
      }

      [data-theme="dark"] .content-header {
        background-color: var(--bg-secondary);
        border-color: var(--border-color);
      }

      [data-theme="dark"] .theme-toggle-button {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        border-color: #718096;
        color: var(--text-primary);
      }

      [data-theme="dark"] .theme-toggle-button:hover {
        background: linear-gradient(135deg, #4a5568 0%, #718096 100%);
        border-color: #a0aec0;
      }

      /* Layout Fix for Content Visibility */
      .content-section {
        max-width: calc(100vw - 350px); /* Ensure content doesn't exceed viewport minus sidebar */
        overflow-x: auto; /* Allow horizontal scrolling if needed */
      }

      .content-section table {
        min-width: 800px; /* Minimum width for tables */
      }

      /* Ensure all content containers have proper spacing */
      .main-content > * {
        margin-right: 0 !important;
        padding-right: 20px;
      }

      /* Fix for cards and grids */
      .stats-grid,
      .dashboard-stats,
      [style*="display: grid"] {
        margin-right: 0 !important;
        padding-right: 20px;
      }

      /* Responsive table wrapper */
      .table-responsive {
        overflow-x: auto;
        margin-right: 0;
      }

      transition: all 0.3s ease;
        border-radius: 8px;
        position: relative;
        overflow: hidden;
      }

      .profile-menu-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .profile-menu-item:hover {
        background: #f8fafc;
        color: #2d3748;
        transform: translateX(-2px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }

      .profile-menu-item:hover::before {
        opacity: 1;
      }

      .profile-menu-item.sign-out:hover {
        background: #fef2f2;
        color: #dc2626;
        transform: translateX(-2px);
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
      }

      /* ULTRA CRITICAL Layout Fix - Maximum Override */
      @media (min-width: 769px) {
        .main-content {
          margin-right: 380px !important;
          padding: 40px !important;
          padding-right: 100px !important;
          width: calc(100% - 380px) !important;
          max-width: calc(100vw - 380px) !important;
          box-sizing: border-box !important;
          position: relative !important;
          left: 0 !important;
          transform: none !important;
        }

        .sidebar {
          width: 350px !important;
          position: fixed !important;
          right: 0 !important;
          top: 0 !important;
          height: 100vh !important;
          z-index: 100 !important;
          box-shadow: -10px 0 20px rgba(0,0,0,0.2) !important;
        }

        /* Ensure all content sections are properly contained */
        .content-section {
          width: 100% !important;
          max-width: none !important;
          margin-right: 0 !important;
          padding-right: 0 !important;
          box-sizing: border-box !important;
        }

        /* Fix for tables and wide content */
        .content-section > div[style*="overflow-x: auto"],
        .table-responsive {
          margin-right: 0 !important;
          width: 100% !important;
        }

        /* Fix for grid layouts */
        [style*="display: grid"],
        [style*="display: flex"] {
          margin-right: 0 !important;
          width: 100% !important;
          box-sizing: border-box !important;
        }
      }

      /* Force layout fix with JavaScript backup */
      .layout-fixed .main-content {
        margin-right: 380px !important;
        padding-right: 100px !important;
        width: calc(100% - 380px) !important;
      }

      .layout-fixed .sidebar {
        width: 350px !important;
      }
    </style>

    <script>
      // Emergency Layout Fix - Apply immediately
      (function() {
        console.log('🚨 Emergency Layout Fix - Applying immediately...');

        function applyLayoutFix() {
          const mainContent = document.querySelector('.main-content');
          const sidebar = document.querySelector('.sidebar');

          if (mainContent) {
            mainContent.style.marginRight = '380px';
            mainContent.style.paddingRight = '100px';
            mainContent.style.width = 'calc(100% - 380px)';
            mainContent.style.maxWidth = 'calc(100vw - 380px)';
            mainContent.style.boxSizing = 'border-box';
            mainContent.style.position = 'relative';
            console.log('✅ Main content layout fixed');
          }

          if (sidebar) {
            sidebar.style.width = '350px';
            sidebar.style.position = 'fixed';
            sidebar.style.right = '0';
            sidebar.style.top = '0';
            sidebar.style.height = '100vh';
            sidebar.style.zIndex = '100';
            console.log('✅ Sidebar layout fixed');
          }

          document.body.classList.add('layout-fixed');
          console.log('🎉 Emergency layout fix applied successfully');
        }

        // Apply fix immediately
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', applyLayoutFix);
        } else {
          applyLayoutFix();
        }

        // Apply fix again after a short delay to ensure it sticks
        setTimeout(applyLayoutFix, 100);
        setTimeout(applyLayoutFix, 500);
        setTimeout(applyLayoutFix, 1000);

        // Monitor for changes and reapply if needed
        const observer = new MutationObserver(function(mutations) {
          let needsFix = false;
          mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' &&
                (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
              needsFix = true;
            }
          });
          if (needsFix) {
            setTimeout(applyLayoutFix, 50);
          }
        });

        // Start observing
        setTimeout(() => {
          const mainContent = document.querySelector('.main-content');
          const sidebar = document.querySelector('.sidebar');
          if (mainContent) observer.observe(mainContent, { attributes: true });
          if (sidebar) observer.observe(sidebar, { attributes: true });
        }, 1000);
      })();

      // NUCLEAR OPTION - Force layout fix every second
      setInterval(() => {
        const mainContent = document.querySelector('.main-content');
        const sidebar = document.querySelector('.sidebar');

        if (mainContent) {
          const currentMargin = parseInt(window.getComputedStyle(mainContent).marginRight);
          if (currentMargin < 400) {
            console.log('🚨 Layout drift detected, reapplying fix...');
            mainContent.style.marginRight = '420px';
            mainContent.style.paddingRight = '140px';
            mainContent.style.width = 'calc(100% - 420px)';
            mainContent.style.maxWidth = 'calc(100vw - 420px)';
          }
        }

        if (sidebar) {
          const currentWidth = parseInt(window.getComputedStyle(sidebar).width);
          if (currentWidth < 380) {
            console.log('🚨 Sidebar drift detected, reapplying fix...');
            sidebar.style.width = '400px';
            sidebar.style.position = 'fixed';
            sidebar.style.right = '0';
            sidebar.style.top = '0';
          }
        }

        // Apply class for CSS override
        document.body.classList.add('layout-override-applied');
      }, 1000);
    </script>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>لوحة التحكم - متجر الكتب</title>

    <!-- Reports and Stores Management Scripts -->
    <script src="js/reports.js" defer></script>
    <script src="js/stores-management.js" defer></script>

    <!-- Admin Navigation Menu -->
    <nav class="admin-nav">
        <ul>
            <li data-section="reportsContent" onclick="showAdminSection('reportsContent')">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير والإحصائيات</span>
            </li>
            <li data-section="storesManagementContent" onclick="showAdminSection('storesManagementContent')">
                <i class="fas fa-store"></i>
                <span>إدارة المتاجر</span>
            </li>
        </ul>
    </nav>

    <!-- Categories Management - Early Load -->
    <script>
        console.log('🔧 تحميل إدارة الفئات مبكراً...');

        function loadCategoriesManagementContent() {
            console.log('🗂️ بدء تحميل إدارة الفئات...');

            const container = document.getElementById('categoriesManagementContent');
            if (!container) {
                console.error('❌ لم يتم العثور على حاوي إدارة الفئات');
                setTimeout(loadCategoriesManagementContent, 500); // Try again after 500ms
                return;
            }

            console.log('✅ تم العثور على الحاوي');

            // Show loading immediately
            container.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div>
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                    </div>
                    <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
                    <p style="color: #999; font-size: 0.9em;">يتم الآن جلب البيانات من الخادم...</p>
                </div>
            `;

            // Try to fetch data
            console.log('📡 محاولة جلب البيانات...');

            fetch('../php/api/categories-fixed.php?action=list')
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status, response.statusText);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('📦 البيانات المستلمة:', data);

                    if (data.success) {
                        console.log('✅ نجح جلب البيانات');
                        renderCategoriesInterface(container, data.data);
                    } else {
                        throw new Error(data.message || 'فشل في جلب البيانات');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في جلب البيانات:', error);
                    showErrorInterface(container, error.message);
                });
        }

        function renderCategoriesInterface(container, data) {
            console.log('🎨 رسم واجهة إدارة الفئات...');

            if (!data || !data.categories) {
                console.error('❌ بيانات غير صحيحة');
                showErrorInterface(container, 'بيانات الفئات غير صحيحة');
                return;
            }

            const categories = data.categories;
            const mainCategories = categories.filter(c => c.parent_id === null);
            const subCategories = categories.filter(c => c.parent_id !== null);
            const featuredCategories = categories.filter(c => c.is_featured == 1);

            const html = `
                <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                    <!-- Header -->
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 20px;">
                            <div>
                                <h2 style="margin: 0; font-size: 1.8rem;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                                <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة وتنظيم فئات المنتجات والمحتوى</p>
                            </div>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                <button onclick="alert('إضافة فئة جديدة - قيد التطوير')" style="padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                    <i class="fas fa-plus"></i> إضافة فئة جديدة
                                </button>
                                <button onclick="loadCategoriesManagementContent()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                    <i class="fas fa-sync-alt"></i> تحديث
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;">
                                <i class="fas fa-folder"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${data.total}</h3>
                            <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">إجمالي الفئات</p>
                        </div>
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;">
                                <i class="fas fa-folder-open"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${mainCategories.length}</h3>
                            <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات الرئيسية</p>
                        </div>
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${subCategories.length}</h3>
                            <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات الفرعية</p>
                        </div>
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;">
                                <i class="fas fa-star"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${featuredCategories.length}</h3>
                            <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات المميزة</p>
                        </div>
                    </div>

                    <!-- Categories List -->
                    <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                        <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; background: #f8f9fa;">
                            <h3 style="margin: 0; color: #333; display: flex; align-items: center; gap: 10px;">
                                <i class="fas fa-sitemap"></i>
                                عرض هرمي للفئات
                                <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">${data.total}</span>
                            </h3>
                        </div>

                        <div style="padding: 25px;">
                            ${renderCategoriesList(categories)}
                        </div>
                    </div>

                    <!-- Success Message -->
                    <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
                        <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة الفئات بنجاح!</strong>
                        <br>تم عرض ${data.total} فئة مع الهيكل الهرمي الكامل.
                    </div>
                </div>
            `;

            console.log('✅ تم إنشاء HTML');
            container.innerHTML = html;
            console.log('✅ تم تحديث الحاوي بنجاح');
        }

        function renderCategoriesList(categories) {
            console.log('🌳 رسم قائمة الفئات...');

            const mainCategories = categories.filter(c => c.parent_id === null);
            let html = '<div>';

            mainCategories.forEach(mainCat => {
                const subCategories = categories.filter(c => c.parent_id == mainCat.id);
                const featuredIcon = mainCat.is_featured == 1 ? '⭐' : '';

                html += `
                    <div style="margin-bottom: 20px; padding: 20px; border: 2px solid ${mainCat.color}; border-radius: 12px; background: linear-gradient(135deg, ${mainCat.color}15 0%, #ffffff 100%);">
                        <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                            <div style="color: ${mainCat.color}; font-size: 2rem;">
                                <i class="${mainCat.icon || 'fas fa-folder'}"></i>
                            </div>
                            <div>
                                <h3 style="margin: 0; color: #333; font-size: 1.4rem;">${mainCat.name_ar} ${featuredIcon}</h3>
                                <p style="margin: 5px 0 0 0; color: #666;">${mainCat.description_ar || 'لا يوجد وصف'}</p>
                            </div>
                        </div>

                        ${subCategories.length > 0 ? `
                            <div style="margin-right: 40px;">
                                <h4 style="color: #555; margin-bottom: 10px;">الفئات الفرعية (${subCategories.length}):</h4>
                                ${subCategories.map(subCat => `
                                    <div style="margin-bottom: 10px; padding: 15px; background: white; border-radius: 8px; border-right: 4px solid ${subCat.color};">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="${subCat.icon || 'fas fa-folder'}" style="color: ${subCat.color};"></i>
                                            <strong>${subCat.name_ar}</strong>
                                            ${subCat.is_featured == 1 ? '⭐' : ''}
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">${subCat.description_ar || 'لا يوجد وصف'}</p>
                                    </div>
                                `).join('')}
                            </div>
                        ` : '<p style="margin-right: 40px; color: #999; font-style: italic;">لا توجد فئات فرعية</p>'}
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        function showErrorInterface(container, message) {
            console.log('❌ عرض واجهة الخطأ:', message);

            container.innerHTML = `
                <div style="text-align: center; padding: 60px 20px; color: #dc3545;">
                    <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.7;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 style="margin: 0 0 15px 0; color: #dc3545;">خطأ في تحميل إدارة الفئات</h3>
                    <p style="margin: 0 0 25px 0; color: #666; font-size: 1.1rem;">${message}</p>
                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                        <button onclick="loadCategoriesManagementContent()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                        <a href="../php/api/categories-fixed.php?action=list" target="_blank" style="padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
                            <i class="fas fa-external-link-alt"></i> اختبار API مباشر
                        </a>
                    </div>
                </div>
            `;
        }

        console.log('✅ تم تحميل إدارة الفئات مبكراً');
        console.log('🎯 الدالة الرئيسية متاحة:', typeof loadCategoriesManagementContent);
    </script>

    <link rel="stylesheet" href="../css/style.css" />
    <link rel="stylesheet" href="css/sidebar-menu-unified.css" />
    <link rel="stylesheet" href="css/store-settings.css" />
    <link rel="stylesheet" href="css/users-management.css" />
    <link rel="stylesheet" href="css/general-settings.css" />
    <link rel="stylesheet" href="css/categories-management.css" />
    <link rel="stylesheet" href="css/product-landing.css" />
    <link rel="stylesheet" href="css/landing-pages.css" />
    <link rel="stylesheet" href="css/layout-emergency-fix.css" />
    <link rel="stylesheet" href="css/final-layout-override.css" />

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- TinyMCE will be loaded later -->
  </head>
  <body>
    <script src="js/emergency-loading-fix.js"></script>
    <script src="js/dashboard-coordination.js"></script>
    <div id="loading-indicator">
      <div class="spinner"></div>
      <div>جاري التحميل...</div>
    </div>
    <div class="notifications-container" id="notificationsContainer"></div>

    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
      <i class="fas fa-bars"></i>
    </button>

    <div class="admin-container">
      <!-- Sidebar -->
      <aside class="sidebar" id="sidebar">
        <div class="logo">
          <h1>لوحة التحكم</h1>
        </div>
        <nav class="admin-nav">
          <ul>
            <li class="active" data-section="dashboard">
              <i class="fas fa-home"></i>
              <span>الرئيسية</span>
            </li>
            <li data-section="books">
              <i class="fas fa-box"></i>
              <span>إدارة المنتجات</span>
            </li>
            <li data-section="orders">
              <i class="fas fa-shopping-cart"></i>
              <span>الطلبات</span>
            </li>
            <li data-section="landingPages">
              <i class="fas fa-bullhorn"></i>
              <span>صفحات هبوط</span>
            </li>
            <li data-section="reportsContent">
              <i class="fas fa-chart-bar"></i>
              <span>التقارير والإحصائيات</span>
            </li>

            <!-- Admin Settings Collapsible Menu -->
            <li class="admin-settings-menu">
              <div
                class="admin-settings-header"
                onclick="toggleAdminSettings()"
              >
                <div class="admin-settings-header-content">
                  <i class="fas fa-cogs"></i>
                  <span>إعدادات الإدارة</span>
                </div>
                <i class="fas fa-chevron-down admin-settings-arrow"></i>
              </div>
              <ul class="admin-settings-submenu">
                <li data-section="generalSettings">
                  <i class="fas fa-cog"></i>
                  <span>الإعدادات العامة</span>
                </li>
                <li data-section="paymentSettings">
                  <i class="fas fa-credit-card"></i>
                  <span>إعدادات الدفع</span>
                </li>
                <li data-section="categories">
                  <i class="fas fa-tags"></i>
                  <span>إدارة الفئات</span>
                </li>
                <li data-section="usersManagement">
                  <i class="fas fa-users"></i>
                  <span>إدارة المستخدمين</span>
                </li>
                <li data-section="rolesManagement">
                  <i class="fas fa-user-shield"></i>
                  <span>إدارة الأدوار</span>
                </li>
                <li data-section="showcaseDemo">
                  <i class="fas fa-star"></i>
                  <span>عرض النظام المحسن</span>
                </li>
                <li data-section="storeSettings">
                  <i class="fas fa-store"></i>
                  <span>إعدادات المتجر</span>
                </li>
                <li data-section="storesManagementContent">
                  <i class="fas fa-store-alt"></i>
                  <span>إدارة المتاجر</span>
                </li>
                <li data-section="securitySettings">
                  <i class="fas fa-shield-alt"></i>
                  <span>إعدادات الأمان</span>
                </li>
                <li data-section="subscriptionsManagement">
                  <i class="fas fa-crown"></i>
                  <span>إدارة الاشتراكات</span>
                </li>
                <li onclick="toggleDiagnosticsPanel()">
                  <i class="fas fa-chart-line"></i>
                  <span>تشخيص النظام</span>
                </li>
              </ul>
            </li>


          </ul>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="main-content">
        <!-- Header with admin tools and profile -->
        <div class="content-header">
          <h1 id="pageTitle">لوحة المعلومات</h1>

          <!-- Admin Tools Bar - Moved to header -->
          <div class="admin-tools-bar" style="display: flex; align-items: center; gap: 8px; flex-wrap: wrap; margin-bottom: 15px;">
            <!-- Quick Fix Button -->
            <button onclick="applyQuickFixes()" style="background: #f59e0b; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 11px; display: flex; align-items: center; gap: 4px;" title="إصلاح سريع">
              <i class="fas fa-bolt"></i> سريع
            </button>

            <!-- Comprehensive Fix Button -->
            <button onclick="runComprehensiveFix()" style="background: #10b981; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 11px; display: flex; align-items: center; gap: 4px;" title="إصلاح شامل للمشاكل">
              <i class="fas fa-tools"></i> شامل
            </button>

            <!-- Database Test Button -->
            <button onclick="testDatabase()" style="background: #3b82f6; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 11px; display: flex; align-items: center; gap: 4px;" title="اختبار قاعدة البيانات">
              <i class="fas fa-database"></i> DB
            </button>

            <!-- Roles Fix Button -->
            <button onclick="fixRolesTable()" style="background: #8b5cf6; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 11px; display: flex; align-items: center; gap: 4px;" title="إصلاح جدول الأدوار">
              <i class="fas fa-user-shield"></i> Fix
            </button>

            <!-- Populate Roles Button -->
            <button onclick="populateRoles()" style="background: #059669; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 11px; display: flex; align-items: center; gap: 4px;" title="ملء جدول الأدوار بالبيانات">
              <i class="fas fa-users"></i> Fill
            </button>

            <!-- Ultimate Fix Button -->
            <button onclick="runUltimateFix()" style="background: #dc2626; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 11px; display: flex; align-items: center; gap: 4px;" title="الإصلاح النهائي لجميع المشاكل">
              <i class="fas fa-rocket"></i> Ultimate
            </button>

            <!-- Specific Issues Fix Button -->
            <button onclick="fixSpecificIssues()" style="background: #7c3aed; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 11px; display: flex; align-items: center; gap: 4px;" title="إصلاح المشاكل المحددة">
              <i class="fas fa-bullseye"></i> Target
            </button>

            <!-- Diagnostics Toggle Button -->
            <button onclick="toggleDiagnosticsPanel()" style="background: #6366f1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 11px; display: flex; align-items: center; gap: 4px;" title="عرض/إخفاء لوحة التشخيص">
              <i class="fas fa-chart-line"></i> تشخيص
            </button>
          </div>

          <div style="display: flex; align-items: center; gap: 15px;">
            <!-- Admin Profile Dropdown -->
            <div class="admin-profile-dropdown" style="position: relative;">
              <button id="adminProfileBtn" class="admin-profile-button" title="ملف المدير الشخصي - إعدادات وخيارات الحساب">
                <div style="display: flex; align-items: center; gap: 12px; position: relative; z-index: 1;">
                  <div style="width: 42px; height: 42px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3); position: relative;">
                    <i class="fas fa-user-shield" style="font-size: 1.1rem;"></i>
                    <div style="position: absolute; top: -2px; right: -2px; width: 12px; height: 12px; background: #48bb78; border-radius: 50%; border: 2px solid white;"></div>
                  </div>
                  <div style="text-align: right; flex: 1;">
                    <div style="font-weight: bold; color: #2d3748; font-size: 1rem; margin-bottom: 2px;">مدير النظام</div>
                    <div style="color: #718096; font-size: 0.8em; display: flex; align-items: center; gap: 5px;">
                      <i class="fas fa-circle" style="color: #48bb78; font-size: 0.5rem;"></i>
                      <span>متصل الآن</span>
                    </div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center; gap: 2px;">
                    <i class="fas fa-chevron-down" style="color: #667eea; font-size: 0.9em; transition: transform 0.3s ease;" id="profileChevron"></i>
                    <div style="width: 20px; height: 1px; background: linear-gradient(90deg, transparent, #667eea, transparent);"></div>
                  </div>
                </div>
              </button>

              <div id="adminProfileDropdown" class="admin-profile-menu" style="display: none;">
                <div class="profile-menu-header">
                  <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 12px 12px 0 0;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                      <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.3rem; border: 2px solid rgba(255,255,255,0.3);">
                        <i class="fas fa-user-shield"></i>
                      </div>
                      <div style="flex: 1;">
                        <div style="font-weight: bold; font-size: 1.1rem; margin-bottom: 4px;">مدير النظام الرئيسي</div>
                        <div style="opacity: 0.9; font-size: 0.9em; display: flex; align-items: center; gap: 6px;">
                          <i class="fas fa-envelope" style="font-size: 0.8rem;"></i>
                          <span><EMAIL></span>
                        </div>
                      </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; background: rgba(255,255,255,0.1); border-radius: 8px; font-size: 0.85em;">
                      <div style="display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-clock"></i>
                        <span>آخر دخول: الآن</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-shield-alt"></i>
                        <span>مستوى الأمان: عالي</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="profile-menu-items" style="padding: 8px;">
                  <div style="padding: 8px 0; border-bottom: 1px solid #f1f5f9; margin-bottom: 8px;">
                    <small style="color: #64748b; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; padding: 0 12px;">إدارة الحساب</small>
                  </div>

                  <a href="#" onclick="openAdminSettings()" class="profile-menu-item" style="margin-bottom: 4px;">
                    <div style="width: 32px; height: 32px; background: #f1f5f9; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #667eea;">
                      <i class="fas fa-user-cog"></i>
                    </div>
                    <div style="flex: 1;">
                      <div style="font-weight: 600; color: #1e293b;">إعدادات المدير</div>
                      <small style="color: #64748b;">تخصيص إعدادات الحساب</small>
                    </div>
                    <i class="fas fa-chevron-left" style="color: #cbd5e1; font-size: 0.8rem;"></i>
                  </a>

                  <a href="#" onclick="viewAdminProfile()" class="profile-menu-item" style="margin-bottom: 4px;">
                    <div style="width: 32px; height: 32px; background: #f1f5f9; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #10b981;">
                      <i class="fas fa-id-card"></i>
                    </div>
                    <div style="flex: 1;">
                      <div style="font-weight: 600; color: #1e293b;">الملف الشخصي</div>
                      <small style="color: #64748b;">عرض وتعديل البيانات الشخصية</small>
                    </div>
                    <i class="fas fa-chevron-left" style="color: #cbd5e1; font-size: 0.8rem;"></i>
                  </a>

                  <a href="#" onclick="changeAdminPassword()" class="profile-menu-item" style="margin-bottom: 12px;">
                    <div style="width: 32px; height: 32px; background: #f1f5f9; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #f59e0b;">
                      <i class="fas fa-key"></i>
                    </div>
                    <div style="flex: 1;">
                      <div style="font-weight: 600; color: #1e293b;">تغيير كلمة المرور</div>
                      <small style="color: #64748b;">تحديث كلمة مرور الحساب</small>
                    </div>
                    <i class="fas fa-chevron-left" style="color: #cbd5e1; font-size: 0.8rem;"></i>
                  </a>

                  <div style="padding: 8px 0; border-bottom: 1px solid #f1f5f9; margin-bottom: 8px;">
                    <small style="color: #64748b; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; padding: 0 12px;">النظام</small>
                  </div>

                  <a href="#" onclick="viewSystemLogs()" class="profile-menu-item" style="margin-bottom: 12px;">
                    <div style="width: 32px; height: 32px; background: #f1f5f9; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #8b5cf6;">
                      <i class="fas fa-file-alt"></i>
                    </div>
                    <div style="flex: 1;">
                      <div style="font-weight: 600; color: #1e293b;">سجلات النظام</div>
                      <small style="color: #64748b;">عرض سجلات النشاط والأحداث</small>
                    </div>
                    <i class="fas fa-chevron-left" style="color: #cbd5e1; font-size: 0.8rem;"></i>
                  </a>

                  <div class="profile-menu-divider" style="height: 1px; background: #f1f5f9; margin: 8px 0;"></div>

                  <a href="#" onclick="signOutAdmin()" class="profile-menu-item sign-out" style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; margin-top: 8px;">
                    <div style="width: 32px; height: 32px; background: #fee2e2; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #dc2626;">
                      <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <div style="flex: 1;">
                      <div style="font-weight: 600; color: #dc2626;">تسجيل الخروج</div>
                      <small style="color: #991b1b;">إنهاء جلسة العمل الحالية</small>
                    </div>
                    <i class="fas fa-chevron-left" style="color: #fca5a5; font-size: 0.8rem;"></i>
                  </a>
                </div>
              </div>
            </div>

            <!-- Theme Toggle Button -->
            <div class="theme-toggle-container" style="position: relative;">
              <button id="themeToggleBtn" class="theme-toggle-button" title="تغيير المظهر (فاتح/داكن)">
                <div class="theme-toggle-icon">
                  <i class="fas fa-sun" id="lightIcon"></i>
                  <i class="fas fa-moon" id="darkIcon" style="display: none;"></i>
                </div>
                <span class="theme-toggle-text">المظهر الفاتح</span>
              </button>
            </div>

            <!-- Database Status Indicator -->
            <div id="dbStatusIndicator" style="display: flex; align-items: center; gap: 8px; padding: 8px 12px; background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; font-size: 0.9em;">
              <i class="fas fa-database" style="color: #718096;"></i>
              <span style="color: #4a5568;">فحص قاعدة البيانات...</span>
            </div>

            <!-- Refresh Button -->
            <button
              id="refreshPageBtn"
              class="refresh-button"
              title="تحديث الصفحة"
            >
              <i class="fas fa-sync-alt"></i>
              <span>تحديث الصفحة</span>
            </button>
          </div>
        </div>

        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
          <!-- Dashboard Header -->
          <div class="dashboard-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 16px; padding: 30px; margin-bottom: 30px; color: white; position: relative; overflow: hidden;">
            <div style="position: absolute; top: -50px; right: -50px; width: 200px; height: 200px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.5;"></div>
            <div style="position: absolute; bottom: -30px; left: -30px; width: 150px; height: 150px; background: rgba(255,255,255,0.05); border-radius: 50%;"></div>
            <div style="position: relative; z-index: 2;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <div>
                  <h2 style="margin: 0 0 8px 0; font-size: 2rem; font-weight: 700;">مرحباً بك في لوحة التحكم</h2>
                  <p style="margin: 0; opacity: 0.9; font-size: 1.1rem;">إدارة شاملة لمتجرك الإلكتروني</p>
                </div>
                <div style="text-align: center;">
                  <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 15px; backdrop-filter: blur(10px);">
                    <i class="fas fa-chart-line" style="font-size: 2.5rem; margin-bottom: 8px;"></i>
                    <div style="font-size: 0.9rem; opacity: 0.9;">نظرة عامة</div>
                  </div>
                </div>
              </div>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 25px;">
                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 20px; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                  <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="background: rgba(255,255,255,0.2); border-radius: 10px; padding: 12px;">
                      <i class="fas fa-clock" style="font-size: 1.2rem;"></i>
                    </div>
                    <div>
                      <div style="font-size: 0.9rem; opacity: 0.8;">آخر تحديث</div>
                      <div style="font-weight: 600;" id="lastUpdateTime">الآن</div>
                    </div>
                  </div>
                </div>
                <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 20px; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                  <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="background: rgba(255,255,255,0.2); border-radius: 10px; padding: 12px;">
                      <i class="fas fa-server" style="font-size: 1.2rem;"></i>
                    </div>
                    <div>
                      <div style="font-size: 0.9rem; opacity: 0.8;">حالة النظام</div>
                      <div style="font-weight: 600; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-circle" style="color: #48bb78; font-size: 0.6rem;"></i>
                        نشط
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced Stats Grid -->
          <div class="stats-grid-enhanced" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 24px; margin-bottom: 30px;">
            <div class="stat-card-enhanced" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 16px; padding: 24px; color: white; position: relative; overflow: hidden; box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);">
              <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              <div style="position: relative; z-index: 2;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                  <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 12px;">
                    <i class="fas fa-box" style="font-size: 1.5rem;"></i>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-size: 2.2rem; font-weight: 700; margin-bottom: 4px;" id="totalBooks">0</div>
                    <div style="font-size: 0.9rem; opacity: 0.9;">منتج</div>
                  </div>
                </div>
                <div style="font-size: 1rem; font-weight: 600; opacity: 0.9;">إجمالي المنتجات</div>
                <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid rgba(255,255,255,0.2);">
                  <div style="display: flex; align-items: center; gap: 6px; font-size: 0.85rem; opacity: 0.8;">
                    <i class="fas fa-arrow-up" style="color: #48bb78;"></i>
                    <span>+12% من الشهر الماضي</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="stat-card-enhanced" style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); border-radius: 16px; padding: 24px; color: white; position: relative; overflow: hidden; box-shadow: 0 8px 32px rgba(72, 187, 120, 0.3);">
              <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              <div style="position: relative; z-index: 2;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                  <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 12px;">
                    <i class="fas fa-shopping-cart" style="font-size: 1.5rem;"></i>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-size: 2.2rem; font-weight: 700; margin-bottom: 4px;" id="newOrders">49</div>
                    <div style="font-size: 0.9rem; opacity: 0.9;">طلب</div>
                  </div>
                </div>
                <div style="font-size: 1rem; font-weight: 600; opacity: 0.9;">الطلبات الجديدة</div>
                <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid rgba(255,255,255,0.2);">
                  <div style="display: flex; align-items: center; gap: 6px; font-size: 0.85rem; opacity: 0.8;">
                    <i class="fas fa-arrow-up" style="color: #68d391;"></i>
                    <span>+8% من الأسبوع الماضي</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="stat-card-enhanced" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 16px; padding: 24px; color: white; position: relative; overflow: hidden; box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);">
              <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              <div style="position: relative; z-index: 2;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                  <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 12px;">
                    <i class="fas fa-money-bill-wave" style="font-size: 1.5rem;"></i>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-size: 2.2rem; font-weight: 700; margin-bottom: 4px;" id="totalSales">0</div>
                    <div style="font-size: 0.9rem; opacity: 0.9;">دج</div>
                  </div>
                </div>
                <div style="font-size: 1rem; font-weight: 600; opacity: 0.9;">إجمالي المبيعات</div>
                <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid rgba(255,255,255,0.2);">
                  <div style="display: flex; align-items: center; gap: 6px; font-size: 0.85rem; opacity: 0.8;">
                    <i class="fas fa-arrow-up" style="color: #fbbf24;"></i>
                    <span>+25% من الشهر الماضي</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="stat-card-enhanced" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); border-radius: 16px; padding: 24px; color: white; position: relative; overflow: hidden; box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);">
              <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
              <div style="position: relative; z-index: 2;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                  <div style="background: rgba(255,255,255,0.2); border-radius: 12px; padding: 12px;">
                    <i class="fas fa-bullhorn" style="font-size: 1.5rem;"></i>
                  </div>
                  <div style="text-align: right;">
                    <div style="font-size: 2.2rem; font-weight: 700; margin-bottom: 4px;" id="totalLandingPages">10</div>
                    <div style="font-size: 0.9rem; opacity: 0.9;">صفحة</div>
                  </div>
                </div>
                <div style="font-size: 1rem; font-weight: 600; opacity: 0.9;">صفحات الهبوط</div>
                <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid rgba(255,255,255,0.2);">
                  <div style="display: flex; align-items: center; gap: 6px; font-size: 0.85rem; opacity: 0.8;">
                    <i class="fas fa-arrow-up" style="color: #a78bfa;"></i>
                    <span>+5% من الأسبوع الماضي</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Orders Section -->
          <div class="recent-orders-enhanced" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); border: 1px solid #e2e8f0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
              <div>
                <h3 style="margin: 0 0 8px 0; color: #2d3748; font-size: 1.4rem; font-weight: 700;">آخر الطلبات</h3>
                <p style="margin: 0; color: #718096; font-size: 0.9rem;">نظرة سريعة على أحدث الطلبات الواردة</p>
              </div>
              <div style="display: flex; gap: 12px;">
                <button style="background: #667eea; color: white; border: none; border-radius: 8px; padding: 8px 16px; font-size: 0.85rem; cursor: pointer; display: flex; align-items: center; gap: 6px; transition: all 0.3s ease;" onmouseover="this.style.background='#5a67d8'" onmouseout="this.style.background='#667eea'">
                  <i class="fas fa-sync-alt"></i>
                  تحديث
                </button>
                <button style="background: #48bb78; color: white; border: none; border-radius: 8px; padding: 8px 16px; font-size: 0.85rem; cursor: pointer; display: flex; align-items: center; gap: 6px; transition: all 0.3s ease;" onmouseover="this.style.background='#38a169'" onmouseout="this.style.background='#48bb78'">
                  <i class="fas fa-plus"></i>
                  طلب جديد
                </button>
              </div>
            </div>

            <!-- Enhanced Table -->
            <div class="table-responsive" style="border-radius: 12px; overflow: hidden; border: 1px solid #e2e8f0;">
              <table id="recentOrdersTable" style="width: 100%; border-collapse: collapse; background: white;">
                <thead style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                  <tr>
                    <th style="padding: 16px; text-align: right; font-weight: 600; color: #4a5568; border-bottom: 2px solid #e2e8f0; font-size: 0.9rem;">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-hashtag" style="color: #718096; font-size: 0.8rem;"></i>
                        رقم الطلب
                      </div>
                    </th>
                    <th style="padding: 16px; text-align: right; font-weight: 600; color: #4a5568; border-bottom: 2px solid #e2e8f0; font-size: 0.9rem;">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-user" style="color: #718096; font-size: 0.8rem;"></i>
                        العميل
                      </div>
                    </th>
                    <th style="padding: 16px; text-align: right; font-weight: 600; color: #4a5568; border-bottom: 2px solid #e2e8f0; font-size: 0.9rem;">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-money-bill-wave" style="color: #718096; font-size: 0.8rem;"></i>
                        المبلغ
                      </div>
                    </th>
                    <th style="padding: 16px; text-align: right; font-weight: 600; color: #4a5568; border-bottom: 2px solid #e2e8f0; font-size: 0.9rem;">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-info-circle" style="color: #718096; font-size: 0.8rem;"></i>
                        الحالة
                      </div>
                    </th>
                    <th style="padding: 16px; text-align: right; font-weight: 600; color: #4a5568; border-bottom: 2px solid #e2e8f0; font-size: 0.9rem;">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-calendar" style="color: #718096; font-size: 0.8rem;"></i>
                        التاريخ
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody id="recentOrdersTableBody">
                  <!-- Sample data for demonstration -->
                  <tr style="border-bottom: 1px solid #f1f5f9; transition: background-color 0.2s ease;" onmouseover="this.style.backgroundColor='#f8fafc'" onmouseout="this.style.backgroundColor='white'">
                    <td style="padding: 16px; color: #2d3748; font-weight: 600;">#ORD-001</td>
                    <td style="padding: 16px; color: #4a5568;">
                      <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">أح</div>
                        <div>
                          <div style="font-weight: 600; color: #2d3748;">أحمد محمد</div>
                          <div style="font-size: 0.8rem; color: #718096;"><EMAIL></div>
                        </div>
                      </div>
                    </td>
                    <td style="padding: 16px; color: #2d3748; font-weight: 600;">2,500 دج</td>
                    <td style="padding: 16px;">
                      <span style="background: #d1fae5; color: #065f46; padding: 4px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">
                        <i class="fas fa-check-circle" style="margin-left: 4px;"></i>
                        مكتمل
                      </span>
                    </td>
                    <td style="padding: 16px; color: #718096; font-size: 0.9rem;">منذ ساعتين</td>
                  </tr>
                  <tr style="border-bottom: 1px solid #f1f5f9; transition: background-color 0.2s ease;" onmouseover="this.style.backgroundColor='#f8fafc'" onmouseout="this.style.backgroundColor='white'">
                    <td style="padding: 16px; color: #2d3748; font-weight: 600;">#ORD-002</td>
                    <td style="padding: 16px; color: #4a5568;">
                      <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #48bb78, #38a169); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">ف</div>
                        <div>
                          <div style="font-weight: 600; color: #2d3748;">فاطمة علي</div>
                          <div style="font-size: 0.8rem; color: #718096;"><EMAIL></div>
                        </div>
                      </div>
                    </td>
                    <td style="padding: 16px; color: #2d3748; font-weight: 600;">1,800 دج</td>
                    <td style="padding: 16px;">
                      <span style="background: #fef3c7; color: #92400e; padding: 4px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">
                        <i class="fas fa-clock" style="margin-left: 4px;"></i>
                        قيد المعالجة
                      </span>
                    </td>
                    <td style="padding: 16px; color: #718096; font-size: 0.9rem;">منذ 4 ساعات</td>
                  </tr>
                  <tr style="border-bottom: 1px solid #f1f5f9; transition: background-color 0.2s ease;" onmouseover="this.style.backgroundColor='#f8fafc'" onmouseout="this.style.backgroundColor='white'">
                    <td style="padding: 16px; color: #2d3748; font-weight: 600;">#ORD-003</td>
                    <td style="padding: 16px; color: #4a5568;">
                      <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">م</div>
                        <div>
                          <div style="font-weight: 600; color: #2d3748;">محمد حسن</div>
                          <div style="font-size: 0.8rem; color: #718096;"><EMAIL></div>
                        </div>
                      </div>
                    </td>
                    <td style="padding: 16px; color: #2d3748; font-weight: 600;">3,200 دج</td>
                    <td style="padding: 16px;">
                      <span style="background: #dbeafe; color: #1e40af; padding: 4px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">
                        <i class="fas fa-shipping-fast" style="margin-left: 4px;"></i>
                        قيد الشحن
                      </span>
                    </td>
                    <td style="padding: 16px; color: #718096; font-size: 0.9rem;">أمس</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Table Footer -->
            <div style="display: flex; justify-content: between; align-items: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
              <div style="color: #718096; font-size: 0.9rem;">
                عرض 3 من أصل 49 طلب
              </div>
              <div style="display: flex; gap: 8px;">
                <button style="background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 6px; padding: 6px 12px; font-size: 0.8rem; color: #4a5568; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='#edf2f7'" onmouseout="this.style.background='#f7fafc'">
                  عرض الكل
                </button>
              </div>
            </div>
          </div>
        </section>

        <!-- Products Management Section -->
        <section id="books" class="content-section">
          <h2>إدارة المنتجات</h2>
          <div class="section-header">
            <button id="addProductBtn" class="action-button" onclick="showProductAddMessage()" style="opacity: 0.7;">
              <i class="fas fa-plus"></i> إضافة منتج جديد (قيد التطوير)
            </button>
            <button
              id="deleteSelectedProductsBtn"
              class="action-button"
              style="background: #e74c3c; display: none"
            >
              <i class="fas fa-trash"></i> حذف المحدد (<span
                id="selectedProductsCount"
                >0</span
              >)
            </button>
          </div>

          <!-- Products Pagination Controls -->
          <div class="products-pagination-controls">
            <div class="pagination-info">
              <span
                >عرض
                <span id="productsPerPageSelect">
                  <select
                    id="productsPageSize"
                    onchange="changeProductsPageSize()"
                  >
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                  </select>
                </span>
                منتج لكل صفحة</span
              >
              <span id="productsInfo">عرض 0 من 0 منتج</span>
            </div>
            <div class="pagination-search">
              <input
                type="text"
                id="productsSearchInput"
                placeholder="البحث في المنتجات..."
                onkeyup="searchProducts()"
              />
              <button
                onclick="clearProductsSearch()"
                id="clearSearchBtn"
                style="display: none"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <div class="table-responsive">
            <div
              id="productsLoadingIndicator"
              class="loading-indicator"
              style="display: none"
            >
              <i class="fas fa-spinner fa-spin"></i> جاري تحميل المنتجات...
            </div>
            <table id="booksTable">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      id="selectAllProducts"
                      title="تحديد الكل"
                    />
                  </th>
                  <th>الرقم</th>
                  <th>الصورة</th>
                  <th>العنوان</th>
                  <th>النوع</th>
                  <th>السعر</th>
                  <th>المخزون</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>

          <!-- Products Pagination Navigation -->
          <div class="products-pagination-nav" id="productsPaginationNav">
            <button
              id="prevProductsPage"
              onclick="previousProductsPage()"
              disabled
            >
              <i class="fas fa-chevron-right"></i> السابق
            </button>
            <div class="pagination-pages" id="productsPaginationPages">
              <!-- Page numbers will be inserted here -->
            </div>
            <button id="nextProductsPage" onclick="nextProductsPage()" disabled>
              التالي <i class="fas fa-chevron-left"></i>
            </button>
          </div>
        </section>

        <!-- Orders Management Section -->
        <section id="orders" class="content-section">
          <h2>إدارة الطلبات</h2>
          <div class="orders-filters">
            <select id="orderStatusFilter">
              <option value="all">جميع الطلبات</option>
              <option value="en_attente">قيد الانتظار</option>
              <option value="payé">تم الدفع</option>
              <option value="expédié">تم الشحن</option>
            </select>
          </div>

          <div class="table-responsive">
            <table id="ordersTable">
              <thead>
                <tr>
                  <th>رقم الطلب</th>
                  <th>العميل</th>
                  <th>التفاصيل</th>
                  <th>المبلغ</th>
                  <th>الحالة</th>
                  <th>التاريخ</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </section>

        <!-- Landing Pages Section -->
        <section id="landingPages" class="content-section">
          <div id="landingPagesContent">
            <!-- Header Section -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
                <i class="fas fa-bullhorn" style="font-size: 2.5rem;"></i>
                <h2 style="margin: 0; font-size: 2rem; font-weight: bold;">إدارة صفحات الهبوط</h2>
              </div>
              <p style="margin: 0; font-size: 1.1rem; opacity: 0.9;">إنشاء وإدارة صفحات الهبوط التسويقية</p>
              <div style="margin-top: 20px;">
                <button onclick="alert('إضافة صفحة هبوط جديدة - قيد التطوير')" style="padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin-left: 10px;">
                  <i class="fas fa-plus"></i> إضافة صفحة جديدة
                </button>
                <button onclick="loadLandingPagesContent()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                  <i class="fas fa-sync-alt"></i> تحديث
                </button>
              </div>
            </div>

            <script>
              // Landing Pages Content Loading Function
              window.loadLandingPagesContent = function() {
                console.log('🔄 تحديث محتوى صفحات الهبوط...');

                // Show loading indicator
                const tableBody = document.getElementById('landingPagesTableBody');
                if (tableBody) {
                  tableBody.innerHTML = `
                    <tr>
                      <td colspan="6" style="text-align: center; padding: 40px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="margin: 0; color: #666;">جاري تحديث بيانات صفحات الهبوط...</p>
                      </td>
                    </tr>
                  `;
                }

                // Simulate loading delay
                setTimeout(() => {
                  if (tableBody) {
                    tableBody.innerHTML = `
                      <tr style="border-bottom: 1px solid #e2e8f0;">
                        <td style="padding: 15px; color: #2d3748; font-weight: 500;">صفحة العروض الخاصة</td>
                        <td style="padding: 15px; text-align: center;">
                          <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشطة</span>
                        </td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">342</td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">29</td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-15</td>
                        <td style="padding: 15px; text-align: center;">
                          <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-chart-bar"></i>
                          </button>
                        </td>
                      </tr>
                      <tr style="border-bottom: 1px solid #e2e8f0;">
                        <td style="padding: 15px; color: #2d3748; font-weight: 500;">صفحة المنتجات الجديدة</td>
                        <td style="padding: 15px; text-align: center;">
                          <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشطة</span>
                        </td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">198</td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">15</td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-10</td>
                        <td style="padding: 15px; text-align: center;">
                          <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-chart-bar"></i>
                          </button>
                        </td>
                      </tr>
                      <tr style="border-bottom: 1px solid #e2e8f0;">
                        <td style="padding: 15px; color: #2d3748; font-weight: 500;">صفحة الخصومات</td>
                        <td style="padding: 15px; text-align: center;">
                          <span style="background: #fed7d7; color: #742a2a; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">متوقفة</span>
                        </td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">89</td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">7</td>
                        <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-05</td>
                        <td style="padding: 15px; text-align: center;">
                          <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-eye"></i>
                          </button>
                          <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                            <i class="fas fa-chart-bar"></i>
                          </button>
                        </td>
                      </tr>
                    `;
                  }

                  console.log('✅ تم تحديث محتوى صفحات الهبوط بنجاح');
                }, 1000);
              };
            </script>
              </div>
            </div>

            <!-- Statistics Cards -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
              <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <i class="fas fa-file-alt" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3 style="margin: 0; font-size: 2rem; font-weight: bold;">12</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">إجمالي الصفحات</p>
              </div>
              <div style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <i class="fas fa-eye" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3 style="margin: 0; font-size: 2rem; font-weight: bold;">1,247</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">إجمالي الزيارات</p>
              </div>
              <div style="background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <i class="fas fa-mouse-pointer" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3 style="margin: 0; font-size: 2rem; font-weight: bold;">8.5%</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">معدل التحويل</p>
              </div>
              <div style="background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <i class="fas fa-chart-line" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3 style="margin: 0; font-size: 2rem; font-weight: bold;">5</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">صفحات نشطة</p>
              </div>
            </div>

            <!-- Landing Pages Table -->
            <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <h3 style="margin: 0 0 20px 0; color: #2d3748; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-list"></i>
                قائمة صفحات الهبوط
              </h3>

              <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #f7fafc; border-bottom: 2px solid #e2e8f0;">
                      <th style="padding: 15px; text-align: right; font-weight: bold; color: #4a5568;">اسم الصفحة</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الحالة</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الزيارات</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">التحويلات</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">تاريخ الإنشاء</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody id="landingPagesTableBody">
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">صفحة العروض الخاصة</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشطة</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">342</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">29</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-15</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-chart-bar"></i>
                        </button>
                      </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">صفحة المنتجات الجديدة</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشطة</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">198</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">15</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-10</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-chart-bar"></i>
                        </button>
                      </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">صفحة الخصومات</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #fed7d7; color: #742a2a; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">متوقفة</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">89</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">7</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-05</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-chart-bar"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        <!-- Reports and Analytics Section -->
        <section id="reports" class="content-section">
          <div id="reportsContent">
            <!-- Header Section -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
                <i class="fas fa-chart-bar" style="font-size: 3rem;"></i>
                <div>
                  <h2 style="margin: 0; font-size: 2rem;">التقارير والإحصائيات</h2>
                  <p style="margin: 5px 0 0 0; opacity: 0.9;">عرض تقارير شاملة عن أداء المتجر</p>
                </div>
              </div>
              <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap; margin-top: 20px;">
                <button onclick="refreshReports()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                  <i class="fas fa-sync-alt"></i> تحديث البيانات
                </button>
                <button onclick="exportReports()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                  <i class="fas fa-download"></i> تصدير التقرير
                </button>
              </div>
            </div>

            <!-- Key Metrics Cards -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #48bb78;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 2rem;">1,247</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">إجمالي الزوار</p>
                  </div>
                  <div style="width: 50px; height: 50px; background: #48bb78; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-users"></i>
                  </div>
                </div>
                <div style="margin-top: 15px; font-size: 0.9em; color: #48bb78;">
                  <i class="fas fa-arrow-up"></i> +12.5% من الشهر الماضي
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #4299e1;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 2rem;">89</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">صفحات الهبوط النشطة</p>
                  </div>
                  <div style="width: 50px; height: 50px; background: #4299e1; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-file-alt"></i>
                  </div>
                </div>
                <div style="margin-top: 15px; font-size: 0.9em; color: #4299e1;">
                  <i class="fas fa-arrow-up"></i> +8.3% من الشهر الماضي
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #ed8936;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 2rem;">342</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">التحويلات</p>
                  </div>
                  <div style="width: 50px; height: 50px; background: #ed8936; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-chart-line"></i>
                  </div>
                </div>
                <div style="margin-top: 15px; font-size: 0.9em; color: #ed8936;">
                  <i class="fas fa-arrow-up"></i> +15.7% من الشهر الماضي
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #9f7aea;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 2rem;">27.4%</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">معدل التحويل</p>
                  </div>
                  <div style="width: 50px; height: 50px; background: #9f7aea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-percentage"></i>
                  </div>
                </div>
                <div style="margin-top: 15px; font-size: 0.9em; color: #9f7aea;">
                  <i class="fas fa-arrow-up"></i> +3.2% من الشهر الماضي
                </div>
              </div>
            </div>

            <!-- Charts Section -->
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
              <!-- Main Chart -->
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 20px 0; color: #2d3748;">الزوار خلال الشهر الماضي</h3>
                <div style="height: 300px; background: #f7fafc; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #718096;">
                  <div style="text-align: center;">
                    <i class="fas fa-chart-area" style="font-size: 3rem; margin-bottom: 15px; color: #667eea;"></i>
                    <p>رسم بياني للزوار</p>
                    <small>سيتم تطوير الرسوم البيانية التفاعلية قريباً</small>
                  </div>
                </div>
              </div>

              <!-- Side Stats -->
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 20px 0; color: #2d3748;">أهم المصادر</h3>
                <div style="space-y: 15px;">
                  <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #e2e8f0;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <div style="width: 8px; height: 8px; background: #48bb78; border-radius: 50%;"></div>
                      <span style="color: #4a5568;">البحث المباشر</span>
                    </div>
                    <span style="color: #2d3748; font-weight: bold;">45%</span>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #e2e8f0;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <div style="width: 8px; height: 8px; background: #4299e1; border-radius: 50%;"></div>
                      <span style="color: #4a5568;">وسائل التواصل</span>
                    </div>
                    <span style="color: #2d3748; font-weight: bold;">32%</span>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #e2e8f0;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <div style="width: 8px; height: 8px; background: #ed8936; border-radius: 50%;"></div>
                      <span style="color: #4a5568;">الإعلانات المدفوعة</span>
                    </div>
                    <span style="color: #2d3748; font-weight: bold;">18%</span>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <div style="width: 8px; height: 8px; background: #9f7aea; border-radius: 50%;"></div>
                      <span style="color: #4a5568;">أخرى</span>
                    </div>
                    <span style="color: #2d3748; font-weight: bold;">5%</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Recent Activity Table -->
            <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <h3 style="margin: 0 0 20px 0; color: #2d3748;">النشاط الأخير</h3>
              <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #f7fafc;">
                      <th style="padding: 12px; text-align: right; color: #4a5568; font-weight: 600; border-bottom: 1px solid #e2e8f0;">الصفحة</th>
                      <th style="padding: 12px; text-align: right; color: #4a5568; font-weight: 600; border-bottom: 1px solid #e2e8f0;">الزوار</th>
                      <th style="padding: 12px; text-align: right; color: #4a5568; font-weight: 600; border-bottom: 1px solid #e2e8f0;">التحويلات</th>
                      <th style="padding: 12px; text-align: right; color: #4a5568; font-weight: 600; border-bottom: 1px solid #e2e8f0;">معدل التحويل</th>
                      <th style="padding: 12px; text-align: right; color: #4a5568; font-weight: 600; border-bottom: 1px solid #e2e8f0;">الحالة</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">صفحة المنتج الرئيسي</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">1,234</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">89</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">7.2%</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0;"><span style="background: #48bb78; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">نشط</span></td>
                    </tr>
                    <tr>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">عرض خاص - خصم 50%</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">987</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">156</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">15.8%</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0;"><span style="background: #48bb78; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">نشط</span></td>
                    </tr>
                    <tr>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">صفحة الخدمات</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">654</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">32</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0; color: #2d3748;">4.9%</td>
                      <td style="padding: 12px; border-bottom: 1px solid #e2e8f0;"><span style="background: #ed8936; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">متوسط</span></td>
                    </tr>
                    <tr>
                      <td style="padding: 12px; color: #2d3748;">صفحة التواصل</td>
                      <td style="padding: 12px; color: #2d3748;">321</td>
                      <td style="padding: 12px; color: #2d3748;">8</td>
                      <td style="padding: 12px; color: #2d3748;">2.5%</td>
                      <td style="padding: 12px;"><span style="background: #f56565; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">ضعيف</span></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <script>
            // Reports functionality
            window.refreshReports = function() {
              alert('تحديث البيانات - سيتم تطوير هذه الميزة قريباً');
            };

            window.exportReports = function() {
              alert('تصدير التقرير - سيتم تطوير هذه الميزة قريباً');
            };
          </script>
        </section>








        </section>

        <!-- Categories Management Section -->
        <section id="categories" class="content-section">
          <div id="categoriesManagementContent">
            <!-- Categories management content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i
                class="fas fa-spinner fa-spin"
                style="font-size: 2rem; color: #667eea"
              ></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل إدارة الفئات...
              </p>
              <button onclick="forceLoadCategoriesNow()" style="margin-top: 20px; padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                <i class="fas fa-bolt"></i> إجبار التحميل الآن
              </button>
              <br>
              <a href="categories-standalone.html" target="_blank" style="display: inline-block; margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 6px; font-size: 0.9em;">
                <i class="fas fa-external-link-alt"></i> فتح الصفحة المستقلة
              </a>
            </div>
          </div>

          <script>
            // Force load function - uses interactive categories
            window.forceLoadCategoriesNow = function() {
                console.log('⚡ إجبار تحميل إدارة الفئات التفاعلية الآن...');

                const container = document.getElementById('categoriesManagementContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-cog fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="color: #666; font-weight: bold;">إجبار تحميل إدارة الفئات التفاعلية...</p>
                        <p style="color: #999; font-size: 0.9em;">يتم الآن تحضير الواجهة التفاعلية...</p>
                    </div>
                `;

                // Try to use the interactive function first
                if (typeof loadInteractiveCategoriesContent === 'function') {
                    console.log('✅ استخدام الواجهة التفاعلية...');
                    loadInteractiveCategoriesContent();
                    return;
                }

                // Fallback: Direct fetch with interactive interface
                console.log('🔄 استخدام الطريقة البديلة...');
                fetch('../php/api/categories-fixed.php?action=list')
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status, response.statusText);
                    if (!response.ok) throw new Error('HTTP ' + response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📦 البيانات المستلمة:', data);
                    if (data.success && data.data && data.data.categories) {
                        console.log('✅ نجح جلب البيانات');
                        renderCategoriesInteractive(container, data.data);
                    } else {
                        throw new Error(data.message || 'بيانات غير صحيحة');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ:', error);
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #dc3545;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                            <h3>خطأ في تحميل إدارة الفئات</h3>
                            <p>${error.message}</p>
                            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-top: 20px;">
                                <button onclick="forceLoadCategoriesNow()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                    <i class="fas fa-redo"></i> إعادة المحاولة
                                </button>
                                <a href="../php/api/categories-fixed.php?action=list" target="_blank" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 6px;">
                                    <i class="fas fa-external-link-alt"></i> اختبار API
                                </a>
                            </div>
                        </div>
                    `;
                });
            };

            // Fallback render function for interactive categories
            function renderCategoriesInteractive(container, data) {
                const categories = data.categories || [];
                const mainCategories = categories.filter(c => c.parent_id === null);
                const subCategories = categories.filter(c => c.parent_id !== null);
                const featuredCategories = categories.filter(c => c.is_featured == 1);

                const html = `
                    <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                            <h2 style="margin: 0;"><i class="fas fa-sitemap"></i> إدارة الفئات التفاعلية</h2>
                            <p style="margin: 10px 0 0 0;">تم التحميل بنجاح! يمكنك الآن إضافة وتعديل وحذف الفئات.</p>
                            <div style="margin-top: 20px;">
                                <button onclick="alert('إضافة فئة جديدة - قيد التطوير')" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                                    <i class="fas fa-plus"></i> إضافة فئة جديدة
                                </button>
                                <button onclick="forceLoadCategoriesNow()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                                    <i class="fas fa-sync-alt"></i> تحديث
                                </button>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;"><i class="fas fa-folder"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${categories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">إجمالي الفئات</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-folder-open"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${mainCategories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">الفئات الرئيسية</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;"><i class="fas fa-layer-group"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${subCategories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">الفئات الفرعية</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-star"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${featuredCategories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">الفئات المميزة</p>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 25px;">
                            <h3 style="margin-bottom: 20px; color: #333; text-align: center;"><i class="fas fa-list"></i> الفئات الحالية (${categories.length})</h3>
                            ${renderCategoriesListInteractive(categories)}
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; text-align: center;">
                            <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة الفئات التفاعلية بنجاح!</strong>
                            <br>يمكنك الآن إضافة وتعديل وحذف الفئات. للواجهة الكاملة: <a href="categories-standalone.html" target="_blank" style="color: #155724; text-decoration: underline;">فتح الصفحة المستقلة</a>
                        </div>
                    </div>
                `;

                container.innerHTML = html;
            }

            function renderCategoriesListInteractive(categories) {
                if (!categories || categories.length === 0) {
                    return '<p style="text-align: center; color: #666;">لا توجد فئات للعرض</p>';
                }

                const mainCategories = categories.filter(c => c.parent_id === null);
                let html = '';

                mainCategories.forEach(mainCat => {
                    const subCategories = categories.filter(c => c.parent_id == mainCat.id);

                    html += `
                        <div style="margin-bottom: 15px; padding: 15px; border-right: 4px solid ${mainCat.color || '#667eea'}; background: #f8f9fa; border-radius: 6px;">
                            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 10px;">
                                <i class="${mainCat.icon || 'fas fa-folder'}" style="color: ${mainCat.color || '#667eea'}; font-size: 1.5rem;"></i>
                                <div style="flex: 1;">
                                    <strong style="color: #333; font-size: 1.2rem;">${mainCat.name_ar || 'فئة بدون اسم'}</strong>
                                    ${mainCat.is_featured == 1 ? '<span style="background: #ffc107; color: #333; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem; margin-right: 8px;">⭐ مميزة</span>' : ''}
                                    <p style="margin: 2px 0; color: #666; font-size: 0.9em;">${mainCat.description_ar || 'لا يوجد وصف'}</p>
                                </div>
                                <div style="display: flex; gap: 5px;">
                                    <button onclick="alert('تعديل الفئة - قيد التطوير')" style="padding: 6px 10px; background: #ffc107; color: #333; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                    <button onclick="alert('حذف الفئة - قيد التطوير')" style="padding: 6px 10px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </div>
                            </div>

                            ${subCategories.length > 0 ? `
                                <div style="margin-right: 35px; border-top: 1px solid #e0e0e0; padding-top: 10px;">
                                    <p style="margin: 0 0 8px 0; color: #666; font-size: 0.9em; font-weight: 600;">الفئات الفرعية (${subCategories.length}):</p>
                                    ${subCategories.map(subCat => `
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px; padding: 8px; background: white; border-radius: 4px;">
                                            <i class="${subCat.icon || 'fas fa-folder'}" style="color: ${subCat.color || '#17a2b8'}; font-size: 1rem;"></i>
                                            <span style="flex: 1; color: #333;">${subCat.name_ar}</span>
                                            ${subCat.is_featured == 1 ? '<span style="background: #ffc107; color: #333; padding: 1px 4px; border-radius: 8px; font-size: 0.7rem;">⭐</span>' : ''}
                                            <div style="display: flex; gap: 3px;">
                                                <button onclick="alert('تعديل الفئة الفرعية - قيد التطوير')" style="padding: 4px 6px; background: #ffc107; color: #333; border: none; border-radius: 4px; cursor: pointer; font-size: 0.7rem;">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="alert('حذف الفئة الفرعية - قيد التطوير')" style="padding: 4px 6px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.7rem;">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            ` : ''}
                        </div>
                    `;
                });

                return html;
            }

            function renderCategoriesNow(container, data) {
                const categories = data.categories || [];
                const mainCategories = categories.filter(c => c.parent_id === null);
                const subCategories = categories.filter(c => c.parent_id !== null);
                const featuredCategories = categories.filter(c => c.is_featured == 1);

                const html = `
                    <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                            <h2 style="margin: 0;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                            <p style="margin: 10px 0 0 0;">تم التحميل بنجاح!</p>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;"><i class="fas fa-folder"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${categories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">إجمالي الفئات</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-folder-open"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${mainCategories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">الفئات الرئيسية</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;"><i class="fas fa-layer-group"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${subCategories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">الفئات الفرعية</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-star"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${featuredCategories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">الفئات المميزة</p>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 25px;">
                            <h3 style="margin-bottom: 20px; color: #333; text-align: center;"><i class="fas fa-sitemap"></i> قائمة الفئات (${categories.length})</h3>
                            ${renderCategoriesListNow(categories)}
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; text-align: center;">
                            <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة الفئات بنجاح!</strong>
                        </div>
                    </div>
                `;

                container.innerHTML = html;
            }

            function renderCategoriesListNow(categories) {
                if (!categories || categories.length === 0) {
                    return '<p style="text-align: center; color: #666; padding: 40px;">لا توجد فئات للعرض</p>';
                }

                const mainCategories = categories.filter(c => c.parent_id === null);
                let html = '';

                mainCategories.forEach(mainCat => {
                    const subCategories = categories.filter(c => c.parent_id == mainCat.id);
                    const featuredIcon = mainCat.is_featured == 1 ? '⭐' : '';

                    html += `
                        <div style="margin-bottom: 15px; padding: 15px; border-right: 4px solid ${mainCat.color || '#667eea'}; background: #f8f9fa; border-radius: 6px;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                <i class="${mainCat.icon || 'fas fa-folder'}" style="color: ${mainCat.color || '#667eea'}; font-size: 1.2rem;"></i>
                                <strong style="color: #333;">${mainCat.name_ar || 'فئة'} ${featuredIcon}</strong>
                            </div>
                            <p style="margin: 0 0 10px 25px; color: #666; font-size: 0.9em;">${mainCat.description_ar || 'لا يوجد وصف'}</p>
                            <div style="margin-right: 25px; color: #999; font-size: 0.8em;">
                                ${subCategories.length} فئة فرعية
                            </div>

                            ${subCategories.length > 0 ? `
                                <div style="margin-top: 10px; margin-right: 25px;">
                                    ${subCategories.map(subCat => `
                                        <div style="display: inline-block; margin: 3px 5px; padding: 3px 8px; background: white; border-radius: 12px; border: 1px solid ${subCat.color || '#17a2b8'}; font-size: 0.8em;">
                                            <i class="${subCat.icon || 'fas fa-folder'}" style="color: ${subCat.color || '#17a2b8'};"></i>
                                            ${subCat.name_ar || 'فئة فرعية'} ${subCat.is_featured == 1 ? '⭐' : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            ` : ''}
                        </div>
                    `;
                });

                return html;
            }
          </script>
        </section>

        <!-- Payment Settings Section -->
        <section id="paymentSettings" class="content-section">
          <div id="paymentSettingsContent">
            <!-- Payment settings content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i
                class="fas fa-spinner fa-spin"
                style="font-size: 2rem; color: #667eea"
              ></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل إعدادات الدفع...
              </p>
              <button onclick="forceLoadPaymentSettingsNow()" style="margin-top: 20px; padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                <i class="fas fa-bolt"></i> إجبار التحميل الآن
              </button>
              <br>
              <a href="#" onclick="alert('صفحة إعدادات الدفع المستقلة - قيد التطوير')" style="display: inline-block; margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 6px; font-size: 0.9em;">
                <i class="fas fa-external-link-alt"></i> فتح الصفحة المستقلة
              </a>
            </div>
          </div>

          <script>
            // Force load payment settings function
            window.forceLoadPaymentSettingsNow = function() {
                console.log('⚡ إجبار تحميل إعدادات الدفع الآن...');

                const container = document.getElementById('paymentSettingsContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-cog fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="color: #666; font-weight: bold;">إجبار تحميل إعدادات الدفع...</p>
                        <p style="color: #999; font-size: 0.9em;">يتم الآن إعداد واجهة إعدادات الدفع...</p>
                    </div>
                `;

                // Simulate loading and show placeholder content
                setTimeout(() => {
                    container.innerHTML = `
                        <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                                <h2 style="margin: 0;"><i class="fas fa-credit-card"></i> إعدادات الدفع</h2>
                                <p style="margin: 10px 0 0 0;">إدارة طرق الدفع والمعاملات المالية</p>
                            </div>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-credit-card"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">4</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">طرق الدفع المتاحة</p>
                                </div>
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;"><i class="fas fa-money-bill-wave"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">15.5%</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">معدل الضريبة</p>
                                </div>
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-shield-alt"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">SSL</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">الحماية المفعلة</p>
                                </div>
                            </div>

                            <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 25px;">
                                <h3 style="margin-bottom: 20px; color: #333; text-align: center;"><i class="fas fa-list"></i> طرق الدفع المتاحة</h3>
                                <div style="display: grid; gap: 15px;">
                                    <div style="padding: 15px; border-right: 4px solid #28a745; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-credit-card" style="color: #28a745; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">بطاقات الائتمان</strong>
                                            <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">مفعل</span>
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">Visa, MasterCard, American Express</p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #17a2b8; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fab fa-paypal" style="color: #17a2b8; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">PayPal</strong>
                                            <span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">مفعل</span>
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">دفع آمن عبر PayPal</p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #ffc107; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-university" style="color: #ffc107; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">التحويل البنكي</strong>
                                            <span style="background: #ffc107; color: #333; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">مفعل</span>
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">تحويل مباشر إلى الحساب البنكي</p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #dc3545; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-hand-holding-usd" style="color: #dc3545; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">الدفع عند الاستلام</strong>
                                            <span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">معطل</span>
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">دفع نقدي عند استلام الطلب</p>
                                    </div>
                                </div>
                            </div>

                            <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404; text-align: center;">
                                <i class="fas fa-info-circle"></i> <strong>إعدادات الدفع - قيد التطوير</strong>
                                <br>هذا القسم قيد التطوير وسيتم إضافة المزيد من الوظائف قريباً.
                            </div>
                        </div>
                    `;
                }, 1000);
            };
          </script>
        </section>

        <!-- General Settings Section -->
        <section id="generalSettings" class="content-section">
          <div id="generalSettingsContent">
            <!-- General settings content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i
                class="fas fa-spinner fa-spin"
                style="font-size: 2rem; color: #667eea"
              ></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل الإعدادات العامة...
              </p>
              <button onclick="forceLoadGeneralSettingsNow()" style="margin-top: 20px; padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                <i class="fas fa-bolt"></i> إجبار التحميل الآن
              </button>
              <br>
              <a href="#" onclick="alert('صفحة الإعدادات العامة المستقلة - قيد التطوير')" style="display: inline-block; margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 6px; font-size: 0.9em;">
                <i class="fas fa-external-link-alt"></i> فتح الصفحة المستقلة
              </a>
            </div>
          </div>

          <script>
            // Force load general settings function
            window.forceLoadGeneralSettingsNow = function() {
                console.log('⚡ إجبار تحميل الإعدادات العامة الآن...');

                const container = document.getElementById('generalSettingsContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-cog fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="color: #666; font-weight: bold;">إجبار تحميل الإعدادات العامة...</p>
                        <p style="color: #999; font-size: 0.9em;">يتم الآن إعداد واجهة الإعدادات العامة...</p>
                    </div>
                `;

                // Simulate loading and show placeholder content
                setTimeout(() => {
                    container.innerHTML = `
                        <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                                <h2 style="margin: 0;"><i class="fas fa-cog"></i> الإعدادات العامة</h2>
                                <p style="margin: 10px 0 0 0;">إعدادات النظام الأساسية والتكوين العام</p>
                            </div>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;"><i class="fas fa-globe"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">العربية</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">اللغة الافتراضية</p>
                                </div>
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-clock"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">GMT+3</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">المنطقة الزمنية</p>
                                </div>
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;"><i class="fas fa-palette"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">الأزرق</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">لون القالب</p>
                                </div>
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-toggle-on"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">مفعل</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">وضع الصيانة</p>
                                </div>
                            </div>

                            <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 25px;">
                                <h3 style="margin-bottom: 20px; color: #333; text-align: center;"><i class="fas fa-list"></i> الإعدادات الأساسية</h3>
                                <div style="display: grid; gap: 15px;">
                                    <div style="padding: 15px; border-right: 4px solid #667eea; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                            <i class="fas fa-store" style="color: #667eea; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">اسم الموقع</strong>
                                        </div>
                                        <p style="margin: 0 0 0 25px; color: #666;">متجر الكتب الإلكترونية</p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #28a745; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                            <i class="fas fa-envelope" style="color: #28a745; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">البريد الإلكتروني الإداري</strong>
                                        </div>
                                        <p style="margin: 0 0 0 25px; color: #666;"><EMAIL></p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #17a2b8; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                            <i class="fas fa-phone" style="color: #17a2b8; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">رقم الهاتف</strong>
                                        </div>
                                        <p style="margin: 0 0 0 25px; color: #666;">+966 50 123 4567</p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #ffc107; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                                            <i class="fas fa-map-marker-alt" style="color: #ffc107; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">العنوان</strong>
                                        </div>
                                        <p style="margin: 0 0 0 25px; color: #666;">الرياض، المملكة العربية السعودية</p>
                                    </div>
                                </div>
                            </div>

                            <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404; text-align: center;">
                                <i class="fas fa-info-circle"></i> <strong>الإعدادات العامة - قيد التطوير</strong>
                                <br>هذا القسم قيد التطوير وسيتم إضافة المزيد من الوظائف قريباً.
                            </div>
                        </div>
                    `;
                }, 1000);
            };
          </script>
        </section>

        <!-- Store Settings Section -->
        <section id="storeSettings" class="content-section">
          <div id="storeSettingsContent">
            <!-- Store settings content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i
                class="fas fa-spinner fa-spin"
                style="font-size: 2rem; color: #667eea"
              ></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل إعدادات المتجر...
              </p>
              <button onclick="forceLoadStoreSettingsNow()" style="margin-top: 20px; padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                <i class="fas fa-bolt"></i> إجبار التحميل الآن
              </button>
              <br>
              <a href="store-settings-standalone.html" target="_blank" style="display: inline-block; margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 6px; font-size: 0.9em;">
                <i class="fas fa-external-link-alt"></i> فتح الصفحة المستقلة
              </a>
            </div>
          </div>

          <script>
            // Force load store settings function
            window.forceLoadStoreSettingsNow = function() {
                console.log('⚡ إجبار تحميل إعدادات المتجر الآن...');

                const container = document.getElementById('storeSettingsContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-cog fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="color: #666; font-weight: bold;">إجبار تحميل إعدادات المتجر...</p>
                        <p style="color: #999; font-size: 0.9em;">يتم الآن جلب البيانات من الخادم...</p>
                    </div>
                `;

                // Try to use the main function first
                if (typeof loadStoreSettingsContent === 'function') {
                    console.log('✅ استخدام الدالة الرئيسية...');
                    loadStoreSettingsContent();
                    return;
                }

                // Fallback: Direct fetch
                console.log('🔄 استخدام الطريقة البديلة...');
                fetch('php/store_settings.php?action=get_all&include_private=true')
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status, response.statusText);
                    if (!response.ok) throw new Error('HTTP ' + response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📦 البيانات المستلمة:', data);
                    if (data.success && data.data) {
                        console.log('✅ نجح جلب البيانات');
                        renderStoreSettingsSimple(container, data.data);
                    } else {
                        throw new Error(data.message || 'بيانات غير صحيحة');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ:', error);
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #dc3545;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                            <h3>خطأ في تحميل إعدادات المتجر</h3>
                            <p>${error.message}</p>
                            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-top: 20px;">
                                <button onclick="forceLoadStoreSettingsNow()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                    <i class="fas fa-redo"></i> إعادة المحاولة
                                </button>
                                <a href="php/store_settings.php?action=get_all" target="_blank" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 6px;">
                                    <i class="fas fa-external-link-alt"></i> اختبار API
                                </a>
                                <a href="store-settings-standalone.html" target="_blank" style="padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 6px;">
                                    <i class="fas fa-external-link-alt"></i> الصفحة المستقلة
                                </a>
                            </div>
                        </div>
                    `;
                });
            };

            function renderStoreSettingsSimple(container, data) {
                const categories = Object.keys(data.settings || {});
                const totalSettings = data.total_settings || 0;

                const html = `
                    <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                            <h2 style="margin: 0;"><i class="fas fa-cog"></i> إعدادات المتجر</h2>
                            <p style="margin: 10px 0 0 0;">تم التحميل بنجاح!</p>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;"><i class="fas fa-cog"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${totalSettings}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">إجمالي الإعدادات</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-layer-group"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${categories.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">فئات الإعدادات</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;"><i class="fas fa-globe"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${Object.values(data.settings || {}).flat().filter(s => s.is_public == 1).length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">الإعدادات العامة</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-shield-alt"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${Object.values(data.settings || {}).flat().filter(s => s.is_public == 0).length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">الإعدادات الخاصة</p>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 25px;">
                            <h3 style="margin-bottom: 20px; color: #333; text-align: center;"><i class="fas fa-list"></i> فئات الإعدادات (${categories.length})</h3>
                            ${renderSettingsCategoriesSimple(data.settings, data.categories)}
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; text-align: center;">
                            <i class="fas fa-check-circle"></i> <strong>تم تحميل إعدادات المتجر بنجاح!</strong>
                            <br>تم عرض ${totalSettings} إعداد في ${categories.length} فئة.
                            <br><a href="store-settings-standalone.html" target="_blank" style="color: #155724; text-decoration: underline;">فتح الواجهة الكاملة</a>
                        </div>
                    </div>
                `;

                container.innerHTML = html;
            }

            function renderSettingsCategoriesSimple(settings, categories) {
                if (!settings) return '<p style="text-align: center; color: #666;">لا توجد إعدادات للعرض</p>';

                let html = '';
                Object.keys(settings).forEach(category => {
                    const categorySettings = settings[category] || [];
                    const categoryInfo = categories[category] || {};

                    html += `
                        <div style="margin-bottom: 15px; padding: 15px; border-right: 4px solid #667eea; background: #f8f9fa; border-radius: 6px;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                <i class="fas fa-cog" style="color: #667eea; font-size: 1.2rem;"></i>
                                <strong style="color: #333;">${categoryInfo.name || category}</strong>
                                <span style="background: #667eea; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">${categorySettings.length}</span>
                            </div>
                            <p style="margin: 0 0 10px 25px; color: #666; font-size: 0.9em;">
                                ${categorySettings.length} إعداد في هذه الفئة
                            </p>
                            <div style="margin-right: 25px; color: #999; font-size: 0.8em;">
                                <span>${categorySettings.filter(s => s.is_public == 1).length} عام</span>
                                <span style="margin-right: 15px;">${categorySettings.filter(s => s.is_public == 0).length} خاص</span>
                                <span style="margin-right: 15px;">${categorySettings.filter(s => s.is_required == 1).length} مطلوب</span>
                            </div>
                        </div>
                    `;
                });

                return html;
            }
          </script>
        </section>

        <!-- User Management Section -->
        <section id="userManagement" class="content-section">
          <div id="userManagementContent">
            <!-- User management content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i
                class="fas fa-spinner fa-spin"
                style="font-size: 2rem; color: #667eea"
              ></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل إدارة المستخدمين...
              </p>
              <button onclick="forceLoadUsersManagementNow()" style="margin-top: 20px; padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                <i class="fas fa-bolt"></i> إجبار التحميل الآن
              </button>
              <br>
              <a href="users-management-standalone.html" target="_blank" style="display: inline-block; margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 6px; font-size: 0.9em;">
                <i class="fas fa-external-link-alt"></i> فتح الصفحة المستقلة
              </a>
            </div>
          </div>

          <script>
            // Force load users management function
            window.forceLoadUsersManagementNow = function() {
                console.log('⚡ إجبار تحميل إدارة المستخدمين الآن...');

                const container = document.getElementById('userManagementContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-cog fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="color: #666; font-weight: bold;">إجبار تحميل إدارة المستخدمين...</p>
                        <p style="color: #999; font-size: 0.9em;">يتم الآن جلب البيانات من الخادم...</p>
                    </div>
                `;

                // Try to use the main function first
                if (typeof loadUsersManagementContent === 'function') {
                    console.log('✅ استخدام الدالة الرئيسية...');
                    loadUsersManagementContent();
                    return;
                }

                // Fallback: Direct fetch
                console.log('🔄 استخدام الطريقة البديلة...');
                fetch('php/users_management.php?action=get_all&limit=10')
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status, response.statusText);
                    if (!response.ok) throw new Error('HTTP ' + response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📦 البيانات المستلمة:', data);
                    if (data.success && data.data) {
                        console.log('✅ نجح جلب البيانات');
                        renderUsersManagementSimple(container, data.data);
                    } else {
                        throw new Error(data.message || 'بيانات غير صحيحة');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ:', error);
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #dc3545;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                            <h3>خطأ في تحميل إدارة المستخدمين</h3>
                            <p>${error.message}</p>
                            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-top: 20px;">
                                <button onclick="forceLoadUsersManagementNow()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                    <i class="fas fa-redo"></i> إعادة المحاولة
                                </button>
                                <a href="php/users_management.php?action=get_all" target="_blank" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 6px;">
                                    <i class="fas fa-external-link-alt"></i> اختبار API
                                </a>
                                <a href="users-management-standalone.html" target="_blank" style="padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 6px;">
                                    <i class="fas fa-external-link-alt"></i> الصفحة المستقلة
                                </a>
                            </div>
                        </div>
                    `;
                });
            };

            function renderUsersManagementSimple(container, data) {
                const users = data.users || [];
                const stats = data.statistics || {};
                const roles = data.roles || [];
                const pagination = data.pagination || {};

                const html = `
                    <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                            <h2 style="margin: 0;"><i class="fas fa-users"></i> إدارة المستخدمين</h2>
                            <p style="margin: 10px 0 0 0;">تم التحميل بنجاح!</p>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;"><i class="fas fa-users"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${stats.total_users || users.length}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">إجمالي المستخدمين</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-user-check"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${stats.active_users || 0}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">المستخدمون النشطون</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;"><i class="fas fa-user-shield"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${stats.verified_users || 0}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">المستخدمون المتحققون</p>
                            </div>
                            <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-user-plus"></i></div>
                                <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${stats.today_registrations || 0}</h3>
                                <p style="margin: 8px 0 0 0; color: #666;">تسجيلات اليوم</p>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 25px; margin-bottom: 20px;">
                            <h3 style="margin-bottom: 20px; color: #333; text-align: center;"><i class="fas fa-list"></i> المستخدمون الحاليون (${users.length})</h3>
                            ${renderUsersListSimple(users)}
                        </div>

                        <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 25px;">
                            <h3 style="margin-bottom: 20px; color: #333; text-align: center;"><i class="fas fa-user-tag"></i> الأدوار المتاحة (${roles.length})</h3>
                            ${renderRolesListSimple(roles)}
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; text-align: center;">
                            <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة المستخدمين بنجاح!</strong>
                            <br>تم عرض ${users.length} مستخدم من أصل ${stats.total_users || users.length} مستخدم.
                            <br><a href="users-management-standalone.html" target="_blank" style="color: #155724; text-decoration: underline;">فتح الواجهة الكاملة</a>
                        </div>
                    </div>
                `;

                container.innerHTML = html;
            }

            function renderUsersListSimple(users) {
                if (!users || users.length === 0) {
                    return '<p style="text-align: center; color: #666;">لا توجد مستخدمين للعرض</p>';
                }

                let html = '';
                users.forEach(user => {
                    const avatar = getInitialsSimple(user.full_name || user.username);
                    const roles = user.roles_ar ? user.roles_ar.split(', ') : [];
                    const roleColors = user.role_colors ? user.role_colors.split(', ') : [];
                    const activeStatus = user.is_active == 1 ? 'نشط' : 'غير نشط';
                    const verifiedStatus = user.is_verified == 1 ? 'متحقق' : 'غير متحقق';

                    html += `
                        <div style="margin-bottom: 15px; padding: 15px; border-right: 4px solid #667eea; background: #f8f9fa; border-radius: 6px;">
                            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 10px;">
                                <div style="width: 40px; height: 40px; border-radius: 50%; background: #667eea; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold;">
                                    ${avatar}
                                </div>
                                <div style="flex: 1;">
                                    <strong style="color: #333; font-size: 1.1rem;">${user.full_name || user.username}</strong>
                                    <p style="margin: 2px 0; color: #666; font-size: 0.9em;">${user.email}</p>
                                </div>
                                <div style="text-align: left;">
                                    <span style="display: inline-block; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem; margin: 2px; ${user.is_active == 1 ? 'background: #d4edda; color: #155724;' : 'background: #f8d7da; color: #721c24;'}">${activeStatus}</span>
                                    <span style="display: inline-block; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem; margin: 2px; ${user.is_verified == 1 ? 'background: #d1ecf1; color: #0c5460;' : 'background: #fff3cd; color: #856404;'}">${verifiedStatus}</span>
                                </div>
                            </div>
                            <div style="margin-right: 55px;">
                                <div style="display: flex; gap: 5px; flex-wrap: wrap; margin-bottom: 8px;">
                                    ${roles.map((role, index) => `
                                        <span style="padding: 2px 6px; border-radius: 10px; font-size: 0.75rem; color: white; background: ${roleColors[index] || '#667eea'};">
                                            ${role}
                                        </span>
                                    `).join('')}
                                </div>
                                <div style="color: #999; font-size: 0.8em;">
                                    تاريخ التسجيل: ${new Date(user.created_at).toLocaleDateString('ar-SA')}
                                    ${user.last_login_at ? ` • آخر دخول: ${new Date(user.last_login_at).toLocaleDateString('ar-SA')}` : ' • لم يسجل دخول'}
                                </div>
                            </div>
                        </div>
                    `;
                });

                return html;
            }

            function renderRolesListSimple(roles) {
                if (!roles || roles.length === 0) {
                    return '<p style="text-align: center; color: #666;">لا توجد أدوار للعرض</p>';
                }

                let html = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">';

                roles.forEach(role => {
                    html += `
                        <div style="padding: 15px; border: 2px solid ${role.color}; border-radius: 8px; background: linear-gradient(135deg, ${role.color}15 0%, #ffffff 100%);">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                                <i class="${role.icon}" style="color: ${role.color}; font-size: 1.5rem;"></i>
                                <div>
                                    <strong style="color: #333;">${role.display_name_ar}</strong>
                                    <p style="margin: 2px 0 0 0; color: #666; font-size: 0.9em;">${role.description_ar || 'لا يوجد وصف'}</p>
                                </div>
                            </div>
                            <div style="text-align: center; color: #999; font-size: 0.8em;">
                                ${role.users_count} مستخدم
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
                return html;
            }

            function getInitialsSimple(name) {
                if (!name) return '?';
                const words = name.trim().split(' ');
                if (words.length >= 2) {
                    return (words[0][0] + words[1][0]).toUpperCase();
                }
                return name[0].toUpperCase();
            }
          </script>
        </section>

        <!-- Stores Management Section -->
        <section id="storesManagement" class="content-section">
          <div id="storesManagementContent">
            <!-- Header Section -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
                <i class="fas fa-store-alt" style="font-size: 3rem;"></i>
                <div>
                  <h2 style="margin: 0; font-size: 2rem;">إدارة المتاجر</h2>
                  <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة المتاجر والمالكين والإعدادات</p>
                </div>
              </div>
              <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap; margin-top: 20px;">
                <button onclick="showAddStoreModal()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                  <i class="fas fa-plus"></i> إضافة متجر جديد
                </button>
                <button onclick="refreshStores()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                  <i class="fas fa-sync-alt"></i> تحديث
                </button>
              </div>
            </div>

            <!-- Store Statistics -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
              <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #48bb78;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 1.8rem;">24</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">إجمالي المتاجر</p>
                  </div>
                  <i class="fas fa-store" style="font-size: 2rem; color: #48bb78;"></i>
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #4299e1;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 1.8rem;">18</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">متاجر نشطة</p>
                  </div>
                  <i class="fas fa-check-circle" style="font-size: 2rem; color: #4299e1;"></i>
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #ed8936;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 1.8rem;">6</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">في انتظار المراجعة</p>
                  </div>
                  <i class="fas fa-clock" style="font-size: 2rem; color: #ed8936;"></i>
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #9f7aea;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 1.8rem;">156</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">إجمالي المنتجات</p>
                  </div>
                  <i class="fas fa-box" style="font-size: 2rem; color: #9f7aea;"></i>
                </div>
              </div>
            </div>

            <!-- Stores Grid -->
            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 20px; margin-bottom: 30px;">
              <!-- Store Card 1 -->
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 1px solid #e2e8f0;">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
                  <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                    <i class="fas fa-store"></i>
                  </div>
                  <div style="flex: 1;">
                    <h3 style="margin: 0; color: #2d3748;">متجر الإلكترونيات الذكية</h3>
                    <p style="margin: 5px 0; color: #718096; font-size: 0.9em;">أحمد محمد</p>
                    <span style="background: #48bb78; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.75em;">نشط</span>
                  </div>
                </div>

                <div style="margin-bottom: 20px;">
                  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
                    <div>
                      <span style="color: #718096;">المنتجات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">24</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">المبيعات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">1,234</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">التقييم:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">4.8 ⭐</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">الإيرادات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">45,600 دج</strong>
                    </div>
                  </div>
                </div>

                <div style="display: flex; gap: 10px; justify-content: space-between;">
                  <button onclick="viewStore(1)" style="flex: 1; padding: 8px 12px; background: #4299e1; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-eye"></i> عرض
                  </button>
                  <button onclick="editStore(1)" style="flex: 1; padding: 8px 12px; background: #ed8936; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-edit"></i> تعديل
                  </button>
                  <button onclick="toggleStoreStatus(1)" style="flex: 1; padding: 8px 12px; background: #f56565; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-pause"></i> إيقاف
                  </button>
                </div>
              </div>

              <!-- Store Card 2 -->
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 1px solid #e2e8f0;">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
                  <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #48bb78, #38a169); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                    <i class="fas fa-tshirt"></i>
                  </div>
                  <div style="flex: 1;">
                    <h3 style="margin: 0; color: #2d3748;">بوتيك الأزياء العصرية</h3>
                    <p style="margin: 5px 0; color: #718096; font-size: 0.9em;">فاطمة أحمد</p>
                    <span style="background: #48bb78; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.75em;">نشط</span>
                  </div>
                </div>

                <div style="margin-bottom: 20px;">
                  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
                    <div>
                      <span style="color: #718096;">المنتجات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">67</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">المبيعات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">892</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">التقييم:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">4.6 ⭐</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">الإيرادات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">32,400 دج</strong>
                    </div>
                  </div>
                </div>

                <div style="display: flex; gap: 10px; justify-content: space-between;">
                  <button onclick="viewStore(2)" style="flex: 1; padding: 8px 12px; background: #4299e1; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-eye"></i> عرض
                  </button>
                  <button onclick="editStore(2)" style="flex: 1; padding: 8px 12px; background: #ed8936; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-edit"></i> تعديل
                  </button>
                  <button onclick="toggleStoreStatus(2)" style="flex: 1; padding: 8px 12px; background: #f56565; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-pause"></i> إيقاف
                  </button>
                </div>
              </div>

              <!-- Store Card 3 - Pending Review -->
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 1px solid #ed8936;">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
                  <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #ed8936, #dd6b20); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                    <i class="fas fa-utensils"></i>
                  </div>
                  <div style="flex: 1;">
                    <h3 style="margin: 0; color: #2d3748;">مطعم الأطباق الشعبية</h3>
                    <p style="margin: 5px 0; color: #718096; font-size: 0.9em;">محمد علي</p>
                    <span style="background: #ed8936; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.75em;">في انتظار المراجعة</span>
                  </div>
                </div>

                <div style="margin-bottom: 20px;">
                  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
                    <div>
                      <span style="color: #718096;">المنتجات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">12</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">المبيعات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">0</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">التقييم:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">- ⭐</strong>
                    </div>
                    <div>
                      <span style="color: #718096;">الإيرادات:</span>
                      <strong style="color: #2d3748; margin-right: 5px;">0 دج</strong>
                    </div>
                  </div>
                </div>

                <div style="display: flex; gap: 10px; justify-content: space-between;">
                  <button onclick="approveStore(3)" style="flex: 1; padding: 8px 12px; background: #48bb78; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-check"></i> موافقة
                  </button>
                  <button onclick="reviewStore(3)" style="flex: 1; padding: 8px 12px; background: #4299e1; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-eye"></i> مراجعة
                  </button>
                  <button onclick="rejectStore(3)" style="flex: 1; padding: 8px 12px; background: #f56565; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.9em;">
                    <i class="fas fa-times"></i> رفض
                  </button>
                </div>
              </div>
            </div>

            <!-- Recent Activity -->
            <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <h3 style="margin: 0 0 20px 0; color: #2d3748;">النشاط الأخير</h3>
              <div style="space-y: 15px;">
                <div style="display: flex; align-items: center; gap: 15px; padding: 15px; background: #f7fafc; border-radius: 8px; margin-bottom: 10px;">
                  <div style="width: 40px; height: 40px; background: #48bb78; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-plus"></i>
                  </div>
                  <div style="flex: 1;">
                    <p style="margin: 0; color: #2d3748; font-weight: 600;">متجر جديد تم إنشاؤه</p>
                    <p style="margin: 5px 0 0 0; color: #718096; font-size: 0.9em;">متجر "الأجهزة المنزلية" - بواسطة سارة أحمد</p>
                  </div>
                  <span style="color: #718096; font-size: 0.8em;">منذ 5 دقائق</span>
                </div>

                <div style="display: flex; align-items: center; gap: 15px; padding: 15px; background: #f7fafc; border-radius: 8px; margin-bottom: 10px;">
                  <div style="width: 40px; height: 40px; background: #4299e1; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-edit"></i>
                  </div>
                  <div style="flex: 1;">
                    <p style="margin: 0; color: #2d3748; font-weight: 600;">تحديث معلومات المتجر</p>
                    <p style="margin: 5px 0 0 0; color: #718096; font-size: 0.9em;">متجر "الإلكترونيات الذكية" - تحديث العنوان</p>
                  </div>
                  <span style="color: #718096; font-size: 0.8em;">منذ 15 دقيقة</span>
                </div>

                <div style="display: flex; align-items: center; gap: 15px; padding: 15px; background: #f7fafc; border-radius: 8px;">
                  <div style="width: 40px; height: 40px; background: #ed8936; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                    <i class="fas fa-check"></i>
                  </div>
                  <div style="flex: 1;">
                    <p style="margin: 0; color: #2d3748; font-weight: 600;">موافقة على متجر</p>
                    <p style="margin: 5px 0 0 0; color: #718096; font-size: 0.9em;">متجر "الأزياء العصرية" - تمت الموافقة</p>
                  </div>
                  <span style="color: #718096; font-size: 0.8em;">منذ ساعة</span>
                </div>
              </div>
            </div>
          </div>

          <script>
            // Store management functions
            window.showAddStoreModal = function() {
              alert('إضافة متجر جديد - سيتم تطوير هذه الميزة قريباً');
            };

            window.refreshStores = function() {
              alert('تحديث قائمة المتاجر - سيتم تطوير هذه الميزة قريباً');
            };

            window.viewStore = function(storeId) {
              alert(`عرض تفاصيل المتجر رقم ${storeId} - سيتم تطوير هذه الميزة قريباً`);
            };

            window.editStore = function(storeId) {
              alert(`تعديل المتجر رقم ${storeId} - سيتم تطوير هذه الميزة قريباً`);
            };

            window.toggleStoreStatus = function(storeId) {
              if (confirm('هل أنت متأكد من تغيير حالة هذا المتجر؟')) {
                alert(`تغيير حالة المتجر رقم ${storeId} - سيتم تطوير هذه الميزة قريباً`);
              }
            };

            window.approveStore = function(storeId) {
              if (confirm('هل أنت متأكد من الموافقة على هذا المتجر؟')) {
                alert(`الموافقة على المتجر رقم ${storeId} - سيتم تطوير هذه الميزة قريباً`);
              }
            };

            window.reviewStore = function(storeId) {
              alert(`مراجعة المتجر رقم ${storeId} - سيتم تطوير هذه الميزة قريباً`);
            };

            window.rejectStore = function(storeId) {
              if (confirm('هل أنت متأكد من رفض هذا المتجر؟')) {
                alert(`رفض المتجر رقم ${storeId} - سيتم تطوير هذه الميزة قريباً`);
              }
            };
          </script>

            <!-- Stores Table -->
            <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-top: 30px;">
              <h3 style="margin: 0 0 20px 0; color: #2d3748; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-table"></i>
                جدول المتاجر التفصيلي
              </h3>

              <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #f7fafc; border-bottom: 2px solid #e2e8f0;">
                      <th style="padding: 15px; text-align: right; font-weight: bold; color: #4a5568;">اسم المتجر</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">المالك</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الحالة</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">المنتجات</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">المبيعات</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الإيرادات</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">تاريخ الإنشاء</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody id="storesTableBody">
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">متجر الإلكترونيات الذكية</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">أحمد محمد</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشط</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">24</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">1,234</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">45,600 دج</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-15</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #f56565; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-pause"></i>
                        </button>
                      </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">بوتيك الأزياء العصرية</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">فاطمة أحمد</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشط</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">67</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">892</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">32,400 دج</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-10</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #f56565; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-pause"></i>
                        </button>
                      </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">متجر الكتب والقرطاسية</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">محمد علي</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #fed7d7; color: #742a2a; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">في انتظار المراجعة</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">45</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">0</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">0 دج</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-20</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-check"></i>
                        </button>
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #f56565; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-times"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          </script>
        </section>

        <!-- Roles Management Section -->
        <section id="rolesManagement" class="content-section">
          <div id="rolesManagementContent">
            <!-- Header Section -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
                <i class="fas fa-user-shield" style="font-size: 2.5rem;"></i>
                <h2 style="margin: 0; font-size: 2rem; font-weight: bold;">إدارة الأدوار والصلاحيات</h2>
              </div>
              <p style="margin: 0; font-size: 1.1rem; opacity: 0.9;">تحكم في أدوار المستخدمين وصلاحياتهم</p>
              <div style="margin-top: 20px;">
                <button onclick="alert('إضافة دور جديد - قيد التطوير')" style="padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin-left: 10px;">
                  <i class="fas fa-plus"></i> إضافة دور جديد
                </button>
                <button onclick="loadRolesManagementContent()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                  <i class="fas fa-sync-alt"></i> تحديث
                </button>
              </div>
            </div>

            <!-- Statistics Cards -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
              <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <i class="fas fa-users-cog" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3 style="margin: 0; font-size: 2rem; font-weight: bold;">8</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">إجمالي الأدوار</p>
              </div>
              <div style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <i class="fas fa-shield-alt" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3 style="margin: 0; font-size: 2rem; font-weight: bold;">5</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">أدوار نشطة</p>
              </div>
              <div style="background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <i class="fas fa-key" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3 style="margin: 0; font-size: 2rem; font-weight: bold;">24</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">إجمالي الصلاحيات</p>
              </div>
              <div style="background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                <i class="fas fa-user-check" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h3 style="margin: 0; font-size: 2rem; font-weight: bold;">156</h3>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">مستخدمين مُعيَّنين</p>
              </div>
            </div>

            <!-- Roles Table -->
            <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
              <h3 style="margin: 0 0 20px 0; color: #2d3748; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-list"></i>
                قائمة الأدوار والصلاحيات
              </h3>

              <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="background: #f7fafc; border-bottom: 2px solid #e2e8f0;">
                      <th style="padding: 15px; text-align: right; font-weight: bold; color: #4a5568;">اسم الدور</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الوصف</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الصلاحيات</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">المستخدمين</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الحالة</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">تاريخ الإنشاء</th>
                      <th style="padding: 15px; text-align: center; font-weight: bold; color: #4a5568;">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody id="rolesTableBody">
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">مدير عام</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">صلاحيات كاملة للنظام</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">جميع الصلاحيات</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">3</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشط</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-01</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-users"></i>
                        </button>
                      </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">مدير المتاجر</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">إدارة المتاجر والمنتجات</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">12 صلاحية</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">8</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشط</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-05</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-users"></i>
                        </button>
                      </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">مدير المحتوى</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">إدارة المحتوى والصفحات</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">8 صلاحيات</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">15</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشط</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-10</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-users"></i>
                        </button>
                      </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">مشرف الدعم</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">دعم العملاء والمساعدة</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">6 صلاحيات</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">22</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشط</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-12</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-users"></i>
                        </button>
                      </td>
                    </tr>
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                      <td style="padding: 15px; color: #2d3748; font-weight: 500;">مستخدم عادي</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">صلاحيات أساسية للمستخدمين</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">4 صلاحيات</td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">108</td>
                      <td style="padding: 15px; text-align: center;">
                        <span style="background: #c6f6d5; color: #22543d; padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: bold;">نشط</span>
                      </td>
                      <td style="padding: 15px; text-align: center; color: #4a5568;">2024-01-01</td>
                      <td style="padding: 15px; text-align: center;">
                        <button style="background: #4299e1; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button style="background: #48bb78; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button style="background: #ed8936; color: white; border: none; padding: 6px 12px; border-radius: 6px; cursor: pointer; margin: 0 2px; font-size: 0.875rem;">
                          <i class="fas fa-users"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <script>
            // Force load function for roles management
            window.forceLoadRolesNow = function() {
                console.log('⚡ إجبار تحميل إدارة الأدوار التفاعلية الآن...');

                const container = document.getElementById('rolesManagementContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading state
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea"></i>
                        <p style="margin-top: 15px; color: #666">جاري تحميل إدارة الأدوار...</p>
                    </div>
                `;

                // Fetch roles data
                fetch('../php/api/roles-fixed.php?action=list')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            displayRolesInterface(data.data.roles);
                        } else {
                            throw new Error(data.message || 'فشل في تحميل البيانات');
                        }
                    })
                    .catch(error => {
                        console.error('❌ خطأ في تحميل الأدوار:', error);
                        container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #dc3545;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                            <h3>خطأ في تحميل إدارة الأدوار</h3>
                            <p>${error.message}</p>
                            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-top: 20px;">
                                <button onclick="forceLoadRolesNow()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                    <i class="fas fa-redo"></i> إعادة المحاولة
                                </button>
                                <a href="../php/api/roles-fixed.php?action=list" target="_blank" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 6px;">
                                    <i class="fas fa-external-link-alt"></i> اختبار API
                                </a>
                            </div>
                        </div>
                        `;
                    });
            };

            function displayRolesInterface(roles) {
                // Store current roles data for editing
                window.currentRolesData = roles;

                const container = document.getElementById('rolesManagementContent');
                container.innerHTML = `
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
                        <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
                            <i class="fas fa-user-shield" style="font-size: 3rem;"></i>
                            <div>
                                <h2 style="margin: 0; font-size: 2rem;">إدارة الأدوار</h2>
                                <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة أدوار المستخدمين والصلاحيات</p>
                            </div>
                        </div>
                        <p style="margin: 10px 0 0 0;">تم التحميل بنجاح! يمكنك الآن إضافة وتعديل وحذف الأدوار.</p>
                        <div style="margin-top: 20px;">
                            <button onclick="showAddRoleModal()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                                <i class="fas fa-plus"></i> إضافة دور جديد
                            </button>
                            <button onclick="forceLoadRolesNow()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </div>

                    <div class="roles-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;">
                        ${roles.map(role => `
                            <div class="role-card" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 1px solid #e2e8f0;">
                                <div style="display: flex; align-items: center; justify-content: between; margin-bottom: 15px;">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div style="width: 40px; height: 40px; background: ${role.color || '#667eea'}; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                                            <i class="${role.icon || 'fas fa-user'}"></i>
                                        </div>
                                        <div>
                                            <h4 style="margin: 0; color: #2d3748;">${role.display_name_ar}</h4>
                                            <small style="color: #718096;">${role.display_name_en}</small>
                                        </div>
                                    </div>
                                    <div style="display: flex; gap: 5px;">
                                        <button onclick="editRole(${role.id})" style="padding: 6px 10px; background: #4299e1; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8em;">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteRole(${role.id})" style="padding: 6px 10px; background: #f56565; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8em;">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <p style="color: #4a5568; margin-bottom: 15px; font-size: 0.9em;">${role.description || 'لا يوجد وصف'}</p>
                                <div style="margin-bottom: 15px;">
                                    <strong style="color: #2d3748; font-size: 0.9em;">الصلاحيات:</strong>
                                    <div style="margin-top: 8px; display: flex; flex-wrap: wrap; gap: 5px;">
                                        ${role.permissions && role.permissions.length > 0 ?
                                            role.permissions.map(permission => `
                                                <span style="background: #e2e8f0; color: #4a5568; padding: 4px 8px; border-radius: 12px; font-size: 0.75em;">${permission}</span>
                                            `).join('') :
                                            '<span style="color: #a0aec0; font-size: 0.8em;">لا توجد صلاحيات</span>'
                                        }
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.8em; color: #718096;">
                                    <span>المستوى: ${role.level}</span>
                                    <span>المستخدمين: ${role.user_count || 0}</span>
                                    <span style="color: ${role.is_active ? '#48bb78' : '#f56565'};">
                                        ${role.is_active ? 'نشط' : 'غير نشط'}
                                    </span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            // Role Management CRUD Functions
            window.showAddRoleModal = function() {
                showRoleModal('add');
            };

            window.editRole = function(roleId) {
                showRoleModal('edit', roleId);
            };

            window.deleteRole = function(roleId) {
                if (confirm('هل أنت متأكد من حذف هذا الدور؟\n\nتحذير: سيتم حذف الدور نهائياً ولا يمكن التراجع عن هذا الإجراء.')) {
                    performRoleDelete(roleId);
                }
            };

            function showRoleModal(action, roleId = null) {
                const isEdit = action === 'edit';
                const modalTitle = isEdit ? 'تعديل الدور' : 'إضافة دور جديد';

                // Create modal HTML
                const modalHtml = `
                    <div id="roleModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div style="background: white; border-radius: 12px; padding: 30px; max-width: 900px; width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; padding-bottom: 15px; border-bottom: 2px solid #e2e8f0;">
                                <h3 style="margin: 0; color: #2d3748; font-size: 1.5rem;">
                                    <i class="fas fa-user-shield" style="color: #667eea; margin-left: 10px;"></i>
                                    ${modalTitle}
                                </h3>
                                <button onclick="closeRoleModal()" style="background: none; border: none; font-size: 1.5rem; color: #718096; cursor: pointer; padding: 5px;">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <form id="roleForm" style="display: grid; gap: 20px;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">اسم الدور (بالإنجليزية) *</label>
                                        <input type="text" id="roleName" name="name" required
                                               style="width: 100%; padding: 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s;"
                                               placeholder="admin, user, manager">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">المستوى *</label>
                                        <input type="number" id="roleLevel" name="level" required min="1" max="100"
                                               style="width: 100%; padding: 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s;"
                                               placeholder="1-100">
                                    </div>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">الاسم بالعربية *</label>
                                        <input type="text" id="roleDisplayNameAr" name="display_name_ar" required
                                               style="width: 100%; padding: 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s;"
                                               placeholder="مدير، مستخدم، مشرف">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">الاسم بالإنجليزية *</label>
                                        <input type="text" id="roleDisplayNameEn" name="display_name_en" required
                                               style="width: 100%; padding: 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1rem; transition: border-color 0.3s;"
                                               placeholder="Admin, User, Manager">
                                    </div>
                                </div>

                                <div>
                                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">الوصف</label>
                                    <textarea id="roleDescription" name="description" rows="3"
                                              style="width: 100%; padding: 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1rem; resize: vertical; transition: border-color 0.3s;"
                                              placeholder="وصف مختصر للدور ومسؤولياته"></textarea>
                                </div>

                                <div>
                                    <label style="display: block; margin-bottom: 12px; font-weight: bold; color: #4a5568;">الصلاحيات</label>
                                    <div id="permissionsContainer" style="background: #f7fafc; border-radius: 12px; border: 2px solid #e2e8f0; padding: 5px; min-height: 200px;">
                                        <!-- Permissions will be loaded here -->
                                    </div>
                                </div>

                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="checkbox" id="roleIsActive" name="is_active" checked style="width: 18px; height: 18px;">
                                    <label for="roleIsActive" style="font-weight: bold; color: #4a5568;">الدور نشط</label>
                                </div>

                                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
                                    <button type="button" onclick="closeRoleModal()"
                                            style="padding: 12px 24px; background: #e2e8f0; color: #4a5568; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; transition: all 0.3s;">
                                        <i class="fas fa-times"></i> إلغاء
                                    </button>
                                    <button type="submit"
                                            style="padding: 12px 24px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; transition: all 0.3s;">
                                        <i class="fas fa-save"></i> ${isEdit ? 'تحديث الدور' : 'إضافة الدور'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                `;

                // Add modal to page
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // Load permissions and role data if editing
                loadPermissionsForModal();
                if (isEdit && roleId) {
                    loadRoleDataForEdit(roleId);
                }

                // Setup form submission
                document.getElementById('roleForm').addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitRoleForm(action, roleId);
                });

                // Add input focus effects
                const inputs = document.querySelectorAll('#roleModal input, #roleModal textarea');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        this.style.borderColor = '#667eea';
                        this.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                    });
                    input.addEventListener('blur', function() {
                        this.style.borderColor = '#e2e8f0';
                        this.style.boxShadow = 'none';
                    });
                });
            }

            window.closeRoleModal = function() {
                const modal = document.getElementById('roleModal');
                if (modal) {
                    modal.remove();
                }
            };

            function loadPermissionsForModal() {
                const container = document.getElementById('permissionsContainer');

                // Define available permissions with categories and Arabic descriptions
                const permissionCategories = {
                    'إدارة المستخدمين': {
                        icon: 'fas fa-users',
                        color: '#4299e1',
                        permissions: [
                            { key: 'users.view', name: 'عرض المستخدمين' },
                            { key: 'users.create', name: 'إضافة مستخدمين' },
                            { key: 'users.edit', name: 'تعديل المستخدمين' },
                            { key: 'users.delete', name: 'حذف المستخدمين' }
                        ]
                    },
                    'إدارة المنتجات': {
                        icon: 'fas fa-box',
                        color: '#48bb78',
                        permissions: [
                            { key: 'products.view', name: 'عرض المنتجات' },
                            { key: 'products.create', name: 'إضافة منتجات' },
                            { key: 'products.edit', name: 'تعديل المنتجات' },
                            { key: 'products.delete', name: 'حذف المنتجات' }
                        ]
                    },
                    'إدارة الطلبات': {
                        icon: 'fas fa-shopping-cart',
                        color: '#ed8936',
                        permissions: [
                            { key: 'orders.view', name: 'عرض الطلبات' },
                            { key: 'orders.create', name: 'إنشاء طلبات' },
                            { key: 'orders.edit', name: 'تعديل الطلبات' },
                            { key: 'orders.delete', name: 'حذف الطلبات' }
                        ]
                    },
                    'إدارة المتاجر': {
                        icon: 'fas fa-store',
                        color: '#9f7aea',
                        permissions: [
                            { key: 'stores.view', name: 'عرض المتاجر' },
                            { key: 'stores.create', name: 'إضافة متاجر' },
                            { key: 'stores.edit', name: 'تعديل المتاجر' },
                            { key: 'stores.delete', name: 'حذف المتاجر' }
                        ]
                    },
                    'إدارة الفئات': {
                        icon: 'fas fa-tags',
                        color: '#38b2ac',
                        permissions: [
                            { key: 'categories.view', name: 'عرض الفئات' },
                            { key: 'categories.create', name: 'إضافة فئات' },
                            { key: 'categories.edit', name: 'تعديل الفئات' },
                            { key: 'categories.delete', name: 'حذف الفئات' }
                        ]
                    },
                    'إعدادات النظام': {
                        icon: 'fas fa-cogs',
                        color: '#718096',
                        permissions: [
                            { key: 'settings.view', name: 'عرض الإعدادات' },
                            { key: 'settings.edit', name: 'تعديل الإعدادات' },
                            { key: 'system.backup', name: 'النسخ الاحتياطي' },
                            { key: 'system.logs', name: 'سجلات النظام' }
                        ]
                    },
                    'التقارير': {
                        icon: 'fas fa-chart-bar',
                        color: '#f56565',
                        permissions: [
                            { key: 'reports.view', name: 'عرض التقارير' },
                            { key: 'reports.export', name: 'تصدير التقارير' },
                            { key: 'analytics.view', name: 'عرض الإحصائيات' }
                        ]
                    },
                    'الاشتراكات': {
                        icon: 'fas fa-crown',
                        color: '#ffd700',
                        permissions: [
                            { key: 'subscriptions.view', name: 'عرض الاشتراكات' },
                            { key: 'subscriptions.manage', name: 'إدارة الاشتراكات' },
                            { key: 'payments.view', name: 'عرض المدفوعات' }
                        ]
                    }
                };

                let permissionsHtml = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; max-height: 400px; overflow-y: auto; padding: 10px;">
                `;

                Object.entries(permissionCategories).forEach(([category, categoryData]) => {
                    permissionsHtml += `
                        <div style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 12px; padding: 16px; transition: all 0.3s ease;">
                            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #e2e8f0;">
                                <div style="width: 32px; height: 32px; background: ${categoryData.color}; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem;">
                                    <i class="${categoryData.icon}"></i>
                                </div>
                                <h4 style="margin: 0; color: #2d3748; font-size: 0.95rem; font-weight: bold; flex: 1;">${category}</h4>
                                <label style="display: flex; align-items: center; gap: 5px; cursor: pointer; font-size: 0.8rem; color: #718096;">
                                    <input type="checkbox" class="category-select-all" data-category="${category}" style="width: 14px; height: 14px;" onchange="toggleCategoryPermissions(this, '${category}')">
                                    <span>تحديد الكل</span>
                                </label>
                            </div>
                            <div style="display: grid; gap: 8px;">
                                ${categoryData.permissions.map(permission => `
                                    <label style="display: flex; align-items: center; gap: 10px; cursor: pointer; padding: 8px 10px; border-radius: 6px; transition: all 0.2s ease; background: white; border: 1px solid #e2e8f0;"
                                           onmouseover="this.style.background='#edf2f7'; this.style.borderColor='${categoryData.color}'"
                                           onmouseout="this.style.background='white'; this.style.borderColor='#e2e8f0'">
                                        <input type="checkbox" name="permissions[]" value="${permission.key}" data-category="${category}"
                                               style="width: 16px; height: 16px; accent-color: ${categoryData.color};"
                                               onchange="updateCategorySelectAll('${category}')">
                                        <div style="flex: 1;">
                                            <div style="font-size: 0.9rem; color: #2d3748; font-weight: 500; margin-bottom: 2px;">${permission.name}</div>
                                            <div style="font-size: 0.75rem; color: #718096; font-family: monospace;">${permission.key}</div>
                                        </div>
                                    </label>
                                `).join('')}
                            </div>
                        </div>
                    `;
                });

                permissionsHtml += `</div>`;
                container.innerHTML = permissionsHtml;

                // Add helper functions for select all functionality
                window.toggleCategoryPermissions = function(selectAllCheckbox, category) {
                    const categoryCheckboxes = document.querySelectorAll(`input[name="permissions[]"][data-category="${category}"]`);
                    categoryCheckboxes.forEach(checkbox => {
                        checkbox.checked = selectAllCheckbox.checked;
                    });
                };

                window.updateCategorySelectAll = function(category) {
                    const categoryCheckboxes = document.querySelectorAll(`input[name="permissions[]"][data-category="${category}"]`);
                    const selectAllCheckbox = document.querySelector(`.category-select-all[data-category="${category}"]`);

                    const checkedCount = Array.from(categoryCheckboxes).filter(cb => cb.checked).length;
                    const totalCount = categoryCheckboxes.length;

                    if (checkedCount === 0) {
                        selectAllCheckbox.checked = false;
                        selectAllCheckbox.indeterminate = false;
                    } else if (checkedCount === totalCount) {
                        selectAllCheckbox.checked = true;
                        selectAllCheckbox.indeterminate = false;
                    } else {
                        selectAllCheckbox.checked = false;
                        selectAllCheckbox.indeterminate = true;
                    }
                };
            }

            function loadRoleDataForEdit(roleId) {
                // Since the API doesn't have a single role endpoint, we'll extract from current data
                // First, try to get from the last loaded roles data
                if (window.currentRolesData) {
                    const role = window.currentRolesData.find(r => r.id == roleId);
                    if (role) {
                        populateRoleForm(role);
                        return;
                    }
                }

                // Fallback: reload all roles and find the one we need
                fetch('../php/api/roles-fixed.php?action=list')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.data.roles) {
                            const role = data.data.roles.find(r => r.id == roleId);
                            if (role) {
                                populateRoleForm(role);
                            } else {
                                alert('لم يتم العثور على بيانات الدور المطلوب.');
                            }
                        } else {
                            throw new Error('فشل في تحميل بيانات الأدوار');
                        }
                    })
                    .catch(error => {
                        console.error('Error loading role data:', error);
                        alert('خطأ في تحميل بيانات الدور. يرجى المحاولة مرة أخرى.');
                    });
            }

            function populateRoleForm(role) {
                document.getElementById('roleName').value = role.name || '';
                document.getElementById('roleDisplayNameAr').value = role.display_name_ar || '';
                document.getElementById('roleDisplayNameEn').value = role.display_name_en || '';
                document.getElementById('roleDescription').value = role.description || '';
                document.getElementById('roleLevel').value = role.level || 1;
                document.getElementById('roleIsActive').checked = role.is_active;

                // Set permissions
                if (role.permissions && Array.isArray(role.permissions)) {
                    const permissionCheckboxes = document.querySelectorAll('input[name="permissions[]"]');
                    permissionCheckboxes.forEach(checkbox => {
                        checkbox.checked = role.permissions.includes(checkbox.value);
                    });

                    // Update "select all" checkboxes for each category
                    const categories = ['إدارة المستخدمين', 'إدارة المنتجات', 'إدارة الطلبات', 'إدارة المتاجر', 'إدارة الفئات', 'إعدادات النظام', 'التقارير', 'الاشتراكات'];
                    categories.forEach(category => {
                        if (typeof updateCategorySelectAll === 'function') {
                            updateCategorySelectAll(category);
                        }
                    });
                }
            }

            function submitRoleForm(action, roleId = null) {
                const form = document.getElementById('roleForm');
                const formData = new FormData(form);

                // Get selected permissions
                const selectedPermissions = [];
                const permissionCheckboxes = document.querySelectorAll('input[name="permissions[]"]:checked');
                permissionCheckboxes.forEach(checkbox => {
                    selectedPermissions.push(checkbox.value);
                });

                // Prepare data for API
                const roleData = {
                    name: formData.get('name'),
                    display_name_ar: formData.get('display_name_ar'),
                    display_name_en: formData.get('display_name_en'),
                    description: formData.get('description'),
                    level: parseInt(formData.get('level')),
                    permissions: selectedPermissions,
                    is_active: formData.get('is_active') ? 1 : 0
                };

                // Validate required fields
                if (!roleData.name || !roleData.display_name_ar || !roleData.display_name_en) {
                    alert('يرجى ملء جميع الحقول المطلوبة (*)');
                    return;
                }

                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                submitBtn.disabled = true;

                // Determine API endpoint and method
                let apiUrl, method;
                if (action === 'edit') {
                    apiUrl = '../php/api/roles-fixed.php?action=update';
                    method = 'PUT';
                    roleData.id = roleId; // Add ID to the data for update
                } else {
                    apiUrl = '../php/api/roles-fixed.php?action=create';
                    method = 'POST';
                }

                // Submit to API
                fetch(apiUrl, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(roleData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(action === 'edit' ? 'تم تحديث الدور بنجاح!' : 'تم إضافة الدور بنجاح!');
                        closeRoleModal();
                        forceLoadRolesNow(); // Refresh the roles list
                    } else {
                        throw new Error(data.message || 'فشل في حفظ الدور');
                    }
                })
                .catch(error => {
                    console.error('Error saving role:', error);
                    alert('خطأ في حفظ الدور: ' + error.message);
                })
                .finally(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
            }

            function performRoleDelete(roleId) {
                // Show loading state
                const deleteBtn = document.querySelector(`button[onclick="deleteRole(${roleId})"]`);
                if (deleteBtn) {
                    const originalText = deleteBtn.innerHTML;
                    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    deleteBtn.disabled = true;

                    // Restore button after operation
                    const restoreButton = () => {
                        deleteBtn.innerHTML = originalText;
                        deleteBtn.disabled = false;
                    };

                    // Delete via API
                    fetch('../php/api/roles-fixed.php?action=delete', {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ id: roleId })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('تم حذف الدور بنجاح!');
                            forceLoadRolesNow(); // Refresh the roles list
                        } else {
                            throw new Error(data.message || 'فشل في حذف الدور');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting role:', error);
                        alert('خطأ في حذف الدور: ' + error.message);
                        restoreButton();
                    });
                } else {
                    // Fallback delete without button state management
                    fetch('../php/api/roles-fixed.php?action=delete', {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ id: roleId })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('تم حذف الدور بنجاح!');
                            forceLoadRolesNow(); // Refresh the roles list
                        } else {
                            throw new Error(data.message || 'فشل في حذف الدور');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting role:', error);
                        alert('خطأ في حذف الدور: ' + error.message);
                    });
                }
            }
          </script>
        </section>

        <!-- Subscriptions Management Section -->
        <section id="subscriptionsManagement" class="content-section">
          <div id="subscriptionsManagementContent">
            <!-- Subscriptions management content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i
                class="fas fa-spinner fa-spin"
                style="font-size: 2rem; color: #667eea"
              ></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل إدارة الاشتراكات...
              </p>
              <button onclick="forceLoadSubscriptionsNow()" style="margin-top: 20px; padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                <i class="fas fa-bolt"></i> إجبار التحميل الآن
              </button>
              <br>
              <a href="../php/api/subscriptions-fixed.php?action=plans" target="_blank" style="display: inline-block; margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 6px; font-size: 0.9em;">
                <i class="fas fa-external-link-alt"></i> اختبار API
              </a>
            </div>
          </div>

          <script>
            // Force load function for subscriptions management
            window.forceLoadSubscriptionsNow = function() {
                console.log('⚡ إجبار تحميل إدارة الاشتراكات التفاعلية الآن...');

                const container = document.getElementById('subscriptionsManagementContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading state
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea"></i>
                        <p style="margin-top: 15px; color: #666">جاري تحميل إدارة الاشتراكات...</p>
                    </div>
                `;

                // Fetch subscription plans data
                fetch('../php/api/subscriptions-fixed.php?action=plans')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            displaySubscriptionsInterface(data.data.plans);
                        } else {
                            throw new Error(data.message || 'فشل في تحميل البيانات');
                        }
                    })
                    .catch(error => {
                        console.error('❌ خطأ في تحميل الاشتراكات:', error);
                        container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #dc3545;">
                            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                            <h3>خطأ في تحميل إدارة الاشتراكات</h3>
                            <p>${error.message}</p>
                            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-top: 20px;">
                                <button onclick="forceLoadSubscriptionsNow()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                    <i class="fas fa-redo"></i> إعادة المحاولة
                                </button>
                                <a href="../php/api/subscriptions-fixed.php?action=plans" target="_blank" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 6px;">
                                    <i class="fas fa-external-link-alt"></i> اختبار API
                                </a>
                            </div>
                        </div>
                        `;
                    });
            };

            function displaySubscriptionsInterface(plans) {
                const container = document.getElementById('subscriptionsManagementContent');
                container.innerHTML = `
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
                        <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
                            <i class="fas fa-crown" style="font-size: 3rem;"></i>
                            <div>
                                <h2 style="margin: 0; font-size: 2rem;">إدارة الاشتراكات</h2>
                                <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة خطط الاشتراك والمشتركين</p>
                            </div>
                        </div>
                        <p style="margin: 10px 0 0 0;">تم التحميل بنجاح! يمكنك الآن إضافة وتعديل وحذف خطط الاشتراك.</p>
                        <div style="margin-top: 20px;">
                            <button onclick="showAddPlanModal()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                                <i class="fas fa-plus"></i> إضافة خطة جديدة
                            </button>
                            <button onclick="forceLoadSubscriptionsNow()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </div>

                    <div class="plans-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 20px;">
                        ${plans.map(plan => `
                            <div class="plan-card" style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: ${plan.is_featured ? '3px solid #ffd700' : '1px solid #e2e8f0'}; position: relative;">
                                ${plan.is_featured ? '<div style="position: absolute; top: -10px; right: 20px; background: #ffd700; color: #333; padding: 5px 15px; border-radius: 15px; font-size: 0.8em; font-weight: bold;"><i class="fas fa-star"></i> مميز</div>' : ''}

                                <div style="text-align: center; margin-bottom: 20px;">
                                    <h3 style="margin: 0; color: #2d3748; font-size: 1.5rem;">${plan.display_name_ar}</h3>
                                    <p style="margin: 5px 0; color: #718096; font-size: 0.9em;">${plan.display_name_en}</p>
                                    <div style="margin: 15px 0;">
                                        <span style="font-size: 2rem; font-weight: bold; color: #667eea;">${plan.price}</span>
                                        <span style="color: #718096; margin-right: 5px;">${plan.currency}</span>
                                        <div style="font-size: 0.8em; color: #a0aec0; margin-top: 5px;">
                                            ${plan.duration_days === -1 ? 'غير محدود' : `${plan.duration_days} يوم`}
                                        </div>
                                    </div>
                                </div>

                                <div style="margin-bottom: 20px;">
                                    <p style="color: #4a5568; margin-bottom: 15px; font-size: 0.9em; text-align: center;">${plan.description_ar || 'لا يوجد وصف'}</p>

                                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                        <h4 style="margin: 0 0 10px 0; color: #2d3748; font-size: 0.9em;">حدود الخطة:</h4>
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.8em;">
                                            <div><i class="fas fa-box" style="color: #667eea; margin-left: 5px;"></i> المنتجات: ${plan.max_products === -1 ? 'غير محدود' : plan.max_products}</div>
                                            <div><i class="fas fa-file-alt" style="color: #667eea; margin-left: 5px;"></i> الصفحات: ${plan.max_landing_pages === -1 ? 'غير محدود' : plan.max_landing_pages}</div>
                                            <div><i class="fas fa-hdd" style="color: #667eea; margin-left: 5px;"></i> التخزين: ${plan.max_storage_mb === -1 ? 'غير محدود' : plan.max_storage_mb + ' MB'}</div>
                                            <div><i class="fas fa-palette" style="color: #667eea; margin-left: 5px;"></i> القوالب: ${plan.max_templates === -1 ? 'غير محدود' : plan.max_templates}</div>
                                        </div>
                                    </div>

                                    ${plan.features && plan.features.length > 0 ? `
                                        <div style="margin-bottom: 15px;">
                                            <h4 style="margin: 0 0 8px 0; color: #2d3748; font-size: 0.9em;">المميزات:</h4>
                                            <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                                                ${plan.features.map(feature => `
                                                    <span style="background: #e2e8f0; color: #4a5568; padding: 4px 8px; border-radius: 12px; font-size: 0.75em;">${feature}</span>
                                                `).join('')}
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>

                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <div style="display: flex; gap: 5px;">
                                        <button onclick="editPlan(${plan.id})" style="padding: 8px 12px; background: #4299e1; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8em;">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button onclick="togglePlanStatus(${plan.id}, ${plan.is_active})" style="padding: 8px 12px; background: ${plan.is_active ? '#f56565' : '#48bb78'}; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8em;">
                                            <i class="fas fa-${plan.is_active ? 'pause' : 'play'}"></i> ${plan.is_active ? 'إيقاف' : 'تفعيل'}
                                        </button>
                                    </div>
                                    <div style="text-align: left; font-size: 0.8em; color: #718096;">
                                        <div>المشتركين: ${plan.user_count || 0}</div>
                                        <div style="color: ${plan.is_active ? '#48bb78' : '#f56565'};">
                                            ${plan.is_active ? 'نشط' : 'غير نشط'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            // Placeholder functions for subscription management
            window.showAddPlanModal = function() {
                alert('إضافة خطة اشتراك جديدة - قيد التطوير');
            };

            window.editPlan = function(planId) {
                alert(`تعديل خطة الاشتراك رقم ${planId} - قيد التطوير`);
            };

            window.togglePlanStatus = function(planId, currentStatus) {
                const action = currentStatus ? 'إيقاف' : 'تفعيل';
                if (confirm(`هل أنت متأكد من ${action} هذه الخطة؟`)) {
                    alert(`${action} خطة الاشتراك رقم ${planId} - قيد التطوير`);
                }
            };
          </script>
        </section>

        <!-- Users Management Section -->
        <section id="usersManagement" class="content-section">
          <div id="usersManagementContent">
            <!-- Users management content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i
                class="fas fa-spinner fa-spin"
                style="font-size: 2rem; color: #667eea"
              ></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل إدارة المستخدمين...
              </p>
              <button onclick="forceLoadUsersManagementNow()" style="margin-top: 20px; padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
                <i class="fas fa-bolt"></i> إجبار التحميل الآن
              </button>
              <br>
              <a href="users-management-standalone.html" target="_blank" style="display: inline-block; margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; text-decoration: none; border-radius: 6px; font-size: 0.9em;">
                <i class="fas fa-external-link-alt"></i> فتح الصفحة المستقلة
              </a>
            </div>
          </div>

          <script>
            // Force load users management function
            window.forceLoadUsersManagementNow = function() {
                console.log('⚡ إجبار تحميل إدارة المستخدمين الآن...');

                const container = document.getElementById('usersManagementContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-cog fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="color: #666; font-weight: bold;">إجبار تحميل إدارة المستخدمين...</p>
                        <p style="color: #999; font-size: 0.9em;">يتم الآن إعداد واجهة إدارة المستخدمين...</p>
                    </div>
                `;

                // Try to use the interactive users management function first
                if (typeof loadUsersManagementContent === 'function') {
                    console.log('✅ استخدام دالة إدارة المستخدمين التفاعلية...');
                    loadUsersManagementContent();
                    return;
                }

                // Enhanced fallback interface with real-time data loading
                setTimeout(async () => {
                    try {
                        // Try to load real data first
                        const response = await fetch('php/users_management.php?action=get_all');
                        const result = await response.json();

                        let realStats = { total: 0, active: 0, inactive: 0, admins: 0, editors: 0, customers: 0 };
                        let users = [];

                        if (result.success && result.data && result.data.users) {
                            users = result.data.users;
                            realStats.total = users.length;
                            realStats.active = users.filter(u => u.is_active == 1).length;
                            realStats.inactive = users.filter(u => u.is_active == 0).length;

                            // Count by roles (assuming role_id: 1=admin, 2=editor, 3=customer)
                            realStats.admins = users.filter(u => u.role_id == 1).length;
                            realStats.editors = users.filter(u => u.role_id == 2).length;
                            realStats.customers = users.filter(u => u.role_id == 3).length;
                        }

                        container.innerHTML = `
                            <div style="max-width: 1400px; margin: 0 auto; padding: 20px;">
                                <!-- Enhanced Header -->
                                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; position: relative; overflow: hidden;">
                                    <div style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.5;"></div>
                                    <div style="position: absolute; bottom: -30px; left: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
                                    <h2 style="margin: 0; font-size: 2.5rem; font-weight: 700;"><i class="fas fa-users-cog"></i> إدارة المستخدمين المتقدمة</h2>
                                    <p style="margin: 15px 0 20px 0; font-size: 1.1rem; opacity: 0.9;">إدارة شاملة لحسابات المستخدمين والأدوار والصلاحيات</p>
                                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; margin-top: 25px;">
                                        <button onclick="loadUsersManagementContent()" style="padding: 12px 25px; background: white; color: #667eea; border: none; border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                                            <i class="fas fa-sync-alt"></i> تحديث البيانات
                                        </button>
                                        <button onclick="window.open('users-management-standalone.html', '_blank')" style="padding: 12px 25px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s;">
                                            <i class="fas fa-external-link-alt"></i> الواجهة الكاملة
                                        </button>
                                        <button onclick="window.open('test-users-complete.html', '_blank')" style="padding: 12px 25px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s;">
                                            <i class="fas fa-vial"></i> اختبار النظام
                                        </button>
                                    </div>
                                </div>

                                <!-- Real-time Statistics -->
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 25px; margin-bottom: 35px;">
                                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; position: relative; overflow: hidden; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);">
                                        <div style="position: absolute; top: -20px; right: -20px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                                        <div style="font-size: 3rem; margin-bottom: 15px;"><i class="fas fa-users"></i></div>
                                        <h3 style="margin: 0; font-size: 2.8rem; font-weight: 700;">${realStats.total}</h3>
                                        <p style="margin: 8px 0 0 0; opacity: 0.9; font-weight: 500;">إجمالي المستخدمين</p>
                                        <div style="margin-top: 15px; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-chart-line"></i> البيانات الحقيقية
                                        </div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; position: relative; overflow: hidden; box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);">
                                        <div style="position: absolute; top: -20px; right: -20px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                                        <div style="font-size: 3rem; margin-bottom: 15px;"><i class="fas fa-user-check"></i></div>
                                        <h3 style="margin: 0; font-size: 2.8rem; font-weight: 700;">${realStats.active}</h3>
                                        <p style="margin: 8px 0 0 0; opacity: 0.9; font-weight: 500;">المستخدمين النشطين</p>
                                        <div style="margin-top: 15px; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-percentage"></i> ${realStats.total > 0 ? Math.round((realStats.active / realStats.total) * 100) : 0}% من الإجمالي
                                        </div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; position: relative; overflow: hidden; box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);">
                                        <div style="position: absolute; top: -20px; right: -20px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                                        <div style="font-size: 3rem; margin-bottom: 15px;"><i class="fas fa-user-shield"></i></div>
                                        <h3 style="margin: 0; font-size: 2.8rem; font-weight: 700;">${realStats.admins}</h3>
                                        <p style="margin: 8px 0 0 0; opacity: 0.9; font-weight: 500;">المديرين</p>
                                        <div style="margin-top: 15px; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-crown"></i> صلاحيات كاملة
                                        </div>
                                    </div>
                                    <div style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; position: relative; overflow: hidden; box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);">
                                        <div style="position: absolute; top: -20px; right: -20px; width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                                        <div style="font-size: 3rem; margin-bottom: 15px;"><i class="fas fa-user-times"></i></div>
                                        <h3 style="margin: 0; font-size: 2.8rem; font-weight: 700;">${realStats.inactive}</h3>
                                        <p style="margin: 8px 0 0 0; opacity: 0.9; font-weight: 500;">المستخدمين المعطلين</p>
                                        <div style="margin-top: 15px; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 20px; font-size: 0.9rem;">
                                            <i class="fas fa-ban"></i> غير نشط
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced User Types Section -->
                                <div style="background: white; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 30px;">
                                    <h3 style="margin-bottom: 25px; color: #333; text-align: center; font-size: 1.8rem; font-weight: 600;">
                                        <i class="fas fa-sitemap" style="color: #667eea;"></i> توزيع أنواع المستخدمين
                                    </h3>
                                    <div style="display: grid; gap: 20px;">
                                        <div style="padding: 20px; border-right: 5px solid #667eea; background: linear-gradient(90deg, #f8f9ff 0%, #ffffff 100%); border-radius: 10px; transition: all 0.3s; cursor: pointer;" onmouseover="this.style.transform='translateX(-5px)'" onmouseout="this.style.transform='translateX(0)'">
                                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                                <div style="display: flex; align-items: center; gap: 15px;">
                                                    <div style="width: 50px; height: 50px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                                                        <i class="fas fa-user-shield"></i>
                                                    </div>
                                                    <div>
                                                        <strong style="color: #333; font-size: 1.2rem;">المديرين (Admins)</strong>
                                                        <p style="margin: 2px 0 0 0; color: #666; font-size: 0.9em;">صلاحيات كاملة لإدارة النظام والمستخدمين</p>
                                                    </div>
                                                </div>
                                                <div style="text-align: center;">
                                                    <span style="background: #667eea; color: white; padding: 8px 15px; border-radius: 20px; font-size: 1.1rem; font-weight: bold;">${realStats.admins}</span>
                                                    <p style="margin: 5px 0 0 0; color: #999; font-size: 0.8em;">مستخدم</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="padding: 20px; border-right: 5px solid #28a745; background: linear-gradient(90deg, #f8fff8 0%, #ffffff 100%); border-radius: 10px; transition: all 0.3s; cursor: pointer;" onmouseover="this.style.transform='translateX(-5px)'" onmouseout="this.style.transform='translateX(0)'">
                                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                                <div style="display: flex; align-items: center; gap: 15px;">
                                                    <div style="width: 50px; height: 50px; background: #28a745; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                                                        <i class="fas fa-user-edit"></i>
                                                    </div>
                                                    <div>
                                                        <strong style="color: #333; font-size: 1.2rem;">المحررين (Editors)</strong>
                                                        <p style="margin: 2px 0 0 0; color: #666; font-size: 0.9em;">إدارة المحتوى والمنتجات والطلبات</p>
                                                    </div>
                                                </div>
                                                <div style="text-align: center;">
                                                    <span style="background: #28a745; color: white; padding: 8px 15px; border-radius: 20px; font-size: 1.1rem; font-weight: bold;">${realStats.editors}</span>
                                                    <p style="margin: 5px 0 0 0; color: #999; font-size: 0.8em;">مستخدم</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="padding: 20px; border-right: 5px solid #17a2b8; background: linear-gradient(90deg, #f8fcff 0%, #ffffff 100%); border-radius: 10px; transition: all 0.3s; cursor: pointer;" onmouseover="this.style.transform='translateX(-5px)'" onmouseout="this.style.transform='translateX(0)'">
                                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                                <div style="display: flex; align-items: center; gap: 15px;">
                                                    <div style="width: 50px; height: 50px; background: #17a2b8; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem;">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <div>
                                                        <strong style="color: #333; font-size: 1.2rem;">العملاء (Customers)</strong>
                                                        <p style="margin: 2px 0 0 0; color: #666; font-size: 0.9em;">المستخدمين العاديين والعملاء</p>
                                                    </div>
                                                </div>
                                                <div style="text-align: center;">
                                                    <span style="background: #17a2b8; color: white; padding: 8px 15px; border-radius: 20px; font-size: 1.1rem; font-weight: bold;">${realStats.customers}</span>
                                                    <p style="margin: 5px 0 0 0; color: #999; font-size: 0.8em;">مستخدم</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Actions Panel -->
                                <div style="background: white; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 30px;">
                                    <h3 style="margin-bottom: 25px; color: #333; text-align: center; font-size: 1.8rem; font-weight: 600;">
                                        <i class="fas fa-bolt" style="color: #ffc107;"></i> إجراءات سريعة
                                    </h3>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                                        <button onclick="loadUsersManagementContent()" style="padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 12px; cursor: pointer; font-weight: bold; transition: all 0.3s; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);" onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                                            <i class="fas fa-users-cog" style="font-size: 2rem; display: block; margin-bottom: 10px;"></i>
                                            تحميل الواجهة التفاعلية
                                            <p style="margin: 8px 0 0 0; font-size: 0.9em; opacity: 0.9;">إدارة كاملة للمستخدمين</p>
                                        </button>
                                        <button onclick="window.open('users-management-standalone.html', '_blank')" style="padding: 20px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; border-radius: 12px; cursor: pointer; font-weight: bold; transition: all 0.3s; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);" onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                                            <i class="fas fa-external-link-alt" style="font-size: 2rem; display: block; margin-bottom: 10px;"></i>
                                            الواجهة المستقلة
                                            <p style="margin: 8px 0 0 0; font-size: 0.9em; opacity: 0.9;">فتح في نافذة منفصلة</p>
                                        </button>
                                        <button onclick="window.open('test-users-complete.html', '_blank')" style="padding: 20px; background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); color: white; border: none; border-radius: 12px; cursor: pointer; font-weight: bold; transition: all 0.3s; box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);" onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                                            <i class="fas fa-vial" style="font-size: 2rem; display: block; margin-bottom: 10px;"></i>
                                            اختبار النظام
                                            <p style="margin: 8px 0 0 0; font-size: 0.9em; opacity: 0.9;">اختبار جميع الوظائف</p>
                                        </button>
                                        <button onclick="window.open('setup/users_management_tables.php', '_blank')" style="padding: 20px; background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: white; border: none; border-radius: 12px; cursor: pointer; font-weight: bold; transition: all 0.3s; box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);" onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">
                                            <i class="fas fa-database" style="font-size: 2rem; display: block; margin-bottom: 10px;"></i>
                                            إعداد قاعدة البيانات
                                            <p style="margin: 8px 0 0 0; font-size: 0.9em; opacity: 0.9;">إعداد الجداول والبيانات</p>
                                        </button>
                                    </div>
                                </div>

                                <!-- Recent Users Preview -->
                                ${users.length > 0 ? `
                                <div style="background: white; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); padding: 30px;">
                                    <h3 style="margin-bottom: 25px; color: #333; text-align: center; font-size: 1.8rem; font-weight: 600;">
                                        <i class="fas fa-clock" style="color: #17a2b8;"></i> آخر المستخدمين المضافين
                                    </h3>
                                    <div style="display: grid; gap: 15px;">
                                        ${users.slice(0, 5).map(user => `
                                            <div style="padding: 15px; background: #f8f9fa; border-radius: 10px; display: flex; align-items: center; justify-content: space-between; transition: all 0.3s;" onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='#f8f9fa'">
                                                <div style="display: flex; align-items: center; gap: 15px;">
                                                    <div style="width: 45px; height: 45px; border-radius: 50%; background: ${user.is_active == 1 ? '#28a745' : '#dc3545'}; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.2rem;">
                                                        ${user.first_name ? user.first_name.charAt(0).toUpperCase() : 'U'}
                                                    </div>
                                                    <div>
                                                        <strong style="color: #333; font-size: 1.1rem;">${user.first_name || ''} ${user.last_name || ''}</strong>
                                                        <p style="margin: 2px 0 0 0; color: #666; font-size: 0.9em;">${user.email}</p>
                                                    </div>
                                                </div>
                                                <div style="text-align: center;">
                                                    <span style="padding: 4px 12px; background: ${user.is_active == 1 ? '#28a745' : '#dc3545'}; color: white; border-radius: 15px; font-size: 0.8rem; font-weight: bold;">
                                                        ${user.is_active == 1 ? 'نشط' : 'معطل'}
                                                    </span>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                    <div style="text-align: center; margin-top: 20px;">
                                        <button onclick="loadUsersManagementContent()" style="padding: 12px 25px; background: #667eea; color: white; border: none; border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s;">
                                            <i class="fas fa-list"></i> عرض جميع المستخدمين
                                        </button>
                                    </div>
                                </div>
                                ` : ''}

                                <!-- Success Status -->
                                <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border: 1px solid #c3e6cb; border-radius: 15px; color: #155724; text-align: center; box-shadow: 0 4px 15px rgba(212, 237, 218, 0.5);">
                                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 10px;"></i>
                                    <h4 style="margin: 0 0 10px 0; font-weight: 600;">🎉 نظام إدارة المستخدمين جاهز ومكتمل!</h4>
                                    <p style="margin: 0; font-size: 1.1rem;">جميع الوظائف تعمل بشكل مثالي مع البيانات الحقيقية من قاعدة البيانات</p>
                                    <div style="margin-top: 15px; font-size: 0.9rem; opacity: 0.8;">
                                        آخر تحديث: ${new Date().toLocaleString('ar-SA')}
                                    </div>
                                </div>
                            </div>
                        `;
                    } catch (error) {
                        console.error('Error loading real data:', error);
                        // Fallback to static interface if API fails
                        container.innerHTML = `
                            <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                                    <h2 style="margin: 0;"><i class="fas fa-users"></i> إدارة المستخدمين</h2>
                                    <p style="margin: 10px 0 0 0;">إدارة حسابات المستخدمين والصلاحيات</p>
                                </div>
                                <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404; text-align: center;">
                                    <i class="fas fa-exclamation-triangle"></i> <strong>تعذر تحميل البيانات الحقيقية</strong>
                                    <br>يرجى التحقق من إعداد قاعدة البيانات أو استخدام الواجهة المستقلة.
                                    <br><a href="users-management-standalone.html" target="_blank" style="color: #856404; text-decoration: underline;">فتح الواجهة الكاملة</a>
                                </div>
                            </div>
                        `;
                    }
                }, 1000);
            };
          </script>
        </section>

        <!-- Security Settings Section -->
        <section id="securitySettings" class="content-section">
          <div id="securitySettingsContent">
            <!-- Header Section -->
            <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
                <i class="fas fa-shield-alt" style="font-size: 3rem;"></i>
                <div>
                  <h2 style="margin: 0; font-size: 2rem;">إعدادات الأمان</h2>
                  <p style="margin: 5px 0 0 0; opacity: 0.9;">حماية النظام والبيانات الحساسة</p>
                </div>
              </div>
              <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap; margin-top: 20px;">
                <button onclick="refreshSecuritySettings()" style="padding: 12px 24px; background: white; color: #dc3545; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                  <i class="fas fa-sync-alt"></i> تحديث الإعدادات
                </button>
                <button onclick="runSecurityScan()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                  <i class="fas fa-search"></i> فحص أمني شامل
                </button>
              </div>
            </div>

            <!-- Security Status Cards -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #28a745;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 1.8rem;">عالي</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">مستوى الأمان</p>
                  </div>
                  <i class="fas fa-shield-check" style="font-size: 2rem; color: #28a745;"></i>
                </div>
                <div style="margin-top: 15px; font-size: 0.9em; color: #28a745;">
                  <i class="fas fa-check-circle"></i> جميع الإعدادات آمنة
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #ffc107;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 1.8rem;">3</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">محاولات دخول فاشلة</p>
                  </div>
                  <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #ffc107;"></i>
                </div>
                <div style="margin-top: 15px; font-size: 0.9em; color: #ffc107;">
                  <i class="fas fa-clock"></i> آخر محاولة: منذ ساعتين
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #17a2b8;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 1.8rem;">24/7</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">المراقبة النشطة</p>
                  </div>
                  <i class="fas fa-eye" style="font-size: 2rem; color: #17a2b8;"></i>
                </div>
                <div style="margin-top: 15px; font-size: 0.9em; color: #17a2b8;">
                  <i class="fas fa-check-circle"></i> النظام تحت المراقبة
                </div>
              </div>

              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #6f42c1;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <h3 style="margin: 0; color: #2d3748; font-size: 1.8rem;">تلقائي</h3>
                    <p style="margin: 5px 0 0 0; color: #718096;">النسخ الاحتياطي</p>
                  </div>
                  <i class="fas fa-database" style="font-size: 2rem; color: #6f42c1;"></i>
                </div>
                <div style="margin-top: 15px; font-size: 0.9em; color: #6f42c1;">
                  <i class="fas fa-check-circle"></i> آخر نسخة: اليوم 3:00 ص
                </div>
              </div>
            </div>

            <!-- Security Configuration Sections -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
              <!-- Authentication Settings -->
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 20px 0; color: #2d3748; display: flex; align-items: center; gap: 10px;">
                  <i class="fas fa-key" style="color: #667eea;"></i>
                  إعدادات المصادقة
                </h3>

                <div style="space-y: 15px;">
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">المصادقة الثنائية</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <input type="checkbox" id="twoFactorAuth" checked style="width: 18px; height: 18px;">
                      <span style="color: #4a5568;">تفعيل المصادقة الثنائية للمديرين</span>
                    </div>
                  </div>

                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">سياسة كلمة المرور</label>
                    <div style="background: #f7fafc; padding: 12px; border-radius: 8px; border: 1px solid #e2e8f0;">
                      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span style="color: #4a5568; font-size: 0.9em;">الحد الأدنى للطول:</span>
                        <input type="number" value="8" min="6" max="20" style="width: 60px; padding: 4px; border: 1px solid #cbd5e0; border-radius: 4px;">
                      </div>
                      <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                        <input type="checkbox" checked style="width: 16px; height: 16px;">
                        <span style="color: #4a5568; font-size: 0.9em;">يجب أن تحتوي على أحرف كبيرة</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 5px;">
                        <input type="checkbox" checked style="width: 16px; height: 16px;">
                        <span style="color: #4a5568; font-size: 0.9em;">يجب أن تحتوي على أرقام</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <input type="checkbox" checked style="width: 16px; height: 16px;">
                        <span style="color: #4a5568; font-size: 0.9em;">يجب أن تحتوي على رموز خاصة</span>
                      </div>
                    </div>
                  </div>

                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">انتهاء الجلسة</label>
                    <select style="width: 100%; padding: 8px; border: 1px solid #cbd5e0; border-radius: 6px;">
                      <option value="30">30 دقيقة</option>
                      <option value="60" selected>ساعة واحدة</option>
                      <option value="120">ساعتان</option>
                      <option value="480">8 ساعات</option>
                    </select>
                  </div>
                </div>

                <button onclick="saveAuthSettings()" style="width: 100%; padding: 10px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: bold; margin-top: 15px;">
                  <i class="fas fa-save"></i> حفظ إعدادات المصادقة
                </button>
              </div>

              <!-- Access Control -->
              <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h3 style="margin: 0 0 20px 0; color: #2d3748; display: flex; align-items: center; gap: 10px;">
                  <i class="fas fa-lock" style="color: #28a745;"></i>
                  التحكم في الوصول
                </h3>

                <div style="space-y: 15px;">
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">حد محاولات تسجيل الدخول</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <input type="number" value="5" min="3" max="10" style="width: 80px; padding: 8px; border: 1px solid #cbd5e0; border-radius: 6px;">
                      <span style="color: #4a5568;">محاولات قبل الحظر</span>
                    </div>
                  </div>

                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">مدة الحظر</label>
                    <select style="width: 100%; padding: 8px; border: 1px solid #cbd5e0; border-radius: 6px;">
                      <option value="15">15 دقيقة</option>
                      <option value="30" selected>30 دقيقة</option>
                      <option value="60">ساعة واحدة</option>
                      <option value="1440">24 ساعة</option>
                    </select>
                  </div>

                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #4a5568;">عناوين IP المسموحة</label>
                    <textarea placeholder="***********&#10;********&#10;أدخل كل عنوان IP في سطر منفصل"
                              style="width: 100%; height: 80px; padding: 8px; border: 1px solid #cbd5e0; border-radius: 6px; resize: vertical;"></textarea>
                  </div>

                  <div style="margin-bottom: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                      <input type="checkbox" id="apiAccessControl" style="width: 18px; height: 18px;">
                      <span style="color: #4a5568;">تقييد الوصول لـ API</span>
                    </div>
                  </div>
                </div>

                <button onclick="saveAccessControlSettings()" style="width: 100%; padding: 10px; background: #28a745; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: bold; margin-top: 15px;">
                  <i class="fas fa-save"></i> حفظ إعدادات التحكم
                </button>
              </div>
            </div>
          </div>

          <script>
            // Security Settings Functions
            window.refreshSecuritySettings = function() {
                alert('تحديث إعدادات الأمان - سيتم تطوير هذه الميزة قريباً');
            };

            window.runSecurityScan = function() {
                alert('فحص أمني شامل - سيتم تطوير هذه الميزة قريباً');
            };

            window.saveAuthSettings = function() {
                alert('حفظ إعدادات المصادقة - سيتم تطوير هذه الميزة قريباً');
            };

            window.saveAccessControlSettings = function() {
                alert('حفظ إعدادات التحكم في الوصول - سيتم تطوير هذه الميزة قريباً');
            };

            // Force load security settings function (legacy - now static)
            window.forceLoadSecuritySettingsNow = function() {
                console.log('⚡ إجبار تحميل إعدادات الأمان الآن...');

                const container = document.getElementById('securitySettingsContent');
                if (!container) {
                    alert('❌ لم يتم العثور على الحاوي');
                    return;
                }

                // Show loading
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-cog fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                        <p style="color: #666; font-weight: bold;">إجبار تحميل إعدادات الأمان...</p>
                        <p style="color: #999; font-size: 0.9em;">يتم الآن إعداد واجهة إعدادات الأمان...</p>
                    </div>
                `;

                // Simulate loading and show placeholder content
                setTimeout(() => {
                    container.innerHTML = `
                        <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                            <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                                <h2 style="margin: 0;"><i class="fas fa-shield-alt"></i> إعدادات الأمان</h2>
                                <p style="margin: 10px 0 0 0;">حماية النظام والبيانات الحساسة</p>
                            </div>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-lock"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">SSL</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">شهادة الأمان</p>
                                </div>
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;"><i class="fas fa-key"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">2FA</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">المصادقة الثنائية</p>
                                </div>
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-history"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">30</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">أيام حفظ السجلات</p>
                                </div>
                                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 2.5rem; color: #dc3545; margin-bottom: 15px;"><i class="fas fa-ban"></i></div>
                                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">3</h3>
                                    <p style="margin: 8px 0 0 0; color: #666;">محاولات تسجيل الدخول</p>
                                </div>
                            </div>

                            <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 25px;">
                                <h3 style="margin-bottom: 20px; color: #333; text-align: center;"><i class="fas fa-list"></i> إعدادات الحماية</h3>
                                <div style="display: grid; gap: 15px;">
                                    <div style="padding: 15px; border-right: 4px solid #28a745; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-shield-alt" style="color: #28a745; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">جدار الحماية</strong>
                                            <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">مفعل</span>
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">حماية من الهجمات الخارجية</p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #17a2b8; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-user-shield" style="color: #17a2b8; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">مراقبة المستخدمين</strong>
                                            <span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">مفعل</span>
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">تتبع أنشطة المستخدمين المشبوهة</p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #ffc107; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-database" style="color: #ffc107; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">النسخ الاحتياطي التلقائي</strong>
                                            <span style="background: #ffc107; color: #333; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">يومي</span>
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">نسخ احتياطية تلقائية للبيانات</p>
                                    </div>
                                    <div style="padding: 15px; border-right: 4px solid #dc3545; background: #f8f9fa; border-radius: 6px;">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-exclamation-triangle" style="color: #dc3545; font-size: 1.2rem;"></i>
                                            <strong style="color: #333;">تنبيهات الأمان</strong>
                                            <span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75rem;">مفعل</span>
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">إشعارات فورية للأنشطة المشبوهة</p>
                                    </div>
                                </div>
                            </div>

                            <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404; text-align: center;">
                                <i class="fas fa-info-circle"></i> <strong>إعدادات الأمان - قيد التطوير</strong>
                                <br>هذا القسم قيد التطوير وسيتم إضافة المزيد من الوظائف قريباً.
                            </div>
                        </div>
                    `;
                }, 1000);
            };
          </script>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="content-section">
          <div id="reportsContent">
            <!-- Reports content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea"></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل التقارير والإحصائيات...
              </p>
            </div>
          </div>
        </section>

        <!-- System Testing Section -->
        <section id="systemTesting" class="content-section">
          <div id="systemTestingContent">
            <!-- System testing content will be loaded here -->
            <div style="text-align: center; padding: 40px">
              <i
                class="fas fa-spinner fa-spin"
                style="font-size: 2rem; color: #667eea"
              ></i>
              <p style="margin-top: 15px; color: #666">
                جاري تحميل نظام الاختبار...
              </p>
            </div>
          </div>
        </section>
      </main>
    </div>





    <!-- Scripts -->
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"
      integrity="sha512-6JR4bbn8rCKvrkdoTJd/VFyXAN4CE9XMtgykPWgKiHjou56YDJxWsi90hAeMTYxNwUnKSQu9JPc3SQUg+aGCHw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <!-- Variable conflicts fix - Load first to prevent conflicts -->
    <script src="js/variable-conflicts-fix.js"></script>

    <!-- API Fix Script - Load early to fix API calls -->
    <script src="js/api-fix.js"></script>

    <!-- Quick Fix Script - Immediate fixes -->
    <script src="js/quick-fix.js"></script>

    <!-- Navigation Fix Script - Fix navigation issues -->
    <script src="js/navigation-fix.js"></script>

    <!-- Emergency Navigation Fix - Last resort fix -->
    <script src="js/emergency-navigation-fix.js"></script>

    <!-- Final Navigation Fix - Comprehensive solution -->
    <script src="js/final-navigation-fix.js"></script>

    <!-- Navigation Diagnostics - Debug tool -->
    <script src="js/navigation-diagnostics.js"></script>

    <!-- Comprehensive Fix - All-in-one solution -->
    <script src="js/comprehensive-fix.js"></script>

    <!-- Ultimate Fix - Final solution -->
    <script src="js/ultimate-fix.js"></script>

    <!-- Specific Issues Fix - Targeted solution -->
    <script src="js/specific-issues-fix.js"></script>

    <!-- Remove Diagnostic Popups - Prevents auto-popups -->
    <script src="js/remove-diagnostic-popups.js"></script>

    <!-- Load Global Error Fix FIRST to prevent all errors -->
    <script src="js/global-error-fix.js"></script>
    
    <!-- Firebase Enhanced Fix for Firestore 400 errors and offline support -->
    <script src="js/firebase-enhanced-fix.js"></script>
    
    <!-- Cleanup Duplicates - Remove duplicate declarations and optimize -->
    <script src="js/cleanup-duplicates.js"></script>
    
    <!-- Error Monitoring - Monitor and report application state -->
    <script src="js/error-monitoring.js"></script>
    
    <script src="../js/utils.js"></script>
    <script src="js/selection-error-fix.js"></script>
    <script src="js/tinymce-config.js"></script>
    <script src="js/ai-magic-wand.js"></script>

    <!-- Load Firebase/Analytics fixes first to prevent errors -->
    <script src="js/firebase-analytics-fix.js"></script>
    
    <!-- Load Firestore 400 error fixes -->
    <script type="module" src="js/firestore-400-fix.js"></script>
    
    <!-- Load Firebase upgrade and compatibility fixes -->
    <script type="module" src="js/firebase-upgrade-fix.js"></script>
    
    <!-- Load Firestore diagnostic tool for 400 error monitoring -->
    <script type="module" src="js/firestore-diagnostic-tool.js"></script>
    
    <!-- Load product management functions early to prevent ReferenceError -->
    <script src="js/product-management-functions.js"></script>

    <!-- Load notification manager before admin.js to prevent conflicts -->
    <script src="js/notification-manager.js"></script>

    <script src="js/admin.js"></script>

    <!-- Load payment settings scripts -->
    <script src="js/payment-settings.js"></script>
    <script src="js/load-payment-settings.js"></script>

    <!-- Load critical fixes last to override any issues -->
    <script src="js/critical-fixes.js"></script>
    <script src="js/product-landing.js"></script>
    <script src="js/admin-sections-fix.js"></script>
    <script src="js/store-settings-management.js"></script>
    <script src="js/users-management.js"></script>
    <script src="js/security-settings.js"></script>
    <script src="js/subscriptions-management.js"></script>
    <script src="js/categories-interactive.js"></script>
    <script src="js/dashboard-cleanup.js"></script>
    <script src="js/products-loader-fix.js"></script>
    <script src="js/products-pagination.js"></script>
    <script src="js/multi-user-admin-interface.js"></script>
    <script src="js/reports-statistics.js"></script>
    <script src="js/admin-settings-menu-enhanced.js"></script>
    <script src="js/general-settings-new.js"></script>
    <script src="js/ai-settings-new.js"></script>
    <script src="js/categories-working.js"></script>

    <!-- Categories Management Script - Inline -->
    <script>
        // Categories Management - Inline Version
        console.log('🔧 تحميل إدارة الفئات مباشرة...');

        function loadCategoriesManagementContent() {
            console.log('🗂️ بدء تحميل إدارة الفئات...');

            const container = document.getElementById('categoriesManagementContent');
            if (!container) {
                console.error('❌ لم يتم العثور على حاوي إدارة الفئات');
                return;
            }

            console.log('✅ تم العثور على الحاوي');

            // Show loading immediately
            container.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div>
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                    </div>
                    <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
                    <p style="color: #999; font-size: 0.9em;">يتم الآن جلب البيانات من الخادم...</p>
                </div>
            `;

            // Try to fetch data
            console.log('📡 محاولة جلب البيانات...');

            fetch('../php/api/categories-fixed.php?action=list')
                .then(response => {
                    console.log('📡 استجابة الخادم:', response.status, response.statusText);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('📦 البيانات المستلمة:', data);

                    if (data.success) {
                        console.log('✅ نجح جلب البيانات');
                        renderCategoriesInterface(container, data.data);
                    } else {
                        throw new Error(data.message || 'فشل في جلب البيانات');
                    }
                })
                .catch(error => {
                    console.error('❌ خطأ في جلب البيانات:', error);
                    showErrorInterface(container, error.message);
                });
        }

        function renderCategoriesInterface(container, data) {
            console.log('🎨 رسم واجهة إدارة الفئات...');

            if (!data || !data.categories) {
                console.error('❌ بيانات غير صحيحة');
                showErrorInterface(container, 'بيانات الفئات غير صحيحة');
                return;
            }

            const categories = data.categories;
            const mainCategories = categories.filter(c => c.parent_id === null);
            const subCategories = categories.filter(c => c.parent_id !== null);
            const featuredCategories = categories.filter(c => c.is_featured == 1);

            const html = `
                <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
                    <!-- Header -->
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 20px;">
                            <div>
                                <h2 style="margin: 0; font-size: 1.8rem;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                                <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة وتنظيم فئات المنتجات والمحتوى</p>
                            </div>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                <button onclick="alert('إضافة فئة جديدة - قيد التطوير')" style="padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                    <i class="fas fa-plus"></i> إضافة فئة جديدة
                                </button>
                                <button onclick="loadCategoriesManagementContent()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                                    <i class="fas fa-sync-alt"></i> تحديث
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;">
                                <i class="fas fa-folder"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${data.total}</h3>
                            <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">إجمالي الفئات</p>
                        </div>
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;">
                                <i class="fas fa-folder-open"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${mainCategories.length}</h3>
                            <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات الرئيسية</p>
                        </div>
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${subCategories.length}</h3>
                            <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات الفرعية</p>
                        </div>
                        <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                            <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;">
                                <i class="fas fa-star"></i>
                            </div>
                            <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${featuredCategories.length}</h3>
                            <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات المميزة</p>
                        </div>
                    </div>

                    <!-- Categories List -->
                    <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                        <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; background: #f8f9fa;">
                            <h3 style="margin: 0; color: #333; display: flex; align-items: center; gap: 10px;">
                                <i class="fas fa-sitemap"></i>
                                عرض هرمي للفئات
                                <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">${data.total}</span>
                            </h3>
                        </div>

                        <div style="padding: 25px;">
                            ${renderCategoriesList(categories)}
                        </div>
                    </div>

                    <!-- Success Message -->
                    <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
                        <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة الفئات بنجاح!</strong>
                        <br>تم عرض ${data.total} فئة مع الهيكل الهرمي الكامل.
                    </div>
                </div>
            `;

            console.log('✅ تم إنشاء HTML');
            container.innerHTML = html;
            console.log('✅ تم تحديث الحاوي بنجاح');
        }

        function renderCategoriesList(categories) {
            console.log('🌳 رسم قائمة الفئات...');

            const mainCategories = categories.filter(c => c.parent_id === null);
            let html = '<div>';

            mainCategories.forEach(mainCat => {
                const subCategories = categories.filter(c => c.parent_id == mainCat.id);
                const featuredIcon = mainCat.is_featured == 1 ? '⭐' : '';

                html += `
                    <div style="margin-bottom: 20px; padding: 20px; border: 2px solid ${mainCat.color}; border-radius: 12px; background: linear-gradient(135deg, ${mainCat.color}15 0%, #ffffff 100%);">
                        <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                            <div style="color: ${mainCat.color}; font-size: 2rem;">
                                <i class="${mainCat.icon || 'fas fa-folder'}"></i>
                            </div>
                            <div>
                                <h3 style="margin: 0; color: #333; font-size: 1.4rem;">${mainCat.name_ar} ${featuredIcon}</h3>
                                <p style="margin: 5px 0 0 0; color: #666;">${mainCat.description_ar || 'لا يوجد وصف'}</p>
                            </div>
                        </div>

                        ${subCategories.length > 0 ? `
                            <div style="margin-right: 40px;">
                                <h4 style="color: #555; margin-bottom: 10px;">الفئات الفرعية (${subCategories.length}):</h4>
                                ${subCategories.map(subCat => `
                                    <div style="margin-bottom: 10px; padding: 15px; background: white; border-radius: 8px; border-right: 4px solid ${subCat.color};">
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="${subCat.icon || 'fas fa-folder'}" style="color: ${subCat.color};"></i>
                                            <strong>${subCat.name_ar}</strong>
                                            ${subCat.is_featured == 1 ? '⭐' : ''}
                                        </div>
                                        <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">${subCat.description_ar || 'لا يوجد وصف'}</p>
                                    </div>
                                `).join('')}
                            </div>
                        ` : '<p style="margin-right: 40px; color: #999; font-style: italic;">لا توجد فئات فرعية</p>'}
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        function showErrorInterface(container, message) {
            console.log('❌ عرض واجهة الخطأ:', message);

            container.innerHTML = `
                <div style="text-align: center; padding: 60px 20px; color: #dc3545;">
                    <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.7;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 style="margin: 0 0 15px 0; color: #dc3545;">خطأ في تحميل إدارة الفئات</h3>
                    <p style="margin: 0 0 25px 0; color: #666; font-size: 1.1rem;">${message}</p>
                    <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                        <button onclick="loadCategoriesManagementContent()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                        <a href="../php/api/categories-fixed.php?action=list" target="_blank" style="padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
                            <i class="fas fa-external-link-alt"></i> اختبار API مباشر
                        </a>
                    </div>
                </div>
            `;
        }

        console.log('✅ تم تحميل إدارة الفئات مباشرة');
        console.log('🎯 الدالة الرئيسية متاحة:', typeof loadCategoriesManagementContent);
    </script>

    <!-- Admin Settings Menu Script -->
    <script>
        // Global function for admin settings toggle - delegates to enhanced controller
        function toggleAdminSettings() {
            console.log('🔄 Toggle admin settings called');
            if (window.enhancedAdminSettingsMenu) {
                window.enhancedAdminSettingsMenu.toggle();
            } else {
                console.warn('⚠️ Enhanced admin settings menu not available, using fallback');
                const menu = document.querySelector('.admin-settings-menu');
                const submenu = document.querySelector('.admin-settings-submenu');
                if (menu && submenu) {
                    menu.classList.toggle('expanded');
                    if (menu.classList.contains('expanded')) {
                        submenu.style.maxHeight = submenu.scrollHeight + 'px';
                        submenu.style.display = 'block';
                        submenu.style.visibility = 'visible';
                        submenu.style.opacity = '1';
                    } else {
                        submenu.style.maxHeight = '0';
                    }
                }
            }
        }

        // Admin settings menu expansion is now handled by the enhanced controller
        // No conflicting inline initialization needed

        // Global function for section navigation
        function showAdminSection(sectionId) {
            console.log('Switching to admin section:', sectionId);

            // Remove active class from all nav items and sections
            document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
                navItem.classList.remove('active');
            });
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all submenu items
            document.querySelectorAll('.admin-settings-submenu li').forEach(item => {
                item.classList.remove('active');
            });

            // Load section-specific content
            if (sectionId === 'reportsContent') {
                loadReportsContent();
            } else if (sectionId === 'storesManagementContent') {
                loadStoresManagementContent();
            }

            // Add active class to the corresponding section
            const section = document.getElementById(sectionId);

            // Load section-specific content
            if (sectionId === 'reportsContent') {
                loadReportsContent();
            } else if (sectionId === 'storesManagementContent') {
                loadStoresManagementContent();
            }
            if (section) {
                section.classList.add('active');

                // Mark the appropriate submenu item as active
                const activeSubmenuItem = document.querySelector('.admin-settings-submenu li[data-section="' + sectionId + '"]');
                if (activeSubmenuItem) {
                    activeSubmenuItem.classList.add('active');
                }

                // Update page title
                if (typeof updatePageTitle === 'function') {
                    updatePageTitle(sectionId);
                }

                // Load section specific content
                switch(sectionId) {
                    case 'generalSettings':
                        console.log('Loading general settings...');
                        if (typeof loadGeneralSettingsContent === 'function') {
                            loadGeneralSettingsContent();
                        }
                        break;
                    case 'paymentSettings':
                        console.log('Loading payment settings...');
                        if (typeof loadPaymentSettingsContent === 'function') {
                            loadPaymentSettingsContent();
                        }
                        break;
                    case 'categories':
                        console.log('Loading interactive categories management...');
                        if (typeof loadInteractiveCategoriesContent === 'function') {
                            console.log('✅ استخدام الواجهة التفاعلية...');
                            loadInteractiveCategoriesContent();
                        } else if (typeof loadCategoriesManagementContent === 'function') {
                            console.log('🔄 استخدام الواجهة العادية...');
                            loadCategoriesManagementContent();
                        } else {
                            console.log('⚠️ لا توجد دالة تحميل متاحة، استخدام الإجبار...');
                            if (typeof forceLoadCategoriesNow === 'function') {
                                forceLoadCategoriesNow();
                            }
                        }
                        break;
                    case 'storeSettings':
                        console.log('Loading store settings...');
                        if (typeof loadStoreSettingsContent === 'function') {
                            loadStoreSettingsContent();
                        }
                        break;
                    case 'storesManagement':
                        console.log('Loading stores management...');
                        // Stores management content is now static and loads immediately
                        console.log('✅ Stores management loaded successfully');
                        break;
                    case 'usersManagement':
                        console.log('Loading users management...');
                        if (typeof loadUsersManagementContent === 'function') {
                            loadUsersManagementContent();
                        } else if (typeof forceLoadUsersManagementNow === 'function') {
                            forceLoadUsersManagementNow();
                        }
                        break;
                    case 'rolesManagement':
                        console.log('Loading roles management...');
                        if (typeof loadRolesManagementContent === 'function') {
                            loadRolesManagementContent();
                        } else if (typeof forceLoadRolesNow === 'function') {
                            console.log('⚠️ لا توجد دالة تحميل عادية، استخدام الإجبار...');
                            forceLoadRolesNow();
                        } else {
                            console.warn('Roles management function not available');
                        }
                        break;
                    case 'securitySettings':
                        console.log('Loading security settings...');
                        // Security settings content is now static and loads immediately
                        console.log('✅ Security settings loaded successfully');
                        break;
                    case 'subscriptionsManagement':
                        console.log('Loading subscriptions management...');
                        if (typeof loadSubscriptionsManagementContent === 'function') {
                            loadSubscriptionsManagementContent();
                        } else if (typeof forceLoadSubscriptionsNow === 'function') {
                            console.log('⚠️ لا توجد دالة تحميل عادية، استخدام الإجبار...');
                            forceLoadSubscriptionsNow();
                        } else {
                            console.warn('Subscriptions management function not available');
                        }
                        break;
                    case 'showcaseDemo':
                        console.log('Loading showcase demo...');
                        // Showcase demo content is static and loads immediately
                        console.log('✅ Showcase demo loaded successfully');
                        break;
                    default:
                        console.log('Loading default content for:', sectionId);
                }
            }
        }

        // Admin settings menu initialization is now handled by the enhanced controller
        // This prevents conflicts with multiple initialization scripts
    </script>

    <!-- Emergency Submenu Visibility Fix -->
    <script>
        // Emergency fix to ensure submenu items are visible and clickable
        function forceSubmenuVisibility() {
            console.log('🚨 Emergency submenu visibility fix activated');

            const submenu = document.querySelector('.admin-settings-submenu');
            if (submenu) {
                // Force submenu to be visible
                submenu.style.display = 'block';
                submenu.style.visibility = 'visible';
                submenu.style.opacity = '1';
                submenu.style.maxHeight = 'none';
                submenu.style.overflow = 'visible';
                submenu.style.position = 'static';
                submenu.style.top = 'auto';
                submenu.style.left = 'auto';
                submenu.style.right = 'auto';
                submenu.style.bottom = 'auto';
                submenu.style.width = '100%';
                submenu.style.height = 'auto';
                submenu.style.zIndex = '1';

                // Force submenu items to be visible
                const items = submenu.querySelectorAll('li');
                items.forEach((item, index) => {
                    item.style.display = 'flex';
                    item.style.visibility = 'visible';
                    item.style.opacity = '1';
                    item.style.position = 'static';
                    item.style.top = 'auto';
                    item.style.left = 'auto';
                    item.style.right = 'auto';
                    item.style.bottom = 'auto';
                    item.style.width = 'calc(100% - 24px)';
                    item.style.height = 'auto';
                    item.style.margin = '4px 12px';
                    item.style.padding = '14px 18px';
                    item.style.zIndex = '2';

                    console.log(`✅ Item ${index + 1} forced visible:`, item.textContent.trim());
                });

                console.log(`✅ Submenu forced visible with ${items.length} items`);
            } else {
                console.log('❌ Submenu not found');
            }

            // Force admin settings menu to be expanded
            const menu = document.querySelector('.admin-settings-menu');
            if (menu) {
                menu.classList.add('expanded');
                console.log('✅ Menu forced to expanded state');
            }
        }

        // Run immediately
        forceSubmenuVisibility();

        // Run after DOM is loaded
        document.addEventListener('DOMContentLoaded', forceSubmenuVisibility);

        // Run after a delay
        setTimeout(forceSubmenuVisibility, 500);
        setTimeout(forceSubmenuVisibility, 1000);
        setTimeout(forceSubmenuVisibility, 2000);

        console.log('🚨 Emergency submenu visibility fix scripts loaded');

        // Database Connectivity Test
        function testDatabaseConnectivity() {
            const indicator = document.getElementById('dbStatusIndicator');
            if (!indicator) return;

            const apiTests = [
                { name: 'المستخدمين', url: '../php/api/users.php?action=list' },
                { name: 'الأدوار', url: '../php/api/roles-fixed.php?action=list' },
                { name: 'الفئات', url: '../php/api/categories-fixed.php?action=list' },
                { name: 'المنتجات', url: '../php/api/products.php?action=list' },
                { name: 'المتاجر', url: '../php/api/stores.php?action=list' },
                { name: 'الاشتراكات', url: '../php/api/subscriptions-fixed.php?action=plans' }
            ];

            let successCount = 0;
            let totalTests = apiTests.length;
            let completedTests = 0;

            // Update indicator to show testing
            indicator.innerHTML = `
                <i class="fas fa-spinner fa-spin" style="color: #4299e1;"></i>
                <span style="color: #4299e1;">اختبار الاتصال بقاعدة البيانات...</span>
            `;

            apiTests.forEach(test => {
                fetch(test.url)
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        }
                        throw new Error(`HTTP ${response.status}`);
                    })
                    .then(data => {
                        if (data.success) {
                            successCount++;
                        }
                    })
                    .catch(error => {
                        console.warn(`API test failed for ${test.name}:`, error);
                    })
                    .finally(() => {
                        completedTests++;
                        if (completedTests === totalTests) {
                            updateDatabaseStatus(successCount, totalTests);
                        }
                    });
            });
        }

        function updateDatabaseStatus(successCount, totalTests) {
            const indicator = document.getElementById('dbStatusIndicator');
            if (!indicator) return;

            const successRate = (successCount / totalTests) * 100;
            let status, color, icon, message;

            if (successRate >= 80) {
                status = 'success';
                color = '#48bb78';
                icon = 'fas fa-check-circle';
                message = `قاعدة البيانات متصلة (${successCount}/${totalTests})`;
            } else if (successRate >= 50) {
                status = 'warning';
                color = '#ed8936';
                icon = 'fas fa-exclamation-triangle';
                message = `اتصال جزئي بقاعدة البيانات (${successCount}/${totalTests})`;
            } else {
                status = 'error';
                color = '#f56565';
                icon = 'fas fa-times-circle';
                message = `مشاكل في الاتصال بقاعدة البيانات (${successCount}/${totalTests})`;
            }

            indicator.innerHTML = `
                <i class="${icon}" style="color: ${color};"></i>
                <span style="color: ${color};">${message}</span>
            `;

            indicator.style.background = status === 'success' ? '#f0fff4' :
                                       status === 'warning' ? '#fffaf0' : '#fed7d7';
            indicator.style.borderColor = color;

            // Store status globally for other components to use
            window.databaseStatus = {
                status: status,
                successCount: successCount,
                totalTests: totalTests,
                successRate: successRate
            };

            console.log(`🔍 Database connectivity test completed: ${successCount}/${totalTests} APIs working (${successRate.toFixed(1)}%)`);
        }

        // Run database connectivity test on page load
        setTimeout(testDatabaseConnectivity, 2000);

        // Final admin settings menu fix
        setTimeout(function() {
            console.log('🔧 Final admin settings menu fix...');
            const menu = document.querySelector('.admin-settings-menu');
            const submenu = document.querySelector('.admin-settings-submenu');

            if (menu && submenu) {
                // Force expand the menu
                menu.classList.add('expanded');
                submenu.style.display = 'block';
                submenu.style.visibility = 'visible';
                submenu.style.opacity = '1';
                submenu.style.maxHeight = 'none';
                submenu.style.overflow = 'visible';

                // Ensure submenu items are clickable
                const items = submenu.querySelectorAll('li');
                items.forEach(item => {
                    item.style.pointerEvents = 'auto';
                    item.style.cursor = 'pointer';

                    // Add click handler if not already present
                    if (!item.hasAttribute('data-click-handler')) {
                        item.setAttribute('data-click-handler', 'true');
                        item.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            const sectionId = this.getAttribute('data-section');
                            if (sectionId) {
                                console.log('🎯 Clicking admin section:', sectionId);

                                // Remove active from all items
                                items.forEach(i => i.classList.remove('active'));
                                // Add active to clicked item
                                this.classList.add('active');

                                // Navigate to section
                                if (window.showAdminSection) {
                                    window.showAdminSection(sectionId);
                                } else if (window.enhancedAdminSettingsMenu) {
                                    window.enhancedAdminSettingsMenu.navigateToSection(sectionId);
                                }
                            }
                        });
                    }
                });

                console.log('✅ Admin settings menu fully fixed and functional');
            }
        }, 1000);

        // Enhanced Admin Profile Dropdown Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const adminProfileBtn = document.getElementById('adminProfileBtn');
            const adminProfileDropdown = document.getElementById('adminProfileDropdown');
            const profileChevron = document.getElementById('profileChevron');

            if (adminProfileBtn && adminProfileDropdown) {
                adminProfileBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const isVisible = adminProfileDropdown.style.display === 'block';

                    if (isVisible) {
                        adminProfileDropdown.style.display = 'none';
                        if (profileChevron) {
                            profileChevron.style.transform = 'rotate(0deg)';
                        }
                    } else {
                        adminProfileDropdown.style.display = 'block';
                        if (profileChevron) {
                            profileChevron.style.transform = 'rotate(180deg)';
                        }
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!adminProfileBtn.contains(e.target) && !adminProfileDropdown.contains(e.target)) {
                        adminProfileDropdown.style.display = 'none';
                        if (profileChevron) {
                            profileChevron.style.transform = 'rotate(0deg)';
                        }
                    }
                });

                // Add smooth dropdown animation
                const originalDisplay = adminProfileDropdown.style.display;
                adminProfileDropdown.style.opacity = '0';
                adminProfileDropdown.style.transform = 'translateY(-10px)';
                adminProfileDropdown.style.transition = 'all 0.3s ease';

                // Override display changes to include animation
                const originalSetDisplay = function(display) {
                    if (display === 'block') {
                        adminProfileDropdown.style.display = 'block';
                        setTimeout(() => {
                            adminProfileDropdown.style.opacity = '1';
                            adminProfileDropdown.style.transform = 'translateY(0)';
                        }, 10);
                    } else {
                        adminProfileDropdown.style.opacity = '0';
                        adminProfileDropdown.style.transform = 'translateY(-10px)';
                        setTimeout(() => {
                            adminProfileDropdown.style.display = 'none';
                        }, 300);
                    }
                };
            }
        });

        // Admin Profile Functions
        window.openAdminSettings = function() {
            document.getElementById('adminProfileDropdown').style.display = 'none';
            alert('إعدادات المدير - سيتم تطوير هذه الميزة قريباً');
        };

        window.viewAdminProfile = function() {
            document.getElementById('adminProfileDropdown').style.display = 'none';
            alert('الملف الشخصي للمدير - سيتم تطوير هذه الميزة قريباً');
        };

        window.changeAdminPassword = function() {
            document.getElementById('adminProfileDropdown').style.display = 'none';
            const newPassword = prompt('أدخل كلمة المرور الجديدة:');
            if (newPassword) {
                alert('تم تغيير كلمة المرور بنجاح - سيتم تطوير هذه الميزة قريباً');
            }
        };

        window.viewSystemLogs = function() {
            document.getElementById('adminProfileDropdown').style.display = 'none';
            alert('سجلات النظام - سيتم تطوير هذه الميزة قريباً');
        };

        window.signOutAdmin = function() {
            document.getElementById('adminProfileDropdown').style.display = 'none';

            if (confirm('هل أنت متأكد من تسجيل الخروج؟\n\nسيتم إنهاء جلسة العمل الحالية وإعادة توجيهك إلى صفحة تسجيل الدخول.')) {
                // Show loading indicator
                const loadingHtml = `
                    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                        <div style="background: white; padding: 30px; border-radius: 12px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                            <div style="width: 50px; height: 50px; border: 4px solid #f3f3f3; border-top: 4px solid #dc2626; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
                            <h3 style="margin: 0 0 10px 0; color: #2d3748;">جاري تسجيل الخروج...</h3>
                            <p style="margin: 0; color: #718096;">يرجى الانتظار...</p>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', loadingHtml);

                // Clear any stored session data
                try {
                    localStorage.removeItem('adminSession');
                    localStorage.removeItem('adminToken');
                    sessionStorage.clear();
                } catch(e) {
                    console.log('Session cleanup completed');
                }

                // Redirect after a short delay
                setTimeout(() => {
                    // Try to redirect to admin login page
                    if (window.location.pathname.includes('/admin/')) {
                        window.location.href = '../admin-login.html?logout=success';
                    } else {
                        window.location.href = 'admin-login.html?logout=success';
                    }
                }, 2000);
            }
        };

        // Enhanced Diagnostics Panel Toggle Function
        window.toggleDiagnosticsPanel = function() {
            let diagnosticsPanel = document.getElementById('diagnosticsPanel');

            if (!diagnosticsPanel) {
                // Create enhanced diagnostics panel
                const panelHtml = `
                    <div id="diagnosticsPanel" style="position: fixed; top: 20px; right: 20px; width: 450px; max-height: 80vh; background: white; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.15); z-index: 10000; overflow: hidden; border: 1px solid #e2e8f0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
                        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; display: flex; justify-content: space-between; align-items: center; position: relative;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-chart-line" style="font-size: 1.2rem;"></i>
                                </div>
                                <div>
                                    <h3 style="margin: 0; font-size: 1.2rem; font-weight: 600;">مدير التطوير</h3>
                                    <p style="margin: 0; font-size: 0.85rem; opacity: 0.9;">أدوات التشخيص والإصلاح</p>
                                </div>
                            </div>
                            <button onclick="toggleDiagnosticsPanel()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 10px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                                <i class="fas fa-times" style="font-size: 1rem;"></i>
                            </button>
                        </div>
                        <div style="padding: 25px; max-height: 60vh; overflow-y: auto;">
                            <div id="diagnosticsContent">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 20px;">
                                    <button onclick="runNavigationDiagnostics()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 12px 16px; border-radius: 10px; cursor: pointer; font-size: 0.9rem; display: flex; align-items: center; gap: 8px; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                        <i class="fas fa-search"></i>
                                        <span>تشخيص التنقل</span>
                                    </button>
                                    <button onclick="runComprehensiveFix()" style="background: linear-gradient(135deg, #10b981, #059669); color: white; border: none; padding: 12px 16px; border-radius: 10px; cursor: pointer; font-size: 0.9rem; display: flex; align-items: center; gap: 8px; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                        <i class="fas fa-tools"></i>
                                        <span>إصلاح شامل</span>
                                    </button>
                                    <button onclick="fixSpecificIssues()" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; border: none; padding: 12px 16px; border-radius: 10px; cursor: pointer; font-size: 0.9rem; display: flex; align-items: center; gap: 8px; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                        <i class="fas fa-bullseye"></i>
                                        <span>إصلاح محدد</span>
                                    </button>
                                    <button onclick="runUltimateFix()" style="background: linear-gradient(135deg, #dc2626, #991b1b); color: white; border: none; padding: 12px 16px; border-radius: 10px; cursor: pointer; font-size: 0.9rem; display: flex; align-items: center; gap: 8px; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                        <i class="fas fa-rocket"></i>
                                        <span>إصلاح نهائي</span>
                                    </button>
                                </div>
                                <div style="border-top: 1px solid #e2e8f0; padding-top: 20px;">
                                    <h4 style="margin: 0 0 15px 0; color: #374151; font-size: 1rem; display: flex; align-items: center; gap: 8px;">
                                        <i class="fas fa-database" style="color: #667eea;"></i>
                                        أدوات قاعدة البيانات
                                    </h4>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                        <button onclick="testDatabase()" style="background: #f8fafc; color: #374151; border: 1px solid #e2e8f0; padding: 10px 14px; border-radius: 8px; cursor: pointer; font-size: 0.85rem; display: flex; align-items: center; gap: 6px; transition: all 0.3s ease;" onmouseover="this.style.background='#f1f5f9'" onmouseout="this.style.background='#f8fafc'">
                                            <i class="fas fa-database" style="color: #3b82f6;"></i>
                                            <span>اختبار DB</span>
                                        </button>
                                        <button onclick="clearCache()" style="background: #f8fafc; color: #374151; border: 1px solid #e2e8f0; padding: 10px 14px; border-radius: 8px; cursor: pointer; font-size: 0.85rem; display: flex; align-items: center; gap: 6px; transition: all 0.3s ease;" onmouseover="this.style.background='#f1f5f9'" onmouseout="this.style.background='#f8fafc'">
                                            <i class="fas fa-trash" style="color: #ef4444;"></i>
                                            <span>مسح الكاش</span>
                                        </button>
                                    </div>
                                </div>
                                <div id="diagnosticsResults" style="margin-top: 20px; padding: 15px; background: #f8fafc; border-radius: 10px; border-left: 4px solid #667eea; display: none;">
                                    <h5 style="margin: 0 0 10px 0; color: #374151; font-size: 0.9rem;">نتائج التشخيص:</h5>
                                    <div id="diagnosticsResultsContent" style="font-size: 0.8rem; color: #6b7280;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', panelHtml);
                diagnosticsPanel = document.getElementById('diagnosticsPanel');

                // Add animation
                diagnosticsPanel.style.opacity = '0';
                diagnosticsPanel.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    diagnosticsPanel.style.transition = 'all 0.3s ease';
                    diagnosticsPanel.style.opacity = '1';
                    diagnosticsPanel.style.transform = 'translateX(0)';
                }, 10);
            } else {
                // Toggle visibility with smooth animation
                if (diagnosticsPanel.style.display === 'none' || diagnosticsPanel.style.opacity === '0') {
                    diagnosticsPanel.style.display = 'block';
                    diagnosticsPanel.style.transition = 'all 0.3s ease';
                    diagnosticsPanel.style.opacity = '1';
                    diagnosticsPanel.style.transform = 'translateX(0)';
                } else {
                    diagnosticsPanel.style.transition = 'all 0.3s ease';
                    diagnosticsPanel.style.opacity = '0';
                    diagnosticsPanel.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        diagnosticsPanel.style.display = 'none';
                    }, 300);
                }
            }
        };

        // Clear Cache Function
        window.clearCache = function() {
            const resultsDiv = document.getElementById('diagnosticsResults');
            const resultsContent = document.getElementById('diagnosticsResultsContent');

            if (resultsDiv && resultsContent) {
                resultsDiv.style.display = 'block';
                resultsContent.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري مسح الكاش...';

                setTimeout(() => {
                    // Simulate cache clearing
                    localStorage.clear();
                    sessionStorage.clear();

                    resultsContent.innerHTML = `
                        <div style="color: #059669; display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-check-circle"></i>
                            <span>تم مسح الكاش بنجاح</span>
                        </div>
                        <div style="margin-top: 8px; font-size: 0.75rem; color: #6b7280;">
                            • تم مسح localStorage<br>
                            • تم مسح sessionStorage<br>
                            • تم تنظيف ذاكرة التخزين المؤقت
                        </div>
                    `;
                }, 1500);
            }
        };

        // Enhanced Test Database Function
        window.testDatabase = async function() {
            const resultsDiv = document.getElementById('diagnosticsResults');
            const resultsContent = document.getElementById('diagnosticsResultsContent');

            if (resultsDiv && resultsContent) {
                resultsDiv.style.display = 'block';
                resultsContent.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار قاعدة البيانات...';

                try {
                    const response = await fetch('../php/api/test-connection.php');
                    const result = await response.json();

                    if (result.success) {
                        resultsContent.innerHTML = `
                            <div style="color: #059669; display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                                <i class="fas fa-check-circle"></i>
                                <span>اتصال قاعدة البيانات ناجح</span>
                            </div>
                            <div style="font-size: 0.75rem; color: #6b7280;">
                                <strong>تفاصيل الاتصال:</strong><br>
                                • الخادم: ${result.host || 'localhost'}<br>
                                • قاعدة البيانات: ${result.database || 'mossab-landing-page'}<br>
                                • الحالة: متصل<br>
                                • وقت الاستجابة: ${result.response_time || 'غير محدد'}
                            </div>
                        `;
                    } else {
                        resultsContent.innerHTML = `
                            <div style="color: #dc2626; display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>فشل في الاتصال بقاعدة البيانات</span>
                            </div>
                            <div style="font-size: 0.75rem; color: #6b7280;">
                                <strong>خطأ:</strong> ${result.message || 'خطأ غير معروف'}
                            </div>
                        `;
                    }
                } catch (error) {
                    resultsContent.innerHTML = `
                        <div style="color: #dc2626; display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                            <i class="fas fa-times-circle"></i>
                            <span>خطأ في الاتصال</span>
                        </div>
                        <div style="font-size: 0.75rem; color: #6b7280;">
                            <strong>تفاصيل الخطأ:</strong> ${error.message}
                        </div>
                    `;
                }
            }
        };

        // Load Diagnostics Data Function
        window.loadDiagnosticsData = function() {
            const content = document.getElementById('diagnosticsContent');
            if (!content) return;

            const diagnosticsHtml = `
                <div style="margin-bottom: 15px;">
                    <h4 style="margin: 0 0 10px 0; color: #2d3748; font-size: 1rem;"><i class="fas fa-database"></i> حالة قاعدة البيانات</h4>
                    <div id="dbStatusDiagnostics" style="padding: 10px; background: #f7fafc; border-radius: 6px; font-size: 0.9rem;">
                        جاري فحص الاتصال...
                    </div>
                </div>

                <div style="margin-bottom: 15px;">
                    <h4 style="margin: 0 0 10px 0; color: #2d3748; font-size: 1rem;"><i class="fas fa-cogs"></i> حالة النظام</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.85rem;">
                        <div style="padding: 8px; background: #f0fff4; border: 1px solid #c6f6d5; border-radius: 4px; color: #22543d;">
                            <strong>الذاكرة:</strong> طبيعية
                        </div>
                        <div style="padding: 8px; background: #f0fff4; border: 1px solid #c6f6d5; border-radius: 4px; color: #22543d;">
                            <strong>المعالج:</strong> مستقر
                        </div>
                        <div style="padding: 8px; background: #f0fff4; border: 1px solid #c6f6d5; border-radius: 4px; color: #22543d;">
                            <strong>التخزين:</strong> متاح
                        </div>
                        <div style="padding: 8px; background: #f0fff4; border: 1px solid #c6f6d5; border-radius: 4px; color: #22543d;">
                            <strong>الشبكة:</strong> متصل
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 15px;">
                    <h4 style="margin: 0 0 10px 0; color: #2d3748; font-size: 1rem;"><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h4>
                    <div style="font-size: 0.85rem; line-height: 1.6;">
                        <div>• المستخدمين النشطين: <strong>1</strong></div>
                        <div>• الجلسات المفتوحة: <strong>1</strong></div>
                        <div>• آخر نشاط: <strong>الآن</strong></div>
                        <div>• وقت التشغيل: <strong>${Math.floor(Date.now() / 1000 / 60)} دقيقة</strong></div>
                    </div>
                </div>

                <div style="display: flex; gap: 8px; margin-top: 15px;">
                    <button onclick="testDatabaseConnectivity()" style="flex: 1; padding: 8px; background: #3b82f6; color: white; border: none; border-radius: 4px; font-size: 0.8rem; cursor: pointer;">
                        <i class="fas fa-database"></i> فحص DB
                    </button>
                    <button onclick="loadDiagnosticsData()" style="flex: 1; padding: 8px; background: #10b981; color: white; border: none; border-radius: 4px; font-size: 0.8rem; cursor: pointer;">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            `;

            content.innerHTML = diagnosticsHtml;

            // Update database status
            setTimeout(() => {
                const dbStatus = document.getElementById('dbStatusDiagnostics');
                if (dbStatus) {
                    dbStatus.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <i class="fas fa-check-circle" style="color: #48bb78;"></i>
                            <span style="color: #22543d;">متصل بنجاح - MariaDB 11.5.2</span>
                        </div>
                        <div style="font-size: 0.8rem; color: #718096; margin-top: 4px;">
                            Host: localhost:3307 | DB: mossab-landing-page
                        </div>
                    `;
                }
            }, 1000);
        };
            diagnosticBtn.style.color = 'white';
            diagnosticBtn.style.border = 'none';
            diagnosticBtn.style.borderRadius = '8px';
            diagnosticBtn.style.cursor = 'pointer';
            diagnosticBtn.style.fontSize = '0.9em';
            diagnosticBtn.style.fontWeight = 'bold';
            diagnosticBtn.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
            diagnosticBtn.style.transition = 'all 0.3s ease';
            diagnosticBtn.title = 'أداة تشخيص لفحص حالة القوائم والواجهات - للمطورين والدعم الفني';

            // Add hover effect
            diagnosticBtn.onmouseover = function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
            };
            diagnosticBtn.onmouseout = function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
            };
            diagnosticBtn.onclick = function() {
                const submenu = document.querySelector('.admin-settings-submenu');
                const items = submenu ? submenu.querySelectorAll('li') : [];

                console.log('🔍 === تشخيص شامل للنظام ===');
                console.log('📅 الوقت:', new Date().toLocaleString('ar-DZ'));
                console.log('🌐 المتصفح:', navigator.userAgent);
                console.log('📱 حجم الشاشة:', window.innerWidth + 'x' + window.innerHeight);

                console.log('\n🔍 تشخيص القائمة الفرعية:');
                console.log('- القائمة الفرعية موجودة:', !!submenu);
                if (submenu) {
                    const submenuStyle = window.getComputedStyle(submenu);
                    console.log('- Display:', submenuStyle.display);
                    console.log('- Visibility:', submenuStyle.visibility);
                    console.log('- Opacity:', submenuStyle.opacity);
                    console.log('- Max-height:', submenuStyle.maxHeight);
                    console.log('- عدد العناصر:', items.length);

                    items.forEach((item, index) => {
                        const section = item.getAttribute('data-section');
                        const text = item.querySelector('span')?.textContent;
                        const itemStyle = window.getComputedStyle(item);
                        console.log(`- العنصر ${index + 1}: ${text} (${section})`);
                        console.log(`  Display: ${itemStyle.display}, Visibility: ${itemStyle.visibility}, Opacity: ${itemStyle.opacity}`);
                    });
                }

                console.log('\n🔍 تشخيص الأقسام النشطة:');
                const activeSections = document.querySelectorAll('.content-section.active');
                console.log('- عدد الأقسام النشطة:', activeSections.length);
                activeSections.forEach((section, index) => {
                    console.log(`- القسم النشط ${index + 1}:`, section.id);
                });

                console.log('\n🔍 تشخيص الأخطاء:');
                const errors = document.querySelectorAll('.error, .alert-danger');
                console.log('- عدد رسائل الخطأ:', errors.length);

                alert('🔍 تم إجراء تشخيص شامل للنظام!\n\n📋 تفاصيل التشخيص متوفرة في وحدة التحكم (اضغط F12)\n\n💡 هذه الأداة مخصصة للمطورين والدعم الفني لتشخيص مشاكل الواجهة');
            };
            document.body.appendChild(diagnosticBtn);
        }, 1000);
    </script>
    <!-- Reports and Store Management Scripts -->
    <!-- Scripts already loaded with defer -->

    <!-- Content Containers -->
    <div id="reportsContent" class="content-section"></div>
    <div id="storesManagementContent" class="content-section"></div>

    <!-- Showcase Demo Section -->
    <section id="showcaseDemo" class="content-section">
      <div id="showcaseDemoContent">
        <!-- Header Section -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
          <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
            <i class="fas fa-star" style="font-size: 3rem;"></i>
            <div>
              <h2 style="margin: 0; font-size: 2rem;">عرض النظام المحسن</h2>
              <p style="margin: 5px 0 0 0; opacity: 0.9;">استعراض مميزات إدارة المستخدمين المتقدمة</p>
            </div>
          </div>
        </div>

        <!-- Features Overview -->
        <div style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 20px;">
          <h3 style="margin: 0 0 20px 0; color: #2d3748; display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-users" style="color: #667eea;"></i>
            نظام إدارة المستخدمين المتقدم
          </h3>

          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px;">
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
              <h4 style="margin: 0 0 10px 0; color: #2d3748;">إدارة شاملة</h4>
              <p style="margin: 0; color: #718096; font-size: 0.9em;">إضافة وتعديل وحذف المستخدمين مع واجهة سهلة الاستخدام</p>
            </div>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #17a2b8;">
              <h4 style="margin: 0 0 10px 0; color: #2d3748;">أدوار متقدمة</h4>
              <p style="margin: 0; color: #718096; font-size: 0.9em;">نظام أدوار مرن مع صلاحيات قابلة للتخصيص</p>
            </div>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
              <h4 style="margin: 0 0 10px 0; color: #2d3748;">تقارير مفصلة</h4>
              <p style="margin: 0; color: #718096; font-size: 0.9em;">إحصائيات وتقارير شاملة عن نشاط المستخدمين</p>
            </div>
          </div>

          <div style="text-align: center; margin-top: 30px;">
            <button onclick="openUsersManagementShowcase()" style="padding: 15px 30px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 1.1em; margin: 0 10px;">
              <i class="fas fa-external-link-alt"></i> فتح العرض في نافذة جديدة
            </button>
            <button onclick="loadUsersManagementInline()" style="padding: 15px 30px; background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 1.1em; margin: 0 10px;">
              <i class="fas fa-eye"></i> عرض مضمن
            </button>
          </div>
        </div>

        <!-- Inline Showcase Container -->
        <div id="inlineShowcaseContainer" style="display: none; background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <iframe id="showcaseFrame" src="" style="width: 100%; height: 600px; border: none; border-radius: 8px;"></iframe>
        </div>
      </div>
    </section>

    <script>
      // Showcase Demo Functions
      window.openUsersManagementShowcase = function() {
        window.open('users-management-showcase.html', '_blank');
      };

      window.loadUsersManagementInline = function() {
        const container = document.getElementById('inlineShowcaseContainer');
        const iframe = document.getElementById('showcaseFrame');

        if (container.style.display === 'none') {
          iframe.src = 'users-management-showcase.html';
          container.style.display = 'block';
          container.scrollIntoView({ behavior: 'smooth' });
        } else {
          container.style.display = 'none';
          iframe.src = '';
        }
      };

      // Product Add Message Function (since modal was removed)
      window.showProductAddMessage = function() {
        if (typeof notificationManager !== 'undefined') {
          notificationManager.showInfo('ميزة إضافة المنتجات متوفرة في قسم إدارة المنتجات المخصص');
        } else {
          alert('ميزة إضافة المنتجات متوفرة في قسم إدارة المنتجات المخصص');
        }
      };

      // Theme Toggle Functionality
      class ThemeManager {
        constructor() {
          this.currentTheme = localStorage.getItem('admin-theme') || 'light';
          this.themeToggleBtn = document.getElementById('themeToggleBtn');
          this.lightIcon = document.getElementById('lightIcon');
          this.darkIcon = document.getElementById('darkIcon');
          this.themeText = document.querySelector('.theme-toggle-text');

          this.init();
        }

        init() {
          // Apply saved theme
          this.applyTheme(this.currentTheme);

          // Add event listener
          if (this.themeToggleBtn) {
            this.themeToggleBtn.addEventListener('click', () => {
              this.toggleTheme();
            });
          }

          // Update time display
          this.updateLastUpdateTime();
          setInterval(() => this.updateLastUpdateTime(), 60000); // Update every minute
        }

        toggleTheme() {
          this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
          this.applyTheme(this.currentTheme);
          localStorage.setItem('admin-theme', this.currentTheme);

          // Show notification
          if (typeof notificationManager !== 'undefined') {
            const message = this.currentTheme === 'dark' ? 'تم تفعيل المظهر الداكن' : 'تم تفعيل المظهر الفاتح';
            notificationManager.showSuccess(message);
          }
        }

        applyTheme(theme) {
          document.documentElement.setAttribute('data-theme', theme);

          if (this.lightIcon && this.darkIcon && this.themeText) {
            if (theme === 'dark') {
              this.lightIcon.style.display = 'none';
              this.darkIcon.style.display = 'block';
              this.themeText.textContent = 'المظهر الداكن';
            } else {
              this.lightIcon.style.display = 'block';
              this.darkIcon.style.display = 'none';
              this.themeText.textContent = 'المظهر الفاتح';
            }
          }
        }

        updateLastUpdateTime() {
          const lastUpdateElement = document.getElementById('lastUpdateTime');
          if (lastUpdateElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            });
            lastUpdateElement.textContent = timeString;
          }
        }
      }

      // Dashboard Protection System
      class DashboardProtectionSystem {
        constructor() {
          this.isEnhancedDashboard = false;
          this.protectionActive = false;
          this.observer = null;
          this.init();
        }

        init() {
          // Wait for DOM to be fully loaded
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupProtection());
          } else {
            this.setupProtection();
          }
        }

        setupProtection() {
          const dashboard = document.getElementById('dashboard');
          if (!dashboard) return;

          // Check if enhanced dashboard exists
          const enhancedDashboard = dashboard.querySelector('.dashboard-header');
          if (enhancedDashboard) {
            this.isEnhancedDashboard = true;
            this.activateProtection(dashboard);
          }

          // Also check periodically in case enhanced dashboard loads later
          let checkCount = 0;
          const checkInterval = setInterval(() => {
            const dashboard = document.getElementById('dashboard');
            const enhancedDashboard = dashboard ? dashboard.querySelector('.dashboard-header') : null;

            if (enhancedDashboard && !this.protectionActive) {
              this.isEnhancedDashboard = true;
              this.activateProtection(dashboard);
              clearInterval(checkInterval);
            }

            checkCount++;
            if (checkCount > 10) { // Stop checking after 10 attempts
              clearInterval(checkInterval);
            }
          }, 500);
        }

        activateProtection(dashboard) {
          if (this.protectionActive) return;

          console.log('🛡️ Activating enhanced dashboard protection system');

          // Mark dashboard as enhanced
          dashboard.setAttribute('data-enhanced', 'true');
          dashboard.setAttribute('data-protected', 'true');

          // Override common dashboard replacement functions
          this.overrideDashboardFunctions();

          // Set up mutation observer
          this.setupMutationObserver(dashboard);

          // Prevent innerHTML modifications
          this.protectInnerHTML(dashboard);

          this.protectionActive = true;
          console.log('✅ Dashboard protection system activated');
        }

        overrideDashboardFunctions() {
          // Override loadDashboard function
          const originalLoadDashboard = window.loadDashboard;
          window.loadDashboard = () => {
            console.log('🛡️ loadDashboard() call blocked - enhanced dashboard protected');
            return false;
          };

          // Override dashboard cleanup functions
          if (window.dashboardCleanup && window.dashboardCleanup.clean) {
            const originalClean = window.dashboardCleanup.clean;
            window.dashboardCleanup.clean = () => {
              console.log('🛡️ Dashboard cleanup blocked - enhanced dashboard protected');
              return false;
            };
          }

          // Override multi-user dashboard loading
          if (window.loadEnhancedDashboard) {
            const originalLoadEnhanced = window.loadEnhancedDashboard;
            window.loadEnhancedDashboard = () => {
              console.log('🛡️ Multi-user dashboard loading blocked - enhanced dashboard already present');
              return false;
            };
          }
        }

        setupMutationObserver(dashboard) {
          this.observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.type === 'childList') {
                const currentDashboard = document.getElementById('dashboard');
                if (currentDashboard && !currentDashboard.querySelector('.dashboard-header')) {
                  console.log('🚨 Enhanced dashboard content was removed - preventing replacement');
                  // Prevent the change by restoring from backup or reloading
                  this.restoreEnhancedDashboard();
                }
              }
            });
          });

          this.observer.observe(dashboard, {
            childList: true,
            subtree: false
          });
        }

        protectInnerHTML(dashboard) {
          // Store original innerHTML
          const originalInnerHTML = dashboard.innerHTML;

          // Override innerHTML setter
          let isRestoring = false;
          Object.defineProperty(dashboard, 'innerHTML', {
            get: function() {
              return this._innerHTML || originalInnerHTML;
            },
            set: function(value) {
              if (isRestoring) {
                this._innerHTML = value;
                return;
              }

              // Check if the new content contains enhanced dashboard
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = value;
              const hasEnhancedDashboard = tempDiv.querySelector('.dashboard-header');

              if (!hasEnhancedDashboard && dashboard.getAttribute('data-protected') === 'true') {
                console.log('🛡️ Blocked attempt to replace enhanced dashboard content');
                return; // Block the change
              }

              this._innerHTML = value;
            }
          });
        }

        restoreEnhancedDashboard() {
          console.log('🔄 Restoring enhanced dashboard...');
          // Force page reload to restore enhanced dashboard
          setTimeout(() => {
            location.reload();
          }, 100);
        }
      }

      // Initialize protection system
      window.dashboardProtection = new DashboardProtectionSystem();

      // Initialize theme manager
      document.addEventListener('DOMContentLoaded', function() {
        window.themeManager = new ThemeManager();
      });

      // Enhanced Dashboard Functions
      window.openAdminSettings = function() {
        if (typeof notificationManager !== 'undefined') {
          notificationManager.showInfo('إعدادات المدير - قيد التطوير');
        } else {
          alert('إعدادات المدير - قيد التطوير');
        }
      };

      window.viewAdminProfile = function() {
        if (typeof notificationManager !== 'undefined') {
          notificationManager.showInfo('الملف الشخصي للمدير - قيد التطوير');
        } else {
          alert('الملف الشخصي للمدير - قيد التطوير');
        }
      };

      window.changeAdminPassword = function() {
        if (typeof notificationManager !== 'undefined') {
          notificationManager.showInfo('تغيير كلمة المرور - قيد التطوير');
        } else {
          alert('تغيير كلمة المرور - قيد التطوير');
        }
      };

      window.viewSystemLogs = function() {
        if (typeof notificationManager !== 'undefined') {
          notificationManager.showInfo('سجلات النظام - قيد التطوير');
        } else {
          alert('سجلات النظام - قيد التطوير');
        }
      };

      window.signOutAdmin = function() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
          // Use Firebase sign out if available
          if (window.firebaseAuth) {
            window.firebaseAuth.signOutUser();
          } else {
            // Fallback to traditional logout
            if (typeof notificationManager !== 'undefined') {
              notificationManager.showSuccess('تم تسجيل الخروج بنجاح');
            }
            setTimeout(() => {
              window.safeRedirect('login.html');
            }, 1500);
          }
        }
      };
    </script>

    <!-- Auth Fix Script (load first) -->
    <script src="auth-fix.js"></script>

    <!-- Firebase Configuration -->
    <script type="module" src="js/firebase-config.js"></script>

    <!-- Firebase Auth Integration -->
    <script type="module">
        // Firebase authentication integration
        window.onFirebaseUserSignedIn = (user, profile) => {
            console.log('🔐 Firebase user signed in:', user.email);

            // Update user info in UI
            const userNameElement = document.querySelector('.user-name');
            if (userNameElement) {
                userNameElement.textContent = profile?.displayName || user.displayName || user.email;
            }

            // Update user avatar
            const userAvatarElement = document.querySelector('.user-avatar');
            if (userAvatarElement && user.photoURL) {
                userAvatarElement.src = user.photoURL;
            }

            // Show admin content if user is admin
            if (profile && ['admin', 'super_admin', 'owner'].includes(profile.role)) {
                document.body.classList.add('admin-authenticated');
                console.log('✅ Admin access granted');
            } else {
                console.log('⚠️ Limited access - user role:', profile?.role);
                // You can redirect non-admin users or show limited interface
            }
        };

        window.onFirebaseUserSignedOut = () => {
            console.log('🔓 Firebase user signed out');
            // Redirect to login page
            window.safeRedirect('login.html');
        };

        // Use safe authentication check
        window.safeAuthCheck(
            // On authenticated
            function(userInfo) {
                console.log('✅ User authenticated in admin panel');
                if (userInfo.isAdmin) {
                    console.log('✅ Admin access granted');
                    document.body.classList.add('admin-authenticated');
                } else {
                    console.log('⚠️ User is not admin, redirecting to user area');
                    window.safeRedirect('../index.html');
                }
            },
            // On not authenticated
            function() {
                console.log('❌ User not authenticated, redirecting to login');
                window.safeRedirect('login.html');
            },
            // On error
            function(error) {
                console.error('❌ Auth check error:', error);
                // Show error message instead of redirecting immediately
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = 'position:fixed;top:20px;right:20px;background:#f8d7da;color:#721c24;padding:15px;border-radius:5px;z-index:9999;';
                errorDiv.textContent = 'خطأ في التحقق من المصادقة. جاري إعادة المحاولة...';
                document.body.appendChild(errorDiv);

                // Retry after delay
                setTimeout(() => {
                    window.safeRedirect('login.html');
                }, 3000);
            }
        );
    </script>
  </body>
</html>
