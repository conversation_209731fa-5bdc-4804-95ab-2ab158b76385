<?php
/**
 * Fix Roles and Permissions API
 * إصلاح الأدوار والصلاحيات
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database connection
$host = 'localhost';
$dbname = 'poultraydz';
$username = 'postgres';
$password = 'root';

try {
    $pdo = new PDO("pgsql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $action = $_GET['action'] ?? 'fix_all';
    
    switch ($action) {
        case 'fix_all':
            fixAllTables();
            break;
        case 'check_status':
            checkStatus();
            break;
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ: ' . $e->getMessage()
    ]);
}

function fixAllTables() {
    global $pdo;
    
    $results = [];
    
    try {
        // Check and create role_permissions table
        $stmt = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_name = 'role_permissions'");
        if ($stmt->rowCount() == 0) {
            $createRolePermissions = "
            CREATE TABLE role_permissions (
                id SERIAL PRIMARY KEY,
                role_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                granted BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(role_id, permission_id)
            )";
            $pdo->exec($createRolePermissions);
            $results[] = 'تم إنشاء جدول role_permissions';
        } else {
            // Check if granted column exists
            $stmt = $pdo->query("SELECT column_name FROM information_schema.columns WHERE table_name = 'role_permissions' AND column_name = 'granted'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("ALTER TABLE role_permissions ADD COLUMN granted BOOLEAN DEFAULT TRUE");
                $results[] = 'تم إضافة عمود granted إلى جدول role_permissions';
            }
            $results[] = 'جدول role_permissions موجود ومحدث';
        }
        
        // Check and create user_roles table
        $stmt = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_name = 'user_roles'");
        if ($stmt->rowCount() == 0) {
            $createUserRoles = "
            CREATE TABLE user_roles (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                display_name_ar VARCHAR(100) NOT NULL,
                display_name_en VARCHAR(100) NOT NULL,
                description TEXT,
                level INTEGER DEFAULT 1,
                color VARCHAR(7) DEFAULT '#007bff',
                icon VARCHAR(50) DEFAULT 'fas fa-user',
                is_active BOOLEAN DEFAULT TRUE,
                is_system BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $pdo->exec($createUserRoles);
            $results[] = 'تم إنشاء جدول user_roles';
            
            // Insert default roles
            $defaultRoles = [
                ['super_admin', 'مدير عام', 'Super Admin', 'مدير عام للنظام', 1, '#dc3545', 'fas fa-crown'],
                ['admin', 'مدير', 'Admin', 'مدير النظام', 2, '#007bff', 'fas fa-user-shield'],
                ['store_manager', 'مدير متجر', 'Store Manager', 'مدير متجر إلكتروني', 3, '#28a745', 'fas fa-store'],
                ['user', 'مستخدم', 'User', 'مستخدم عادي', 4, '#6c757d', 'fas fa-user']
            ];
            
            $insertRole = $pdo->prepare("INSERT INTO user_roles (name, display_name_ar, display_name_en, description, level, color, icon, is_system) VALUES (?, ?, ?, ?, ?, ?, ?, TRUE)");
            foreach ($defaultRoles as $role) {
                $insertRole->execute($role);
            }
            $results[] = 'تم إدراج الأدوار الافتراضية';
        } else {
            $results[] = 'جدول user_roles موجود';
        }
        
        // Check and create permissions table
        $stmt = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_name = 'permissions'");
        if ($stmt->rowCount() == 0) {
            $createPermissions = "
            CREATE TABLE permissions (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                display_name_ar VARCHAR(100) NOT NULL,
                display_name_en VARCHAR(100) NOT NULL,
                category VARCHAR(50) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $pdo->exec($createPermissions);
            $results[] = 'تم إنشاء جدول permissions';
            
            // Insert default permissions
            $defaultPermissions = [
                ['manage_users', 'إدارة المستخدمين', 'Manage Users', 'user_management', 'إدارة حسابات المستخدمين'],
                ['manage_stores', 'إدارة المتاجر', 'Manage Stores', 'store_management', 'إدارة المتاجر الإلكترونية'],
                ['manage_products', 'إدارة المنتجات', 'Manage Products', 'product_management', 'إدارة المنتجات والكتالوج'],
                ['manage_orders', 'إدارة الطلبات', 'Manage Orders', 'order_management', 'إدارة الطلبات والمبيعات'],
                ['manage_settings', 'إدارة الإعدادات', 'Manage Settings', 'system_settings', 'إدارة إعدادات النظام'],
                ['view_reports', 'عرض التقارير', 'View Reports', 'reporting', 'عرض التقارير والإحصائيات'],
                ['manage_security', 'إدارة الأمان', 'Manage Security', 'security', 'إدارة إعدادات الأمان']
            ];
            
            $insertPermission = $pdo->prepare("INSERT INTO permissions (name, display_name_ar, display_name_en, category, description) VALUES (?, ?, ?, ?, ?)");
            foreach ($defaultPermissions as $permission) {
                $insertPermission->execute($permission);
            }
            $results[] = 'تم إدراج الصلاحيات الافتراضية';
        } else {
            $results[] = 'جدول permissions موجود';
        }
        
        // Check and create user_role_assignments table
        $stmt = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_name = 'user_role_assignments'");
        if ($stmt->rowCount() == 0) {
            $createUserRoleAssignments = "
            CREATE TABLE user_role_assignments (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL,
                role_id INTEGER NOT NULL,
                assigned_by INTEGER,
                is_active BOOLEAN DEFAULT TRUE,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                UNIQUE(user_id, role_id)
            )";
            $pdo->exec($createUserRoleAssignments);
            $results[] = 'تم إنشاء جدول user_role_assignments';
        } else {
            $results[] = 'جدول user_role_assignments موجود';
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إصلاح جميع الجداول بنجاح',
            'results' => $results
        ]);
        
    } catch (Exception $e) {
        throw new Exception('فشل في إصلاح الجداول: ' . $e->getMessage());
    }
}

function checkStatus() {
    global $pdo;
    
    $status = [];
    
    try {
        // Check tables
        $tables = ['role_permissions', 'user_roles', 'permissions', 'user_role_assignments'];
        
        foreach ($tables as $table) {
            $stmt = $pdo->query("SELECT table_name FROM information_schema.tables WHERE table_name = '$table'");
            $status['tables'][$table] = $stmt->rowCount() > 0;
        }
        
        // Check role_permissions columns
        if ($status['tables']['role_permissions']) {
            $stmt = $pdo->query("SELECT column_name FROM information_schema.columns WHERE table_name = 'role_permissions'");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $status['role_permissions_columns'] = $columns;
            $status['has_granted_column'] = in_array('granted', $columns);
        }
        
        // Count records
        foreach ($tables as $table) {
            if ($status['tables'][$table]) {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $status['counts'][$table] = $stmt->fetchColumn();
            }
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم فحص حالة قاعدة البيانات',
            'status' => $status
        ]);
        
    } catch (Exception $e) {
        throw new Exception('فشل في فحص الحالة: ' . $e->getMessage());
    }
}
?>
