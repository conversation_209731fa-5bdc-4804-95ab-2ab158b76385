/**
 * Remove Diagnostic Popups Script
 * Removes all diagnostic popup windows and prevents them from appearing
 */

console.log('🧹 Remove Diagnostic Popups Script loading...');

// Function to remove all diagnostic popups
function removeAllDiagnosticPopups() {
    console.log('🧹 Removing all diagnostic popups...');

    const popupIds = [
        'navigationDiagnosticsReport',
        'comprehensiveFixResults',
        'specificFixResults',
        'ultimateFixResults',
        'quickFixResults',
        'apiFixResults',
        'navigationFixResults',
        'emergencyFixResults',
        'finalFixResults'
    ];

    let removedCount = 0;

    popupIds.forEach(id => {
        const popup = document.getElementById(id);
        if (popup) {
            popup.remove();
            removedCount++;
            console.log(`✅ Removed popup: ${id}`);
        }
    });

    // Also remove any elements with diagnostic-related classes
    const diagnosticSelectors = [
        '[id*="diagnostic"]',
        '[id*="fix-results"]',
        '[id*="FixResults"]',
        '[class*="diagnostic"]',
        '[class*="fix-popup"]',
        '[style*="z-index: 1000"]'
    ];

    diagnosticSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            // Only remove if it looks like a diagnostic popup
            if (element.style.position === 'fixed' &&
                (element.style.zIndex > 1000 || element.id.includes('Fix') || element.id.includes('diagnostic'))) {
                element.remove();
                removedCount++;
                console.log(`✅ Removed diagnostic element: ${element.id || element.className}`);
            }
        });
    });

    console.log(`🧹 Removed ${removedCount} diagnostic popups/elements`);

    return removedCount;
}

// Function to prevent future popups but allow manual results display
function preventDiagnosticPopups() {
    console.log('🛡️ Setting up popup prevention...');

    // Override the display functions to prevent them from showing automatically
    const functionsToDisable = [
        'displaySpecificResults',
        'displayFixResults',
        'displayUltimateResults',
        'createDiagnosticsReport',
        'displayComprehensiveResults',
        'showQuickFixResults'
    ];

    functionsToDisable.forEach(funcName => {
        if (window[funcName]) {
            const originalFunc = window[funcName];
            window[funcName] = function(...args) {
                console.log(`🛡️ Blocked ${funcName} from displaying popup`);
                // Still run the function but show results in diagnostics panel instead
                if (typeof originalFunc === 'function') {
                    const result = originalFunc.apply(this, args);
                    // If it returns results, show them in the diagnostics panel
                    if (result) {
                        console.log(`📊 ${funcName} results:`, result);
                        showResultsInDiagnosticsPanel(result, funcName);
                    }
                    return result;
                }
            };
            console.log(`✅ Disabled popup function: ${funcName}`);
        }
    });
}

// Function to show results in diagnostics panel instead of popup
function showResultsInDiagnosticsPanel(results, functionName) {
    const resultsDiv = document.getElementById('diagnosticsResults');
    const resultsContent = document.getElementById('diagnosticsResultsContent');

    if (resultsDiv && resultsContent) {
        resultsDiv.style.display = 'block';

        let title = 'نتائج التشخيص';
        let icon = 'fas fa-info-circle';
        let color = '#667eea';

        switch (functionName) {
            case 'displaySpecificResults':
                title = 'نتائج الإصلاح المحدد';
                icon = 'fas fa-bullseye';
                color = '#3b82f6';
                break;
            case 'displayFixResults':
                title = 'نتائج الإصلاح الشامل';
                icon = 'fas fa-tools';
                color = '#10b981';
                break;
            case 'displayUltimateResults':
                title = 'نتائج الإصلاح النهائي';
                icon = 'fas fa-rocket';
                color = '#dc2626';
                break;
            case 'createDiagnosticsReport':
                title = 'تقرير التشخيص';
                icon = 'fas fa-chart-line';
                color = '#667eea';
                break;
        }

        let content = `
            <div style="color: ${color}; display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                <i class="${icon}"></i>
                <span><strong>${title}</strong></span>
            </div>
        `;

        if (results.fixes && results.fixes.length > 0) {
            content += `<div style="margin-bottom: 8px; font-size: 0.75rem;"><strong>الإصلاحات (${results.fixes.length}):</strong></div>`;
            results.fixes.slice(0, 3).forEach(fix => {
                content += `<div style="font-size: 0.7rem; color: #059669; margin-bottom: 2px;">✅ ${fix}</div>`;
            });
            if (results.fixes.length > 3) {
                content += `<div style="font-size: 0.7rem; color: #6b7280;">... و ${results.fixes.length - 3} إصلاحات أخرى</div>`;
            }
        }

        if (results.errors && results.errors.length > 0) {
            content += `<div style="margin: 8px 0 4px 0; font-size: 0.75rem;"><strong>الأخطاء (${results.errors.length}):</strong></div>`;
            results.errors.slice(0, 2).forEach(error => {
                content += `<div style="font-size: 0.7rem; color: #dc2626; margin-bottom: 2px;">❌ ${error}</div>`;
            });
        }

        if (results.warnings && results.warnings.length > 0) {
            content += `<div style="margin: 8px 0 4px 0; font-size: 0.75rem;"><strong>التحذيرات (${results.warnings.length}):</strong></div>`;
            results.warnings.slice(0, 2).forEach(warning => {
                content += `<div style="font-size: 0.7rem; color: #f59e0b; margin-bottom: 2px;">⚠️ ${warning}</div>`;
            });
        }

        resultsContent.innerHTML = content;
    }
}

// Function to clean up on interval
function startCleanupInterval() {
    console.log('⏰ Starting cleanup interval...');

    setInterval(() => {
        const removed = removeAllDiagnosticPopups();
        if (removed > 0) {
            console.log(`🧹 Interval cleanup removed ${removed} popups`);
        }
    }, 5000); // Check every 5 seconds
}

// Main initialization function
function initPopupRemoval() {
    console.log('🚀 Initializing popup removal system...');

    // Remove existing popups
    removeAllDiagnosticPopups();

    // Prevent future popups
    preventDiagnosticPopups();

    // Start cleanup interval
    startCleanupInterval();

    console.log('✅ Popup removal system initialized');
}

// Initialize immediately
initPopupRemoval();

// Also initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPopupRemoval);
} else {
    initPopupRemoval();
}

// Make functions globally available
window.removeAllDiagnosticPopups = removeAllDiagnosticPopups;
window.preventDiagnosticPopups = preventDiagnosticPopups;
window.initPopupRemoval = initPopupRemoval;

console.log('✅ Remove Diagnostic Popups Script loaded');
