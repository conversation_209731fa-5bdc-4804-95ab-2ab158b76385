<?php
/**
 * Products Multi-User API
 * Handles product-related API requests for multi-user system
 */

// Set proper headers for JSON response
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$host = 'localhost';
$port = '3307';
$dbname = 'mossab-landing-page';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit();
}

// Get parameters
$user_id = $_GET['user_id'] ?? 1;
$user_role = $_GET['user_role'] ?? 'admin';
$action = $_GET['action'] ?? 'list';

try {
    switch ($action) {
        case 'list':
        default:
            handleListProducts($pdo, $user_id, $user_role);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function handleListProducts($pdo, $user_id, $user_role) {
    try {
        // Create products table if it doesn't exist
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                category_id INT,
                user_id INT,
                status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
                stock_quantity INT DEFAULT 0,
                image_url VARCHAR(500),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_category_id (category_id),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($createTableSQL);

        // Insert sample data if table is empty
        $countStmt = $pdo->query("SELECT COUNT(*) as count FROM products");
        $count = $countStmt->fetch()['count'];
        
        if ($count == 0) {
            $sampleProducts = [
                [
                    'name' => 'هاتف ذكي متطور',
                    'description' => 'هاتف ذكي بمواصفات عالية وكاميرا متقدمة',
                    'price' => 299.99,
                    'category_id' => 1,
                    'user_id' => 1,
                    'stock_quantity' => 50,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Phone'
                ],
                [
                    'name' => 'لابتوب للألعاب',
                    'description' => 'لابتوب قوي مخصص للألعاب والتصميم',
                    'price' => 899.99,
                    'category_id' => 1,
                    'user_id' => 1,
                    'stock_quantity' => 25,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Laptop'
                ],
                [
                    'name' => 'قميص قطني',
                    'description' => 'قميص قطني عالي الجودة ومريح',
                    'price' => 29.99,
                    'category_id' => 2,
                    'user_id' => 2,
                    'stock_quantity' => 100,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Shirt'
                ],
                [
                    'name' => 'كتاب البرمجة',
                    'description' => 'دليل شامل لتعلم البرمجة من الصفر',
                    'price' => 19.99,
                    'category_id' => 3,
                    'user_id' => 1,
                    'stock_quantity' => 200,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Book'
                ],
                [
                    'name' => 'ساعة ذكية',
                    'description' => 'ساعة ذكية مع مراقب اللياقة البدنية',
                    'price' => 199.99,
                    'category_id' => 1,
                    'user_id' => 2,
                    'stock_quantity' => 75,
                    'image_url' => 'https://via.placeholder.com/300x300?text=Watch'
                ]
            ];
            
            $insertStmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id, user_id, stock_quantity, image_url) VALUES (?, ?, ?, ?, ?, ?, ?)");
            foreach ($sampleProducts as $product) {
                $insertStmt->execute([
                    $product['name'],
                    $product['description'],
                    $product['price'],
                    $product['category_id'],
                    $product['user_id'],
                    $product['stock_quantity'],
                    $product['image_url']
                ]);
            }
        }

        // Build query based on user role
        $whereClause = '';
        $params = [];
        
        if ($user_role !== 'admin') {
            $whereClause = 'WHERE user_id = ?';
            $params[] = $user_id;
        }

        // Fetch products
        $sql = "SELECT * FROM products $whereClause ORDER BY created_at DESC LIMIT 50";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $products = $stmt->fetchAll();

        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM products $whereClause";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];

        // Calculate analytics
        $analytics = [
            'total_products' => $total,
            'active_products' => 0,
            'draft_products' => 0,
            'total_value' => 0,
            'low_stock_count' => 0
        ];

        foreach ($products as $product) {
            if ($product['status'] === 'active') {
                $analytics['active_products']++;
            } elseif ($product['status'] === 'draft') {
                $analytics['draft_products']++;
            }
            
            $analytics['total_value'] += $product['price'] * $product['stock_quantity'];
            
            if ($product['stock_quantity'] < 10) {
                $analytics['low_stock_count']++;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'products' => $products,
                'total' => $total,
                'analytics' => $analytics,
                'user_info' => [
                    'user_id' => $user_id,
                    'user_role' => $user_role
                ]
            ],
            'message' => 'Products retrieved successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to retrieve products: ' . $e->getMessage());
    }
}
?>
