/**
 * Firebase Enhanced Fix - Correction avancée Firebase
 * Résout les erreurs Firestore 400, problèmes hors ligne et optimise les performances
 */

// Configuration avancée Firebase
class FirebaseEnhancedManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.retryAttempts = 0;
        this.maxRetries = 3;
        this.offlineQueue = [];
        this.init();
    }

    init() {
        this.setupNetworkMonitoring();
        this.setupFirebaseErrorHandling();
        this.setupOfflineSupport();
        this.setupPerformanceOptimization();
        console.log('🚀 Firebase Enhanced Manager initialized');
    }

    // 1. Surveillance réseau avancée
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('🌐 Network: Online - Processing offline queue');
            this.processOfflineQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('📱 Network: Offline - Enabling offline mode');
        });

        // Vérification périodique de la connectivité
        setInterval(() => {
            this.checkConnectivity();
        }, 30000); // Toutes les 30 secondes
    }

    async checkConnectivity() {
        try {
            const response = await fetch('/ping', { 
                method: 'HEAD',
                cache: 'no-cache',
                timeout: 5000
            });
            this.isOnline = response.ok;
        } catch (error) {
            this.isOnline = false;
        }
    }

    // 2. Gestion avancée des erreurs Firebase
    setupFirebaseErrorHandling() {
        // Intercepter les erreurs Firestore
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch.apply(this, args);
                
                // Réinitialiser le compteur de tentatives en cas de succès
                if (response.ok) {
                    this.retryAttempts = 0;
                }
                
                return response;
            } catch (error) {
                return this.handleFetchError(error, args);
            }
        };

        // Gestion des erreurs non capturées
        window.addEventListener('unhandledrejection', (event) => {
            this.handleUnhandledRejection(event);
        });
    }

    async handleFetchError(error, args) {
        const url = args[0];
        
        // Erreurs Firebase spécifiques
        if (typeof url === 'string') {
            if (url.includes('firestore.googleapis.com') || 
                url.includes('firebase.googleapis.com')) {
                
                console.debug('🔥 Firebase request failed:', error.message);
                
                // Tentative de retry avec backoff exponentiel
                if (this.retryAttempts < this.maxRetries) {
                    this.retryAttempts++;
                    const delay = Math.pow(2, this.retryAttempts) * 1000;
                    
                    console.log(`🔄 Retrying Firebase request in ${delay}ms (attempt ${this.retryAttempts}/${this.maxRetries})`);
                    
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return window.fetch.apply(this, args);
                } else {
                    // Ajouter à la queue hors ligne
                    this.addToOfflineQueue(args);
                    throw new Error('Firebase temporarily unavailable');
                }
            }
        }
        
        throw error;
    }

    handleUnhandledRejection(event) {
        const error = event.reason;
        
        if (error && error.message) {
            // Erreurs Firebase connues à ignorer
            const ignoredErrors = [
                'Failed to fetch',
                'NetworkError',
                'ERR_ABORTED',
                'Could not reach Cloud Firestore backend',
                'Failed to get document from cache'
            ];
            
            if (ignoredErrors.some(msg => error.message.includes(msg))) {
                console.debug('🔇 Ignored Firebase error:', error.message);
                event.preventDefault();
                return;
            }
        }
    }

    // 3. Support hors ligne avancé
    setupOfflineSupport() {
        // Cache des données critiques
        this.cache = new Map();
        
        // Intercepter les opérations Firestore pour le cache
        this.setupFirestoreCache();
    }

    setupFirestoreCache() {
        // Simuler un cache Firestore simple
        window.firestoreCache = {
            get: (path) => {
                return this.cache.get(path);
            },
            set: (path, data) => {
                this.cache.set(path, {
                    data: data,
                    timestamp: Date.now()
                });
            },
            isExpired: (path, maxAge = 300000) => { // 5 minutes par défaut
                const cached = this.cache.get(path);
                if (!cached) return true;
                return (Date.now() - cached.timestamp) > maxAge;
            }
        };
    }

    addToOfflineQueue(requestArgs) {
        this.offlineQueue.push({
            args: requestArgs,
            timestamp: Date.now()
        });
        
        console.log(`📦 Added request to offline queue (${this.offlineQueue.length} pending)`);
    }

    async processOfflineQueue() {
        if (this.offlineQueue.length === 0) return;
        
        console.log(`🔄 Processing ${this.offlineQueue.length} offline requests`);
        
        const queue = [...this.offlineQueue];
        this.offlineQueue = [];
        
        for (const request of queue) {
            try {
                await fetch.apply(this, request.args);
                console.log('✅ Offline request processed successfully');
            } catch (error) {
                console.warn('⚠️ Failed to process offline request:', error.message);
                // Re-ajouter à la queue si l'erreur persiste
                this.offlineQueue.push(request);
            }
        }
    }

    // 4. Optimisation des performances
    setupPerformanceOptimization() {
        // Debounce pour les requêtes fréquentes
        this.debounceMap = new Map();
        
        // Compression des données
        this.setupDataCompression();
        
        // Lazy loading des modules Firebase
        this.setupLazyLoading();
    }

    debounce(key, func, delay = 300) {
        if (this.debounceMap.has(key)) {
            clearTimeout(this.debounceMap.get(key));
        }
        
        const timeoutId = setTimeout(func, delay);
        this.debounceMap.set(key, timeoutId);
    }

    setupDataCompression() {
        // Compression simple des données JSON
        window.compressData = (data) => {
            try {
                return JSON.stringify(data);
            } catch (error) {
                console.warn('Data compression failed:', error);
                return data;
            }
        };
        
        window.decompressData = (compressedData) => {
            try {
                return JSON.parse(compressedData);
            } catch (error) {
                console.warn('Data decompression failed:', error);
                return compressedData;
            }
        };
    }

    setupLazyLoading() {
        // Chargement différé des modules Firebase non critiques
        window.loadFirebaseModule = async (moduleName) => {
            try {
                switch (moduleName) {
                    case 'analytics':
                        if (!window.firebaseAnalytics) {
                            const { getAnalytics } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js');
                            window.firebaseAnalytics = getAnalytics;
                        }
                        break;
                    case 'storage':
                        if (!window.firebaseStorage) {
                            const { getStorage } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js');
                            window.firebaseStorage = getStorage;
                        }
                        break;
                }
                console.log(`📦 Firebase module '${moduleName}' loaded`);
            } catch (error) {
                console.warn(`Failed to load Firebase module '${moduleName}':`, error);
            }
        };
    }

    // 5. Méthodes utilitaires publiques
    getStatus() {
        return {
            isOnline: this.isOnline,
            retryAttempts: this.retryAttempts,
            offlineQueueSize: this.offlineQueue.length,
            cacheSize: this.cache.size
        };
    }

    clearCache() {
        this.cache.clear();
        console.log('🗑️ Firebase cache cleared');
    }

    clearOfflineQueue() {
        this.offlineQueue = [];
        console.log('🗑️ Offline queue cleared');
    }
}

// Initialisation automatique
if (typeof window.firebaseEnhancedManager === 'undefined') {
    window.firebaseEnhancedManager = new FirebaseEnhancedManager();
}

// Fonctions utilitaires globales
window.getFirebaseStatus = () => {
    return window.firebaseEnhancedManager.getStatus();
};

window.clearFirebaseCache = () => {
    window.firebaseEnhancedManager.clearCache();
};

window.clearFirebaseOfflineQueue = () => {
    window.firebaseEnhancedManager.clearOfflineQueue();
};

// Export pour les modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FirebaseEnhancedManager;
}

console.log('🔥 Firebase Enhanced Fix loaded successfully');