/**
 * Subscription Management System
 * نظام إدارة الاشتراكات
 */

// Global variables
let subscriptionPlans = [];
let subscriptionStats = {};
let currentEditingPlan = null;

/**
 * Load subscription management content (called from admin panel)
 */
function loadSubscriptionsManagementContent() {
    console.log('🔔 تحميل محتوى إدارة الاشتراكات من لوحة التحكم...');
    showSubscriptionsManagement();
}

/**
 * Show subscription management interface
 */
function showSubscriptionsManagement() {
    console.log('🔔 بدء تحميل إدارة الاشتراكات...');

    // Try multiple possible container IDs
    let container = document.getElementById('subscriptionsManagementContent') ||
                   document.getElementById('mainContent');

    if (!container) {
        console.error('❌ لم يتم العثور على حاوي المحتوى');
        console.log('Available containers:',
            Array.from(document.querySelectorAll('[id*="Content"]')).map(el => el.id));
        return;
    }

    console.log('✅ تم العثور على الحاوي:', container.id);

    // Show loading state
    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div style="width: 50px; height: 50px; border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <p style="color: #666; font-size: 1.1rem;">جاري تحميل إدارة الاشتراكات...</p>
        </div>
    `;

    // Load subscription management interface
    setTimeout(() => {
        loadSubscriptionsManagementInterface();
    }, 500);
}

/**
 * Load subscription management interface
 */
function loadSubscriptionsManagementInterface() {
    const container = document.getElementById('subscriptionsManagementContent') ||
                     document.getElementById('mainContent');

    if (!container) {
        console.error('❌ لم يتم العثور على حاوي واجهة إدارة الاشتراكات');
        return;
    }

    console.log('✅ تحميل واجهة إدارة الاشتراكات في:', container.id);

    container.innerHTML = `
        <div class="subscriptions-management-container">
            <!-- Header -->
            <div class="subscriptions-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
                <h1 style="margin: 0 0 10px 0; font-size: 2rem;">
                    <i class="fas fa-crown"></i> إدارة الاشتراكات
                </h1>
                <p style="margin: 0; opacity: 0.9; font-size: 1.1rem;">إدارة خطط الاشتراك والمستخدمين المشتركين</p>
                <div style="margin-top: 20px;">
                    <button onclick="showAddSubscriptionModal()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-plus"></i> إضافة خطة جديدة
                    </button>
                    <button onclick="exportSubscriptionData()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                    <button onclick="window.location.href='users-management.html'" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-users"></i> إدارة المستخدمين
                    </button>
                </div>
            </div>

            <!-- Statistics Dashboard -->
            <div class="subscription-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="stat-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #28a745, #20c997); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                            <i class="fas fa-chart-line" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333; font-size: 1.2rem;">إجمالي الاشتراكات</h3>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">جميع الخطط النشطة</p>
                        </div>
                    </div>
                    <div id="totalSubscriptions" style="font-size: 1.5rem; font-weight: bold; color: #28a745;">-</div>
                </div>

                <div class="stat-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                            <i class="fas fa-users" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333; font-size: 1.2rem;">المستخدمون المشتركون</h3>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">المستخدمون النشطون</p>
                        </div>
                    </div>
                    <div id="activeSubscribers" style="font-size: 1.5rem; font-weight: bold; color: #007bff;">-</div>
                </div>

                <div class="stat-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ffc107, #e0a800); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                            <i class="fas fa-money-bill-wave" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333; font-size: 1.2rem;">الإيرادات الشهرية</h3>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">الشهر الحالي</p>
                        </div>
                    </div>
                    <div id="monthlyRevenue" style="font-size: 1.5rem; font-weight: bold; color: #ffc107;">-</div>
                </div>

                <div class="stat-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #dc3545, #c82333); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                            <i class="fas fa-star" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333; font-size: 1.2rem;">الخطة الأكثر شعبية</h3>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">الأكثر استخداماً</p>
                        </div>
                    </div>
                    <div id="popularPlan" style="font-size: 1.5rem; font-weight: bold; color: #dc3545;">-</div>
                </div>
            </div>

            <!-- Subscription Plans List -->
            <div class="subscription-plans-section" style="background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden;">
                <div class="section-header" style="background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6;">
                    <h3 style="margin: 0; color: #333; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-list" style="color: #667eea;"></i>
                        خطط الاشتراك
                    </h3>
                </div>
                <div class="plans-container" id="subscriptionPlansList" style="padding: 20px;">
                    <!-- Plans will be loaded here -->
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 20px;"></i>
                        <p>جاري تحميل خطط الاشتراك...</p>
                    </div>
                </div>
            </div>
        </div>

        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .stat-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            }

            .plan-card {
                border: 1px solid #dee2e6;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
                transition: all 0.3s ease;
            }

            .plan-card:hover {
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                transform: translateY(-2px);
            }

            .plan-card.popular {
                border-color: #ffc107;
                background: linear-gradient(135deg, #fff9e6, #ffffff);
            }
        </style>
    `;

    // Load subscription data
    loadSubscriptionData();
}

/**
 * Load subscription data from API
 */
async function loadSubscriptionData() {
    try {
        // Load subscription plans
        const plansResponse = await fetch('../php/api/subscriptions.php?action=plans');
        const plansData = await plansResponse.json();

        if (plansData.success) {
            subscriptionPlans = plansData.data;
            displaySubscriptionPlans();
        }

        // Load subscription statistics
        const statsResponse = await fetch('../php/api/subscriptions.php?action=stats');
        const statsData = await statsResponse.json();

        if (statsData.success) {
            subscriptionStats = statsData.data;
            updateSubscriptionStats();
        }

    } catch (error) {
        console.error('Error loading subscription data:', error);
        showErrorMessage('خطأ في تحميل بيانات الاشتراكات: ' + error.message);
    }
}

/**
 * Display subscription plans
 */
function displaySubscriptionPlans() {
    const container = document.getElementById('subscriptionPlansList');
    if (!container) return;

    if (subscriptionPlans.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>لا توجد خطط اشتراك</h3>
                <p>ابدأ بإضافة خطة اشتراك جديدة</p>
                <button onclick="showAddSubscriptionModal()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin-top: 15px;">
                    <i class="fas fa-plus"></i> إضافة خطة جديدة
                </button>
            </div>
        `;
        return;
    }

    let plansHTML = '';

    subscriptionPlans.forEach(plan => {
        const features = JSON.parse(plan.features || '[]');
        const isPopular = plan.user_count > 0; // Simple popularity check

        plansHTML += `
            <div class="plan-card ${isPopular ? 'popular' : ''}" data-plan-id="${plan.id}">
                <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 15px;">
                    <div style="flex: 1;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <h4 style="margin: 0; color: #333; font-size: 1.3rem;">${plan.display_name_ar}</h4>
                            ${isPopular ? '<span style="background: #ffc107; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem; font-weight: bold;">شائع</span>' : ''}
                            ${plan.is_active ? '<span style="background: #28a745; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem;">نشط</span>' : '<span style="background: #dc3545; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8rem;">غير نشط</span>'}
                        </div>
                        <p style="margin: 0 0 10px 0; color: #666;">${plan.description_ar || 'لا يوجد وصف'}</p>
                        <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 10px;">
                            <span style="font-size: 1.5rem; font-weight: bold; color: #667eea;">${plan.price} ${plan.currency}</span>
                            <span style="color: #666;">/ ${plan.duration_days} يوم</span>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="editSubscriptionPlan(${plan.id})" style="padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteSubscriptionPlan(${plan.id})" style="padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <h5 style="margin: 0 0 10px 0; color: #495057;">الحدود والقيود</h5>
                        <ul style="margin: 0; padding: 0; list-style: none; color: #666;">
                            <li style="margin-bottom: 5px;"><i class="fas fa-box" style="width: 20px; color: #667eea;"></i> ${plan.max_products} منتج</li>
                            <li style="margin-bottom: 5px;"><i class="fas fa-file" style="width: 20px; color: #667eea;"></i> ${plan.max_landing_pages} صفحة هبوط</li>
                            <li style="margin-bottom: 5px;"><i class="fas fa-hdd" style="width: 20px; color: #667eea;"></i> ${plan.max_storage_mb} ميجابايت</li>
                            <li><i class="fas fa-palette" style="width: 20px; color: #667eea;"></i> ${plan.max_templates} قالب</li>
                        </ul>
                    </div>

                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <h5 style="margin: 0 0 10px 0; color: #495057;">الإحصائيات</h5>
                        <ul style="margin: 0; padding: 0; list-style: none; color: #666;">
                            <li style="margin-bottom: 5px;"><i class="fas fa-users" style="width: 20px; color: #28a745;"></i> ${plan.user_count} مستخدم</li>
                            <li style="margin-bottom: 5px;"><i class="fas fa-calendar" style="width: 20px; color: #28a745;"></i> منذ ${formatDate(plan.created_at)}</li>
                        </ul>
                    </div>
                </div>

                ${features.length > 0 ? `
                <div style="margin-top: 15px;">
                    <h5 style="margin: 0 0 10px 0; color: #495057;">الميزات المتاحة:</h5>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                        ${features.map(feature => `<span style="background: #e9ecef; padding: 4px 8px; border-radius: 12px; font-size: 0.9rem; color: #495057;">${feature}</span>`).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    });

    container.innerHTML = plansHTML;
}

// Helper function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'أمس';
    if (diffDays < 7) return `${diffDays} أيام`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} أسابيع`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} أشهر`;
    return `${Math.ceil(diffDays / 365)} سنوات`;
}

/**
 * Update subscription statistics
 */
function updateSubscriptionStats() {
    if (!subscriptionStats) return;

    // Update total subscriptions
    const totalEl = document.getElementById('totalSubscriptions');
    if (totalEl) totalEl.textContent = subscriptionStats.total_plans || 0;

    // Update active subscribers
    const subscribersEl = document.getElementById('activeSubscribers');
    if (subscribersEl) subscribersEl.textContent = subscriptionStats.active_subscribers || 0;

    // Update monthly revenue
    const revenueEl = document.getElementById('monthlyRevenue');
    if (revenueEl) revenueEl.textContent = (subscriptionStats.monthly_revenue || 0) + ' دج';

    // Update popular plan
    const popularEl = document.getElementById('popularPlan');
    if (popularEl) popularEl.textContent = subscriptionStats.popular_plan || 'غير محدد';
}

/**
 * Show add subscription modal
 */
function showAddSubscriptionModal() {
    currentEditingPlan = null;
    showSubscriptionModal();
}

/**
 * Edit subscription plan
 */
function editSubscriptionPlan(planId) {
    currentEditingPlan = subscriptionPlans.find(plan => plan.id === planId);
    if (currentEditingPlan) {
        showSubscriptionModal(currentEditingPlan);
    }
}

/**
 * Delete subscription plan
 */
async function deleteSubscriptionPlan(planId) {
    const plan = subscriptionPlans.find(p => p.id === planId);
    if (!plan) return;

    if (!confirm(`هل أنت متأكد من حذف خطة "${plan.display_name_ar}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    try {
        const response = await fetch('../php/api/subscriptions.php?action=delete_plan', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: planId })
        });

        const result = await response.json();

        if (result.success) {
            showSuccessMessage('تم حذف خطة الاشتراك بنجاح');
            loadSubscriptionData(); // Refresh data
        } else {
            showErrorMessage('خطأ في حذف الخطة: ' + (result.message || 'خطأ غير معروف'));
        }

    } catch (error) {
        console.error('Error deleting subscription plan:', error);
        showErrorMessage('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * Show subscription modal for add/edit
 */
function showSubscriptionModal(plan = null) {
    const isEdit = plan !== null;
    const modalTitle = isEdit ? 'تعديل خطة الاشتراك' : 'إضافة خطة اشتراك جديدة';

    // Create modal if it doesn't exist
    let modal = document.getElementById('subscriptionModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'subscriptionModal';
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); display: flex; align-items: center;
            justify-content: center; z-index: 1000;
        `;
        document.body.appendChild(modal);
    }

    modal.innerHTML = `
        <div style="background: white; border-radius: 15px; padding: 30px; max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 25px;">
                <h2 style="margin: 0; color: #333; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-crown" style="color: #667eea;"></i>
                    ${modalTitle}
                </h2>
                <button onclick="closeSubscriptionModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #999;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="subscriptionForm" style="display: grid; gap: 20px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">اسم الخطة (إنجليزي):</label>
                        <input type="text" id="planName" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.name || ''}">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">اسم الخطة (عربي):</label>
                        <input type="text" id="planDisplayNameAr" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.display_name_ar || ''}">
                    </div>
                </div>

                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الوصف (عربي):</label>
                    <textarea id="planDescriptionAr" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;">${plan?.description_ar || ''}</textarea>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">السعر:</label>
                        <input type="number" id="planPrice" min="0" step="0.01" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.price || '0'}">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">العملة:</label>
                        <select id="planCurrency" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <option value="DZD" ${plan?.currency === 'DZD' ? 'selected' : ''}>دينار جزائري (DZD)</option>
                            <option value="USD" ${plan?.currency === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                            <option value="EUR" ${plan?.currency === 'EUR' ? 'selected' : ''}>يورو (EUR)</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">المدة (أيام):</label>
                        <input type="number" id="planDuration" min="1" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.duration_days || '30'}">
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                    <h4 style="margin: 0 0 15px 0; color: #495057;">حدود الخطة:</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">عدد المنتجات:</label>
                            <input type="number" id="maxProducts" min="0" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.max_products || '5'}">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">صفحات الهبوط:</label>
                            <input type="number" id="maxLandingPages" min="0" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.max_landing_pages || '2'}">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">التخزين (ميجابايت):</label>
                            <input type="number" id="maxStorage" min="0" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.max_storage_mb || '100'}">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">عدد القوالب:</label>
                            <input type="number" id="maxTemplates" min="0" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.max_templates || '5'}">
                        </div>
                    </div>
                </div>

                <div style="display: flex; align-items: center; gap: 15px;">
                    <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                        <input type="checkbox" id="planIsActive" ${plan?.is_active ? 'checked' : ''} style="transform: scale(1.2);">
                        <span>خطة نشطة</span>
                    </label>
                    <div style="flex: 1;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">ترتيب العرض:</label>
                        <input type="number" id="planSortOrder" min="0" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;" value="${plan?.sort_order || '0'}">
                    </div>
                </div>

                <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" onclick="closeSubscriptionModal()" style="padding: 12px 24px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-save"></i> ${isEdit ? 'تحديث الخطة' : 'إضافة الخطة'}
                    </button>
                </div>
            </form>
        </div>
    `;

    modal.style.display = 'flex';

    // Add form submit handler
    document.getElementById('subscriptionForm').addEventListener('submit', handleSubscriptionFormSubmit);
}

/**
 * Close subscription modal
 */
function closeSubscriptionModal() {
    const modal = document.getElementById('subscriptionModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Handle subscription form submit
 */
async function handleSubscriptionFormSubmit(event) {
    event.preventDefault();

    const formData = {
        name: document.getElementById('planName').value,
        display_name_ar: document.getElementById('planDisplayNameAr').value,
        description_ar: document.getElementById('planDescriptionAr').value,
        price: parseFloat(document.getElementById('planPrice').value),
        currency: document.getElementById('planCurrency').value,
        duration_days: parseInt(document.getElementById('planDuration').value),
        max_products: parseInt(document.getElementById('maxProducts').value),
        max_landing_pages: parseInt(document.getElementById('maxLandingPages').value),
        max_storage_mb: parseInt(document.getElementById('maxStorage').value),
        max_templates: parseInt(document.getElementById('maxTemplates').value),
        is_active: document.getElementById('planIsActive').checked ? 1 : 0,
        sort_order: parseInt(document.getElementById('planSortOrder').value)
    };

    try {
        const isEdit = currentEditingPlan !== null;
        const url = isEdit ? '../php/api/subscriptions.php?action=update_plan' : '../php/api/subscriptions.php?action=create_plan';
        const method = isEdit ? 'PUT' : 'POST';

        if (isEdit) {
            formData.id = currentEditingPlan.id;
        }

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showSuccessMessage(isEdit ? 'تم تحديث خطة الاشتراك بنجاح' : 'تم إضافة خطة الاشتراك بنجاح');
            closeSubscriptionModal();
            loadSubscriptionData(); // Refresh data
        } else {
            showErrorMessage('خطأ في حفظ الخطة: ' + (result.message || 'خطأ غير معروف'));
        }

    } catch (error) {
        console.error('Error saving subscription plan:', error);
        showErrorMessage('خطأ في الاتصال: ' + error.message);
    }
}

/**
 * Export subscription data
 */
function exportSubscriptionData() {
    alert('تصدير بيانات الاشتراكات - قيد التطوير\nسيتم إضافة هذه الميزة قريباً');
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    // Simple alert for now - can be enhanced with toast notifications
    alert('✅ ' + message);
}

/**
 * Show error message
 */
function showErrorMessage(message) {
    // Simple alert for now - can be enhanced with toast notifications
    alert('❌ ' + message);
}

// Make functions globally available
window.loadSubscriptionsManagementContent = loadSubscriptionsManagementContent;
window.showSubscriptionsManagement = showSubscriptionsManagement;
window.loadSubscriptionsManagementInterface = loadSubscriptionsManagementInterface;
window.loadSubscriptionData = loadSubscriptionData;
window.displaySubscriptionPlans = displaySubscriptionPlans;
window.updateSubscriptionStats = updateSubscriptionStats;
window.showAddSubscriptionModal = showAddSubscriptionModal;
window.editSubscriptionPlan = editSubscriptionPlan;
window.deleteSubscriptionPlan = deleteSubscriptionPlan;
window.showSubscriptionModal = showSubscriptionModal;
window.closeSubscriptionModal = closeSubscriptionModal;
window.handleSubscriptionFormSubmit = handleSubscriptionFormSubmit;
window.exportSubscriptionData = exportSubscriptionData;

console.log('✅ Subscription management module loaded successfully');
