/**
 * Categories Management Debug Version
 * نسخة تشخيص إدارة الفئات
 */

console.log('🔧 تحميل ملف categories-debug.js...');

// Global variables for categories
window.categoriesData = window.categoriesData || [];
window.currentCategory = window.currentCategory || null;
window.categoriesIsLoading = window.categoriesIsLoading || false;

/**
 * Load categories management content - Debug version
 */
function loadCategoriesManagementContent() {
    console.log('🗂️ بدء تحميل إدارة الفئات...');

    const container = document.getElementById('categoriesManagementContent');
    if (!container) {
        console.error('❌ لم يتم العثور على حاوي إدارة الفئات');
        return;
    }

    console.log('✅ تم العثور على الحاوي');

    // Show loading state
    showLoadingState(container);

    // Load categories from server
    console.log('📡 جاري جلب الفئات من الخادم...');

    fetchCategories()
        .then(data => {
            console.log('📦 تم استلام البيانات:', data);

            if (data.success) {
                window.categoriesData = data.data.categories;
                console.log('✅ تم حفظ بيانات الفئات:', window.categoriesData.length, 'فئة');
                renderCategoriesManagement(data.data);
            } else {
                throw new Error(data.message || 'فشل في تحميل الفئات');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل الفئات:', error);
            showErrorState(container, error.message);
        });
}

/**
 * Fetch categories from server
 */
async function fetchCategories(includeInactive = false) {
    try {
        const url = `php/categories.php?action=get_all${includeInactive ? '&include_inactive=true' : ''}`;
        console.log('🌐 طلب البيانات من:', url);

        const response = await fetch(url);
        console.log('📡 استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📋 البيانات المستلمة:', data);

        return data;
    } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        throw new Error('خطأ في الاتصال بالخادم: ' + error.message);
    }
}

/**
 * Show loading state
 */
function showLoadingState(container) {
    console.log('⏳ عرض حالة التحميل...');

    container.innerHTML = `
        <div class="loading-state" style="text-align: center; padding: 40px;">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
            </div>
            <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
        </div>
    `;
}

/**
 * Show error state
 */
function showErrorState(container, message) {
    console.log('❌ عرض حالة الخطأ:', message);

    container.innerHTML = `
        <div class="error-state" style="text-align: center; padding: 40px; color: #dc3545;">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
            </div>
            <h4>خطأ في تحميل إدارة الفئات</h4>
            <p>${message}</p>
            <button class="btn btn-primary" onclick="loadCategoriesManagementContent()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                <i class="fas fa-redo"></i> إعادة المحاولة
            </button>
        </div>
    `;
}

/**
 * Render categories management interface
 */
function renderCategoriesManagement(data) {
    console.log('🎨 بدء رسم واجهة إدارة الفئات...');
    console.log('📊 بيانات الرسم:', data);

    const container = document.getElementById('categoriesManagementContent');

    if (!data || !data.categories) {
        console.error('❌ بيانات غير صحيحة للرسم');
        showErrorState(container, 'بيانات الفئات غير صحيحة');
        return;
    }

    const html = `
        <div class="categories-management-container" style="max-width: 1200px; margin: 0 auto; padding: 20px;">
            <!-- Header -->
            <div class="categories-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px;">
                <div class="header-content">
                    <h2 style="margin: 0; font-size: 1.8rem;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة وتنظيم فئات المنتجات والمحتوى</p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-light" onclick="showAddCategoryModal()" style="margin-left: 10px; padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer;">
                        <i class="fas fa-plus"></i> إضافة فئة جديدة
                    </button>
                    <button class="btn btn-outline-light" onclick="refreshCategories()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer;">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="categories-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div class="stat-icon" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.total}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">إجمالي الفئات</p>
                    </div>
                </div>
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div class="stat-icon" style="font-size: 2rem; color: #28a745; margin-bottom: 10px;">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.categories.filter(c => c.parent_id === null).length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات الرئيسية</p>
                    </div>
                </div>
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div class="stat-icon" style="font-size: 2rem; color: #17a2b8; margin-bottom: 10px;">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.categories.filter(c => c.parent_id !== null).length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات الفرعية</p>
                    </div>
                </div>
                <div class="stat-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div class="stat-icon" style="font-size: 2rem; color: #ffc107; margin-bottom: 10px;">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="margin: 0; font-size: 2rem; color: #333;">${data.categories.filter(c => c.is_featured == 1).length}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">الفئات المميزة</p>
                    </div>
                </div>
            </div>

            <!-- Categories List -->
            <div class="categories-content" style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                <div class="categories-view-toggle" style="padding: 15px 20px; border-bottom: 1px solid #e0e0e0; display: flex; gap: 10px;">
                    <button class="view-btn active" style="padding: 8px 16px; border: 2px solid #667eea; background: #667eea; color: white; border-radius: 6px; cursor: pointer;">
                        <i class="fas fa-sitemap"></i> عرض هرمي
                    </button>
                </div>

                <div id="categoriesListContainer" style="padding: 20px;">
                    ${renderCategoriesHierarchy(data.hierarchy || [])}
                </div>
            </div>
        </div>
    `;

    console.log('✅ تم إنشاء HTML للواجهة');
    container.innerHTML = html;
    console.log('✅ تم تحديث محتوى الحاوي');
}

/**
 * Render categories hierarchy
 */
function renderCategoriesHierarchy(hierarchy, level = 0) {
    console.log('🌳 رسم الهيكل الهرمي، المستوى:', level, 'عدد العناصر:', hierarchy.length);

    if (!hierarchy || hierarchy.length === 0) {
        return '<p style="text-align: center; color: #666; padding: 40px;">لا توجد فئات للعرض</p>';
    }

    let html = '<div class="categories-hierarchy">';

    hierarchy.forEach((category, index) => {
        console.log(`📁 رسم الفئة ${index + 1}:`, category.name_ar);

        const indent = level * 20;
        const statusClass = category.is_active == 1 ? 'active' : 'inactive';
        const featuredClass = category.is_featured == 1 ? 'featured' : '';

        html += `
            <div class="category-item ${statusClass} ${featuredClass}" style="margin-right: ${indent}px; margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; ${category.is_active == 1 ? 'background: #f8f9fa;' : 'background: #f5f5f5; opacity: 0.7;'}">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="category-info" style="display: flex; align-items: center; gap: 15px;">
                        <div class="category-icon" style="color: ${category.color}; font-size: 1.5rem;">
                            <i class="${category.icon || 'fas fa-folder'}"></i>
                        </div>
                        <div class="category-details">
                            <h4 style="margin: 0 0 5px 0; color: #333;">${category.name_ar}</h4>
                            <p style="margin: 0 0 10px 0; color: #666; font-size: 0.9em;">${category.description_ar || 'لا يوجد وصف'}</p>
                            <div class="category-meta" style="display: flex; gap: 15px; font-size: 0.8em; color: #999;">
                                <span class="meta-item">
                                    <i class="fas fa-layer-group"></i>
                                    ${category.subcategories_count || 0} فئة فرعية
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-box"></i>
                                    ${category.products_count || 0} منتج
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-eye"></i>
                                    ${category.views_count || 0} مشاهدة
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="category-actions" style="display: flex; gap: 5px;">
                        <button class="action-btn edit" onclick="editCategory(${category.id})" title="تعديل" style="padding: 8px 12px; border: none; background: #17a2b8; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle ${category.is_active == 1 ? 'active' : 'inactive'}"
                                onclick="toggleCategoryStatus(${category.id})"
                                title="${category.is_active == 1 ? 'إلغاء التفعيل' : 'تفعيل'}"
                                style="padding: 8px 12px; border: none; background: ${category.is_active == 1 ? '#28a745' : '#6c757d'}; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-${category.is_active == 1 ? 'eye-slash' : 'eye'}"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteCategory(${category.id})" title="حذف" style="padding: 8px 12px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Render children
        if (category.children && category.children.length > 0) {
            console.log(`📂 رسم ${category.children.length} فئة فرعية للفئة:`, category.name_ar);
            html += renderCategoriesHierarchy(category.children, level + 1);
        }
    });

    html += '</div>';
    console.log('✅ تم إنهاء رسم الهيكل الهرمي');
    return html;
}

// Placeholder functions
function showAddCategoryModal() {
    console.log('📝 فتح نموذج إضافة فئة جديدة');
    alert('نموذج إضافة فئة جديدة - قيد التطوير');
}

function refreshCategories() {
    console.log('🔄 تحديث الفئات');
    loadCategoriesManagementContent();
}

function editCategory(id) {
    console.log('✏️ تعديل الفئة:', id);
    alert('تعديل الفئة ' + id + ' - قيد التطوير');
}

function toggleCategoryStatus(id) {
    console.log('🔄 تغيير حالة الفئة:', id);
    alert('تغيير حالة الفئة ' + id + ' - قيد التطوير');
}

function deleteCategory(id) {
    console.log('🗑️ حذف الفئة:', id);
    if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
        alert('حذف الفئة ' + id + ' - قيد التطوير');
    }
}

// Make functions globally available
window.loadCategoriesManagementContent = loadCategoriesManagementContent;
window.fetchCategories = fetchCategories;
window.renderCategoriesManagement = renderCategoriesManagement;
window.showAddCategoryModal = showAddCategoryModal;
window.refreshCategories = refreshCategories;
window.editCategory = editCategory;
window.toggleCategoryStatus = toggleCategoryStatus;
window.deleteCategory = deleteCategory;

console.log('✅ تم تحميل ملف categories-debug.js بنجاح');
console.log('🔧 الدوال المتاحة:', Object.keys(window).filter(key => key.includes('Categories') || key.includes('Category')));
