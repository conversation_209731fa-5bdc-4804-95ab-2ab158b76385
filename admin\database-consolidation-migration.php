<?php

/**
 * Database Consolidation Migration Script
 * نص ترحيل توحيد قاعدة البيانات
 *
 * This script consolidates the users and utilisateurs tables into a single users table
 * while preserving all data and maintaining the separate admin table.
 */

session_start();
require_once '../config/config.php';

// Set execution time limit for large migrations
set_time_limit(300);

// HTML header
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ترحيل توحيد قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }

        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }

        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
            color: #856404;
        }

        .info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
            color: #0c5460;
        }

        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }

        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        th,
        td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: right;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            transition: width 0.3s ease;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔄 ترحيل توحيد قاعدة البيانات</h1>
            <p>توحيد جداول المستخدمين وضمان سلامة البيانات</p>
        </div>

        <?php

        class DatabaseConsolidationMigration
        {
            private $pdo;
            private $results = [];

            public function __construct()
            {
                try {
                    $dbConfig = Config::getDbConfig();
                    $dsn = sprintf(
                        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                        $dbConfig['host'],
                        $dbConfig['port'],
                        $dbConfig['database']
                    );

                    $options = [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ];

                    $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
                    $this->log('success', 'تم الاتصال بقاعدة البيانات بنجاح');
                } catch (Exception $e) {
                    $this->log('error', 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage());
                    throw $e;
                }
            }

            private function log($type, $message, $details = null)
            {
                $this->results[] = [
                    'type' => $type,
                    'message' => $message,
                    'details' => $details,
                    'timestamp' => date('Y-m-d H:i:s')
                ];

                // Output immediately for real-time feedback
                $class = $type;
                echo "<div class='step $class'>";
                echo "<strong>" . date('H:i:s') . "</strong> - $message";
                if ($details) {
                    echo "<div class='code'>$details</div>";
                }
                echo "</div>";
                flush();
            }

            public function analyzeCurrentStructure()
            {
                $this->log('info', '🔍 تحليل البنية الحالية لقاعدة البيانات...');

                try {
                    // Check if tables exist
                    $tables = ['users', 'utilisateurs', 'admins'];
                    $tableInfo = [];

                    foreach ($tables as $table) {
                        $stmt = $this->pdo->prepare("SHOW TABLES LIKE ?");
                        $stmt->execute([$table]);
                        $exists = $stmt->fetch() !== false;

                        if ($exists) {
                            // Get table structure
                            $stmt = $this->pdo->prepare("DESCRIBE $table");
                            $stmt->execute();
                            $structure = $stmt->fetchAll();

                            // Get row count
                            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM $table");
                            $stmt->execute();
                            $count = $stmt->fetch()['count'];

                            $tableInfo[$table] = [
                                'exists' => true,
                                'structure' => $structure,
                                'count' => $count
                            ];

                            $this->log('success', "✅ جدول $table موجود - عدد السجلات: $count");
                        } else {
                            $tableInfo[$table] = ['exists' => false];
                            $this->log('warning', "⚠️ جدول $table غير موجود");
                        }
                    }

                    return $tableInfo;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في تحليل البنية: ' . $e->getMessage());
                    throw $e;
                }
            }

            public function backupExistingData()
            {
                $this->log('info', '💾 إنشاء نسخة احتياطية من البيانات الحالية...');

                try {
                    $timestamp = date('Y_m_d_H_i_s');

                    // Backup users table
                    if ($this->tableExists('users')) {
                        $this->pdo->exec("CREATE TABLE users_backup_$timestamp AS SELECT * FROM users");
                        $this->log('success', "✅ تم إنشاء نسخة احتياطية: users_backup_$timestamp");
                    }

                    // Backup utilisateurs table
                    if ($this->tableExists('utilisateurs')) {
                        $this->pdo->exec("CREATE TABLE utilisateurs_backup_$timestamp AS SELECT * FROM utilisateurs");
                        $this->log('success', "✅ تم إنشاء نسخة احتياطية: utilisateurs_backup_$timestamp");
                    }

                    // Backup admins table
                    if ($this->tableExists('admins')) {
                        $this->pdo->exec("CREATE TABLE admins_backup_$timestamp AS SELECT * FROM admins");
                        $this->log('success', "✅ تم إنشاء نسخة احتياطية: admins_backup_$timestamp");
                    }

                    return $timestamp;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في إنشاء النسخة الاحتياطية: ' . $e->getMessage());
                    throw $e;
                }
            }

            private function tableExists($tableName)
            {
                try {
                    $stmt = $this->pdo->prepare("SHOW TABLES LIKE ?");
                    $stmt->execute([$tableName]);
                    return $stmt->fetch() !== false;
                } catch (Exception $e) {
                    return false;
                }
            }

            public function createConsolidatedUsersTable()
            {
                $this->log('info', '🏗️ إنشاء جدول المستخدمين الموحد...');

                try {
                    // Drop existing users table if it exists
                    if ($this->tableExists('users')) {
                        $this->log('warning', '⚠️ جدول users موجود، سيتم إعادة إنشاؤه...');
                    }

                    // Create the consolidated users table with all necessary fields
                    $createUsersSQL = "
            CREATE TABLE IF NOT EXISTS users_new (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                phone VARCHAR(20),
                role_id INT DEFAULT 4,
                subscription_id INT DEFAULT 1,
                store_id INT NULL,
                avatar VARCHAR(500),
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                email_verified TINYINT(1) DEFAULT 0,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                -- Migration tracking fields
                migrated_from ENUM('users', 'utilisateurs') DEFAULT 'users',
                original_id INT,

                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_role_id (role_id),
                INDEX idx_subscription_id (subscription_id),
                INDEX idx_store_id (store_id),
                INDEX idx_status (status),
                INDEX idx_migrated_from (migrated_from)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                    $this->pdo->exec($createUsersSQL);
                    $this->log('success', '✅ تم إنشاء جدول users_new بنجاح');

                    return true;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في إنشاء الجدول الموحد: ' . $e->getMessage());
                    throw $e;
                }
            }

            public function migrateUsersData()
            {
                $this->log('info', '📦 ترحيل بيانات المستخدمين...');

                try {
                    $migratedCount = 0;

                    // Migrate from existing users table
                    if ($this->tableExists('users')) {
                        $stmt = $this->pdo->query("SELECT * FROM users");
                        $users = $stmt->fetchAll();

                        foreach ($users as $user) {
                            $insertSQL = "
                    INSERT INTO users_new (
                        username, email, password, first_name, last_name, phone,
                        role_id, subscription_id, store_id, avatar, status,
                        email_verified, last_login, created_at, updated_at,
                        migrated_from, original_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'users', ?)";

                            $stmt = $this->pdo->prepare($insertSQL);
                            $stmt->execute([
                                $user['username'],
                                $user['email'],
                                $user['password'],
                                $user['first_name'],
                                $user['last_name'],
                                $user['phone'],
                                $user['role_id'],
                                $user['subscription_id'],
                                $user['store_id'],
                                $user['avatar'],
                                $user['status'],
                                $user['email_verified'],
                                $user['last_login'],
                                $user['created_at'],
                                $user['updated_at'],
                                $user['id']
                            ]);
                            $migratedCount++;
                        }

                        $this->log('success', "✅ تم ترحيل $migratedCount مستخدم من جدول users");
                    }

                    // Migrate from utilisateurs table
                    if ($this->tableExists('utilisateurs')) {
                        $stmt = $this->pdo->query("SELECT * FROM utilisateurs");
                        $utilisateurs = $stmt->fetchAll();

                        foreach ($utilisateurs as $utilisateur) {
                            // Generate username from email if not exists
                            $username = explode('@', $utilisateur['email'])[0] . '_' . $utilisateur['id'];

                            // Check if email already exists in users_new
                            $checkStmt = $this->pdo->prepare("SELECT id FROM users_new WHERE email = ?");
                            $checkStmt->execute([$utilisateur['email']]);

                            if (!$checkStmt->fetch()) {
                                $insertSQL = "
                        INSERT INTO users_new (
                            username, email, password, first_name, last_name,
                            role_id, subscription_id, status, email_verified,
                            created_at, updated_at, migrated_from, original_id
                        ) VALUES (?, ?, ?, ?, ?, 4, 1, 'active', 0, ?, ?, 'utilisateurs', ?)";

                                $stmt = $this->pdo->prepare($insertSQL);
                                $stmt->execute([
                                    $username,
                                    $utilisateur['email'],
                                    $utilisateur['mot_de_passe'],
                                    $utilisateur['nom'],
                                    '', // last_name empty
                                    $utilisateur['date_creation'],
                                    $utilisateur['date_creation'],
                                    $utilisateur['id']
                                ]);
                                $migratedCount++;
                            } else {
                                $this->log('warning', "⚠️ البريد الإلكتروني {$utilisateur['email']} موجود مسبقاً، تم تخطيه");
                            }
                        }

                        $utilisateursCount = count($utilisateurs);
                        $this->log('success', "✅ تم معالجة $utilisateursCount مستخدم من جدول utilisateurs");
                    }

                    return $migratedCount;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في ترحيل البيانات: ' . $e->getMessage());
                    throw $e;
                }
            }

            public function replaceOriginalUsersTable()
            {
                $this->log('info', '🔄 استبدال جدول المستخدمين الأصلي...');

                try {
                    // Start transaction
                    $this->pdo->beginTransaction();

                    // Drop old users table
                    if ($this->tableExists('users')) {
                        $this->pdo->exec("DROP TABLE users");
                        $this->log('success', '✅ تم حذف جدول users القديم');
                    }

                    // Rename users_new to users
                    $this->pdo->exec("RENAME TABLE users_new TO users");
                    $this->log('success', '✅ تم إعادة تسمية users_new إلى users');

                    // Drop utilisateurs table as it's no longer needed
                    if ($this->tableExists('utilisateurs')) {
                        $this->pdo->exec("DROP TABLE utilisateurs");
                        $this->log('success', '✅ تم حذف جدول utilisateurs (لم يعد مطلوباً)');
                    }

                    // Commit transaction
                    $this->pdo->commit();

                    return true;
                } catch (Exception $e) {
                    $this->pdo->rollback();
                    $this->log('error', 'خطأ في استبدال الجدول: ' . $e->getMessage());
                    throw $e;
                }
            }

            public function validateMigration()
            {
                $this->log('info', '✅ التحقق من صحة الترحيل...');

                try {
                    // Check if users table exists and has data
                    $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users");
                    $userCount = $stmt->fetch()['count'];
                    $this->log('success', "✅ جدول users يحتوي على $userCount مستخدم");

                    // Check if admins table still exists
                    if ($this->tableExists('admins')) {
                        $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM admins");
                        $adminCount = $stmt->fetch()['count'];
                        $this->log('success', "✅ جدول admins يحتوي على $adminCount مدير");
                    }

                    // Check if utilisateurs table was removed
                    if (!$this->tableExists('utilisateurs')) {
                        $this->log('success', '✅ تم حذف جدول utilisateurs بنجاح');
                    } else {
                        $this->log('warning', '⚠️ جدول utilisateurs ما زال موجوداً');
                    }

                    // Test a sample query
                    $stmt = $this->pdo->query("SELECT id, username, email, migrated_from FROM users LIMIT 5");
                    $sampleUsers = $stmt->fetchAll();

                    $this->log('info', 'عينة من المستخدمين المرحلين:');
                    foreach ($sampleUsers as $user) {
                        $this->log('info', "- {$user['username']} ({$user['email']}) - مرحل من: {$user['migrated_from']}");
                    }

                    return true;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في التحقق من الترحيل: ' . $e->getMessage());
                    return false;
                }
            }

            /**
             * Create role management system
             */
            public function createRoleManagementSystem()
            {
                $this->log('info', '🛡️ إنشاء نظام إدارة الأدوار...');

                try {
                    // Create permissions table
                    $this->log('info', 'إنشاء جدول الصلاحيات...');
                    $createPermissionsSQL = "
                    CREATE TABLE IF NOT EXISTS permissions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL UNIQUE,
                        display_name_ar VARCHAR(150) NOT NULL,
                        display_name_en VARCHAR(150) NOT NULL,
                        description_ar TEXT,
                        description_en TEXT,
                        category VARCHAR(50) NOT NULL DEFAULT 'general',
                        is_active TINYINT(1) DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                        INDEX idx_name (name),
                        INDEX idx_category (category),
                        INDEX idx_is_active (is_active)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                    $this->pdo->exec($createPermissionsSQL);

                    // Create user_roles table
                    $this->log('info', 'إنشاء جدول الأدوار...');
                    $createUserRolesSQL = "
                    CREATE TABLE IF NOT EXISTS user_roles (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(50) NOT NULL UNIQUE,
                        display_name_ar VARCHAR(100) NOT NULL,
                        display_name_en VARCHAR(100) NOT NULL,
                        description_ar TEXT,
                        description_en TEXT,
                        level INT NOT NULL DEFAULT 1,
                        color VARCHAR(7) DEFAULT '#007bff',
                        icon VARCHAR(50) DEFAULT 'fas fa-user',
                        is_active TINYINT(1) DEFAULT 1,
                        is_default TINYINT(1) DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                        INDEX idx_name (name),
                        INDEX idx_level (level),
                        INDEX idx_is_active (is_active),
                        INDEX idx_is_default (is_default)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                    $this->pdo->exec($createUserRolesSQL);

                    // Create role_permissions table
                    $this->log('info', 'إنشاء جدول ربط الأدوار بالصلاحيات...');
                    $createRolePermissionsSQL = "
                    CREATE TABLE IF NOT EXISTS role_permissions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        role_id INT NOT NULL,
                        permission_id INT NOT NULL,
                        granted TINYINT(1) DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                        UNIQUE KEY unique_role_permission (role_id, permission_id),
                        INDEX idx_role_id (role_id),
                        INDEX idx_permission_id (permission_id),
                        INDEX idx_granted (granted),

                        FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
                        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                    $this->pdo->exec($createRolePermissionsSQL);

                    // Create user_role_assignments table
                    $this->log('info', 'إنشاء جدول تعيين الأدوار للمستخدمين...');
                    $createUserRoleAssignmentsSQL = "
                    CREATE TABLE IF NOT EXISTS user_role_assignments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        role_id INT NOT NULL,
                        assigned_by INT NULL,
                        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NULL,
                        is_active TINYINT(1) DEFAULT 1,

                        UNIQUE KEY unique_user_role (user_id, role_id),
                        INDEX idx_user_id (user_id),
                        INDEX idx_role_id (role_id),
                        INDEX idx_assigned_by (assigned_by),
                        INDEX idx_is_active (is_active),
                        INDEX idx_expires_at (expires_at),

                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
                        FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

                    $this->pdo->exec($createUserRoleAssignmentsSQL);

                    // Add role_id column to users table if it doesn't exist
                    $this->log('info', 'تحديث جدول المستخدمين لدعم الأدوار...');
                    try {
                        $this->pdo->exec("ALTER TABLE users ADD COLUMN role_id INT DEFAULT 4 AFTER phone");
                        $this->log('success', 'تم إضافة عمود role_id إلى جدول users');
                    } catch (Exception $e) {
                        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                            $this->log('info', 'عمود role_id موجود بالفعل');
                        } else {
                            throw $e;
                        }
                    }

                    // Populate default data
                    $this->populateDefaultRoleData();

                    $this->log('success', '✅ تم إنشاء نظام إدارة الأدوار بنجاح');
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في إنشاء نظام إدارة الأدوار: ' . $e->getMessage());
                    throw $e;
                }
            }

            /**
             * Populate default role data
             */
            public function populateDefaultRoleData()
            {
                $this->log('info', '📋 إضافة البيانات الافتراضية للأدوار والصلاحيات...');

                try {
                    // Insert default permissions
                    $permissions = [
                        ['users.view', 'عرض المستخدمين', 'View Users', 'users'],
                        ['users.create', 'إنشاء مستخدمين', 'Create Users', 'users'],
                        ['users.edit', 'تعديل المستخدمين', 'Edit Users', 'users'],
                        ['users.delete', 'حذف المستخدمين', 'Delete Users', 'users'],
                        ['users.manage_roles', 'إدارة أدوار المستخدمين', 'Manage User Roles', 'users'],
                        ['stores.view', 'عرض المتاجر', 'View Stores', 'stores'],
                        ['stores.create', 'إنشاء متاجر', 'Create Stores', 'stores'],
                        ['stores.edit', 'تعديل المتاجر', 'Edit Stores', 'stores'],
                        ['stores.delete', 'حذف المتاجر', 'Delete Stores', 'stores'],
                        ['products.view', 'عرض المنتجات', 'View Products', 'products'],
                        ['products.create', 'إنشاء منتجات', 'Create Products', 'products'],
                        ['products.edit', 'تعديل المنتجات', 'Edit Products', 'products'],
                        ['products.delete', 'حذف المنتجات', 'Delete Products', 'products'],
                        ['admin.dashboard', 'لوحة الإدارة', 'Admin Dashboard', 'admin'],
                        ['admin.settings', 'إعدادات النظام', 'System Settings', 'admin'],
                        ['admin.roles', 'إدارة الأدوار', 'Manage Roles', 'admin'],
                        ['content.view', 'عرض المحتوى', 'View Content', 'content'],
                        ['content.create', 'إنشاء محتوى', 'Create Content', 'content'],
                        ['content.edit', 'تعديل المحتوى', 'Edit Content', 'content'],
                        ['reports.view', 'عرض التقارير', 'View Reports', 'reports']
                    ];

                    $insertPermissionSQL = "INSERT IGNORE INTO permissions (name, display_name_ar, display_name_en, category) VALUES (?, ?, ?, ?)";
                    $permStmt = $this->pdo->prepare($insertPermissionSQL);

                    foreach ($permissions as $permission) {
                        $permStmt->execute($permission);
                    }

                    // Insert default roles
                    $roles = [
                        ['super_admin', 'مدير عام', 'Super Administrator', 1, '#dc3545', 'fas fa-crown'],
                        ['admin', 'مدير', 'Administrator', 2, '#fd7e14', 'fas fa-user-shield'],
                        ['editor', 'محرر', 'Editor', 3, '#20c997', 'fas fa-edit'],
                        ['user', 'مستخدم', 'User', 4, '#007bff', 'fas fa-user']
                    ];

                    $insertRoleSQL = "INSERT IGNORE INTO user_roles (name, display_name_ar, display_name_en, level, color, icon, is_default) VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $roleStmt = $this->pdo->prepare($insertRoleSQL);

                    foreach ($roles as $role) {
                        $isDefault = $role[0] === 'user' ? 1 : 0;
                        $roleStmt->execute([
                            $role[0],
                            $role[1],
                            $role[2],
                            $role[3],
                            $role[4],
                            $role[5],
                            $isDefault
                        ]);
                    }

                    // Assign permissions to roles
                    $this->assignDefaultRolePermissions();

                    // Update existing users with default role
                    $this->updateUsersWithDefaultRole();

                    $this->log('success', '✅ تم إضافة البيانات الافتراضية بنجاح');
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في إضافة البيانات الافتراضية: ' . $e->getMessage());
                    throw $e;
                }
            }

            /**
             * Assign default permissions to roles
             */
            private function assignDefaultRolePermissions()
            {
                try {
                    // Get role and permission IDs
                    $rolesStmt = $this->pdo->query("SELECT id, name FROM user_roles");
                    $roles = [];
                    while ($role = $rolesStmt->fetch()) {
                        $roles[$role['name']] = $role['id'];
                    }

                    $permissionsStmt = $this->pdo->query("SELECT id, name FROM permissions");
                    $permissions = [];
                    while ($permission = $permissionsStmt->fetch()) {
                        $permissions[$permission['name']] = $permission['id'];
                    }

                    // Define role-permission assignments
                    $rolePermissions = [
                        'super_admin' => array_keys($permissions), // Super admin gets all permissions
                        'admin' => [
                            'users.view',
                            'users.create',
                            'users.edit',
                            'users.manage_roles',
                            'stores.view',
                            'stores.create',
                            'stores.edit',
                            'products.view',
                            'products.create',
                            'products.edit',
                            'products.delete',
                            'admin.dashboard',
                            'admin.settings',
                            'content.view',
                            'content.create',
                            'content.edit',
                            'reports.view'
                        ],
                        'editor' => [
                            'products.view',
                            'products.create',
                            'products.edit',
                            'content.view',
                            'content.create',
                            'content.edit',
                            'stores.view'
                        ],
                        'user' => [
                            'products.view',
                            'content.view',
                            'stores.view'
                        ]
                    ];

                    $insertRolePermissionSQL = "INSERT IGNORE INTO role_permissions (role_id, permission_id, granted) VALUES (?, ?, 1)";
                    $stmt = $this->pdo->prepare($insertRolePermissionSQL);

                    foreach ($rolePermissions as $roleName => $rolePerms) {
                        if (!isset($roles[$roleName])) continue;

                        $roleId = $roles[$roleName];
                        foreach ($rolePerms as $permName) {
                            if (!isset($permissions[$permName])) continue;
                            $permissionId = $permissions[$permName];
                            $stmt->execute([$roleId, $permissionId]);
                        }
                    }
                } catch (Exception $e) {
                    throw new Exception('خطأ في تعيين الصلاحيات للأدوار: ' . $e->getMessage());
                }
            }

            /**
             * Update existing users with default role
             */
            private function updateUsersWithDefaultRole()
            {
                try {
                    // Get default user role ID
                    $stmt = $this->pdo->prepare("SELECT id FROM user_roles WHERE name = 'user' LIMIT 1");
                    $stmt->execute();
                    $defaultRole = $stmt->fetch();

                    if ($defaultRole) {
                        $defaultRoleId = $defaultRole['id'];

                        // Update users without role_id
                        $updateUsersSQL = "UPDATE users SET role_id = ? WHERE role_id IS NULL OR role_id = 0";
                        $stmt = $this->pdo->prepare($updateUsersSQL);
                        $stmt->execute([$defaultRoleId]);

                        // Create role assignments
                        $insertAssignmentSQL = "INSERT IGNORE INTO user_role_assignments (user_id, role_id, assigned_at) SELECT id, role_id, NOW() FROM users WHERE role_id IS NOT NULL";
                        $this->pdo->exec($insertAssignmentSQL);
                    }
                } catch (Exception $e) {
                    throw new Exception('خطأ في تحديث المستخدمين بالأدوار الافتراضية: ' . $e->getMessage());
                }
            }

            public function runFullMigration()
            {
                $this->log('info', '🚀 بدء عملية الترحيل الكاملة...');

                try {
                    // Step 1: Analyze current structure
                    $tableInfo = $this->analyzeCurrentStructure();

                    // Step 2: Create backup
                    $backupTimestamp = $this->backupExistingData();

                    // Step 3: Create consolidated table
                    $this->createConsolidatedUsersTable();

                    // Step 4: Migrate data
                    $migratedCount = $this->migrateUsersData();

                    // Step 5: Replace original table
                    $this->replaceOriginalUsersTable();

                    // Step 6: Create role management system
                    $this->createRoleManagementSystem();

                    // Step 7: Validate migration
                    $this->validateMigration();

                    $this->log('success', "🎉 تم إكمال الترحيل بنجاح! تم ترحيل $migratedCount مستخدم");
                    $this->log('info', "💾 النسخة الاحتياطية متاحة بالطابع الزمني: $backupTimestamp");

                    return true;
                } catch (Exception $e) {
                    $this->log('error', '❌ فشل في عملية الترحيل: ' . $e->getMessage());
                    return false;
                }
            }
        }

        // Execute migration if requested
        if (isset($_GET['action']) && $_GET['action'] === 'migrate') {
            try {
                $migration = new DatabaseConsolidationMigration();
                $success = $migration->runFullMigration();

                if ($success) {
                    echo "<div class='step success'>";
                    echo "<h3>✅ تم إكمال الترحيل بنجاح!</h3>";
                    echo "<p>يمكنك الآن استخدام النظام الموحد لإدارة المستخدمين.</p>";
                    echo "<a href='users-management-standalone.html' class='btn btn-primary'>اختبار إدارة المستخدمين</a>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='step error'>";
                echo "<h3>❌ فشل في الترحيل</h3>";
                echo "<p>خطأ: " . $e->getMessage() . "</p>";
                echo "</div>";
            }
        } else {
            // Show migration interface
        ?>
            <div class="step info">
                <h3>📋 معلومات الترحيل</h3>
                <p>هذا النص سيقوم بتوحيد جداول المستخدمين التالية:</p>
                <ul>
                    <li><strong>users</strong> - الجدول الرئيسي للمستخدمين</li>
                    <li><strong>utilisateurs</strong> - جدول المستخدمين القديم (سيتم دمجه)</li>
                    <li><strong>admins</strong> - جدول المديرين (سيبقى منفصلاً)</li>
                </ul>

                <h4>ما سيحدث:</h4>
                <ol>
                    <li>إنشاء نسخة احتياطية من جميع الجداول</li>
                    <li>إنشاء جدول مستخدمين موحد جديد</li>
                    <li>ترحيل البيانات من الجدولين القديمين</li>
                    <li>استبدال الجدول القديم بالجديد</li>
                    <li>حذف الجداول غير المطلوبة</li>
                    <li>التحقق من صحة الترحيل</li>
                </ol>
            </div>

            <div class="step warning">
                <h3>⚠️ تحذير مهم</h3>
                <p>تأكد من إنشاء نسخة احتياطية كاملة من قاعدة البيانات قبل المتابعة!</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="?action=migrate" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من المتابعة؟ تأكد من إنشاء نسخة احتياطية أولاً!')">
                    🚀 بدء عملية الترحيل
                </a>
                <a href="index.html" class="btn btn-warning">
                    ↩️ العودة للوحة الإدارة
                </a>
            </div>
        <?php
        }
        ?>

    </div>
</body>

</html>
