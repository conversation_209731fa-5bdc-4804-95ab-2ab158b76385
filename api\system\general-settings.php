<?php

/**
 * General Settings API
 * API الإعدادات العامة
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../config/db_env.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            if ($action === 'categories') {
                getSettingsCategories();
            } elseif ($action === 'backup') {
                backupSettings();
            } elseif ($action === 'restore') {
                restoreSettings();
            } else {
                getSettings();
            }
            break;
        case 'POST':
            if ($action === 'bulk') {
                updateBulkSettings();
            } elseif ($action === 'reset') {
                resetSettings();
            } else {
                createSetting();
            }
            break;
        case 'PUT':
            updateSetting();
            break;
        case 'DELETE':
            if (isset($_GET['key'])) {
                deleteSetting($_GET['key']);
            }
            break;
        default:
            throw new Exception('Method not allowed');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getSettings()
{
    global $pdo;

    $category = $_GET['category'] ?? '';
    $search = $_GET['search'] ?? '';

    // Build WHERE clause
    $where = [];
    $params = [];

    if ($category) {
        $where[] = "category = ?";
        $params[] = $category;
    }

    if ($search) {
        $where[] = "(setting_key LIKE ? OR description_ar LIKE ? OR description_en LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

    $stmt = $pdo->prepare("
        SELECT setting_key, setting_value, data_type, category, description_ar, description_en,
               is_public, validation_rules, default_value, updated_at
        FROM general_settings
        $whereClause
        ORDER BY category ASC, setting_key ASC
    ");
    $stmt->execute($params);
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Group by category
    $grouped = [];
    foreach ($settings as $setting) {
        $category = $setting['category'];
        if (!isset($grouped[$category])) {
            $grouped[$category] = [];
        }

        // Parse validation rules
        $setting['validation_rules'] = json_decode($setting['validation_rules'], true) ?: [];
        $setting['is_public'] = (bool) $setting['is_public'];

        // Format value based on data type
        $setting['setting_value'] = formatSettingValue($setting['setting_value'], $setting['data_type']);
        $setting['default_value'] = formatSettingValue($setting['default_value'], $setting['data_type']);

        $grouped[$category][] = $setting;
    }

    echo json_encode([
        'success' => true,
        'data' => $grouped
    ]);
}

function getSettingsCategories()
{
    global $pdo;

    $stmt = $pdo->query("
        SELECT category, COUNT(*) as count
        FROM general_settings
        GROUP BY category
        ORDER BY category ASC
    ");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add Arabic names for categories
    $categoryNames = [
        'site' => 'إعدادات الموقع',
        'email' => 'إعدادات البريد الإلكتروني',
        'sms' => 'إعدادات الرسائل النصية',
        'social' => 'وسائل التواصل الاجتماعي',
        'seo' => 'تحسين محركات البحث',
        'analytics' => 'التحليلات والإحصائيات',
        'maintenance' => 'الصيانة والنسخ الاحتياطي',
        'localization' => 'التوطين واللغة',
        'api' => 'إعدادات API',
        'system' => 'إعدادات النظام'
    ];

    foreach ($categories as &$category) {
        $category['name_ar'] = $categoryNames[$category['category']] ?? $category['category'];
        $category['count'] = (int) $category['count'];
    }

    echo json_encode([
        'success' => true,
        'data' => $categories
    ]);
}

function createSetting()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid input data');
    }

    // Validate required fields
    $required = ['setting_key', 'setting_value', 'data_type', 'category'];
    foreach ($required as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("Field '$field' is required");
        }
    }

    // Check if setting already exists
    $stmt = $pdo->prepare("SELECT setting_key FROM general_settings WHERE setting_key = ?");
    $stmt->execute([$input['setting_key']]);
    if ($stmt->fetch()) {
        throw new Exception('Setting key already exists');
    }

    // Validate data type
    $validTypes = ['string', 'number', 'boolean', 'json', 'email', 'url', 'color', 'file'];
    if (!in_array($input['data_type'], $validTypes)) {
        throw new Exception('Invalid data type');
    }

    // Validate setting value
    $validatedValue = validateSettingValue($input['setting_value'], $input['data_type'], $input['validation_rules'] ?? []);

    $stmt = $pdo->prepare("
        INSERT INTO general_settings (
            setting_key, setting_value, data_type, category, description_ar, description_en,
            is_public, validation_rules, default_value
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $input['setting_key'],
        $validatedValue,
        $input['data_type'],
        $input['category'],
        $input['description_ar'] ?? '',
        $input['description_en'] ?? '',
        $input['is_public'] ?? false,
        json_encode($input['validation_rules'] ?? []),
        $input['default_value'] ?? $validatedValue
    ]);

    echo json_encode([
        'success' => true,
        'message' => 'Setting created successfully'
    ]);
}

function updateSetting()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['setting_key'])) {
        throw new Exception('Invalid input data');
    }

    $settingKey = $input['setting_key'];

    // Get current setting
    $stmt = $pdo->prepare("SELECT * FROM general_settings WHERE setting_key = ?");
    $stmt->execute([$settingKey]);
    $currentSetting = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$currentSetting) {
        throw new Exception('Setting not found');
    }

    // Build update query
    $fields = [];
    $params = [];

    $allowedFields = [
        'setting_value',
        'description_ar',
        'description_en',
        'is_public',
        'validation_rules',
        'default_value'
    ];

    foreach ($allowedFields as $field) {
        if (array_key_exists($field, $input)) {
            if ($field === 'setting_value') {
                // Validate the new value
                $validationRules = json_decode($currentSetting['validation_rules'], true) ?: [];
                $validatedValue = validateSettingValue($input[$field], $currentSetting['data_type'], $validationRules);
                $fields[] = "$field = ?";
                $params[] = $validatedValue;
            } elseif ($field === 'validation_rules') {
                $fields[] = "$field = ?";
                $params[] = json_encode($input[$field]);
            } else {
                $fields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
    }

    if (empty($fields)) {
        throw new Exception('No fields to update');
    }

    $params[] = $settingKey;

    $stmt = $pdo->prepare("
        UPDATE general_settings
        SET " . implode(', ', $fields) . "
        WHERE setting_key = ?
    ");

    $stmt->execute($params);

    echo json_encode([
        'success' => true,
        'message' => 'Setting updated successfully'
    ]);
}

function updateBulkSettings()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['settings']) || !is_array($input['settings'])) {
        throw new Exception('Invalid input data');
    }

    $pdo->beginTransaction();

    try {
        $updated = 0;
        $errors = [];

        foreach ($input['settings'] as $settingKey => $settingValue) {
            try {
                // Get current setting
                $stmt = $pdo->prepare("SELECT data_type, validation_rules FROM general_settings WHERE setting_key = ?");
                $stmt->execute([$settingKey]);
                $currentSetting = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($currentSetting) {
                    // Validate the new value
                    $validationRules = json_decode($currentSetting['validation_rules'], true) ?: [];
                    $validatedValue = validateSettingValue($settingValue, $currentSetting['data_type'], $validationRules);

                    // Update setting
                    $updateStmt = $pdo->prepare("UPDATE general_settings SET setting_value = ? WHERE setting_key = ?");
                    $updateStmt->execute([$validatedValue, $settingKey]);
                    $updated++;
                } else {
                    $errors[] = "Setting '$settingKey' not found";
                }
            } catch (Exception $e) {
                $errors[] = "Error updating '$settingKey': " . $e->getMessage();
            }
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => "Updated $updated settings successfully",
            'updated_count' => $updated,
            'errors' => $errors
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

function deleteSetting($settingKey)
{
    global $pdo;

    // Check if setting exists
    $stmt = $pdo->prepare("SELECT setting_key FROM general_settings WHERE setting_key = ?");
    $stmt->execute([$settingKey]);
    if (!$stmt->fetch()) {
        throw new Exception('Setting not found');
    }

    $stmt = $pdo->prepare("DELETE FROM general_settings WHERE setting_key = ?");
    $stmt->execute([$settingKey]);

    echo json_encode([
        'success' => true,
        'message' => 'Setting deleted successfully'
    ]);
}

function resetSettings()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);
    $category = $input['category'] ?? null;

    if ($category) {
        // Reset specific category
        $stmt = $pdo->prepare("UPDATE general_settings SET setting_value = default_value WHERE category = ?");
        $stmt->execute([$category]);
        $message = "Settings for category '$category' reset to defaults";
    } else {
        // Reset all settings
        $stmt = $pdo->query("UPDATE general_settings SET setting_value = default_value");
        $message = "All settings reset to defaults";
    }

    echo json_encode([
        'success' => true,
        'message' => $message
    ]);
}

function backupSettings()
{
    global $pdo;

    $stmt = $pdo->query("SELECT * FROM general_settings ORDER BY category ASC, setting_key ASC");
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $backup = [
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0',
        'settings' => $settings
    ];

    // Set headers for file download
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="general_settings_backup_' . date('Y-m-d_H-i-s') . '.json"');

    echo json_encode($backup, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}

function restoreSettings()
{
    global $pdo;

    if (!isset($_FILES['backup_file'])) {
        throw new Exception('No backup file provided');
    }

    $file = $_FILES['backup_file'];

    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error');
    }

    $content = file_get_contents($file['tmp_name']);
    $backup = json_decode($content, true);

    if (!$backup || !isset($backup['settings'])) {
        throw new Exception('Invalid backup file format');
    }

    $pdo->beginTransaction();

    try {
        // Clear existing settings
        $pdo->query("DELETE FROM general_settings");

        // Restore settings
        $stmt = $pdo->prepare("
            INSERT INTO general_settings (
                setting_key, setting_value, data_type, category, description_ar, description_en,
                is_public, validation_rules, default_value, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($backup['settings'] as $setting) {
            $stmt->execute([
                $setting['setting_key'],
                $setting['setting_value'],
                $setting['data_type'],
                $setting['category'],
                $setting['description_ar'],
                $setting['description_en'],
                $setting['is_public'],
                $setting['validation_rules'],
                $setting['default_value'],
                $setting['created_at'],
                $setting['updated_at']
            ]);
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Settings restored successfully'
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

// Helper Functions
function formatSettingValue($value, $dataType)
{
    switch ($dataType) {
        case 'boolean':
            return filter_var($value, FILTER_VALIDATE_BOOLEAN);
        case 'number':
            return is_numeric($value) ? (float) $value : $value;
        case 'json':
            return json_decode($value, true) ?: $value;
        default:
            return $value;
    }
}

function validateSettingValue($value, $dataType, $validationRules = [])
{
    switch ($dataType) {
        case 'email':
            if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email format');
            }
            break;

        case 'url':
            if (!filter_var($value, FILTER_VALIDATE_URL)) {
                throw new Exception('Invalid URL format');
            }
            break;

        case 'number':
            if (!is_numeric($value)) {
                throw new Exception('Value must be a number');
            }
            $value = (float) $value;

            if (isset($validationRules['min']) && $value < $validationRules['min']) {
                throw new Exception("Value must be at least {$validationRules['min']}");
            }
            if (isset($validationRules['max']) && $value > $validationRules['max']) {
                throw new Exception("Value must be at most {$validationRules['max']}");
            }
            break;

        case 'boolean':
            $value = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            if ($value === null) {
                throw new Exception('Value must be a boolean');
            }
            break;

        case 'json':
            if (is_string($value)) {
                $decoded = json_decode($value);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception('Invalid JSON format');
                }
            }
            break;

        case 'color':
            if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $value)) {
                throw new Exception('Invalid color format (use #RRGGBB)');
            }
            break;

        case 'string':
            if (isset($validationRules['min_length']) && strlen($value) < $validationRules['min_length']) {
                throw new Exception("Value must be at least {$validationRules['min_length']} characters");
            }
            if (isset($validationRules['max_length']) && strlen($value) > $validationRules['max_length']) {
                throw new Exception("Value must be at most {$validationRules['max_length']} characters");
            }
            break;
    }

    return $value;
}
