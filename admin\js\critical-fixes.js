/**
 * Critical Fixes for Admin Interface
 * This file contains emergency fixes for critical issues
 */

console.log('🔧 Loading critical fixes...');

// Fix 1: Ensure product management functions are properly available
(function() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCriticalFixes);
    } else {
        initializeCriticalFixes();
    }

    function initializeCriticalFixes() {
        console.log('🚀 Initializing critical fixes...');

        // Fix product management functions
        fixProductManagementFunctions();

        // Fix pagination functions
        fixPaginationFunctions();

        // Fix settings sections
        fixSettingsSections();

        // Fix reports section
        fixReportsSection();

        console.log('✅ Critical fixes initialized');
    }

    function fixProductManagementFunctions() {
        console.log('🔧 Fixing product management functions...');

        // Enhanced editProduct function
        if (typeof window.editProduct !== 'function' || window.editProduct.toString().includes('prompt')) {
            window.editProduct = async function(productId) {
                console.log('🖊️ Enhanced editProduct called with ID:', productId);

                try {
                    // Show loading notification
                    if (typeof notificationManager !== 'undefined') {
                        notificationManager.showInfo('جاري تحميل بيانات المنتج للتعديل...');
                    }

                    // Fetch product data
                    const response = await fetch(`../php/api/products.php?id=${productId}`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (!result.success) {
                        throw new Error(result.message || 'Failed to load product data');
                    }

                    const product = result.data;

                    // Show enhanced edit modal
                    if (typeof showProductEditModal === 'function') {
                        showProductEditModal(product);
                    } else {
                        // Fallback to simple edit
                        const newName = prompt('اسم المنتج الجديد:', product.nom || product.titre);
                        const newPrice = prompt('السعر الجديد:', product.prix);
                        const newCategory = prompt('الفئة الجديدة:', product.categorie);
                        const newDescription = prompt('الوصف الجديد:', product.description);

                        if (newName && newPrice) {
                            if (typeof notificationManager !== 'undefined') {
                                notificationManager.showSuccess('تم تحديث المنتج بنجاح');
                            } else {
                                alert('تم تحديث المنتج بنجاح');
                            }
                        }
                    }

                } catch (error) {
                    console.error('Error editing product:', error);
                    if (typeof notificationManager !== 'undefined') {
                        notificationManager.showError(`حدث خطأ أثناء تعديل المنتج: ${error.message}`);
                    } else {
                        alert(`حدث خطأ أثناء تعديل المنتج: ${error.message}`);
                    }
                }
            };
        }

        // Enhanced viewProduct function
        if (typeof window.viewProduct !== 'function') {
            window.viewProduct = async function(productId) {
                console.log('👁️ Enhanced viewProduct called with ID:', productId);

                try {
                    // Show loading notification
                    if (typeof notificationManager !== 'undefined') {
                        notificationManager.showInfo('جاري تحميل بيانات المنتج...');
                    }

                    // Fetch product data
                    const response = await fetch(`../php/api/products.php?id=${productId}`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (!result.success) {
                        throw new Error(result.message || 'Failed to load product data');
                    }

                    const product = result.data;

                    // Show enhanced view modal
                    if (typeof showProductViewModal === 'function') {
                        showProductViewModal(product);
                    } else {
                        // Fallback to alert
                        const productInfo = `
معلومات المنتج:
الاسم: ${product.nom || product.titre || 'غير محدد'}
السعر: ${product.prix || '0'} دج
الفئة: ${product.categorie || 'غير محدد'}
الوصف: ${product.description || 'لا يوجد وصف'}
الحالة: ${product.actif ? 'نشط' : 'غير نشط'}
                        `;
                        alert(productInfo);
                    }

                } catch (error) {
                    console.error('Error viewing product:', error);
                    if (typeof notificationManager !== 'undefined') {
                        notificationManager.showError(`حدث خطأ أثناء عرض المنتج: ${error.message}`);
                    } else {
                        alert(`حدث خطأ أثناء عرض المنتج: ${error.message}`);
                    }
                }
            };
        }

        // Enhanced viewLandingPage function
        window.viewLandingPage = async function(productId) {
            console.log('🚀 Enhanced viewLandingPage called with ID:', productId);

            try {
                // Try to construct landing page URL
                const landingPageUrl = `../landing-page.php?product_id=${productId}`;

                // Check if the landing page exists
                try {
                    const checkResponse = await fetch(landingPageUrl, { method: 'HEAD' });
                    if (checkResponse.ok) {
                        window.open(landingPageUrl, '_blank');
                    } else {
                        // Try alternative URL
                        const altUrl = `../product-landing.php?id=${productId}`;
                        window.open(altUrl, '_blank');
                    }
                } catch (checkError) {
                    // If check fails, try opening anyway
                    window.open(landingPageUrl, '_blank');
                }

            } catch (error) {
                console.error('Error viewing landing page:', error);
                if (typeof notificationManager !== 'undefined') {
                    notificationManager.showError(`حدث خطأ أثناء عرض صفحة الهبوط: ${error.message}`);
                } else {
                    alert(`حدث خطأ أثناء عرض صفحة الهبوط: ${error.message}`);
                }
            }
        };

        console.log('✅ Product management functions fixed');
    }

    function fixPaginationFunctions() {
        console.log('🔧 Fixing pagination functions...');

        // Ensure pagination functions are available
        if (typeof window.goToProductsPage !== 'function') {
            window.goToProductsPage = function(page) {
                if (typeof window.goToPage === 'function') {
                    window.goToPage(page);
                } else {
                    console.log('Going to page:', page);
                    // Reload products if function available
                    if (typeof loadProducts === 'function') {
                        loadProducts();
                    } else if (typeof loadProductsWithPagination === 'function') {
                        loadProductsWithPagination();
                    }
                }
            };
        }

        if (typeof window.previousProductsPage !== 'function') {
            window.previousProductsPage = function() {
                console.log('Going to previous page');
                if (typeof window.goToProductsPage === 'function') {
                    // Implement previous page logic
                    let page = parseInt(document.querySelector('.pagination .active')?.textContent || '1');
                    if (page > 1) {
                        window.goToProductsPage(page - 1);
                    }
                }
            };
        }

        if (typeof window.nextProductsPage !== 'function') {
            window.nextProductsPage = function() {
                console.log('Going to next page');
                if (typeof window.goToProductsPage === 'function') {
                    // Implement next page logic
                    let page = parseInt(document.querySelector('.pagination .active')?.textContent || '1');
                    window.goToProductsPage(page + 1);
                }
            };
        }

        console.log('✅ Pagination functions fixed');
    }

    function fixSettingsSections() {
        console.log('🔧 Fixing settings sections...');

        // Create fallback content for settings sections
        // Note: rolesManagement is excluded because it has a real implementation in users-management.js
        const settingsSections = [
            'generalSettings',
            'storeSettings',
            'userManagement',
            'storesManagement',
            // 'rolesManagement', // Excluded - has real implementation
            'subscriptionsManagement',
            'securitySettings',
            'systemTesting',
            'categoriesManagement',
            'paymentSettings'
        ];

        settingsSections.forEach(section => {
            const functionName = `load${section.charAt(0).toUpperCase() + section.slice(1)}Content`;

            if (typeof window[functionName] !== 'function') {
                window[functionName] = function() {
                    console.log(`Loading ${section} content...`);

                    const container = document.getElementById(`${section}Content`);
                    if (container) {
                        container.innerHTML = `
                            <div style="text-align: center; padding: 40px;">
                                <i class="fas fa-cog" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
                                <h3 style="color: #2c3e50; margin: 15px 0;">${getSectionTitle(section)}</h3>
                                <p style="color: #666; margin-bottom: 30px;">هذا القسم قيد التطوير</p>
                                <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                    <p>سيتم إضافة المحتوى قريباً...</p>
                                </div>
                            </div>
                        `;
                    }
                };
            }
        });

        function getSectionTitle(section) {
            const titles = {
                'generalSettings': 'الإعدادات العامة',
                'storeSettings': 'إعدادات المتجر',
                'userManagement': 'إدارة المستخدمين',
                'storesManagement': 'إدارة المتاجر',
                'rolesManagement': 'إدارة الأدوار',
                'subscriptionsManagement': 'إدارة الاشتراكات',
                'securitySettings': 'إعدادات الأمان',
                'systemTesting': 'اختبار النظام',
                'categoriesManagement': 'إدارة الفئات',
                'paymentSettings': 'إعدادات الدفع'
            };
            return titles[section] || section;
        }

        console.log('✅ Settings sections fixed');
    }

    function fixReportsSection() {
        console.log('🔧 Fixing reports section...');

        // Ensure reports function works
        if (typeof window.loadReportsContent !== 'function') {
            window.loadReportsContent = function() {
                console.log('Loading reports content...');

                const container = document.getElementById('reportsContent');
                if (container) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <i class="fas fa-chart-bar" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
                            <h3 style="color: #2c3e50; margin: 15px 0;">التقارير والإحصائيات</h3>
                            <p style="color: #666; margin-bottom: 30px;">عرض تقارير شاملة عن أداء المتجر</p>
                            <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <p>التقارير قيد التطوير...</p>
                            </div>
                        </div>
                    `;
                }
            };
        }

        console.log('✅ Reports section fixed');
    }
})();

console.log('✅ Critical fixes loaded successfully');
