<?php
/**
 * Store Settings API
 * واجهة برمجة التطبيقات لإعدادات المتجر
 */

session_start();

// Load configuration
require_once '../../config/config.php';

// Set JSON response headers
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Connect to database
try {
    $dbConfig = Config::getDbConfig();
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $dbConfig['host'],
        $dbConfig['port'],
        $dbConfig['database']
    );
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()]);
    exit;
}

/**
 * Store Settings Manager Class
 */
class StoreSettingsManager {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get all settings grouped by category
     */
    public function getAllSettings($includePrivate = false) {
        try {
            $whereClause = $includePrivate ? '' : 'WHERE is_public = 1';
            
            $stmt = $this->pdo->query("
                SELECT 
                    id, setting_key, setting_value, setting_type, category,
                    display_name_ar, display_name_en, description_ar, description_en,
                    is_required, is_public, sort_order, validation_rules, default_value,
                    created_at, updated_at
                FROM store_settings 
                $whereClause
                ORDER BY category, sort_order, display_name_ar
            ");
            
            $settings = $stmt->fetchAll();
            
            // Group by category
            $grouped = [];
            $categories = [];
            
            foreach ($settings as $setting) {
                $category = $setting['category'];
                
                if (!isset($grouped[$category])) {
                    $grouped[$category] = [];
                    $categories[$category] = [
                        'name' => $this->getCategoryDisplayName($category),
                        'count' => 0
                    ];
                }
                
                // Parse JSON fields
                if ($setting['validation_rules']) {
                    $setting['validation_rules'] = json_decode($setting['validation_rules'], true);
                }
                
                $grouped[$category][] = $setting;
                $categories[$category]['count']++;
            }
            
            // Get configurations
            $configStmt = $this->pdo->query("
                SELECT config_name, config_data, config_type, is_active, created_at, updated_at
                FROM store_configurations 
                WHERE is_active = 1
                ORDER BY config_name
            ");
            
            $configurations = [];
            while ($config = $configStmt->fetch()) {
                $config['config_data'] = json_decode($config['config_data'], true);
                $configurations[$config['config_name']] = $config;
            }
            
            // Get notification settings
            $notificationStmt = $this->pdo->query("
                SELECT * FROM notification_settings 
                ORDER BY notification_type
            ");
            
            $notifications = [];
            while ($notification = $notificationStmt->fetch()) {
                if ($notification['recipients']) {
                    $notification['recipients'] = json_decode($notification['recipients'], true);
                }
                if ($notification['settings']) {
                    $notification['settings'] = json_decode($notification['settings'], true);
                }
                $notifications[$notification['notification_type']] = $notification;
            }
            
            return [
                'success' => true,
                'data' => [
                    'settings' => $grouped,
                    'categories' => $categories,
                    'configurations' => $configurations,
                    'notifications' => $notifications,
                    'total_settings' => count($settings),
                    'total_categories' => count($categories)
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب الإعدادات: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get setting by key
     */
    public function getSettingByKey($key) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM store_settings 
                WHERE setting_key = ?
            ");
            $stmt->execute([$key]);
            $setting = $stmt->fetch();
            
            if (!$setting) {
                return ['success' => false, 'message' => 'الإعداد غير موجود'];
            }
            
            if ($setting['validation_rules']) {
                $setting['validation_rules'] = json_decode($setting['validation_rules'], true);
            }
            
            return ['success' => true, 'data' => ['setting' => $setting]];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب الإعداد: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update setting value
     */
    public function updateSetting($key, $value, $validateOnly = false) {
        try {
            // Get setting info
            $stmt = $this->pdo->prepare("
                SELECT * FROM store_settings 
                WHERE setting_key = ?
            ");
            $stmt->execute([$key]);
            $setting = $stmt->fetch();
            
            if (!$setting) {
                return ['success' => false, 'message' => 'الإعداد غير موجود'];
            }
            
            // Validate value
            $validation = $this->validateSettingValue($setting, $value);
            if (!$validation['valid']) {
                return ['success' => false, 'message' => $validation['message']];
            }
            
            if ($validateOnly) {
                return ['success' => true, 'message' => 'القيمة صحيحة'];
            }
            
            // Update setting
            $updateStmt = $this->pdo->prepare("
                UPDATE store_settings 
                SET setting_value = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE setting_key = ?
            ");
            $updateStmt->execute([$value, $key]);
            
            return ['success' => true, 'message' => 'تم تحديث الإعداد بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تحديث الإعداد: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update multiple settings
     */
    public function updateMultipleSettings($settings) {
        try {
            $this->pdo->beginTransaction();
            
            $updated = 0;
            $errors = [];
            
            foreach ($settings as $key => $value) {
                $result = $this->updateSetting($key, $value);
                if ($result['success']) {
                    $updated++;
                } else {
                    $errors[$key] = $result['message'];
                }
            }
            
            if (empty($errors)) {
                $this->pdo->commit();
                return ['success' => true, 'message' => "تم تحديث $updated إعداد بنجاح"];
            } else {
                $this->pdo->rollback();
                return ['success' => false, 'message' => 'فشل في تحديث بعض الإعدادات', 'errors' => $errors];
            }
            
        } catch (Exception $e) {
            $this->pdo->rollback();
            return ['success' => false, 'message' => 'خطأ في تحديث الإعدادات: ' . $e->getMessage()];
        }
    }
    
    /**
     * Update configuration
     */
    public function updateConfiguration($configName, $configData) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE store_configurations 
                SET config_data = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE config_name = ?
            ");
            
            $jsonData = json_encode($configData, JSON_UNESCAPED_UNICODE);
            $stmt->execute([$jsonData, $configName]);
            
            if ($stmt->rowCount() === 0) {
                // Insert if not exists
                $insertStmt = $this->pdo->prepare("
                    INSERT INTO store_configurations (config_name, config_data, config_type) 
                    VALUES (?, ?, 'custom')
                ");
                $insertStmt->execute([$configName, $jsonData]);
            }
            
            return ['success' => true, 'message' => 'تم تحديث التكوين بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تحديث التكوين: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get public settings for frontend
     */
    public function getPublicSettings() {
        try {
            $stmt = $this->pdo->query("
                SELECT setting_key, setting_value, setting_type 
                FROM store_settings 
                WHERE is_public = 1
                ORDER BY sort_order
            ");
            
            $settings = [];
            while ($row = $stmt->fetch()) {
                $value = $row['setting_value'];
                
                // Convert value based on type
                switch ($row['setting_type']) {
                    case 'boolean':
                        $value = (bool) $value;
                        break;
                    case 'number':
                        $value = is_numeric($value) ? (float) $value : 0;
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }
                
                $settings[$row['setting_key']] = $value;
            }
            
            return ['success' => true, 'data' => ['settings' => $settings]];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب الإعدادات العامة: ' . $e->getMessage()];
        }
    }
    
    /**
     * Validate setting value
     */
    private function validateSettingValue($setting, $value) {
        // Required check
        if ($setting['is_required'] && empty($value)) {
            return ['valid' => false, 'message' => 'هذا الحقل مطلوب'];
        }
        
        // Type validation
        switch ($setting['setting_type']) {
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return ['valid' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
                }
                break;
                
            case 'url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    return ['valid' => false, 'message' => 'الرابط غير صحيح'];
                }
                break;
                
            case 'number':
                if (!empty($value) && !is_numeric($value)) {
                    return ['valid' => false, 'message' => 'يجب أن تكون القيمة رقماً'];
                }
                break;
                
            case 'boolean':
                if (!in_array($value, ['0', '1', 0, 1, true, false])) {
                    return ['valid' => false, 'message' => 'القيمة يجب أن تكون صحيح أو خطأ'];
                }
                break;
                
            case 'color':
                if (!empty($value) && !preg_match('/^#[a-fA-F0-9]{6}$/', $value)) {
                    return ['valid' => false, 'message' => 'لون غير صحيح (استخدم #RRGGBB)'];
                }
                break;
        }
        
        return ['valid' => true];
    }
    
    /**
     * Get category display name
     */
    private function getCategoryDisplayName($category) {
        $names = [
            'general' => 'الإعدادات العامة',
            'currency' => 'العملة والضرائب',
            'shipping' => 'الشحن والتوصيل',
            'email' => 'البريد الإلكتروني',
            'appearance' => 'المظهر والعرض',
            'payment' => 'طرق الدفع',
            'social' => 'وسائل التواصل',
            'seo' => 'تحسين محركات البحث',
            'security' => 'الأمان',
            'notifications' => 'الإشعارات'
        ];
        
        return $names[$category] ?? ucfirst($category);
    }
}

// Handle requests
$manager = new StoreSettingsManager($pdo);
$action = $_GET['action'] ?? $_POST['action'] ?? '';

// Debug logging
error_log("Store Settings API - Action: $action, Method: " . $_SERVER['REQUEST_METHOD']);

switch ($action) {
    case 'get_all':
        $includePrivate = isset($_GET['include_private']) && $_GET['include_private'] === 'true';
        $result = $manager->getAllSettings($includePrivate);
        break;
        
    case 'get_by_key':
        $key = $_GET['key'] ?? '';
        if (empty($key)) {
            $result = ['success' => false, 'message' => 'مفتاح الإعداد مطلوب'];
        } else {
            $result = $manager->getSettingByKey($key);
        }
        break;
        
    case 'get_public':
        $result = $manager->getPublicSettings();
        break;
        
    case 'update_setting':
        $key = $_POST['key'] ?? '';
        $value = $_POST['value'] ?? '';
        $validateOnly = isset($_POST['validate_only']) && $_POST['validate_only'] === 'true';
        
        if (empty($key)) {
            $result = ['success' => false, 'message' => 'مفتاح الإعداد مطلوب'];
        } else {
            $result = $manager->updateSetting($key, $value, $validateOnly);
        }
        break;
        
    case 'update_multiple':
        $input = json_decode(file_get_contents('php://input'), true);
        $settings = $input['settings'] ?? $_POST['settings'] ?? [];
        
        if (empty($settings)) {
            $result = ['success' => false, 'message' => 'لا توجد إعدادات للتحديث'];
        } else {
            $result = $manager->updateMultipleSettings($settings);
        }
        break;
        
    case 'update_configuration':
        $input = json_decode(file_get_contents('php://input'), true);
        $configName = $input['config_name'] ?? $_POST['config_name'] ?? '';
        $configData = $input['config_data'] ?? $_POST['config_data'] ?? [];
        
        if (empty($configName)) {
            $result = ['success' => false, 'message' => 'اسم التكوين مطلوب'];
        } else {
            $result = $manager->updateConfiguration($configName, $configData);
        }
        break;
        
    default:
        $result = ['success' => false, 'message' => 'إجراء غير صحيح'];
        break;
}

// Return JSON response
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
