<?php
/**
 * Command Line Database Migration Verification
 * التحقق من ترحيل قاعدة البيانات عبر سطر الأوامر
 */

require_once __DIR__ . '/../php/config.php';

echo "=== Database Migration Verification ===\n";
echo "التحقق من ترحيل قاعدة البيانات\n\n";

try {
    $pdo = getPDOConnection();
    echo "✅ Database connection successful\n";
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // 1. Check produits table structure
    echo "1. Checking produits table structure...\n";
    echo "1. فحص هيكل جدول produits...\n";
    
    $stmt = $pdo->query("DESCRIBE produits");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Columns in produits table:\n";
    echo "الأعمدة في جدول produits:\n";
    foreach ($columns as $column) {
        echo "  - {$column['Field']} ({$column['Type']}) - Default: " . ($column['Default'] ?? 'NULL') . "\n";
    }
    echo "\n";
    
    // Check for required columns
    $requiredColumns = ['id', 'type', 'titre', 'auteur', 'description', 'prix', 'stock', 'image_url', 
                       'has_landing_page', 'landing_page_enabled', 'slug', 'actif'];
    $existingColumns = array_column($columns, 'Field');
    $missingColumns = array_diff($requiredColumns, $existingColumns);
    
    if (empty($missingColumns)) {
        echo "✅ All required columns present\n";
        echo "✅ جميع الأعمدة المطلوبة موجودة\n";
    } else {
        echo "❌ Missing columns: " . implode(', ', $missingColumns) . "\n";
        echo "❌ أعمدة مفقودة: " . implode(', ', $missingColumns) . "\n";
    }
    echo "\n";
    
    // 2. Check produits table data
    echo "2. Checking produits table data...\n";
    echo "2. فحص بيانات جدول produits...\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
    $count = $stmt->fetch()['count'];
    echo "Total products: $count\n";
    echo "إجمالي المنتجات: $count\n";
    
    if ($count > 0) {
        $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM produits GROUP BY type");
        $types = $stmt->fetchAll();
        echo "Product types:\n";
        echo "أنواع المنتجات:\n";
        foreach ($types as $type) {
            echo "  - {$type['type']}: {$type['count']} items\n";
        }
    }
    echo "\n";
    
    // 3. Check foreign key constraints
    echo "3. Checking foreign key constraints...\n";
    echo "3. فحص قيود المفاتيح الخارجية...\n";
    
    $stmt = $pdo->query("
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND REFERENCED_TABLE_NAME IS NOT NULL
        AND REFERENCED_TABLE_NAME IN ('produits', 'livres')
        ORDER BY TABLE_NAME, CONSTRAINT_NAME
    ");
    $constraints = $stmt->fetchAll();
    
    echo "Foreign key constraints:\n";
    echo "قيود المفاتيح الخارجية:\n";
    foreach ($constraints as $constraint) {
        echo "  - {$constraint['TABLE_NAME']}.{$constraint['COLUMN_NAME']} -> {$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']}\n";
    }
    echo "\n";
    
    // 4. Check specific tables that should reference produits
    echo "4. Checking tables that should reference produits...\n";
    echo "4. فحص الجداول التي يجب أن تشير إلى produits...\n";
    
    $tablesToCheck = ['details_commande', 'panier', 'product_content_blocks'];
    
    foreach ($tablesToCheck as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table $table exists\n";
            
            // Check if it has foreign key to produits
            $stmt = $pdo->query("
                SELECT CONSTRAINT_NAME, REFERENCED_TABLE_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = '$table'
                AND REFERENCED_TABLE_NAME = 'produits'
            ");
            $fk = $stmt->fetch();
            
            if ($fk) {
                echo "  ✅ Has foreign key to produits: {$fk['CONSTRAINT_NAME']}\n";
            } else {
                echo "  ❌ No foreign key to produits found\n";
            }
            
            // Check record count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "  📊 Records: $count\n";
        } else {
            echo "❌ Table $table does not exist\n";
        }
    }
    echo "\n";
    
    // 5. Check indexes on produits table
    echo "5. Checking indexes on produits table...\n";
    echo "5. فحص الفهارس على جدول produits...\n";
    
    $stmt = $pdo->query("SHOW INDEX FROM produits");
    $indexes = $stmt->fetchAll();
    
    echo "Indexes on produits:\n";
    echo "الفهارس على produits:\n";
    foreach ($indexes as $index) {
        echo "  - {$index['Key_name']} on {$index['Column_name']}\n";
    }
    echo "\n";
    
    // 6. Check if livres table still exists
    echo "6. Checking if livres table still exists...\n";
    echo "6. فحص ما إذا كان جدول livres لا يزال موجوداً...\n";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'livres'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM livres");
        $count = $stmt->fetch()['count'];
        echo "⚠️ livres table still exists with $count records\n";
        echo "⚠️ جدول livres لا يزال موجوداً مع $count سجل\n";
        echo "   Consider dropping it after verifying migration is complete\n";
        echo "   فكر في حذفه بعد التأكد من اكتمال الترحيل\n";
    } else {
        echo "✅ livres table has been removed\n";
        echo "✅ تم حذف جدول livres\n";
    }
    echo "\n";
    
    // 7. Test a sample query
    echo "7. Testing sample queries...\n";
    echo "7. اختبار استعلامات عينة...\n";
    
    try {
        $stmt = $pdo->query("SELECT id, titre, type, prix FROM produits LIMIT 3");
        $products = $stmt->fetchAll();
        
        echo "Sample products:\n";
        echo "منتجات عينة:\n";
        foreach ($products as $product) {
            echo "  - ID: {$product['id']}, Title: {$product['titre']}, Type: {$product['type']}, Price: {$product['prix']}\n";
        }
    } catch (Exception $e) {
        echo "❌ Error querying produits: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 8. Summary
    echo "8. Migration Summary:\n";
    echo "8. ملخص الترحيل:\n";
    
    $issues = [];
    
    // Check critical components
    if (!empty($missingColumns)) {
        $issues[] = "Missing columns in produits table";
    }
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND REFERENCED_TABLE_NAME = 'produits'
    ");
    $fkCount = $stmt->fetch()['count'];
    
    if ($fkCount == 0) {
        $issues[] = "No foreign keys pointing to produits table";
    }
    
    if (empty($issues)) {
        echo "✅ Migration appears to be successful!\n";
        echo "✅ يبدو أن الترحيل نجح!\n";
        echo "All critical components are in place.\n";
        echo "جميع المكونات الحرجة في مكانها.\n";
    } else {
        echo "❌ Migration has issues:\n";
        echo "❌ الترحيل به مشاكل:\n";
        foreach ($issues as $issue) {
            echo "  - $issue\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n=== Verification Complete ===\n";
echo "=== اكتمل التحقق ===\n";
?>
