# متجر الكتب - Landing Page

## 📚 وصف المشروع

موقع متجر إلكتروني احترافي متعدد المنتجات مع واجهة مستخدم عربية كاملة، يتيح للزوار تصفح وشراء الكتب والحقائب والحواسيب المحمولة مع نظام سلة مشتريات متكامل ولوحة تحكم للإدارة.

## 🚀 المميزات

- واجهة مستخدم عربية بالكامل
- تصميم متجاوب مع جميع الأجهزة
- دعم متعدد المنتجات (كتب، حقائب، حواسيب محمولة)
- نظام سلة مشتريات متكامل
- نظام إدارة شامل للمنتجات والطلبات
- نظام دفع عبر CCP
- لوحة تحكم آمنة للمسؤولين
- إعدادات متجر قابلة للتخصيص
- نظام إحصائيات متقدم
- صفحات مخصصة للمنتجات مع:
  - معرض صور متقدم مع Swiper
  - محتوى غني قابل للتحرير
  - مشاركة على وسائل التواصل الاجتماعي
  - روابط SEO صديقة
  - واجهة إدارة سهلة الاستخدام

## 💻 المتطلبات التقنية

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)

## ⚙️ التثبيت

1. **إعداد قاعدة البيانات**

   ```sql
   -- إنشاء قاعدة البيانات
   CREATE DATABASE `Mossab-landing-page` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

   -- استيراد هيكل قاعدة البيانات
   -- قم بتنفيذ الأوامر SQL الموجودة في ملف database.sql
   ```

2. **تكوين الاتصال بقاعدة البيانات**

   - افتح ملف `php/config.php`
   - قم بتعديل معلومات الاتصال حسب إعدادات قاعدة البيانات الخاصة بك:
     ```php
     define('DB_HOST', 'localhost');
     define('DB_PORT', '3307');
     define('DB_NAME', 'Mossab-landing-page');
     define('DB_USER', 'root');
     define('DB_PASS', '');
     ```

3. **إنشاء حساب المسؤول**

   - قم بإنشاء حساب مسؤول جديد باستخدام SQL:
     ```sql
     INSERT INTO admins (nom_utilisateur, mot_de_passe)
     VALUES ('admin', '$2y$10$YOUR_HASHED_PASSWORD');
     ```

4. **تكوين مجلدات التخزين**
   - تأكد من أن المجلد `images/` قابل للكتابة
   - قم بإنشاء المجلدات التالية وجعلها قابلة للكتابة:
     - `uploads/receipts/`
     - `uploads/products/`
     - `uploads/product_galleries/`

## 📁 هيكل المشروع

```
├── admin/                 # ملفات لوحة التحكم
│   ├── css/              # تنسيقات لوحة التحكم
│   ├── js/               # سكربتات لوحة التحكم
│   ├── index.html        # الصفحة الرئيسية للوحة
│   └── login.html        # صفحة تسجيل الدخول
├── css/                   # ملفات CSS الرئيسية
├── images/                # صور المنتجات
├── js/                    # ملفات JavaScript
├── php/                   # ملفات PHP
│   ├── config.php         # إعدادات الاتصال
│   ├── functions.php      # الدوال المساعدة
│   ├── ProductLanding.php # إدارة صفحات المنتجات
│   └── api/              # واجهات برمجة التطبيق
├── uploads/               # مجلد تحميل الملفات
│   ├── receipts/         # إيصالات الدفع
│   ├── products/         # صور المنتجات
│   └── product_galleries/ # معارض صور المنتجات
├── product-landing.php    # قالب صفحة المنتج
└── index.html             # الصفحة الرئيسية
```

## 🔒 الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- حماية ضد هجمات SQL Injection
- التحقق من صحة المدخلات
- جلسات آمنة للمستخدمين
- حماية ضد هجمات XSS
- حماية ضد هجمات CSRF
- تحقق من امتدادات الملفات المرفوعة
- حماية مجلدات التحميل

## 📱 التوافق

- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب لجميع أحجام الشاشات
- دعم كامل للغة العربية (RTL)
- تحسين لشاشات الموبايل
- تحسين SEO للمنتجات

## 🛠️ التخصيص

- تخصيص الألوان والتصميم عبر CSS
- تعديل النصوص والمحتوى
- إضافة أنواع منتجات جديدة
- تخصيص إعدادات المتجر من لوحة التحكم:
  - اسم المتجر
  - معلومات الاتصال
  - العنوان
  - تخصيص صفحات المنتجات
  - إدارة معارض الصور
  - تحرير المحتوى الغني

## 📊 الإحصائيات والتقارير

- إحصائيات المبيعات
- تتبع المخزون
- تقارير الطلبات
- إحصائيات المنتجات
- تحليلات صفحات المنتجات

## 📞 الدعم

للمساعدة أو الاستفسارات، يرجى التواصل عبر:

- البريد الإلكتروني: <EMAIL>

## 📄 الترخيص

جميع الحقوق محفوظة © 2024

Oui, vous devez absolument configurer Google Sign-In dans la console Firebase ! L'erreur que vous voyez indique des problèmes de configuration et de CORS.

## 📋 **Guide de configuration Google Sign-In dans Firebase Console**

### 🔧 **Étape 1 : Activer Google Sign-In**

1. **Allez sur** [Firebase Console](https://console.firebase.google.com/)
2. **Sélectionnez** votre projet `landingpage-a7491`
3. **Cliquez** sur "Authentication" dans le menu de gauche
4. **Allez** dans l'onglet "Sign-in method"
5. **Cliquez** sur "Google" dans la liste des providers
6. **Activez** le toggle "Enable"
7. **Ajoutez** votre email comme "Project support email"
8. **Cliquez** "Save"

### 🌐 **Étape 2 : Configurer les domaines autorisés**

1. **Dans Authentication > Settings**
2. **Scrollez** jusqu'à "Authorized domains"
3. **Ajoutez** ces domaines :
   - `localhost`
   - `127.0.0.1`
   - Votre domaine de production (si applicable)

### 🔑 **Étape 3 : Configurer OAuth (Important !)**

1. **Allez** sur [Google Cloud Console](https://console.cloud.google.com/)
2. **Sélectionnez** le même projet
3. **APIs & Services > Credentials**
4. **Trouvez** votre "Web client" OAuth 2.0
5. **Cliquez** "Edit" (icône crayon)
6. **Dans "Authorized JavaScript origins"**, ajoutez :
   ```
   http://localhost:8000
   http://127.0.0.1:8000
   ```
7. **Dans "Authorized redirect URIs"**, ajoutez :
   ```
   http://localhost:8000/__/auth/handler
   ```
8. **Cliquez** "Save"

## 🔧 **Correction du problème CORS**

Créons un fichier de configuration pour résoudre les problèmes CORS :

## 🔧 **Page de test Google Sign-In**
