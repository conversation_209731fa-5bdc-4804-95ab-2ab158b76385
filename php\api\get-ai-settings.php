<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Prevent any output before JSON
ob_start();

try {
    // Database connection
    require_once '../config/database.php';
    
    $response = [
        'success' => true,
        'data' => [
            'ai_enabled' => true,
            'ai_provider' => 'openai',
            'ai_model' => 'gpt-3.5-turbo',
            'ai_features' => [
                'content_generation' => true,
                'image_generation' => false,
                'translation' => true,
                'optimization' => true
            ],
            'api_status' => 'active',
            'usage_limit' => 1000,
            'usage_current' => 45,
            'last_updated' => date('Y-m-d H:i:s')
        ],
        'message' => 'AI settings retrieved successfully'
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => 'Failed to retrieve AI settings',
        'message' => $e->getMessage(),
        'data' => [
            'ai_enabled' => false,
            'ai_provider' => null,
            'ai_model' => null,
            'ai_features' => [
                'content_generation' => false,
                'image_generation' => false,
                'translation' => false,
                'optimization' => false
            ],
            'api_status' => 'inactive',
            'usage_limit' => 0,
            'usage_current' => 0,
            'last_updated' => null
        ]
    ];
}

// Clear any output buffer and send JSON
ob_clean();
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;
?>
