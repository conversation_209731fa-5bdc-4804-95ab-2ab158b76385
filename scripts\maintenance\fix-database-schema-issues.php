<?php
/**
 * Fix Database Schema Issues
 * Addresses missing columns and schema problems causing API failures
 */

require_once 'php/config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح مشاكل قاعدة البيانات</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
        .sql-code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; }
        .table-info { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 إصلاح مشاكل قاعدة البيانات</h1>";

try {
    $pdo = getPDOConnection();
    
    echo "<div class='section'>";
    echo "<h2>1️⃣ فحص الجداول الحالية</h2>";
    
    // Check current table structure
    $tables_to_check = ['product_images', 'produits', 'landing_pages', 'users', 'stores'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $pdo->prepare("DESCRIBE {$table}");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div class='table-info'>";
            echo "<h4>📋 جدول {$table}:</h4>";
            echo "<ul>";
            foreach ($columns as $column) {
                echo "<li>{$column['Field']} - {$column['Type']}</li>";
            }
            echo "</ul>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في فحص جدول {$table}: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ إصلاح جدول product_images</h2>";
    
    // Check if file_size column exists
    $stmt = $pdo->prepare("SHOW COLUMNS FROM product_images LIKE 'file_size'");
    $stmt->execute();
    $file_size_exists = $stmt->fetch();
    
    if (!$file_size_exists) {
        echo "<div class='warning'>⚠️ العمود file_size غير موجود في جدول product_images</div>";
        
        // Add file_size column
        $sql = "ALTER TABLE product_images ADD COLUMN file_size BIGINT DEFAULT 0 AFTER image_url";
        echo "<div class='sql-code'>{$sql}</div>";
        
        try {
            $pdo->exec($sql);
            echo "<div class='success'>✅ تم إضافة العمود file_size بنجاح</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ فشل في إضافة العمود file_size: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='success'>✅ العمود file_size موجود بالفعل</div>";
    }
    
    // Check if alt_text column exists (useful for SEO)
    $stmt = $pdo->prepare("SHOW COLUMNS FROM product_images LIKE 'alt_text'");
    $stmt->execute();
    $alt_text_exists = $stmt->fetch();
    
    if (!$alt_text_exists) {
        echo "<div class='info'>ℹ️ إضافة عمود alt_text للصور</div>";
        
        $sql = "ALTER TABLE product_images ADD COLUMN alt_text VARCHAR(255) DEFAULT NULL AFTER file_size";
        echo "<div class='sql-code'>{$sql}</div>";
        
        try {
            $pdo->exec($sql);
            echo "<div class='success'>✅ تم إضافة العمود alt_text بنجاح</div>";
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ تحذير: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ إصلاح جدول landing_pages</h2>";
    
    // Check if store_id column exists in landing_pages
    $stmt = $pdo->prepare("SHOW COLUMNS FROM landing_pages LIKE 'store_id'");
    $stmt->execute();
    $store_id_exists = $stmt->fetch();
    
    if (!$store_id_exists) {
        echo "<div class='warning'>⚠️ العمود store_id غير موجود في جدول landing_pages</div>";
        
        $sql = "ALTER TABLE landing_pages ADD COLUMN store_id INT(11) DEFAULT NULL AFTER id";
        echo "<div class='sql-code'>{$sql}</div>";
        
        try {
            $pdo->exec($sql);
            echo "<div class='success'>✅ تم إضافة العمود store_id بنجاح</div>";
            
            // Add foreign key constraint
            $sql_fk = "ALTER TABLE landing_pages ADD CONSTRAINT fk_landing_pages_store FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE";
            echo "<div class='sql-code'>{$sql_fk}</div>";
            
            try {
                $pdo->exec($sql_fk);
                echo "<div class='success'>✅ تم إضافة القيد الخارجي بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='warning'>⚠️ تحذير في إضافة القيد الخارجي: " . $e->getMessage() . "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ فشل في إضافة العمود store_id: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='success'>✅ العمود store_id موجود بالفعل</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4️⃣ إصلاح جدول produits</h2>";
    
    // Check if store_id column exists in produits
    $stmt = $pdo->prepare("SHOW COLUMNS FROM produits LIKE 'store_id'");
    $stmt->execute();
    $store_id_produits_exists = $stmt->fetch();
    
    if (!$store_id_produits_exists) {
        echo "<div class='warning'>⚠️ العمود store_id غير موجود في جدول produits</div>";
        
        $sql = "ALTER TABLE produits ADD COLUMN store_id INT(11) DEFAULT NULL AFTER id";
        echo "<div class='sql-code'>{$sql}</div>";
        
        try {
            $pdo->exec($sql);
            echo "<div class='success'>✅ تم إضافة العمود store_id بنجاح</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ فشل في إضافة العمود store_id: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='success'>✅ العمود store_id موجود بالفعل</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5️⃣ تحديث البيانات الموجودة</h2>";
    
    // Update existing product images with estimated file sizes
    echo "<div class='info'>ℹ️ تحديث أحجام الملفات للصور الموجودة</div>";
    
    $stmt = $pdo->prepare("SELECT id, image_url FROM product_images WHERE file_size = 0 OR file_size IS NULL");
    $stmt->execute();
    $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $updated_count = 0;
    foreach ($images as $image) {
        $image_path = __DIR__ . '/' . ltrim($image['image_url'], '/');
        
        if (file_exists($image_path)) {
            $file_size = filesize($image_path);
            
            $update_stmt = $pdo->prepare("UPDATE product_images SET file_size = ? WHERE id = ?");
            $update_stmt->execute([$file_size, $image['id']]);
            $updated_count++;
        } else {
            // Estimate file size based on typical image sizes
            $estimated_size = 150000; // 150KB average
            
            $update_stmt = $pdo->prepare("UPDATE product_images SET file_size = ? WHERE id = ?");
            $update_stmt->execute([$estimated_size, $image['id']]);
            $updated_count++;
        }
    }
    
    echo "<div class='success'>✅ تم تحديث {$updated_count} صورة</div>";
    
    // Update landing pages with store_id if missing
    echo "<div class='info'>ℹ️ تحديث store_id لصفحات الهبوط</div>";
    
    $stmt = $pdo->prepare("
        UPDATE landing_pages lp
        JOIN produits p ON lp.produit_id = p.id
        SET lp.store_id = p.store_id
        WHERE lp.store_id IS NULL AND p.store_id IS NOT NULL
    ");
    $stmt->execute();
    $updated_landing_pages = $stmt->rowCount();
    
    echo "<div class='success'>✅ تم تحديث {$updated_landing_pages} صفحة هبوط</div>";
    
    // Update products with store_id if missing (assign to demo user's store)
    echo "<div class='info'>ℹ️ تحديث store_id للمنتجات</div>";
    
    $stmt = $pdo->prepare("SELECT store_id FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $demo_store = $stmt->fetch();
    
    if ($demo_store && $demo_store['store_id']) {
        $stmt = $pdo->prepare("UPDATE produits SET store_id = ? WHERE store_id IS NULL");
        $stmt->execute([$demo_store['store_id']]);
        $updated_products = $stmt->rowCount();
        
        echo "<div class='success'>✅ تم تحديث {$updated_products} منتج</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>6️⃣ اختبار SubscriptionLimits بعد الإصلاح</h2>";
    
    try {
        require_once 'php/SubscriptionLimits.php';
        
        $limitsManager = new SubscriptionLimits();
        
        // Get demo user
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $demoUser = $stmt->fetch();
        
        if ($demoUser) {
            $userId = $demoUser['id'];
            
            // Test the methods that were failing
            $usage = $limitsManager->getUserUsage($userId);
            echo "<div class='success'>✅ getUserUsage() يعمل بشكل صحيح</div>";
            
            $limits = $limitsManager->getUserLimits($userId);
            echo "<div class='success'>✅ getUserLimits() يعمل بشكل صحيح</div>";
            
            $limitsAndUsage = $limitsManager->getUserLimitsAndUsage($userId);
            echo "<div class='success'>✅ getUserLimitsAndUsage() يعمل بشكل صحيح</div>";
            
            echo "<div class='info'>";
            echo "<h4>📊 إحصائيات الاستخدام:</h4>";
            echo "<ul>";
            echo "<li>المنتجات: {$usage['products']}</li>";
            echo "<li>صفحات الهبوط: {$usage['landing_pages']}</li>";
            echo "<li>الفئات: {$usage['categories']}</li>";
            echo "<li>التخزين: {$usage['storage_mb']} ميجابايت</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='warning'>⚠️ المستخدم التجريبي غير موجود</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في اختبار SubscriptionLimits: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص الإصلاحات</h2>";
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إصلاح مشاكل قاعدة البيانات بنجاح!</h3>";
    echo "<ul>";
    echo "<li>✅ إضافة العمود file_size إلى جدول product_images</li>";
    echo "<li>✅ إضافة العمود alt_text للصور</li>";
    echo "<li>✅ التأكد من وجود العمود store_id في الجداول المطلوبة</li>";
    echo "<li>✅ تحديث البيانات الموجودة</li>";
    echo "<li>✅ اختبار فئة SubscriptionLimits</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔄 الخطوات التالية:</h4>";
    echo "<ul>";
    echo "<li>اختبار APIs للتأكد من عدم وجود أخطاء timeout</li>";
    echo "<li>فحص endpoints الخاصة بـ landing pages</li>";
    echo "<li>إصلاح مشاكل JavaScript في لوحة التحكم</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
