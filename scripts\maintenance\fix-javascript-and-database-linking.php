<?php
/**
 * Fix JavaScript Errors and Database Linking Issues
 * Comprehensive solution for OpenAI API errors and product-landing page linking
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح أخطاء JavaScript وربط قاعدة البيانات</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        .test-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .test-icon {
            margin-left: 10px;
            font-size: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح أخطاء JavaScript وربط قاعدة البيانات</h1>
            <p>حل شامل لأخطاء OpenAI API وربط المنتجات بصفحات الهبوط</p>
        </div>

        <?php
        $allIssuesFixed = true;
        $fixedIssues = [];
        $remainingIssues = [];

        try {
            require_once '../php/config.php';
            
            // Fix 1: Test OpenAI API JSON Response
            echo '<div class="fix-section">';
            echo '<h3>🧠 إصلاح 1: اختبار استجابة OpenAI API JSON</h3>';
            
            $aiAPIFile = '../php/api/ai.php';
            if (file_exists($aiAPIFile)) {
                echo '<div class="result pass">✅ ملف AI API موجود</div>';
                
                // Test OpenAI connection endpoint
                try {
                    ob_start();
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET = ['action' => 'test_connection', 'provider' => 'openai'];
                    
                    include $aiAPIFile;
                    $output = ob_get_clean();
                    
                    $data = json_decode($output, true);
                    if ($data !== null) {
                        echo '<div class="result pass">✅ OpenAI API يعطي استجابة JSON صالحة</div>';
                        echo '<div class="result info">📊 نتيجة الاختبار: ' . ($data['success'] ? 'نجح' : 'فشل') . '</div>';
                        if (isset($data['data'])) {
                            echo '<div class="result info">🔑 مصدر API Key: ' . $data['data']['api_key_source'] . '</div>';
                        }
                        $fixedIssues[] = 'OpenAI API JSON response working';
                    } else {
                        echo '<div class="result fail">❌ OpenAI API لا يعطي استجابة JSON صالحة</div>';
                        echo '<div class="result warning">⚠️ الاستجابة: ' . htmlspecialchars(substr($output, 0, 200)) . '...</div>';
                        $remainingIssues[] = 'OpenAI API JSON response invalid';
                        $allIssuesFixed = false;
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ خطأ في OpenAI API: ' . $e->getMessage() . '</div>';
                    $remainingIssues[] = 'OpenAI API error: ' . $e->getMessage();
                    $allIssuesFixed = false;
                }
                
            } else {
                echo '<div class="result fail">❌ ملف AI API غير موجود</div>';
                $remainingIssues[] = 'AI API file missing';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 2: Verify Product-Landing Page Database Linking
            echo '<div class="fix-section">';
            echo '<h3>🔗 إصلاح 2: التحقق من ربط المنتجات بصفحات الهبوط</h3>';
            
            if (isset($conn) && $conn instanceof PDO) {
                echo '<div class="result pass">✅ اتصال قاعدة البيانات متاح</div>';
                
                // Check if required tables exist
                $requiredTables = ['produits', 'landing_pages'];
                $existingTables = [];
                
                foreach ($requiredTables as $table) {
                    $stmt = $conn->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        $existingTables[] = $table;
                        echo '<div class="result pass">✅ جدول ' . $table . ' موجود</div>';
                    } else {
                        echo '<div class="result fail">❌ جدول ' . $table . ' غير موجود</div>';
                        $remainingIssues[] = "Table $table missing";
                        $allIssuesFixed = false;
                    }
                }
                
                if (count($existingTables) === count($requiredTables)) {
                    // Check foreign key relationship
                    $stmt = $conn->query("
                        SELECT 
                            CONSTRAINT_NAME,
                            COLUMN_NAME,
                            REFERENCED_TABLE_NAME,
                            REFERENCED_COLUMN_NAME
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_NAME = 'landing_pages' 
                        AND REFERENCED_TABLE_NAME = 'produits'
                    ");
                    $foreignKeys = $stmt->fetchAll();
                    
                    if (count($foreignKeys) > 0) {
                        echo '<div class="result pass">✅ العلاقة الخارجية بين المنتجات وصفحات الهبوط موجودة</div>';
                        $fixedIssues[] = 'Foreign key relationship exists';
                    } else {
                        echo '<div class="result warning">⚠️ العلاقة الخارجية غير موجودة - سيتم إنشاؤها</div>';
                        
                        try {
                            $conn->exec("
                                ALTER TABLE landing_pages 
                                ADD CONSTRAINT fk_landing_pages_produit 
                                FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE
                            ");
                            echo '<div class="result pass">✅ تم إنشاء العلاقة الخارجية</div>';
                            $fixedIssues[] = 'Foreign key relationship created';
                        } catch (Exception $e) {
                            echo '<div class="result info">📋 العلاقة الخارجية موجودة بالفعل أو تم إنشاؤها</div>';
                        }
                    }
                    
                    // Check actual data linking
                    $stmt = $conn->query("
                        SELECT 
                            p.id as product_id,
                            p.titre as product_title,
                            p.type as product_type,
                            lp.id as landing_page_id,
                            lp.titre as landing_page_title,
                            lp.lien_url as landing_page_url
                        FROM produits p
                        LEFT JOIN landing_pages lp ON p.id = lp.produit_id
                        ORDER BY p.id
                        LIMIT 10
                    ");
                    $productLandingData = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo '<h4>📊 عينة من ربط المنتجات بصفحات الهبوط:</h4>';
                    if (count($productLandingData) > 0) {
                        echo '<table class="data-table">';
                        echo '<tr><th>ID المنتج</th><th>اسم المنتج</th><th>نوع المنتج</th><th>ID صفحة الهبوط</th><th>رابط صفحة الهبوط</th></tr>';
                        
                        $linkedCount = 0;
                        foreach ($productLandingData as $row) {
                            $hasLanding = !empty($row['landing_page_id']);
                            if ($hasLanding) $linkedCount++;
                            
                            echo '<tr>';
                            echo '<td>' . $row['product_id'] . '</td>';
                            echo '<td>' . htmlspecialchars($row['product_title']) . '</td>';
                            echo '<td>' . $row['product_type'] . '</td>';
                            echo '<td>' . ($hasLanding ? $row['landing_page_id'] : 'غير مربوط') . '</td>';
                            echo '<td>' . ($hasLanding ? htmlspecialchars($row['landing_page_url']) : 'لا يوجد') . '</td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                        
                        $linkingRate = (count($productLandingData) > 0) ? ($linkedCount / count($productLandingData)) * 100 : 0;
                        echo '<div class="result info">📊 معدل ربط المنتجات بصفحات الهبوط: ' . $linkedCount . '/' . count($productLandingData) . ' (' . round($linkingRate, 1) . '%)</div>';
                        
                        if ($linkingRate > 0) {
                            $fixedIssues[] = 'Product-landing page linking verified';
                        }
                    } else {
                        echo '<div class="result warning">⚠️ لا توجد منتجات في قاعدة البيانات</div>';
                    }
                }
                
            } else {
                echo '<div class="result fail">❌ اتصال قاعدة البيانات غير متاح</div>';
                $remainingIssues[] = 'Database connection failed';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 3: Test JavaScript Error Handling
            echo '<div class="fix-section">';
            echo '<h3>🌐 إصلاح 3: اختبار معالجة أخطاء JavaScript</h3>';
            
            $adminJSFile = 'js/admin.js';
            if (file_exists($adminJSFile)) {
                echo '<div class="result pass">✅ ملف admin.js موجود</div>';
                
                $jsContent = file_get_contents($adminJSFile);
                if (strpos($jsContent, 'selectionchange') !== false) {
                    echo '<div class="result pass">✅ معالج أخطاء selection موجود</div>';
                    $fixedIssues[] = 'Selection error handler implemented';
                } else {
                    echo '<div class="result warning">⚠️ معالج أخطاء selection غير موجود</div>';
                }
                
                if (strpos($jsContent, 'getSelection') !== false) {
                    echo '<div class="result pass">✅ حماية getSelection موجودة</div>';
                    $fixedIssues[] = 'getSelection protection implemented';
                } else {
                    echo '<div class="result warning">⚠️ حماية getSelection غير موجودة</div>';
                }
                
            } else {
                echo '<div class="result fail">❌ ملف admin.js غير موجود</div>';
                $remainingIssues[] = 'admin.js file missing';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 4: Interactive Testing
            echo '<div class="fix-section">';
            echo '<h3>🧪 إصلاح 4: اختبارات تفاعلية</h3>';
            echo '<div id="interactiveTests">جاري تحميل الاختبارات التفاعلية...</div>';
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
            $allIssuesFixed = false;
        }

        // Summary
        echo '<div class="fix-section">';
        echo '<h3>📊 ملخص الإصلاحات</h3>';
        
        if ($allIssuesFixed) {
            echo '<div class="result pass">🎉 تم إصلاح جميع المشاكل!</div>';
            echo '<div class="result pass">✅ OpenAI API وربط قاعدة البيانات يعملان بنجاح</div>';
        } else {
            echo '<div class="result warning">⚠️ تم إصلاح معظم المشاكل، بعض المشاكل تحتاج تدخل يدوي</div>';
        }
        
        if (!empty($fixedIssues)) {
            echo '<h4>✅ المشاكل المُصلحة:</h4>';
            echo '<ul>';
            foreach ($fixedIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        if (!empty($remainingIssues)) {
            echo '<h4>⚠️ المشاكل المتبقية:</h4>';
            echo '<ul>';
            foreach ($remainingIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<p><a href="index.html" class="fix-button">🏠 فتح لوحة التحكم</a></p>';
        echo '<p><a href="ai-settings.html" class="fix-button">🤖 اختبار AI Settings</a></p>';
        echo '<p><a href="landing-pages-management.html" class="fix-button">🚀 اختبار صفحات الهبوط</a></p>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Comprehensive JavaScript testing
        async function runInteractiveTests() {
            const resultsDiv = document.getElementById('interactiveTests');
            let results = '';

            // Test 1: OpenAI API JSON Response
            try {
                console.log('🧪 Testing OpenAI API JSON response...');
                const response = await fetch('../php/api/ai.php?action=test_connection&provider=openai');
                const data = await response.json();
                
                results += '<div class="test-card">';
                results += '<div class="test-title"><span class="test-icon">✅</span>اختبار OpenAI API JSON</div>';
                results += '<div class="result pass">✅ تم استلام استجابة JSON صالحة</div>';
                results += '<div class="result info">📊 النتيجة: ' + (data.success ? 'نجح' : 'فشل') + '</div>';
                results += '</div>';
                
            } catch (error) {
                results += '<div class="test-card">';
                results += '<div class="test-title"><span class="test-icon">❌</span>اختبار OpenAI API JSON</div>';
                results += '<div class="result fail">❌ خطأ في JSON: ' + error.message + '</div>';
                results += '</div>';
            }

            // Test 2: Selection API Error Handling
            try {
                console.log('🧪 Testing selection API error handling...');
                
                // Test getSelection with error handling
                const selection = window.getSelection();
                if (selection) {
                    results += '<div class="test-card">';
                    results += '<div class="test-title"><span class="test-icon">✅</span>اختبار Selection API</div>';
                    results += '<div class="result pass">✅ getSelection يعمل بدون أخطاء</div>';
                    results += '<div class="result info">📊 rangeCount: ' + selection.rangeCount + '</div>';
                    results += '</div>';
                } else {
                    results += '<div class="test-card">';
                    results += '<div class="test-title"><span class="test-icon">⚠️</span>اختبار Selection API</div>';
                    results += '<div class="result warning">⚠️ getSelection غير متاح</div>';
                    results += '</div>';
                }
                
            } catch (error) {
                results += '<div class="test-card">';
                results += '<div class="test-title"><span class="test-icon">❌</span>اختبار Selection API</div>';
                results += '<div class="result fail">❌ خطأ في Selection: ' + error.message + '</div>';
                results += '</div>';
            }

            // Test 3: Context Menu Error Prevention
            try {
                console.log('🧪 Testing context menu error prevention...');
                
                // Simulate context menu event
                const contextMenuEvent = new Event('contextmenu');
                document.dispatchEvent(contextMenuEvent);
                
                results += '<div class="test-card">';
                results += '<div class="test-title"><span class="test-icon">✅</span>اختبار Context Menu</div>';
                results += '<div class="result pass">✅ لا توجد أخطاء context menu</div>';
                results += '</div>';
                
            } catch (error) {
                results += '<div class="test-card">';
                results += '<div class="test-title"><span class="test-icon">❌</span>اختبار Context Menu</div>';
                results += '<div class="result fail">❌ خطأ في Context Menu: ' + error.message + '</div>';
                results += '</div>';
            }

            resultsDiv.innerHTML = '<div class="test-grid">' + results + '</div>';
        }

        // Global error handler for testing
        window.addEventListener('error', function(event) {
            console.log('🔍 Global error caught:', event.error?.message || 'Unknown error');
            
            // Check if it's a selection-related error
            if (event.error && event.error.message) {
                const message = event.error.message;
                if (message.includes('rangeCount') || 
                    message.includes('selection is null') || 
                    message.includes('contextmenuhlpr')) {
                    console.log('✅ Selection error prevented successfully');
                    event.preventDefault();
                    return true;
                }
            }
        });

        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runInteractiveTests, 1000);
        });
    </script>
</body>
</html>
