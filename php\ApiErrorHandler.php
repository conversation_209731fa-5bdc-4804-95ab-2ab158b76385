<?php
/**
 * API Error Handler and Response Standardization
 * Comprehensive error handling system for Mossaab Landing Page APIs
 * 
 * Addresses critical JSON parsing errors:
 * - "Failed to execute 'json' on 'Response': Unexpected end of JSON input"
 * - "JSON.parse: unexpected character at line 1 column 1"
 */

class ApiErrorHandler
{
    private static $logFile = 'logs/api_errors.log';
    private static $requestLogFile = 'logs/api_requests.log';
    
    /**
     * Initialize error handler
     */
    public static function init()
    {
        // Ensure logs directory exists
        $logDir = dirname(__DIR__ . '/' . self::$logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Set error handlers
        set_error_handler([self::class, 'handleError']);
        set_exception_handler([self::class, 'handleException']);
        register_shutdown_function([self::class, 'handleShutdown']);
        
        // Start output buffering to catch any unexpected output
        ob_start();
    }
    
    /**
     * Send standardized JSON response with comprehensive error handling
     */
    public static function sendResponse($success, $data = null, $message = '', $httpCode = 200, $errorCode = '')
    {
        // Clean any previous output that might corrupt JSON
        if (ob_get_level()) {
            $previousOutput = ob_get_clean();
            if (!empty(trim($previousOutput))) {
                self::logError("Unexpected output before JSON response: " . $previousOutput);
            }
        }
        
        // Set proper headers
        http_response_code($httpCode);
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        
        // Build response
        $response = [
            'success' => $success,
            'timestamp' => date('c'),
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
            'endpoint' => $_SERVER['REQUEST_URI'] ?? 'UNKNOWN'
        ];

        if ($success) {
            $response['data'] = $data;
            if ($message) {
                $response['message'] = $message;
            }
        } else {
            $response['error'] = [
                'message' => $message,
                'code' => $errorCode,
                'http_code' => $httpCode
            ];
            
            // Log error
            self::logError("API Error: $errorCode - $message", [
                'endpoint' => $_SERVER['REQUEST_URI'] ?? 'UNKNOWN',
                'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
                'data' => $data
            ]);
        }

        // Log request/response for debugging
        self::logRequest($_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN', $_SERVER['REQUEST_URI'] ?? 'UNKNOWN', $response);

        // Encode JSON with proper flags to prevent corruption
        $json = json_encode($response, JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_PRETTY_PRINT);
        
        if ($json === false) {
            // JSON encoding failed - send emergency response
            $emergencyResponse = [
                'success' => false,
                'error' => [
                    'message' => 'JSON encoding failed: ' . json_last_error_msg(),
                    'code' => 'JSON_ENCODING_ERROR',
                    'http_code' => 500
                ],
                'timestamp' => date('c')
            ];
            
            self::logError("JSON encoding failed: " . json_last_error_msg(), $response);
            echo json_encode($emergencyResponse, JSON_UNESCAPED_UNICODE);
        } else {
            echo $json;
        }
        
        exit;
    }
    
    /**
     * Send success response
     */
    public static function sendSuccess($data = null, $message = '')
    {
        self::sendResponse(true, $data, $message, 200);
    }
    
    /**
     * Send error response
     */
    public static function sendError($message, $httpCode = 400, $errorCode = '', $data = null)
    {
        self::sendResponse(false, $data, $message, $httpCode, $errorCode);
    }
    
    /**
     * Validate and parse JSON input
     */
    public static function getJsonInput()
    {
        $input = file_get_contents('php://input');
        
        if (empty($input)) {
            // Fallback to POST data
            return $_POST;
        }
        
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            self::sendError(
                'بيانات JSON غير صالحة: ' . json_last_error_msg(),
                400,
                'INVALID_JSON_INPUT',
                ['raw_input' => substr($input, 0, 500)] // Log first 500 chars for debugging
            );
            return false;
        }
        
        return $data;
    }
    
    /**
     * Handle PHP errors
     */
    public static function handleError($severity, $message, $file, $line)
    {
        // Don't handle suppressed errors
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorInfo = [
            'severity' => $severity,
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
        ];
        
        self::logError("PHP Error: $message", $errorInfo);
        
        // For fatal errors, send JSON error response
        if ($severity === E_ERROR || $severity === E_CORE_ERROR || $severity === E_COMPILE_ERROR) {
            self::sendError('خطأ داخلي في الخادم', 500, 'INTERNAL_SERVER_ERROR');
        }
        
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public static function handleException($exception)
    {
        $errorInfo = [
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ];
        
        self::logError("Uncaught Exception: " . $exception->getMessage(), $errorInfo);
        
        self::sendError(
            'خطأ غير متوقع: ' . $exception->getMessage(),
            500,
            'UNCAUGHT_EXCEPTION'
        );
    }
    
    /**
     * Handle shutdown errors
     */
    public static function handleShutdown()
    {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            self::logError("Fatal Error: " . $error['message'], $error);
            
            // Clean any output buffer
            if (ob_get_level()) {
                ob_clean();
            }
            
            self::sendError(
                'خطأ فادح في الخادم',
                500,
                'FATAL_ERROR'
            );
        }
    }
    
    /**
     * Log error to file
     */
    private static function logError($message, $context = [])
    {
        $logEntry = [
            'timestamp' => date('c'),
            'message' => $message,
            'context' => $context,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'UNKNOWN',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN'
        ];
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
        
        // Ensure log directory exists
        $logDir = dirname(__DIR__ . '/' . self::$logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents(__DIR__ . '/' . self::$logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Log API request/response for debugging
     */
    private static function logRequest($method, $uri, $response)
    {
        $logEntry = [
            'timestamp' => date('c'),
            'method' => $method,
            'uri' => $uri,
            'response_success' => $response['success'] ?? false,
            'response_size' => strlen(json_encode($response)),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN'
        ];
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
        
        // Ensure log directory exists
        $logDir = dirname(__DIR__ . '/' . self::$requestLogFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents(__DIR__ . '/' . self::$requestLogFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Validate required fields
     */
    public static function validateRequired($data, $requiredFields)
    {
        $missing = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || (is_string($data[$field]) && trim($data[$field]) === '')) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            self::sendError(
                'الحقول التالية مطلوبة: ' . implode(', ', $missing),
                400,
                'MISSING_REQUIRED_FIELDS',
                ['missing_fields' => $missing]
            );
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if request method is allowed
     */
    public static function checkMethod($allowedMethods)
    {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        if (!in_array($method, $allowedMethods)) {
            self::sendError(
                'طريقة الطلب غير مسموحة',
                405,
                'METHOD_NOT_ALLOWED',
                ['allowed_methods' => $allowedMethods, 'current_method' => $method]
            );
            return false;
        }
        
        return true;
    }
}
