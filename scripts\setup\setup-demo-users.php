<?php
require_once '../php/config/database.php';

try {
    $pdo = getPDOConnection();
    
    // Create admins table if it doesn't exist
    $createAdminsTable = "
    CREATE TABLE IF NOT EXISTS admins (
        id INT PRIMARY KEY AUTO_INCREMENT,
        nom_utilisateur VARCHAR(50) NOT NULL UNIQUE,
        mot_de_passe VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        nom_complet VARCHAR(100),
        role VARCHAR(50) DEFAULT 'admin',
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createAdminsTable);
    echo "✅ Table 'admins' créée avec succès<br>";
    
    // Demo admin users
    $demoUsers = [
        [
            'username' => 'admin',
            'password' => 'admin123',
            'email' => '<EMAIL>',
            'full_name' => 'مدير النظام الرئيسي',
            'role' => 'super_admin'
        ],
        [
            'username' => 'mossaab',
            'password' => 'mossaab2024',
            'email' => '<EMAIL>',
            'full_name' => 'مصعب - مالك المتجر',
            'role' => 'owner'
        ],
        [
            'username' => 'manager',
            'password' => 'manager123',
            'email' => '<EMAIL>',
            'full_name' => 'مدير المتجر',
            'role' => 'manager'
        ],
        [
            'username' => 'demo',
            'password' => 'demo123',
            'email' => '<EMAIL>',
            'full_name' => 'حساب تجريبي',
            'role' => 'demo'
        ]
    ];
    
    // Insert demo users
    $insertStmt = $pdo->prepare("
        INSERT INTO admins (nom_utilisateur, mot_de_passe, email, nom_complet, role) 
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        mot_de_passe = VALUES(mot_de_passe),
        email = VALUES(email),
        nom_complet = VALUES(nom_complet),
        role = VALUES(role)
    ");
    
    foreach ($demoUsers as $user) {
        $hashedPassword = password_hash($user['password'], PASSWORD_DEFAULT);
        $insertStmt->execute([
            $user['username'],
            $hashedPassword,
            $user['email'],
            $user['full_name'],
            $user['role']
        ]);
        echo "✅ Utilisateur '{$user['username']}' créé/mis à jour<br>";
    }
    
    echo "<br><h2>🎉 Configuration terminée avec succès!</h2>";
    echo "<h3>Comptes de démonstration créés :</h3>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>Nom d'utilisateur</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>Mot de passe</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>Rôle</th>";
    echo "<th style='padding: 10px; border: 1px solid #ddd;'>Description</th>";
    echo "</tr>";
    
    $descriptions = [
        'admin' => 'Administrateur principal avec tous les privilèges',
        'mossaab' => 'Propriétaire du magasin - accès complet',
        'manager' => 'Gestionnaire du magasin - accès limité',
        'demo' => 'Compte de démonstration - accès en lecture seule'
    ];
    
    foreach ($demoUsers as $user) {
        echo "<tr>";
        echo "<td style='padding: 10px; border: 1px solid #ddd; font-weight: bold;'>{$user['username']}</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd; font-family: monospace;'>{$user['password']}</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd;'>{$user['role']}</td>";
        echo "<td style='padding: 10px; border: 1px solid #ddd;'>{$descriptions[$user['username']]}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    echo "<h3>🔗 Liens utiles :</h3>";
    echo "<ul>";
    echo "<li><a href='login.html' target='_blank'>Page de connexion</a></li>";
    echo "<li><a href='index.html' target='_blank'>Tableau de bord admin</a></li>";
    echo "<li><a href='../index.html' target='_blank'>Site principal</a></li>";
    echo "</ul>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>Instructions :</strong><br>";
    echo "1. Utilisez n'importe quel compte ci-dessus pour vous connecter<br>";
    echo "2. Allez sur <a href='login.html'>login.html</a><br>";
    echo "3. Entrez le nom d'utilisateur et le mot de passe<br>";
    echo "4. Vous serez redirigé vers le tableau de bord admin";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Erreur : " . $e->getMessage();
}
?>
