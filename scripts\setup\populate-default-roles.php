<?php

/**
 * Default Roles and Permissions Populator
 * مُعبئ الأدوار والصلاحيات الافتراضية
 *
 * This script populates the role management system with default data
 */

session_start();
require_once '../config/config.php';

// Set execution time limit
set_time_limit(300);

// HTML header
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة الأدوار والصلاحيات الافتراضية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }

        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .success {
            border-left-color: #28a745;
            background: #d4edda;
            color: #155724;
        }

        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
            color: #856404;
        }

        .info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
            color: #0c5460;
        }

        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            font-size: 12px;
        }

        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        th,
        td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: right;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎭 إضافة الأدوار والصلاحيات الافتراضية</h1>
            <p>تعبئة النظام بالأدوار والصلاحيات الأساسية</p>
        </div>

        <?php

        class DefaultRolesPopulator
        {
            private $pdo;
            private $results = [];

            public function __construct()
            {
                try {
                    $dbConfig = Config::getDbConfig();
                    $dsn = sprintf(
                        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                        $dbConfig['host'],
                        $dbConfig['port'],
                        $dbConfig['database']
                    );

                    $options = [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ];

                    $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
                    $this->log('success', 'تم الاتصال بقاعدة البيانات بنجاح');
                } catch (Exception $e) {
                    $this->log('error', 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage());
                    throw $e;
                }
            }

            private function log($type, $message, $details = null)
            {
                $this->results[] = [
                    'type' => $type,
                    'message' => $message,
                    'details' => $details,
                    'timestamp' => date('Y-m-d H:i:s')
                ];

                // Output immediately for real-time feedback
                $class = $type;
                echo "<div class='step $class'>";
                echo "<strong>" . date('H:i:s') . "</strong> - $message";
                if ($details) {
                    echo "<div class='code'>$details</div>";
                }
                echo "</div>";
                flush();
            }

            public function populateDefaultPermissions()
            {
                $this->log('info', '🔐 إضافة الصلاحيات الافتراضية...');

                $permissions = [
                    // User Management Permissions
                    ['users.view', 'عرض المستخدمين', 'View Users', 'عرض قائمة المستخدمين', 'View users list', 'users'],
                    ['users.create', 'إنشاء مستخدمين', 'Create Users', 'إنشاء مستخدمين جدد', 'Create new users', 'users'],
                    ['users.edit', 'تعديل المستخدمين', 'Edit Users', 'تعديل بيانات المستخدمين', 'Edit user data', 'users'],
                    ['users.delete', 'حذف المستخدمين', 'Delete Users', 'حذف المستخدمين', 'Delete users', 'users'],
                    ['users.manage_roles', 'إدارة أدوار المستخدمين', 'Manage User Roles', 'تعيين وإدارة أدوار المستخدمين', 'Assign and manage user roles', 'users'],

                    // Store Management Permissions
                    ['stores.view', 'عرض المتاجر', 'View Stores', 'عرض قائمة المتاجر', 'View stores list', 'stores'],
                    ['stores.create', 'إنشاء متاجر', 'Create Stores', 'إنشاء متاجر جديدة', 'Create new stores', 'stores'],
                    ['stores.edit', 'تعديل المتاجر', 'Edit Stores', 'تعديل بيانات المتاجر', 'Edit store data', 'stores'],
                    ['stores.delete', 'حذف المتاجر', 'Delete Stores', 'حذف المتاجر', 'Delete stores', 'stores'],
                    ['stores.manage_settings', 'إدارة إعدادات المتاجر', 'Manage Store Settings', 'إدارة إعدادات المتاجر', 'Manage store settings', 'stores'],

                    // Product Management Permissions
                    ['products.view', 'عرض المنتجات', 'View Products', 'عرض قائمة المنتجات', 'View products list', 'products'],
                    ['products.create', 'إنشاء منتجات', 'Create Products', 'إنشاء منتجات جديدة', 'Create new products', 'products'],
                    ['products.edit', 'تعديل المنتجات', 'Edit Products', 'تعديل بيانات المنتجات', 'Edit product data', 'products'],
                    ['products.delete', 'حذف المنتجات', 'Delete Products', 'حذف المنتجات', 'Delete products', 'products'],

                    // System Administration Permissions
                    ['admin.dashboard', 'لوحة الإدارة', 'Admin Dashboard', 'الوصول للوحة الإدارة', 'Access admin dashboard', 'admin'],
                    ['admin.settings', 'إعدادات النظام', 'System Settings', 'إدارة إعدادات النظام', 'Manage system settings', 'admin'],
                    ['admin.roles', 'إدارة الأدوار', 'Manage Roles', 'إدارة الأدوار والصلاحيات', 'Manage roles and permissions', 'admin'],
                    ['admin.logs', 'عرض السجلات', 'View Logs', 'عرض سجلات النظام', 'View system logs', 'admin'],

                    // Content Management Permissions
                    ['content.view', 'عرض المحتوى', 'View Content', 'عرض المحتوى', 'View content', 'content'],
                    ['content.create', 'إنشاء محتوى', 'Create Content', 'إنشاء محتوى جديد', 'Create new content', 'content'],
                    ['content.edit', 'تعديل المحتوى', 'Edit Content', 'تعديل المحتوى', 'Edit content', 'content'],
                    ['content.delete', 'حذف المحتوى', 'Delete Content', 'حذف المحتوى', 'Delete content', 'content'],

                    // Reports and Analytics
                    ['reports.view', 'عرض التقارير', 'View Reports', 'عرض التقارير والإحصائيات', 'View reports and analytics', 'reports'],
                    ['reports.export', 'تصدير التقارير', 'Export Reports', 'تصدير التقارير', 'Export reports', 'reports']
                ];

                try {
                    $insertPermissionSQL = "
                INSERT IGNORE INTO permissions (name, display_name_ar, display_name_en, description_ar, description_en, category)
                VALUES (?, ?, ?, ?, ?, ?)
            ";

                    $stmt = $this->pdo->prepare($insertPermissionSQL);
                    $addedCount = 0;

                    foreach ($permissions as $permission) {
                        $stmt->execute($permission);
                        if ($stmt->rowCount() > 0) {
                            $addedCount++;
                        }
                    }

                    $this->log('success', "✅ تم إضافة $addedCount صلاحية من أصل " . count($permissions));

                    return true;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في إضافة الصلاحيات: ' . $e->getMessage());
                    throw $e;
                }
            }

            public function populateDefaultRoles()
            {
                $this->log('info', '👥 إضافة الأدوار الافتراضية...');

                $roles = [
                    [
                        'name' => 'super_admin',
                        'display_name_ar' => 'مدير عام',
                        'display_name_en' => 'Super Administrator',
                        'description_ar' => 'مدير عام للنظام مع جميع الصلاحيات',
                        'description_en' => 'Super administrator with all permissions',
                        'level' => 1,
                        'color' => '#dc3545',
                        'icon' => 'fas fa-crown',
                        'is_default' => 0
                    ],
                    [
                        'name' => 'admin',
                        'display_name_ar' => 'مدير',
                        'display_name_en' => 'Administrator',
                        'description_ar' => 'مدير النظام مع صلاحيات إدارية',
                        'description_en' => 'System administrator with admin permissions',
                        'level' => 2,
                        'color' => '#fd7e14',
                        'icon' => 'fas fa-user-shield',
                        'is_default' => 0
                    ],
                    [
                        'name' => 'editor',
                        'display_name_ar' => 'محرر',
                        'display_name_en' => 'Editor',
                        'description_ar' => 'محرر المحتوى والمنتجات',
                        'description_en' => 'Content and product editor',
                        'level' => 3,
                        'color' => '#20c997',
                        'icon' => 'fas fa-edit',
                        'is_default' => 0
                    ],
                    [
                        'name' => 'user',
                        'display_name_ar' => 'مستخدم',
                        'display_name_en' => 'User',
                        'description_ar' => 'مستخدم عادي مع صلاحيات أساسية',
                        'description_en' => 'Regular user with basic permissions',
                        'level' => 4,
                        'color' => '#007bff',
                        'icon' => 'fas fa-user',
                        'is_default' => 1
                    ]
                ];

                try {
                    $insertRoleSQL = "
                INSERT IGNORE INTO user_roles (name, display_name_ar, display_name_en, description_ar, description_en, level, color, icon, is_default)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";

                    $stmt = $this->pdo->prepare($insertRoleSQL);
                    $addedCount = 0;

                    foreach ($roles as $role) {
                        $stmt->execute([
                            $role['name'],
                            $role['display_name_ar'],
                            $role['display_name_en'],
                            $role['description_ar'],
                            $role['description_en'],
                            $role['level'],
                            $role['color'],
                            $role['icon'],
                            $role['is_default']
                        ]);

                        if ($stmt->rowCount() > 0) {
                            $addedCount++;
                            $this->log('success', "✅ تم إضافة دور: {$role['display_name_ar']} ({$role['name']})");
                        }
                    }

                    $this->log('success', "✅ تم إضافة $addedCount دور من أصل " . count($roles));

                    return true;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في إضافة الأدوار: ' . $e->getMessage());
                    throw $e;
                }
            }

            public function assignRolePermissions()
            {
                $this->log('info', '🔗 ربط الأدوار بالصلاحيات...');

                try {
                    // Get role and permission IDs
                    $rolesStmt = $this->pdo->query("SELECT id, name FROM user_roles");
                    $roles = [];
                    while ($role = $rolesStmt->fetch()) {
                        $roles[$role['name']] = $role['id'];
                    }

                    $permissionsStmt = $this->pdo->query("SELECT id, name FROM permissions");
                    $permissions = [];
                    while ($permission = $permissionsStmt->fetch()) {
                        $permissions[$permission['name']] = $permission['id'];
                    }

                    // Define role-permission assignments
                    $rolePermissions = [
                        'super_admin' => array_keys($permissions), // Super admin gets all permissions
                        'admin' => [
                            'users.view',
                            'users.create',
                            'users.edit',
                            'users.manage_roles',
                            'stores.view',
                            'stores.create',
                            'stores.edit',
                            'stores.manage_settings',
                            'products.view',
                            'products.create',
                            'products.edit',
                            'products.delete',
                            'admin.dashboard',
                            'admin.settings',
                            'admin.logs',
                            'content.view',
                            'content.create',
                            'content.edit',
                            'content.delete',
                            'reports.view',
                            'reports.export'
                        ],
                        'editor' => [
                            'products.view',
                            'products.create',
                            'products.edit',
                            'content.view',
                            'content.create',
                            'content.edit',
                            'stores.view',
                            'stores.edit',
                            'reports.view'
                        ],
                        'user' => [
                            'products.view',
                            'content.view',
                            'stores.view'
                        ]
                    ];

                    $insertRolePermissionSQL = "
                INSERT IGNORE INTO role_permissions (role_id, permission_id, granted)
                VALUES (?, ?, 1)
            ";

                    $stmt = $this->pdo->prepare($insertRolePermissionSQL);
                    $totalAssigned = 0;

                    foreach ($rolePermissions as $roleName => $rolePerms) {
                        if (!isset($roles[$roleName])) {
                            $this->log('warning', "⚠️ الدور $roleName غير موجود");
                            continue;
                        }

                        $roleId = $roles[$roleName];
                        $assignedCount = 0;

                        foreach ($rolePerms as $permName) {
                            if (!isset($permissions[$permName])) {
                                $this->log('warning', "⚠️ الصلاحية $permName غير موجودة");
                                continue;
                            }

                            $permissionId = $permissions[$permName];
                            $stmt->execute([$roleId, $permissionId]);

                            if ($stmt->rowCount() > 0) {
                                $assignedCount++;
                                $totalAssigned++;
                            }
                        }

                        $this->log('success', "✅ تم ربط $assignedCount صلاحية بالدور: $roleName");
                    }

                    $this->log('success', "✅ تم ربط $totalAssigned صلاحية إجمالية");

                    return true;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في ربط الأدوار بالصلاحيات: ' . $e->getMessage());
                    throw $e;
                }
            }

            public function updateExistingUsers()
            {
                $this->log('info', '👤 تحديث المستخدمين الموجودين...');

                try {
                    // Get default user role ID
                    $stmt = $this->pdo->prepare("SELECT id FROM user_roles WHERE name = 'user' LIMIT 1");
                    $stmt->execute();
                    $defaultRole = $stmt->fetch();

                    if (!$defaultRole) {
                        $this->log('error', '❌ لم يتم العثور على الدور الافتراضي للمستخدمين');
                        return false;
                    }

                    $defaultRoleId = $defaultRole['id'];

                    // Update users without role_id
                    $updateUsersSQL = "UPDATE users SET role_id = ? WHERE role_id IS NULL OR role_id = 0";
                    $stmt = $this->pdo->prepare($updateUsersSQL);
                    $stmt->execute([$defaultRoleId]);

                    $updatedCount = $stmt->rowCount();
                    $this->log('success', "✅ تم تحديث $updatedCount مستخدم بالدور الافتراضي");

                    // Create role assignments for existing users
                    $insertAssignmentSQL = "
                INSERT IGNORE INTO user_role_assignments (user_id, role_id, assigned_at)
                SELECT id, role_id, NOW() FROM users WHERE role_id IS NOT NULL
            ";

                    $this->pdo->exec($insertAssignmentSQL);
                    $this->log('success', '✅ تم إنشاء تعيينات الأدوار للمستخدمين الموجودين');

                    return true;
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في تحديث المستخدمين: ' . $e->getMessage());
                    throw $e;
                }
            }

            public function populateAllDefaults()
            {
                $this->log('info', '🚀 بدء تعبئة جميع البيانات الافتراضية...');

                try {
                    $this->populateDefaultPermissions();
                    $this->populateDefaultRoles();
                    $this->assignRolePermissions();
                    $this->updateExistingUsers();

                    $this->log('success', '🎉 تم إكمال تعبئة جميع البيانات الافتراضية بنجاح!');

                    return true;
                } catch (Exception $e) {
                    $this->log('error', '❌ فشل في تعبئة البيانات الافتراضية: ' . $e->getMessage());
                    return false;
                }
            }

            public function showCurrentData()
            {
                $this->log('info', '📊 عرض البيانات الحالية...');

                try {
                    // Show roles
                    $rolesStmt = $this->pdo->query("SELECT * FROM user_roles ORDER BY level");
                    $roles = $rolesStmt->fetchAll();

                    echo "<table>";
                    echo "<tr><th>الدور</th><th>الاسم بالعربية</th><th>المستوى</th><th>اللون</th><th>افتراضي</th></tr>";
                    foreach ($roles as $role) {
                        echo "<tr>";
                        echo "<td>{$role['name']}</td>";
                        echo "<td>{$role['display_name_ar']}</td>";
                        echo "<td>{$role['level']}</td>";
                        echo "<td><span style='color: {$role['color']}'>{$role['color']}</span></td>";
                        echo "<td>" . ($role['is_default'] ? 'نعم' : 'لا') . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";

                    // Show permissions count
                    $permStmt = $this->pdo->query("SELECT category, COUNT(*) as count FROM permissions GROUP BY category");
                    $permCounts = $permStmt->fetchAll();

                    echo "<h4>الصلاحيات حسب الفئة:</h4>";
                    echo "<table>";
                    echo "<tr><th>الفئة</th><th>عدد الصلاحيات</th></tr>";
                    foreach ($permCounts as $count) {
                        echo "<tr><td>{$count['category']}</td><td>{$count['count']}</td></tr>";
                    }
                    echo "</table>";
                } catch (Exception $e) {
                    $this->log('error', 'خطأ في عرض البيانات: ' . $e->getMessage());
                }
            }
        }

        // Execute population if requested
        if (isset($_GET['action']) && $_GET['action'] === 'populate') {
            try {
                $populator = new DefaultRolesPopulator();
                $success = $populator->populateAllDefaults();

                if ($success) {
                    echo "<div class='step success'>";
                    echo "<h3>✅ تم إكمال تعبئة البيانات الافتراضية بنجاح!</h3>";
                    echo "<p>تم إنشاء الأدوار والصلاحيات وربطها ببعضها البعض.</p>";
                    echo "<a href='role-management-apis.php' class='btn btn-primary'>إنشاء APIs إدارة الأدوار</a>";
                    echo "</div>";

                    $populator->showCurrentData();
                }
            } catch (Exception $e) {
                echo "<div class='step error'>";
                echo "<h3>❌ فشل في تعبئة البيانات</h3>";
                echo "<p>خطأ: " . $e->getMessage() . "</p>";
                echo "</div>";
            }
        } elseif (isset($_GET['action']) && $_GET['action'] === 'show') {
            try {
                $populator = new DefaultRolesPopulator();
                $populator->showCurrentData();
            } catch (Exception $e) {
                echo "<div class='step error'>";
                echo "<h3>❌ خطأ في عرض البيانات</h3>";
                echo "<p>خطأ: " . $e->getMessage() . "</p>";
                echo "</div>";
            }
        } else {
            // Show population interface
        ?>
            <div class="step info">
                <h3>📋 البيانات الافتراضية التي سيتم إضافتها</h3>

                <h4>الأدوار:</h4>
                <ul>
                    <li><strong>مدير عام (super_admin)</strong> - جميع الصلاحيات</li>
                    <li><strong>مدير (admin)</strong> - صلاحيات إدارية</li>
                    <li><strong>محرر (editor)</strong> - تحرير المحتوى والمنتجات</li>
                    <li><strong>مستخدم (user)</strong> - صلاحيات أساسية (افتراضي)</li>
                </ul>

                <h4>فئات الصلاحيات:</h4>
                <ul>
                    <li><strong>إدارة المستخدمين</strong> - عرض، إنشاء، تعديل، حذف</li>
                    <li><strong>إدارة المتاجر</strong> - عرض، إنشاء، تعديل، حذف، إعدادات</li>
                    <li><strong>إدارة المنتجات</strong> - عرض، إنشاء، تعديل، حذف</li>
                    <li><strong>إدارة النظام</strong> - لوحة الإدارة، الإعدادات، الأدوار، السجلات</li>
                    <li><strong>إدارة المحتوى</strong> - عرض، إنشاء، تعديل، حذف</li>
                    <li><strong>التقارير</strong> - عرض، تصدير</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="?action=populate" class="btn btn-primary">
                    🎭 إضافة البيانات الافتراضية
                </a>
                <a href="?action=show" class="btn btn-success">
                    📊 عرض البيانات الحالية
                </a>
                <a href="create-role-management-schema.php" class="btn btn-danger">
                    ↩️ العودة لإنشاء المخطط
                </a>
            </div>
        <?php
        }
        ?>

    </div>
</body>

</html>
