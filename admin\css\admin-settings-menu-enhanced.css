/**
 * Enhanced Admin Settings Menu Styles
 * تحسينات إضافية لمظهر قائمة إعدادات الإدارة
 */

/* Enhanced Admin Settings Menu */
.admin-settings-menu {
    margin: 8px 15px !important;
    padding: 0 !important;
    background: none !important;
    border-radius: 12px !important;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: block !important;
    width: calc(100% - 30px) !important;
}

/* Expand admin settings menu by default */
.admin-settings-menu {
    /* Add expanded class by default through CSS */
}

/* Force admin settings menu to be expanded by default */
.admin-settings-menu {
    /* Ensure menu is always expanded */
}

/* Critical fix: Force submenu to be visible immediately */
.admin-settings-menu .admin-settings-submenu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: 500px !important;
    overflow: visible !important;
    position: static !important;
}

/* Force menu to appear expanded by default */
.admin-settings-menu {
    /* Add expanded class styling by default */
}

.admin-settings-menu .admin-settings-arrow {
    transform: rotate(180deg) !important;
}

.admin-settings-menu .admin-settings-submenu {
    /* Force submenu to be visible by default */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: 400px !important;
    overflow: visible !important;
}

/* Force submenu items to be visible */
.admin-settings-submenu li {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.admin-settings-menu .admin-settings-submenu {
    /* Override default collapsed state - expand by default */
    max-height: 400px !important;
    padding: 12px 0 16px 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Rotate arrow by default to indicate expanded state */
.admin-settings-menu .admin-settings-arrow {
    transform: rotate(180deg) !important;
}

.admin-settings-header {
    padding: 16px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    color: rgba(255, 255, 255, 0.95);
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
}

.admin-settings-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 12px;
}

.admin-settings-header:hover::before {
    opacity: 1;
}

.admin-settings-header:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    color: #ffffff;
    transform: translateX(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.admin-settings-header-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.admin-settings-header i:first-child {
    font-size: 1.2rem;
    width: 28px;
    margin-left: 12px;
    flex-shrink: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    order: 1;
}

.admin-settings-header span {
    font-size: 1rem;
    font-weight: 600;
    text-align: right;
    margin-right: 12px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
    order: 2;
}

.admin-settings-arrow {
    font-size: 0.9rem !important;
    width: 20px !important;
    margin-left: 0 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    opacity: 0.8;
    order: 3;
}

.admin-settings-menu.expanded .admin-settings-arrow {
    transform: rotate(180deg);
    opacity: 1;
}

.admin-settings-menu.expanded .admin-settings-header {
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.admin-settings-submenu {
    list-style: none;
    padding: 12px 0 16px 0;
    margin: 0;
    max-height: 400px;
    overflow: visible;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.15) 100%);
    border-radius: 0 0 12px 12px;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    width: 100%;
    position: relative;
    top: 0;
    left: 0;
    display: block;
    visibility: visible;
    opacity: 1;
}

.admin-settings-menu.expanded .admin-settings-submenu {
    max-height: 400px;
    padding: 12px 0 16px 0;
}

.admin-settings-submenu li {
    margin: 4px 12px !important;
    padding: 14px 18px !important;
    cursor: pointer;
    border-radius: 10px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 0.9rem !important;
    display: flex !important;
    align-items: center !important;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    width: calc(100% - 24px);
    box-sizing: border-box;
}

.admin-settings-submenu li::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 10px;
}

.admin-settings-submenu li:hover::before {
    opacity: 1;
}

.admin-settings-submenu li:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    color: #ffffff !important;
    transform: translateX(-4px) translateY(-1px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.admin-settings-submenu li.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.12) 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateX(-2px) !important;
}

.admin-settings-submenu li.active::after {
    content: '';
    position: absolute;
    right: -1px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: 2px 0 0 2px;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
}

.admin-settings-submenu li i {
    font-size: 1.1rem !important;
    width: 24px !important;
    margin-left: 12px !important;
    flex-shrink: 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    opacity: 0.9;
}

.admin-settings-submenu li:hover i,
.admin-settings-submenu li.active i {
    opacity: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.admin-settings-submenu li span {
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    flex: 1;
    text-align: right;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.3px;
}

/* Animation for menu expansion */
@keyframes menuExpand {
    from {
        max-height: 0;
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        max-height: 400px;
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes menuCollapse {
    from {
        max-height: 400px;
        opacity: 1;
        transform: translateY(0);
    }
    to {
        max-height: 0;
        opacity: 0;
        transform: translateY(-10px);
    }
}

.admin-settings-menu.expanded .admin-settings-submenu {
    animation: menuExpand 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
    .admin-settings-menu {
        margin: 6px 10px !important;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    }

    .admin-settings-header {
        padding: 14px 16px;
        font-size: 0.9rem;
    }

    .admin-settings-header i:first-child {
        font-size: 1.1rem;
        width: 24px;
        margin-left: 10px;
    }

    .admin-settings-header span {
        font-size: 0.9rem;
        font-weight: 500;
    }

    .admin-settings-submenu {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.2) 100%);
    }

    .admin-settings-submenu li {
        margin: 3px 8px !important;
        padding: 12px 14px !important;
        font-size: 0.85rem !important;
    }

    .admin-settings-submenu li i {
        font-size: 1rem !important;
        width: 20px !important;
        margin-left: 10px !important;
    }

    .admin-settings-submenu li span {
        font-size: 0.85rem !important;
    }

    .admin-settings-submenu li:hover {
        transform: translateX(-2px) translateY(-1px) !important;
    }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .admin-settings-menu {
        border-color: rgba(255, 255, 255, 0.15);
    }

    .admin-settings-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
    }

    .admin-settings-submenu {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.2) 100%);
    }

    .admin-settings-submenu li {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.04) 100%) !important;
        border-color: rgba(255, 255, 255, 0.08);
    }
}
