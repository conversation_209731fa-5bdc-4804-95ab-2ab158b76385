<?php

/**
 * Database Migration System
 * Version-controlled database schema management with rollback capabilities
 * Part of DevOps implementation for Mossaab Landing Page
 */

require_once __DIR__ . '/../config/environment.php';

class DatabaseMigration
{
    private $pdo;
    private $migrationsPath;
    private $migrationsTable = 'migrations';

    public function __construct()
    {
        $this->pdo = EnvironmentConfig::getInstance()->getDatabaseConnection();
        $this->migrationsPath = __DIR__ . '/migrations/';
        $this->ensureMigrationsTable();
    }

    /**
     * Ensure migrations table exists
     */
    private function ensureMigrationsTable()
    {
        $sql = "
            CREATE TABLE IF NOT EXISTS {$this->migrationsTable} (
                id INT AUTO_INCREMENT PRIMARY KEY,
                migration VARCHAR(255) NOT NULL UNIQUE,
                batch INT NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_batch (batch),
                INDEX idx_migration (migration)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $this->pdo->exec($sql);
    }

    /**
     * Run pending migrations
     */
    public function migrate()
    {
        $pendingMigrations = $this->getPendingMigrations();

        if (empty($pendingMigrations)) {
            echo "✅ No pending migrations.\n";
            return true;
        }

        $batch = $this->getNextBatchNumber();
        $success = true;

        echo "🚀 Running " . count($pendingMigrations) . " migrations...\n";

        foreach ($pendingMigrations as $migration) {
            try {
                echo "⏳ Migrating: {$migration}\n";

                $this->pdo->beginTransaction();

                // Execute migration
                $this->executeMigration($migration, 'up');

                // Record migration
                $this->recordMigration($migration, $batch);

                $this->pdo->commit();

                echo "✅ Migrated: {$migration}\n";
            } catch (Exception $e) {
                $this->pdo->rollBack();
                echo "❌ Migration failed: {$migration}\n";
                echo "Error: " . $e->getMessage() . "\n";
                $success = false;
                break;
            }
        }

        if ($success) {
            echo "🎉 All migrations completed successfully!\n";
        }

        return $success;
    }

    /**
     * Rollback migrations
     */
    public function rollback($steps = 1)
    {
        $migrationsToRollback = $this->getMigrationsToRollback($steps);

        if (empty($migrationsToRollback)) {
            echo "✅ No migrations to rollback.\n";
            return true;
        }

        $success = true;

        echo "🔄 Rolling back " . count($migrationsToRollback) . " migrations...\n";

        foreach (array_reverse($migrationsToRollback) as $migration) {
            try {
                echo "⏳ Rolling back: {$migration['migration']}\n";

                $this->pdo->beginTransaction();

                // Execute rollback
                $this->executeMigration($migration['migration'], 'down');

                // Remove migration record
                $this->removeMigrationRecord($migration['migration']);

                $this->pdo->commit();

                echo "✅ Rolled back: {$migration['migration']}\n";
            } catch (Exception $e) {
                $this->pdo->rollBack();
                echo "❌ Rollback failed: {$migration['migration']}\n";
                echo "Error: " . $e->getMessage() . "\n";
                $success = false;
                break;
            }
        }

        if ($success) {
            echo "🎉 Rollback completed successfully!\n";
        }

        return $success;
    }

    /**
     * Get migration status
     */
    public function status()
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();

        echo "📊 Migration Status:\n";
        echo "==================\n";

        foreach ($allMigrations as $migration) {
            $status = isset($executedMigrations[$migration]) ? '✅ Migrated' : '⏳ Pending';
            $batch = isset($executedMigrations[$migration]) ? " (Batch: {$executedMigrations[$migration]['batch']})" : '';
            echo "{$status} {$migration}{$batch}\n";
        }

        $pendingCount = count($allMigrations) - count($executedMigrations);
        echo "\n📈 Summary: " . count($executedMigrations) . " migrated, {$pendingCount} pending\n";
    }

    /**
     * Create a new migration file
     */
    public function create($name, $table = null)
    {
        $timestamp = date('Y_m_d_His');
        $className = $this->studlyCase($name);
        $filename = "{$timestamp}_{$name}.php";
        $filepath = $this->migrationsPath . $filename;

        // Ensure migrations directory exists
        if (!is_dir($this->migrationsPath)) {
            mkdir($this->migrationsPath, 0755, true);
        }

        $template = $this->getMigrationTemplate($className, $table);

        if (file_put_contents($filepath, $template)) {
            echo "✅ Migration created: {$filename}\n";
            return $filepath;
        } else {
            echo "❌ Failed to create migration: {$filename}\n";
            return false;
        }
    }

    /**
     * Get pending migrations
     */
    private function getPendingMigrations()
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = array_keys($this->getExecutedMigrations());

        return array_diff($allMigrations, $executedMigrations);
    }

    /**
     * Get all migration files
     */
    private function getAllMigrationFiles()
    {
        if (!is_dir($this->migrationsPath)) {
            return [];
        }

        $files = scandir($this->migrationsPath);
        $migrations = [];

        foreach ($files as $file) {
            if (preg_match('/^\d{4}_\d{2}_\d{2}_\d{6}_.*\.php$/', $file)) {
                $migrations[] = pathinfo($file, PATHINFO_FILENAME);
            }
        }

        sort($migrations);
        return $migrations;
    }

    /**
     * Get executed migrations
     */
    private function getExecutedMigrations()
    {
        $stmt = $this->pdo->prepare("SELECT migration, batch, executed_at FROM {$this->migrationsTable} ORDER BY migration");
        $stmt->execute();

        $migrations = [];
        while ($row = $stmt->fetch()) {
            $migrations[$row['migration']] = $row;
        }

        return $migrations;
    }

    /**
     * Get migrations to rollback
     */
    private function getMigrationsToRollback($steps)
    {
        $stmt = $this->pdo->prepare("
            SELECT migration, batch
            FROM {$this->migrationsTable}
            ORDER BY batch DESC, migration DESC
            LIMIT ?
        ");
        $stmt->execute([$steps]);

        return $stmt->fetchAll();
    }

    /**
     * Execute migration
     */
    private function executeMigration($migration, $direction)
    {
        $filepath = $this->migrationsPath . $migration . '.php';

        if (!file_exists($filepath)) {
            throw new Exception("Migration file not found: {$filepath}");
        }

        // Include migration file
        require_once $filepath;

        // Get class name from migration name
        $parts = explode('_', $migration);
        array_shift($parts); // Remove timestamp parts
        array_shift($parts);
        array_shift($parts);
        array_shift($parts);

        $className = $this->studlyCase(implode('_', $parts));

        if (!class_exists($className)) {
            throw new Exception("Migration class not found: {$className}");
        }

        $migrationInstance = new $className($this->pdo);

        if (!method_exists($migrationInstance, $direction)) {
            throw new Exception("Method '{$direction}' not found in migration: {$className}");
        }

        $migrationInstance->$direction();
    }

    /**
     * Record migration
     */
    private function recordMigration($migration, $batch)
    {
        $stmt = $this->pdo->prepare("INSERT INTO {$this->migrationsTable} (migration, batch) VALUES (?, ?)");
        $stmt->execute([$migration, $batch]);
    }

    /**
     * Remove migration record
     */
    private function removeMigrationRecord($migration)
    {
        $stmt = $this->pdo->prepare("DELETE FROM {$this->migrationsTable} WHERE migration = ?");
        $stmt->execute([$migration]);
    }

    /**
     * Get next batch number
     */
    private function getNextBatchNumber()
    {
        $stmt = $this->pdo->prepare("SELECT MAX(batch) as max_batch FROM {$this->migrationsTable}");
        $stmt->execute();
        $result = $stmt->fetch();

        return ($result['max_batch'] ?? 0) + 1;
    }

    /**
     * Convert string to StudlyCase
     */
    private function studlyCase($string)
    {
        return str_replace(' ', '', ucwords(str_replace(['_', '-'], ' ', $string)));
    }

    /**
     * Get migration template
     */
    private function getMigrationTemplate($className, $table)
    {
        $tableOperations = $table ? $this->getTableOperations($table) : $this->getGenericOperations();

        return "<?php
/**
 * Migration: {$className}
 * Created: " . date('Y-m-d H:i:s') . "
 */

class {$className}
{
    private \$pdo;

    public function __construct(\$pdo)
    {
        \$this->pdo = \$pdo;
    }

    /**
     * Run the migration
     */
    public function up()
    {
{$tableOperations['up']}
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
{$tableOperations['down']}
    }
}
";
    }

    /**
     * Get table-specific operations
     */
    private function getTableOperations($table)
    {
        return [
            'up' => "        // Create {$table} table
        \$sql = \"
            CREATE TABLE {$table} (
                id INT AUTO_INCREMENT PRIMARY KEY,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        \";

        \$this->pdo->exec(\$sql);",

            'down' => "        // Drop {$table} table
        \$this->pdo->exec(\"DROP TABLE IF EXISTS {$table}\");"
        ];
    }

    /**
     * Get generic operations
     */
    private function getGenericOperations()
    {
        return [
            'up' => "        // Add your migration logic here
        // Example:
        // \$this->pdo->exec(\"ALTER TABLE users ADD COLUMN email VARCHAR(255)\");",

            'down' => "        // Add your rollback logic here
        // Example:
        // \$this->pdo->exec(\"ALTER TABLE users DROP COLUMN email\");"
        ];
    }
}

// CLI Interface
if (php_sapi_name() === 'cli') {
    $migration = new DatabaseMigration();

    $command = $argv[1] ?? 'help';

    switch ($command) {
        case 'migrate':
            $migration->migrate();
            break;

        case 'rollback':
            $steps = isset($argv[2]) ? (int)$argv[2] : 1;
            $migration->rollback($steps);
            break;

        case 'status':
            $migration->status();
            break;

        case 'create':
            if (!isset($argv[2])) {
                echo "❌ Migration name is required.\n";
                echo "Usage: php DatabaseMigration.php create migration_name [table_name]\n";
                exit(1);
            }
            $name = $argv[2];
            $table = $argv[3] ?? null;
            $migration->create($name, $table);
            break;

        case 'help':
        default:
            echo "🗄️  Database Migration System\n";
            echo "============================\n\n";
            echo "Available commands:\n";
            echo "  migrate              Run pending migrations\n";
            echo "  rollback [steps]     Rollback migrations (default: 1 step)\n";
            echo "  status               Show migration status\n";
            echo "  create <name> [table] Create new migration\n";
            echo "  help                 Show this help message\n\n";
            echo "Examples:\n";
            echo "  php DatabaseMigration.php migrate\n";
            echo "  php DatabaseMigration.php rollback 2\n";
            echo "  php DatabaseMigration.php create add_email_to_users\n";
            echo "  php DatabaseMigration.php create create_products_table products\n";
            break;
    }
}
