// Firebase Configuration and Authentication
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signInWithPopup, GoogleAuthProvider, signOut, onAuthStateChanged, signInAnonymously } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
import { getFirestore, doc, setDoc, getDoc, getDocFromCache, enableIndexedDbPersistence } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";
import { getAnalytics } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAHYP4efj_6z7lodL56YF2_vZfLVRnraBs",
  authDomain: "landingpage-a7491.firebaseapp.com",
  projectId: "landingpage-a7491",
  storageBucket: "landingpage-a7491.firebasestorage.app",
  messagingSenderId: "538587228680",
  appId: "1:538587228680:web:662bc194bf9894634b3fbd",
  measurementId: "G-NXQWCWG5YD"
};

// Initialize Firebase with error handling
let app, auth, db, analytics;

try {
    console.log('🔥 Initializing Firebase...');
    app = initializeApp(firebaseConfig);
    console.log('✅ Firebase App initialized successfully');

    auth = getAuth(app);
    console.log('✅ Firebase Auth initialized successfully');

    db = getFirestore(app);
    console.log('✅ Firestore initialized successfully');

    // Initialize Analytics (optional, may fail in some environments)
    try {
        analytics = getAnalytics(app);
        console.log('✅ Firebase Analytics initialized successfully');
    } catch (analyticsError) {
        console.warn('⚠️ Firebase Analytics initialization failed:', analyticsError.message);
        analytics = null;
    }

} catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    throw new Error(`Firebase initialization failed: ${error.message}`);
}

// Enable offline persistence with enhanced error handling
try {
    enableIndexedDbPersistence(db).then(() => {
        console.log('✅ Firestore offline persistence enabled');
    }).catch((err) => {
        if (err.code === 'failed-precondition') {
            console.warn('⚠️ Firestore persistence failed: Multiple tabs open');
        } else if (err.code === 'unimplemented') {
            console.warn('⚠️ Firestore persistence not supported by browser');
        } else {
            console.warn('⚠️ Firestore persistence error:', err.message);
        }
    });
} catch (error) {
    console.warn('⚠️ Firestore persistence setup error:', error.message);
}

// Add network connectivity monitoring
function monitorFirestoreConnectivity() {
    if (!db) return;

    // Monitor online/offline status
    window.addEventListener('online', () => {
        console.log('🌐 Network connection restored');
    });

    window.addEventListener('offline', () => {
        console.warn('📡 Network connection lost - using offline cache');
    });
}

// Initialize connectivity monitoring
monitorFirestoreConnectivity();

// Google Auth Provider
const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');

// Firebase Auth Manager
class FirebaseAuthManager {
    constructor() {
        this.currentUser = null;
        this.userProfile = null;
        this.app = app; // Expose Firebase app instance
        this.db = db;   // Expose Firestore database instance
        this.auth = auth; // Expose Firebase auth instance

        console.log('🔥 Firebase Auth Manager initialized');
        console.log('📱 Firebase App:', this.app ? 'INITIALIZED' : 'FAILED');
        console.log('🗄️ Firestore DB:', this.db ? 'CONNECTED' : 'FAILED');

        // Initialize auth listener
        this.initializeAuthListener();

        // Firestore connection test disabled to prevent 400 errors on login page
        // Test can be manually triggered if needed
        // setTimeout(() => {
        //     this.testFirestoreConnection().catch(error => {
        //         console.warn('⚠️ Initial Firestore connection test failed:', error.message);
        //     });
        // }, 1000);
    }

    // Initialize authentication state listener
    initializeAuthListener() {
        try {
            onAuthStateChanged(auth, async (user) => {
                try {
                    if (user) {
                        console.log('🔐 Firebase user state changed: signed in', user.email);
                        this.currentUser = user;

                        // Skip profile loading on login page to prevent Firestore 400 errors
                        const isLoginPage = window.location.pathname.includes('login.html') ||
                                          window.location.pathname.includes('admin-login.html');

                        if (!isLoginPage) {
                            await this.loadUserProfile(user.uid);
                        } else {
                            // Create minimal profile for login page
                            this.userProfile = {
                                uid: user.uid,
                                email: user.email,
                                displayName: user.displayName || user.email.split('@')[0],
                                role: 'user',
                                isActive: true,
                                loginPageMode: true
                            };
                            console.log('📱 Login page mode: using minimal profile');
                        }

                        // Only call onUserSignedIn if it's not an automatic state change on login page
                        if (!isLoginPage || window.loginFormSubmitted) {
                            this.onUserSignedIn(user);
                        } else {
                            console.log('🔄 Skipping onUserSignedIn callback on login page (automatic state change)');
                        }
                    } else {
                        console.log('🔓 Firebase user state changed: signed out');
                        this.currentUser = null;
                        this.userProfile = null;
                        this.onUserSignedOut();
                    }
                } catch (error) {
                    console.error('❌ Error in auth state change handler:', error);
                }
            });
        } catch (error) {
            console.error('❌ Error initializing auth listener:', error);
        }
    }

    // Sign in with email and password
    async signInWithEmail(email, password) {
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            console.log('✅ Email sign-in successful:', userCredential.user.email);
            return { success: true, user: userCredential.user };
        } catch (error) {
            console.error('❌ Email sign-in error:', error);
            return { success: false, error: this.getErrorMessage(error) };
        }
    }

    // Sign up with email and password
    async signUpWithEmail(email, password, displayName = '', role = 'user') {
        try {
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;

            // Skip Firestore operations on login page to prevent 400 errors
            const isLoginPage = window.location.pathname.includes('login.html') ||
                              window.location.pathname.includes('admin-login.html');

            if (!isLoginPage) {
                // Create user profile in Firestore
                await this.createUserProfile(user.uid, {
                    email: user.email,
                    displayName: displayName || user.email.split('@')[0],
                    role: role,
                    createdAt: new Date().toISOString(),
                    lastLogin: new Date().toISOString(),
                    isActive: true
                });
            } else {
                console.log('📱 Login page mode: skipping Firestore profile creation');
            }

            console.log('✅ Email sign-up successful:', user.email);
            return { success: true, user: user };
        } catch (error) {
            console.error('❌ Email sign-up error:', error);
            return { success: false, error: this.getErrorMessage(error) };
        }
    }

    // Sign in with Google
    async signInWithGoogle() {
        try {
            const result = await signInWithPopup(auth, googleProvider);
            const user = result.user;

            // Skip Firestore operations on login page to prevent 400 errors
            const isLoginPage = window.location.pathname.includes('login.html') ||
                              window.location.pathname.includes('admin-login.html');

            if (!isLoginPage) {
                // Check if user profile exists, create if not
                const userDoc = await getDoc(doc(db, 'users', user.uid));
                if (!userDoc.exists()) {
                    await this.createUserProfile(user.uid, {
                        email: user.email,
                        displayName: user.displayName,
                        photoURL: user.photoURL,
                        role: 'user',
                        provider: 'google',
                        createdAt: new Date().toISOString(),
                        lastLogin: new Date().toISOString(),
                        isActive: true
                    });
                } else {
                    // Update last login
                    await setDoc(doc(db, 'users', user.uid), {
                        lastLogin: new Date().toISOString()
                    }, { merge: true });
                }
            } else {
                console.log('📱 Login page mode: skipping Firestore profile operations');
            }

            console.log('✅ Google sign-in successful:', user.email);
            return { success: true, user: user };
        } catch (error) {
            console.error('❌ Google sign-in error:', error);
            return { success: false, error: this.getErrorMessage(error) };
        }
    }

    // Sign out
    async signOutUser() {
        try {
            await signOut(auth);
            console.log('✅ User signed out successfully');
            return { success: true };
        } catch (error) {
            console.error('❌ Sign-out error:', error);
            return { success: false, error: this.getErrorMessage(error) };
        }
    }

    // Create user profile in Firestore with retry mechanism
    async createUserProfile(uid, profileData, retryCount = 0) {
        const maxRetries = 3;

        try {
            await setDoc(doc(db, 'users', uid), profileData);
            console.log('✅ User profile created:', uid);
        } catch (error) {
            console.error('❌ Error creating user profile:', error);

            // Retry logic for network errors
            if (retryCount < maxRetries && (error.code === 'unavailable' || error.code === 'deadline-exceeded')) {
                console.log(`🔄 Retrying profile creation (${retryCount + 1}/${maxRetries})...`);
                await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                return this.createUserProfile(uid, profileData, retryCount + 1);
            }

            throw error;
        }
    }

    // Load user profile from Firestore with offline fallback
    async loadUserProfile(uid) {
        try {
            const userDocRef = doc(db, 'users', uid);
            let userDoc;

            try {
                // Try to get document from server
                userDoc = await getDoc(userDocRef);
            } catch (error) {
                if (error.code === 'unavailable' || !navigator.onLine) {
                    console.debug('🔄 Server unavailable, trying cache...');
                    try {
                        userDoc = await getDocFromCache(userDocRef);
                        console.log('📱 User profile loaded from cache');
                    } catch (cacheError) {
                        console.warn('⚠️ No cached profile available');
                        throw cacheError;
                    }
                } else {
                    throw error;
                }
            }

            if (userDoc && userDoc.exists()) {
                this.userProfile = userDoc.data();
                console.log('✅ User profile loaded:', this.userProfile);
            } else {
                console.log('⚠️ No user profile found, creating default...');
                // Create default profile
                const defaultProfile = {
                    email: this.currentUser.email,
                    displayName: this.currentUser.displayName || this.currentUser.email.split('@')[0],
                    role: 'user',
                    createdAt: new Date().toISOString(),
                    lastLogin: new Date().toISOString(),
                    isActive: true
                };

                // Only try to create if online
                if (navigator.onLine) {
                    try {
                        await this.createUserProfile(uid, defaultProfile);
                    } catch (createError) {
                        console.warn('⚠️ Could not create profile online, using local:', createError.message);
                    }
                }

                this.userProfile = defaultProfile;
            }
        } catch (error) {
            console.error('❌ Error loading user profile:', error);

            // Fallback to basic profile
            this.userProfile = {
                uid: uid,
                email: this.currentUser?.email || '<EMAIL>',
                displayName: this.currentUser?.displayName || 'Utilisateur',
                role: 'user',
                isActive: true,
                offline: true
            };
        }
    }

    // Check if user is admin
    isAdmin() {
        return this.userProfile && ['admin', 'super_admin', 'owner'].includes(this.userProfile.role);
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }

    // Get current user info
    getCurrentUser() {
        return {
            user: this.currentUser,
            profile: this.userProfile,
            isAuthenticated: this.isAuthenticated(),
            isAdmin: this.isAdmin()
        };
    }

    // Handle user signed in
    onUserSignedIn(user) {
        console.log('🔐 User signed in:', user.email);
        // Update UI or redirect as needed
        if (typeof window.onFirebaseUserSignedIn === 'function') {
            window.onFirebaseUserSignedIn(user, this.userProfile);
        }
    }

    // Handle user signed out
    onUserSignedOut() {
        console.log('🔓 User signed out');

        // Prevent multiple calls
        if (this.signOutInProgress) {
            console.log('⚠️ Sign out already in progress, skipping');
            return;
        }

        this.signOutInProgress = true;

        // Update UI or redirect as needed
        if (typeof window.onFirebaseUserSignedOut === 'function') {
            window.onFirebaseUserSignedOut();
        }

        // Reset flag after delay
        setTimeout(() => {
            this.signOutInProgress = false;
        }, 1000);
    }

    // Check if Firebase is properly initialized
    isFirebaseInitialized() {
        return !!(this.app && this.auth && this.db);
    }

    // Get Firebase initialization status
    getInitializationStatus() {
        return {
            app: !!this.app,
            auth: !!this.auth,
            db: !!this.db,
            analytics: !!analytics,
            manager: true
        };
    }

    // Test Firestore connectivity
    async testFirestoreConnection() {
        if (!this.db) {
            throw new Error('Firestore not initialized');
        }

        // Check network connectivity first
        if (!navigator.onLine) {
            console.warn('📡 Device is offline - Firestore may use cached data');
        }

        try {
            // Method 1: If user is already authenticated, test with their credentials
            if (this.currentUser) {
                console.log('🔍 Testing Firestore with authenticated user...');
                const testDoc = doc(this.db, 'users', this.currentUser.uid);

                // Add timeout for authenticated user test
                const authTestPromise = getDoc(testDoc);
                const authTimeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Authenticated test timed out after 8 seconds')), 8000);
                });

                await Promise.race([authTestPromise, authTimeoutPromise]);
                console.log('✅ Firestore connection test successful (authenticated)');
                return true;
            }

            // Method 2: Test with anonymous authentication for connectivity check
            console.log('🔍 Testing Firestore with anonymous authentication...');

            try {
                // Sign in anonymously for the test
                console.log('🔐 Attempting anonymous sign-in...');
                const anonymousUser = await signInAnonymously(this.auth);
                console.log('✅ Anonymous sign-in successful:', anonymousUser.user.uid);

                try {
                    // Try to access a public collection or create a test document
                    const testDoc = doc(this.db, 'system', 'connectivity_test');
                    const testData = {
                        timestamp: new Date().toISOString(),
                        test: true,
                        uid: anonymousUser.user.uid
                    };

                    console.log('📝 Attempting to write test document...');

                    // Add timeout to prevent hanging writes
                    const writePromise = setDoc(testDoc, testData);
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('Write operation timed out after 10 seconds')), 10000);
                    });

                    // Race between write and timeout
                    await Promise.race([writePromise, timeoutPromise]);
                    console.log('✅ Firestore connection test successful (anonymous write)');

                    // Clean up: sign out the anonymous user
                    await signOut(this.auth);
                    console.log('🚪 Anonymous user signed out');

                    return true;

                } catch (writeError) {
                    console.log('⚠️ Write test failed, trying read test...');
                    console.log('Write error details:', writeError.code, writeError.message);

                    // If write fails, try a simple read operation with timeout
                    try {
                        const readPromise = getDoc(testDoc);
                        const readTimeoutPromise = new Promise((_, reject) => {
                            setTimeout(() => reject(new Error('Read operation timed out after 5 seconds')), 5000);
                        });

                        await Promise.race([readPromise, readTimeoutPromise]);
                        console.log('✅ Firestore connection test successful (anonymous read)');

                        // Clean up: sign out the anonymous user
                        await signOut(this.auth);
                        return true;

                    } catch (readError) {
                        console.warn('⚠️ Both read and write tests failed');
                        console.log('Read error details:', readError.code, readError.message);

                        // Check if it's a network/backend issue
                        if (readError.message.includes('timeout') || readError.code === 'unavailable') {
                            console.warn('🌐 Firestore backend appears to be unavailable');

                            // Clean up: sign out the anonymous user
                            await signOut(this.auth);

                            // Return true for basic connectivity (auth worked, just backend issues)
                            console.log('✅ Basic connectivity confirmed (auth successful, backend unavailable)');
                            return true;
                        }

                        // Clean up: sign out the anonymous user
                        await signOut(this.auth);
                        throw readError;
                    }
                }

            } catch (anonymousAuthError) {
                console.error('❌ Anonymous authentication failed:', anonymousAuthError.code, anonymousAuthError.message);

                // If anonymous auth fails, throw the error to be handled by the outer catch
                throw anonymousAuthError;
            }

        } catch (error) {
            // Enhanced error logging with complete error details
            console.error('❌ Firestore connection test failed - Full Error Details:');
            console.error('- Error Code:', error.code || 'No code');
            console.error('- Error Message:', error.message || 'No message');
            console.error('- Error Stack:', error.stack || 'No stack trace');
            console.error('- Full Error Object:', error);

            // Enhanced error handling for different types of connectivity issues
            if (error.code === 'unavailable') {
                console.warn('🌐 Firestore backend unavailable - network connectivity issue');
                console.warn('💡 This may be temporary - Firestore backend is experiencing issues');
            } else if (error.code === 'permission-denied') {
                console.warn('🔒 Firestore permission denied - check security rules');
            } else if (error.code === 'failed-precondition') {
                console.warn('⚠️ Firestore precondition failed - check authentication');
            } else if (error.code === 'auth/admin-restricted-operation') {
                console.error('🚫 Anonymous authentication not enabled in Firebase Console');
            } else if (error.code === 'auth/operation-not-allowed') {
                console.error('🚫 Authentication method not allowed - check Firebase Console settings');
            } else if (error.message && error.message.includes('timeout')) {
                console.warn('⏱️ Firestore operation timed out - backend may be slow or unavailable');
                console.warn('💡 This indicates network or backend performance issues');
            } else if (error.message && error.message.includes('400')) {
                console.warn('🔴 HTTP 400 error from Firestore - backend connectivity issue');
                console.warn('💡 This is likely a temporary Firestore service issue');
            } else {
                console.error('❓ Unknown error type:', error.code);
            }

            // If anonymous auth fails, try a basic connectivity test
            try {
                console.log('🔍 Trying basic Firestore connectivity test...');

                // Test if we can at least access the Firestore instance
                if (this.db && this.db.app) {
                    console.log('✅ Firestore instance is accessible (basic test)');

                    // Check if we're online
                    if (navigator.onLine) {
                        console.log('🌐 Network connection available');

                        // If we got this far, authentication worked but Firestore backend has issues
                        if (error.code === 'unavailable' || error.message.includes('timeout') || error.message.includes('400')) {
                            console.log('✅ Basic connectivity confirmed - Firebase Auth working, Firestore backend temporarily unavailable');
                            return true; // Consider it working since auth is functional
                        }

                        return true;
                    } else {
                        console.warn('📡 Offline mode - Firestore may use cache');
                        return true; // Still consider it working in offline mode
                    }
                }

                return false;

            } catch (basicError) {
                console.error('❌ Basic Firestore connectivity test failed:', basicError.message);

                // Even if basic test fails, if we got anonymous auth working, that's progress
                if (error.message && error.message.includes('Anonymous sign-in successful')) {
                    console.log('✅ Partial connectivity - Authentication working, database backend issues');
                    return true;
                }

                return false;
            }
        }
    }

    // Test user profile creation (for testing Firestore write operations)
    async testUserProfileCreation() {
        if (!this.currentUser) {
            throw new Error('No authenticated user for profile test');
        }

        try {
            const testProfile = {
                email: '<EMAIL>', // Fixed test email
                displayName: 'Utilisateur', // Test display name
                role: 'user',
                createdAt: new Date().toISOString(),
                lastLogin: new Date().toISOString(),
                isActive: true,
                testProfile: true, // Mark as test profile
                offline: true // Mark as offline test
            };

            const userDoc = doc(this.db, 'users', this.currentUser.uid);
            await setDoc(userDoc, testProfile, { merge: true });

            console.log('✅ User profile creation test successful');
            return true;

        } catch (error) {
            console.warn('⚠️ User profile creation test failed:', error.message);
            return false;
        }
    }

    // Get user-friendly error message
    getErrorMessage(error) {
        const errorMessages = {
            'auth/user-not-found': 'لم يتم العثور على المستخدم',
            'auth/wrong-password': 'كلمة المرور غير صحيحة',
            'auth/email-already-in-use': 'البريد الإلكتروني مستخدم بالفعل',
            'auth/weak-password': 'كلمة المرور ضعيفة جداً',
            'auth/invalid-email': 'البريد الإلكتروني غير صالح',
            'auth/too-many-requests': 'محاولات كثيرة جداً، حاول لاحقاً',
            'auth/network-request-failed': 'خطأ في الشبكة',
            'auth/popup-closed-by-user': 'تم إغلاق النافثة المنبثقة',
            'auth/cancelled-popup-request': 'تم إلغاء طلب النافذة المنبثقة',
            'permission-denied': 'ليس لديك صلاحية للوصول إلى هذه البيانات',
            'unavailable': 'الخدمة غير متاحة حالياً'
        };

        return errorMessages[error.code] || error.message || 'حدث خطأ غير متوقع';
    }
}

// Create global instance with error handling
try {
    window.firebaseAuth = new FirebaseAuthManager();
    console.log('🎉 Firebase Auth Manager created and available globally');

    // Verify initialization
    const status = window.firebaseAuth.getInitializationStatus();
    console.log('🔍 Firebase Initialization Status:', status);

    if (!window.firebaseAuth.isFirebaseInitialized()) {
        console.error('❌ Firebase initialization incomplete!');
    }

} catch (error) {
    console.error('❌ Failed to create Firebase Auth Manager:', error);
    window.firebaseAuth = null;
}

// Export for module usage
export { FirebaseAuthManager, auth, db, analytics };
