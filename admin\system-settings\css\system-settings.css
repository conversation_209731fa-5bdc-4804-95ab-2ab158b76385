/**
 * System Settings Styles
 * أنماط إعدادات النظام
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    color: #333;
}

/* Container */
.system-settings-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.system-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.system-header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.system-header p {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin-bottom: 20px;
}

/* Navigation Breadcrumb */
.breadcrumb {
    background: rgba(255, 255, 255, 0.9);
    padding: 15px 25px;
    border-radius: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95rem;
}

.breadcrumb a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s;
}

.breadcrumb a:hover {
    color: #2980b9;
}

.breadcrumb .separator {
    color: #bdc3c7;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

/* Setting Card */
.setting-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.setting-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #9b59b6);
}

.setting-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.setting-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.setting-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #3498db, #9b59b6);
}

.setting-title {
    flex: 1;
}

.setting-title h3 {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 5px;
}

.setting-title p {
    font-size: 0.9rem;
    color: #7f8c8d;
    line-height: 1.4;
}

.setting-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-family: inherit;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(149, 165, 166, 0.3);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.status-active {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.status-inactive {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.status-warning {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .system-settings-container {
        padding: 15px;
    }

    .system-header {
        padding: 20px;
    }

    .system-header h1 {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }

    .settings-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .setting-card {
        padding: 20px;
    }

    .setting-actions {
        flex-direction: column;
    }

    .quick-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .quick-stats {
        grid-template-columns: 1fr;
    }

    .breadcrumb {
        flex-wrap: wrap;
    }
}

/* Categories Specific Styles */
.categories-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.categories-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
}

.enhanced-input, .enhanced-select {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-family: inherit;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
}

.enhanced-input:focus, .enhanced-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.view-mode-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
    gap: 5px;
}

.view-mode-btn {
    padding: 8px 15px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    font-weight: 500;
}

.view-mode-btn.active {
    background: #3498db;
    color: white;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.category-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.category-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.category-info small {
    color: #95a5a6;
    font-size: 0.8rem;
}

.category-status {
    margin-right: auto;
}

.category-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 15px;
}

.category-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 0.85rem;
}

.stat-item i {
    color: #95a5a6;
}

.category-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-xs {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.85rem;
}

/* Tree View Styles */
.categories-tree {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.tree-node {
    margin-bottom: 5px;
}

.tree-node-content {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.tree-node-content:hover {
    background: #f8f9fa;
}

.tree-toggle {
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.tree-spacer {
    width: 20px;
}

.tree-icon {
    width: 30px;
    height: 30px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.tree-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.tree-name {
    font-weight: 500;
    color: #2c3e50;
}

.tree-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.tree-status.status-active {
    background: #27ae60;
}

.tree-status.status-inactive {
    background: #e74c3c;
}

.tree-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.tree-node-content:hover .tree-actions {
    opacity: 1;
}

.tree-children {
    margin-right: 20px;
    border-right: 2px solid #e9ecef;
    padding-right: 10px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #95a5a6;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #6c757d;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.form-control {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-family: inherit;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control.is-invalid {
    border-color: #e74c3c;
}

.form-control.is-invalid:focus {
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.form-check input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3498db;
}

.form-check label {
    margin: 0;
    cursor: pointer;
}

/* Color and Icon Pickers */
.color-icon-group {
    display: flex;
    gap: 15px;
    align-items: end;
}

.color-picker-group {
    flex: 1;
}

.icon-picker-group {
    flex: 2;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    margin-top: 5px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 10px;
}

.empty-state p {
    margin-bottom: 30px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 30px;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
}
