<?php

/**
 * General Settings Management
 * إدارة الإعدادات العامة للموقع
 */

session_start();

// Load configuration
require_once '../../config/config.php';

// Connect to database
try {
    $dbConfig = Config::getDbConfig();
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $dbConfig['host'],
        $dbConfig['port'],
        $dbConfig['database']
    );

    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];

    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'] ?? '', $options);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()]);
    exit;
}

// التحقق من تسجيل الدخول - تعطيل مؤقتاً للاختبار
// if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
//     http_response_code(403);
//     echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
//     exit;
// }

header('Content-Type: application/json; charset=utf-8');

class GeneralSettingsManager
{
    private $pdo;

    public function __construct($pdo)
    {
        $this->pdo = $pdo;
    }

    /**
     * جلب جميع الإعدادات مجمعة حسب المجموعة
     */
    public function getAllSettings()
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT setting_key, setting_value, setting_type, setting_group,
                       setting_label_ar, setting_description_ar, is_required, sort_order
                FROM general_settings
                ORDER BY setting_group, sort_order, setting_key
            ");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // تجميع الإعدادات حسب المجموعة
            $grouped = [];
            foreach ($settings as $setting) {
                $group = $setting['setting_group'];
                if (!isset($grouped[$group])) {
                    $grouped[$group] = [];
                }
                $grouped[$group][] = $setting;
            }

            return ['success' => true, 'data' => $grouped];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب الإعدادات: ' . $e->getMessage()];
        }
    }

    /**
     * جلب إعداد محدد
     */
    public function getSetting($key)
    {
        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM general_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ? $result['setting_value'] : null;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * تحديث إعداد واحد
     */
    public function updateSetting($key, $value, $userId = null)
    {
        try {
            // جلب القيمة القديمة للسجل
            $oldValue = $this->getSetting($key);

            // تحديث الإعداد
            $stmt = $this->pdo->prepare("
                UPDATE general_settings
                SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
                WHERE setting_key = ?
            ");
            $stmt->execute([$value, $key]);

            // تسجيل التغيير في السجل
            if ($userId) {
                $this->logSettingChange($key, $oldValue, $value, $userId);
            }

            return ['success' => true, 'message' => 'تم تحديث الإعداد بنجاح'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تحديث الإعداد: ' . $e->getMessage()];
        }
    }

    /**
     * تحديث عدة إعدادات دفعة واحدة
     */
    public function updateMultipleSettings($settings, $userId = null)
    {
        try {
            $this->pdo->beginTransaction();

            $updated = 0;
            foreach ($settings as $key => $value) {
                $result = $this->updateSetting($key, $value, $userId);
                if ($result['success']) {
                    $updated++;
                }
            }

            $this->pdo->commit();
            return ['success' => true, 'message' => "تم تحديث {$updated} إعداد بنجاح"];
        } catch (Exception $e) {
            $this->pdo->rollBack();
            return ['success' => false, 'message' => 'خطأ في تحديث الإعدادات: ' . $e->getMessage()];
        }
    }

    /**
     * تسجيل تغيير الإعدادات
     */
    private function logSettingChange($key, $oldValue, $newValue, $userId)
    {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO settings_history (setting_key, old_value, new_value, changed_by, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $key,
                $oldValue,
                $newValue,
                $userId,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (Exception $e) {
            // تسجيل الخطأ ولكن لا نوقف العملية
            error_log("Error logging setting change: " . $e->getMessage());
        }
    }

    /**
     * رفع ملف (للشعار والأيقونة)
     */
    public function uploadFile($fileKey, $file)
    {
        try {
            $uploadDir = '../../uploads/settings/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/x-icon'];
            $maxSize = 5 * 1024 * 1024; // 5MB

            if (!in_array($file['type'], $allowedTypes)) {
                return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
            }

            if ($file['size'] > $maxSize) {
                return ['success' => false, 'message' => 'حجم الملف كبير جداً (الحد الأقصى 5MB)'];
            }

            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = $fileKey . '_' . time() . '.' . $extension;
            $filepath = $uploadDir . $filename;

            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                // حذف الملف القديم إن وجد
                $oldFile = $this->getSetting($fileKey);
                if ($oldFile && file_exists('../../' . $oldFile)) {
                    unlink('../../' . $oldFile);
                }

                $relativePath = 'uploads/settings/' . $filename;
                return ['success' => true, 'path' => $relativePath];
            } else {
                return ['success' => false, 'message' => 'فشل في رفع الملف'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في رفع الملف: ' . $e->getMessage()];
        }
    }

    /**
     * جلب سجل التغييرات
     */
    public function getSettingsHistory($limit = 50)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT sh.*, u.username, gs.setting_label_ar
                FROM settings_history sh
                LEFT JOIN users u ON sh.changed_by = u.id
                LEFT JOIN general_settings gs ON sh.setting_key = gs.setting_key
                ORDER BY sh.changed_at DESC
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return ['success' => true, 'data' => $history];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب السجل: ' . $e->getMessage()];
        }
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validateSettings($settings)
    {
        $errors = [];

        foreach ($settings as $key => $value) {
            switch ($key) {
                case 'site_name':
                    if (empty(trim($value))) {
                        $errors[] = 'اسم الموقع مطلوب';
                    }
                    break;
                case 'admin_email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = 'بريد المدير غير صحيح';
                    }
                    break;
                case 'site_url':
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $errors[] = 'رابط الموقع غير صحيح';
                    }
                    break;
                case 'smtp_port':
                    if (!empty($value) && (!is_numeric($value) || $value < 1 || $value > 65535)) {
                        $errors[] = 'منفذ SMTP غير صحيح';
                    }
                    break;
            }
        }

        return $errors;
    }
}

// معالجة الطلبات
$manager = new GeneralSettingsManager($pdo);
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_all':
        echo json_encode($manager->getAllSettings());
        break;

    case 'get_setting':
        $key = $_GET['key'] ?? '';
        if ($key) {
            $value = $manager->getSetting($key);
            echo json_encode(['success' => true, 'value' => $value]);
        } else {
            echo json_encode(['success' => false, 'message' => 'مفتاح الإعداد مطلوب']);
        }
        break;

    case 'update':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $settings = $_POST['settings'] ?? [];

            // التحقق من صحة البيانات
            $errors = $manager->validateSettings($settings);
            if (!empty($errors)) {
                echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
                break;
            }

            // معالجة رفع الملفات
            if (!empty($_FILES)) {
                foreach ($_FILES as $fileKey => $file) {
                    if ($file['error'] === UPLOAD_ERR_OK) {
                        $uploadResult = $manager->uploadFile($fileKey, $file);
                        if ($uploadResult['success']) {
                            $settings[$fileKey] = $uploadResult['path'];
                        } else {
                            echo json_encode($uploadResult);
                            exit;
                        }
                    }
                }
            }

            $result = $manager->updateMultipleSettings($settings, $_SESSION['user_id']);
            echo json_encode($result);
        } else {
            echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
        }
        break;

    case 'get_history':
        $limit = $_GET['limit'] ?? 50;
        echo json_encode($manager->getSettingsHistory($limit));
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
        break;
}
