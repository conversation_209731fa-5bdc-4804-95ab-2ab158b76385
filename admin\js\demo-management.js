/**
 * Demo Data Management
 * إدارة البيانات التجريبية
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeDemoManagement();
});

/**
 * Initialize Demo Management
 */
function initializeDemoManagement() {
    loadDemoStats();
    setupEventListeners();
}

/**
 * Setup Event Listeners
 */
function setupEventListeners() {
    // Install demo data button
    const installBtn = document.getElementById('installDemoBtn');
    if (installBtn) {
        installBtn.addEventListener('click', installDemoData);
    }
    
    // Reset demo data button
    const resetBtn = document.getElementById('resetDemoBtn');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetDemoData);
    }
    
    // Refresh stats button
    const refreshBtn = document.getElementById('refreshStatsBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadDemoStats);
    }
    
    // Tab navigation
    const tabButtons = document.querySelectorAll('.demo-tab-btn');
    tabButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            switchDemoTab(e.target.dataset.tab);
        });
    });
}

/**
 * Load Demo Statistics
 */
async function loadDemoStats() {
    try {
        showLoading('demoStatsContainer');
        
        const response = await fetch('php/api/demo-data.php?action=get_stats');
        const result = await response.json();
        
        if (result.success) {
            updateStatsUI(result.data);
        } else {
            showNotification('فشل في تحميل الإحصائيات: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error loading demo stats:', error);
        showNotification('خطأ في تحميل الإحصائيات', 'error');
    } finally {
        hideLoading('demoStatsContainer');
    }
}

/**
 * Update Statistics UI
 */
function updateStatsUI(stats) {
    // Update stat cards
    updateStatCard('sellersCount', stats.sellers || 0);
    updateStatCard('storesCount', stats.stores || 0);
    updateStatCard('productsCount', stats.products || 0);
    updateStatCard('landingPagesCount', stats.landing_pages || 0);
    updateStatCard('customersCount', stats.customers || 0);
    updateStatCard('ordersCount', stats.orders || 0);
    
    // Update recent activity
    updateRecentActivity(stats.recent_activity || []);
    
    // Update demo status
    const isDemoInstalled = stats.products > 0 && stats.landing_pages > 0;
    updateDemoStatus(isDemoInstalled);
}

/**
 * Update Stat Card
 */
function updateStatCard(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        
        // Add animation
        element.classList.add('stat-updated');
        setTimeout(() => {
            element.classList.remove('stat-updated');
        }, 500);
    }
}

/**
 * Update Recent Activity
 */
function updateRecentActivity(activities) {
    const container = document.getElementById('recentActivityList');
    if (!container) return;
    
    if (activities.length === 0) {
        container.innerHTML = '<div class="no-activity">لا توجد أنشطة حديثة</div>';
        return;
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">
                <i class="fas ${getActivityIcon(activity.type)}"></i>
            </div>
            <div class="activity-details">
                <div class="activity-title">${activity.title}</div>
                <div class="activity-value">${formatCurrency(activity.value)}</div>
                <div class="activity-time">${formatDateTime(activity.created_at)}</div>
            </div>
        </div>
    `).join('');
}

/**
 * Get Activity Icon
 */
function getActivityIcon(type) {
    const icons = {
        'order': 'fa-shopping-cart',
        'customer': 'fa-user',
        'product': 'fa-box',
        'landing_page': 'fa-file-alt'
    };
    return icons[type] || 'fa-info-circle';
}

/**
 * Update Demo Status
 */
function updateDemoStatus(isInstalled) {
    const statusElement = document.getElementById('demoStatus');
    const installBtn = document.getElementById('installDemoBtn');
    const resetBtn = document.getElementById('resetDemoBtn');
    
    if (statusElement) {
        statusElement.className = `demo-status ${isInstalled ? 'installed' : 'not-installed'}`;
        statusElement.innerHTML = `
            <i class="fas ${isInstalled ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
            ${isInstalled ? 'البيانات التجريبية مثبتة' : 'البيانات التجريبية غير مثبتة'}
        `;
    }
    
    if (installBtn) {
        installBtn.disabled = isInstalled;
        installBtn.textContent = isInstalled ? 'مثبت بالفعل' : 'تثبيت البيانات التجريبية';
    }
    
    if (resetBtn) {
        resetBtn.disabled = !isInstalled;
    }
}

/**
 * Install Demo Data
 */
async function installDemoData() {
    if (!confirm('هل أنت متأكد من تثبيت البيانات التجريبية؟ سيتم إنشاء بيانات جديدة.')) {
        return;
    }
    
    try {
        const installBtn = document.getElementById('installDemoBtn');
        if (installBtn) {
            installBtn.disabled = true;
            installBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التثبيت...';
        }
        
        const response = await fetch('php/api/demo-data.php?action=install_demo', {
            method: 'POST'
        });
        const result = await response.json();
        
        if (result.success) {
            showNotification('تم تثبيت البيانات التجريبية بنجاح!', 'success');
            loadDemoStats(); // Refresh stats
        } else {
            showNotification('فشل في تثبيت البيانات التجريبية: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error installing demo data:', error);
        showNotification('خطأ في تثبيت البيانات التجريبية', 'error');
    } finally {
        const installBtn = document.getElementById('installDemoBtn');
        if (installBtn) {
            installBtn.disabled = false;
            installBtn.textContent = 'تثبيت البيانات التجريبية';
        }
    }
}

/**
 * Reset Demo Data
 */
async function resetDemoData() {
    if (!confirm('هل أنت متأكد من حذف جميع البيانات التجريبية؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }
    
    try {
        const resetBtn = document.getElementById('resetDemoBtn');
        if (resetBtn) {
            resetBtn.disabled = true;
            resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
        }
        
        const response = await fetch('php/api/demo-data.php?action=reset_demo', {
            method: 'POST'
        });
        const result = await response.json();
        
        if (result.success) {
            showNotification('تم حذف البيانات التجريبية بنجاح!', 'success');
            loadDemoStats(); // Refresh stats
        } else {
            showNotification('فشل في حذف البيانات التجريبية: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error resetting demo data:', error);
        showNotification('خطأ في حذف البيانات التجريبية', 'error');
    } finally {
        const resetBtn = document.getElementById('resetDemoBtn');
        if (resetBtn) {
            resetBtn.disabled = false;
            resetBtn.textContent = 'حذف البيانات التجريبية';
        }
    }
}

/**
 * Switch Demo Tab
 */
function switchDemoTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.demo-tab-content');
    tabContents.forEach(content => {
        content.style.display = 'none';
    });
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.demo-tab-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(tabName + 'Tab');
    if (selectedTab) {
        selectedTab.style.display = 'block';
    }
    
    // Add active class to selected tab button
    const selectedBtn = document.querySelector(`[data-tab="${tabName}"]`);
    if (selectedBtn) {
        selectedBtn.classList.add('active');
    }
    
    // Load tab-specific data
    loadTabData(tabName);
}

/**
 * Load Tab Data
 */
async function loadTabData(tabName) {
    const actions = {
        'products': 'get_products',
        'landingPages': 'get_landing_pages',
        'orders': 'get_orders',
        'customers': 'get_customers'
    };
    
    const action = actions[tabName];
    if (!action) return;
    
    try {
        const response = await fetch(`php/api/demo-data.php?action=${action}`);
        const result = await response.json();
        
        if (result.success) {
            updateTabContent(tabName, result.data);
        } else {
            console.error('Failed to load tab data:', result.message);
        }
    } catch (error) {
        console.error('Error loading tab data:', error);
    }
}

/**
 * Update Tab Content
 */
function updateTabContent(tabName, data) {
    const container = document.getElementById(tabName + 'List');
    if (!container) return;
    
    if (data.length === 0) {
        container.innerHTML = '<div class="no-data">لا توجد بيانات</div>';
        return;
    }
    
    // Generate content based on tab type
    switch (tabName) {
        case 'products':
            container.innerHTML = generateProductsList(data);
            break;
        case 'landingPages':
            container.innerHTML = generateLandingPagesList(data);
            break;
        case 'orders':
            container.innerHTML = generateOrdersList(data);
            break;
        case 'customers':
            container.innerHTML = generateCustomersList(data);
            break;
    }
}

/**
 * Generate Products List
 */
function generateProductsList(products) {
    return products.map(product => `
        <div class="demo-item">
            <div class="item-info">
                <h4>${product.name}</h4>
                <p>الفئة: ${product.category_name || 'غير محدد'}</p>
                <p>السعر: ${formatCurrency(product.sale_price || product.price)}</p>
                <p>المخزون: ${product.stock_quantity}</p>
            </div>
            <div class="item-status ${product.is_active ? 'active' : 'inactive'}">
                ${product.is_active ? 'نشط' : 'غير نشط'}
            </div>
        </div>
    `).join('');
}

/**
 * Generate Landing Pages List
 */
function generateLandingPagesList(pages) {
    return pages.map(page => `
        <div class="demo-item">
            <div class="item-info">
                <h4>${page.title}</h4>
                <p>النوع: ${page.template_type}</p>
                <p>المشاهدات: ${page.page_views}</p>
                <p>معدل التحويل: ${page.conversion_rate}%</p>
            </div>
            <div class="item-status ${page.is_active ? 'active' : 'inactive'}">
                ${page.is_active ? 'نشط' : 'غير نشط'}
            </div>
        </div>
    `).join('');
}

/**
 * Generate Orders List
 */
function generateOrdersList(orders) {
    return orders.map(order => `
        <div class="demo-item">
            <div class="item-info">
                <h4>طلب #${order.id}</h4>
                <p>العميل: ${order.customer_name}</p>
                <p>المبلغ: ${formatCurrency(order.total_amount)}</p>
                <p>التاريخ: ${formatDate(order.created_at)}</p>
            </div>
            <div class="item-status status-${order.status}">
                ${getOrderStatusText(order.status)}
            </div>
        </div>
    `).join('');
}

/**
 * Generate Customers List
 */
function generateCustomersList(customers) {
    return customers.map(customer => `
        <div class="demo-item">
            <div class="item-info">
                <h4>${customer.name}</h4>
                <p>البريد: ${customer.email}</p>
                <p>الطلبات: ${customer.total_orders}</p>
                <p>إجمالي الإنفاق: ${formatCurrency(customer.total_spent)}</p>
            </div>
            <div class="item-status ${customer.is_active ? 'active' : 'inactive'}">
                ${customer.is_active ? 'نشط' : 'غير نشط'}
            </div>
        </div>
    `).join('');
}

/**
 * Utility Functions
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-DZ', {
        style: 'currency',
        currency: 'DZD'
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-DZ');
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('ar-DZ');
}

function getOrderStatusText(status) {
    const statusMap = {
        'pending': 'في الانتظار',
        'confirmed': 'مؤكد',
        'shipped': 'تم الشحن',
        'delivered': 'تم التسليم',
        'cancelled': 'ملغي'
    };
    return statusMap[status] || status;
}

function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '<div class="loading">جاري التحميل...</div>';
    }
}

function hideLoading(containerId) {
    // Loading will be replaced by actual content
}

// Make functions globally available
window.initializeDemoManagement = initializeDemoManagement;
window.loadDemoStats = loadDemoStats;
window.installDemoData = installDemoData;
window.resetDemoData = resetDemoData;
window.switchDemoTab = switchDemoTab;
