<?php
require_once __DIR__ . '/../php/config.php';

try {
    $pdo = getPDOConnection();
    
    echo "🔍 CHECKING DEMO STORE DETAILS\n";
    echo "=" . str_repeat("=", 40) . "\n\n";
    
    // Get demo store information
    echo "📋 Demo Store Information:\n";
    echo "-" . str_repeat("-", 25) . "\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            s.*,
            u.email as owner_email,
            CONCAT(u.first_name, ' ', u.last_name) as owner_name
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.store_slug = 'mossaab-store'
    ");
    $stmt->execute();
    $demoStore = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($demoStore) {
        echo "✅ Demo Store Found:\n";
        echo "   Store ID: {$demoStore['id']}\n";
        echo "   Store Name: {$demoStore['store_name']}\n";
        echo "   Store Slug: {$demoStore['store_slug']}\n";
        echo "   User ID: {$demoStore['user_id']}\n";
        echo "   Owner Email: {$demoStore['owner_email']}\n";
        echo "   Owner Name: {$demoStore['owner_name']}\n";
        echo "   Status: {$demoStore['status']}\n";
    } else {
        echo "❌ Demo store not found!\n";
        exit;
    }
    
    echo "\n📦 Current Products for Demo Store:\n";
    echo "-" . str_repeat("-", 35) . "\n";
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total_products
        FROM produits 
        WHERE store_id = ? AND actif = 1
    ");
    $stmt->execute([$demoStore['id']]);
    $storeProducts = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total_products
        FROM produits 
        WHERE store_id IS NULL AND actif = 1
    ");
    $stmt->execute();
    $globalProducts = $stmt->fetchColumn();
    
    echo "Products specifically for this store (store_id = {$demoStore['id']}): {$storeProducts}\n";
    echo "Global products (store_id = NULL): {$globalProducts}\n";
    echo "Total products available to store: " . ($storeProducts + $globalProducts) . "\n";
    
    // Show sample products
    echo "\n📋 Sample Current Products:\n";
    echo "-" . str_repeat("-", 25) . "\n";
    
    $stmt = $pdo->prepare("
        SELECT id, titre, prix, type, store_id
        FROM produits 
        WHERE (store_id = ? OR store_id IS NULL) AND actif = 1
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$demoStore['id']]);
    $sampleProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sampleProducts as $product) {
        $storeAssociation = $product['store_id'] ? "Store {$product['store_id']}" : "Global";
        echo "   • {$product['titre']} - {$product['prix']} DZD ({$product['type']}) [{$storeAssociation}]\n";
    }
    
    echo "\n🎯 READY FOR PRODUCT ADDITION\n";
    echo "=" . str_repeat("=", 40) . "\n";
    echo "Demo Store ID: {$demoStore['id']}\n";
    echo "Demo User ID: {$demoStore['user_id']}\n";
    echo "Owner Email: {$demoStore['owner_email']}\n";
    echo "Current Store-Specific Products: {$storeProducts}\n";
    echo "Ready to add 10 new products!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
