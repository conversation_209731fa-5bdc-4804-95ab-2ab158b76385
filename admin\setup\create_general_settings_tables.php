<?php

/**
 * Create General Settings Tables
 * إنشاء جداول الإعدادات العامة
 */

require_once '../../config/database.php';

try {
    echo "<h2>إنشاء جداول الإعدادات العامة</h2>\n";

    // Read SQL file
    $sqlFile = '../sql/general_settings.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }

    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }

    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function ($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );

    $pdo->beginTransaction();

    foreach ($statements as $statement) {
        if (trim($statement)) {
            echo "<p>تنفيذ: " . substr($statement, 0, 50) . "...</p>\n";
            $pdo->exec($statement);
        }
    }

    $pdo->commit();

    echo "<div style='color: green; font-weight: bold; margin: 20px 0;'>";
    echo "✅ تم إنشاء جداول الإعدادات العامة بنجاح!";
    echo "</div>\n";

    // Verify tables were created
    echo "<h3>التحقق من الجداول المنشأة:</h3>\n";

    $tables = ['general_settings', 'settings_history'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ جدول $table موجود</p>\n";

                // Count records
                $countStmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $count = $countStmt->fetchColumn();
                echo "<p style='margin-right: 20px;'>عدد السجلات: $count</p>\n";
            } else {
                echo "<p style='color: red;'>❌ جدول $table غير موجود</p>\n";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في التحقق من جدول $table: " . $e->getMessage() . "</p>\n";
        }
    }

    echo "<h3>عينة من الإعدادات المدرجة:</h3>\n";
    try {
        $stmt = $pdo->query("SELECT setting_key, setting_label_ar, setting_value FROM general_settings LIMIT 10");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في جلب الإعدادات: " . $e->getMessage() . "</p>\n";
        $settings = [];
    }

    if ($settings) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f0f0f0;'><th>المفتاح</th><th>التسمية</th><th>القيمة</th></tr>\n";
        foreach ($settings as $setting) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
            echo "<td>" . htmlspecialchars($setting['setting_label_ar']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($setting['setting_value'], 0, 50)) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }

    echo "<div style='margin: 20px 0; padding: 15px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 5px;'>";
    echo "<strong>الخطوات التالية:</strong><br>";
    echo "1. تأكد من أن الإعدادات تظهر في لوحة الإدارة<br>";
    echo "2. اختبر حفظ وتحديث الإعدادات<br>";
    echo "3. تحقق من سجل التغييرات<br>";
    echo "</div>\n";
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    echo "<div style='color: red; font-weight: bold; margin: 20px 0;'>";
    echo "❌ خطأ في إنشاء الجداول: " . $e->getMessage();
    echo "</div>\n";

    echo "<div style='margin: 20px 0; padding: 15px; background: #ffe7e7; border: 1px solid #ffb3b3; border-radius: 5px;'>";
    echo "<strong>نصائح لحل المشكلة:</strong><br>";
    echo "1. تأكد من صحة إعدادات قاعدة البيانات<br>";
    echo "2. تأكد من وجود صلاحيات إنشاء الجداول<br>";
    echo "3. تحقق من ملف SQL للتأكد من صحة الاستعلامات<br>";
    echo "</div>\n";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد جداول الإعدادات العامة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        h3 {
            color: #555;
            margin-top: 25px;
        }

        p {
            margin: 8px 0;
        }

        table {
            font-size: 0.9em;
        }

        th,
        td {
            padding: 8px 12px;
            text-align: right;
        }

        th {
            font-weight: bold;
        }

        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .back-link:hover {
            background: #5a67d8;
        }
    </style>
</head>

<body>
    <div class="container">
        <a href="../index.html" class="back-link">← العودة إلى لوحة الإدارة</a>
    </div>
</body>

</html>
