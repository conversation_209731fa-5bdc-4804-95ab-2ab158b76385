<?php

/**
 * Categories Management API
 * واجهة برمجة تطبيقات إدارة الفئات
 */

session_start();

// Load configuration
require_once '../../config/config.php';

// Connect to database
try {
    $dbConfig = Config::getDbConfig();
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $dbConfig['host'],
        $dbConfig['port'],
        $dbConfig['database']
    );

    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];

    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'] ?? '', $options);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()]);
    exit;
}

// التحقق من تسجيل الدخول - تعطيل مؤقتاً للاختبار
// if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
//     http_response_code(403);
//     echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
//     exit;
// }

header('Content-Type: application/json; charset=utf-8');

class CategoriesManager
{
    private $pdo;

    public function __construct($pdo)
    {
        $this->pdo = $pdo;
    }

    /**
     * Get all categories with hierarchy
     */
    public function getAllCategories($includeInactive = false)
    {
        try {
            $whereClause = $includeInactive ? '' : 'WHERE c.is_active = 1';

            $sql = "
                SELECT
                    c.*,
                    p.name_ar as parent_name_ar,
                    p.name_en as parent_name_en,
                    s.products_count,
                    s.subcategories_count,
                    s.views_count
                FROM categories c
                LEFT JOIN categories p ON c.parent_id = p.id
                LEFT JOIN category_stats s ON c.id = s.category_id
                $whereClause
                ORDER BY c.parent_id ASC, c.sort_order ASC, c.name_ar ASC
            ";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $categories = $stmt->fetchAll();

            // Build hierarchy
            $hierarchy = $this->buildHierarchy($categories);

            return [
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'hierarchy' => $hierarchy,
                    'total' => count($categories)
                ]
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب الفئات: ' . $e->getMessage()];
        }
    }

    /**
     * Get category by ID
     */
    public function getCategoryById($id)
    {
        try {
            $sql = "
                SELECT
                    c.*,
                    p.name_ar as parent_name_ar,
                    p.name_en as parent_name_en,
                    s.products_count,
                    s.subcategories_count,
                    s.views_count
                FROM categories c
                LEFT JOIN categories p ON c.parent_id = p.id
                LEFT JOIN category_stats s ON c.id = s.category_id
                WHERE c.id = ?
            ";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$id]);
            $category = $stmt->fetch();

            if (!$category) {
                return ['success' => false, 'message' => 'الفئة غير موجودة'];
            }

            // Get subcategories
            $subcategoriesStmt = $this->pdo->prepare("
                SELECT id, name_ar, name_en, slug, is_active, sort_order
                FROM categories
                WHERE parent_id = ?
                ORDER BY sort_order ASC, name_ar ASC
            ");
            $subcategoriesStmt->execute([$id]);
            $subcategories = $subcategoriesStmt->fetchAll();

            // Get category path
            $path = $this->getCategoryPath($id);

            return [
                'success' => true,
                'data' => [
                    'category' => $category,
                    'subcategories' => $subcategories,
                    'path' => $path
                ]
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في جلب الفئة: ' . $e->getMessage()];
        }
    }

    /**
     * Create new category
     */
    public function createCategory($data)
    {
        try {
            // Validate required fields
            if (empty($data['name_ar'])) {
                return ['success' => false, 'message' => 'الاسم العربي مطلوب'];
            }

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateSlug($data['name_ar']);
            }

            // Check if slug already exists
            if ($this->slugExists($data['slug'])) {
                $data['slug'] = $this->generateUniqueSlug($data['slug']);
            }

            $sql = "
                INSERT INTO categories (
                    name_ar, name_en, name_fr, slug, description_ar, description_en, description_fr,
                    parent_id, image, icon, color, sort_order, is_active, is_featured,
                    meta_title_ar, meta_title_en, meta_title_fr,
                    meta_description_ar, meta_description_en, meta_description_fr,
                    meta_keywords_ar, meta_keywords_en, meta_keywords_fr,
                    created_by
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ";

            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                $data['name_ar'],
                $data['name_en'] ?? null,
                $data['name_fr'] ?? null,
                $data['slug'],
                $data['description_ar'] ?? null,
                $data['description_en'] ?? null,
                $data['description_fr'] ?? null,
                $data['parent_id'] ?? null,
                $data['image'] ?? null,
                $data['icon'] ?? 'fas fa-folder',
                $data['color'] ?? '#667eea',
                $data['sort_order'] ?? 0,
                $data['is_active'] ?? 1,
                $data['is_featured'] ?? 0,
                $data['meta_title_ar'] ?? null,
                $data['meta_title_en'] ?? null,
                $data['meta_title_fr'] ?? null,
                $data['meta_description_ar'] ?? null,
                $data['meta_description_en'] ?? null,
                $data['meta_description_fr'] ?? null,
                $data['meta_keywords_ar'] ?? null,
                $data['meta_keywords_en'] ?? null,
                $data['meta_keywords_fr'] ?? null,
                $_SESSION['user_id'] ?? 1
            ]);

            if ($result) {
                $categoryId = $this->pdo->lastInsertId();

                // Initialize category stats
                $this->initializeCategoryStats($categoryId);

                return [
                    'success' => true,
                    'message' => 'تم إنشاء الفئة بنجاح',
                    'data' => ['id' => $categoryId, 'slug' => $data['slug']]
                ];
            } else {
                return ['success' => false, 'message' => 'فشل في إنشاء الفئة'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في إنشاء الفئة: ' . $e->getMessage()];
        }
    }

    /**
     * Update category
     */
    public function updateCategory($id, $data)
    {
        try {
            // Check if category exists
            $existingCategory = $this->getCategoryById($id);
            if (!$existingCategory['success']) {
                return $existingCategory;
            }

            // Validate required fields
            if (empty($data['name_ar'])) {
                return ['success' => false, 'message' => 'الاسم العربي مطلوب'];
            }

            // Check if slug is unique (excluding current category)
            if (!empty($data['slug']) && $this->slugExists($data['slug'], $id)) {
                return ['success' => false, 'message' => 'الرابط المختصر موجود مسبقاً'];
            }

            $sql = "
                UPDATE categories SET
                    name_ar = ?, name_en = ?, name_fr = ?, slug = ?,
                    description_ar = ?, description_en = ?, description_fr = ?,
                    parent_id = ?, image = ?, icon = ?, color = ?,
                    sort_order = ?, is_active = ?, is_featured = ?,
                    meta_title_ar = ?, meta_title_en = ?, meta_title_fr = ?,
                    meta_description_ar = ?, meta_description_en = ?, meta_description_fr = ?,
                    meta_keywords_ar = ?, meta_keywords_en = ?, meta_keywords_fr = ?,
                    updated_by = ?
                WHERE id = ?
            ";

            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                $data['name_ar'],
                $data['name_en'] ?? null,
                $data['name_fr'] ?? null,
                $data['slug'] ?? $existingCategory['data']['category']['slug'],
                $data['description_ar'] ?? null,
                $data['description_en'] ?? null,
                $data['description_fr'] ?? null,
                $data['parent_id'] ?? null,
                $data['image'] ?? null,
                $data['icon'] ?? 'fas fa-folder',
                $data['color'] ?? '#667eea',
                $data['sort_order'] ?? 0,
                $data['is_active'] ?? 1,
                $data['is_featured'] ?? 0,
                $data['meta_title_ar'] ?? null,
                $data['meta_title_en'] ?? null,
                $data['meta_title_fr'] ?? null,
                $data['meta_description_ar'] ?? null,
                $data['meta_description_en'] ?? null,
                $data['meta_description_fr'] ?? null,
                $data['meta_keywords_ar'] ?? null,
                $data['meta_keywords_en'] ?? null,
                $data['meta_keywords_fr'] ?? null,
                $_SESSION['user_id'] ?? 1,
                $id
            ]);

            if ($result) {
                return ['success' => true, 'message' => 'تم تحديث الفئة بنجاح'];
            } else {
                return ['success' => false, 'message' => 'فشل في تحديث الفئة'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تحديث الفئة: ' . $e->getMessage()];
        }
    }

    /**
     * Delete category
     */
    public function deleteCategory($id)
    {
        try {
            // Check if category exists
            $category = $this->getCategoryById($id);
            if (!$category['success']) {
                return $category;
            }

            // Check if category has subcategories
            if ($category['data']['category']['subcategories_count'] > 0) {
                return ['success' => false, 'message' => 'لا يمكن حذف فئة تحتوي على فئات فرعية'];
            }

            // Check if category has products (if products table exists)
            $productsCount = $this->getCategoryProductsCount($id);
            if ($productsCount > 0) {
                return ['success' => false, 'message' => 'لا يمكن حذف فئة تحتوي على منتجات'];
            }

            $stmt = $this->pdo->prepare("DELETE FROM categories WHERE id = ?");
            $result = $stmt->execute([$id]);

            if ($result) {
                return ['success' => true, 'message' => 'تم حذف الفئة بنجاح'];
            } else {
                return ['success' => false, 'message' => 'فشل في حذف الفئة'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في حذف الفئة: ' . $e->getMessage()];
        }
    }

    /**
     * Toggle category status
     */
    public function toggleCategoryStatus($id)
    {
        try {
            $stmt = $this->pdo->prepare("UPDATE categories SET is_active = NOT is_active WHERE id = ?");
            $result = $stmt->execute([$id]);

            if ($result) {
                // Get new status
                $statusStmt = $this->pdo->prepare("SELECT is_active FROM categories WHERE id = ?");
                $statusStmt->execute([$id]);
                $newStatus = $statusStmt->fetchColumn();

                $message = $newStatus ? 'تم تفعيل الفئة' : 'تم إلغاء تفعيل الفئة';
                return ['success' => true, 'message' => $message, 'is_active' => (bool)$newStatus];
            } else {
                return ['success' => false, 'message' => 'فشل في تغيير حالة الفئة'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تغيير حالة الفئة: ' . $e->getMessage()];
        }
    }

    /**
     * Search categories
     */
    public function searchCategories($query, $filters = [])
    {
        try {
            $whereConditions = [];
            $params = [];

            // Text search
            if (!empty($query)) {
                $whereConditions[] = "(c.name_ar LIKE ? OR c.name_en LIKE ? OR c.description_ar LIKE ?)";
                $searchTerm = "%$query%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            // Status filter
            if (isset($filters['is_active'])) {
                $whereConditions[] = "c.is_active = ?";
                $params[] = $filters['is_active'];
            }

            // Parent filter
            if (isset($filters['parent_id'])) {
                if ($filters['parent_id'] === 'null') {
                    $whereConditions[] = "c.parent_id IS NULL";
                } else {
                    $whereConditions[] = "c.parent_id = ?";
                    $params[] = $filters['parent_id'];
                }
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            $sql = "
                SELECT
                    c.*,
                    p.name_ar as parent_name_ar,
                    s.products_count,
                    s.subcategories_count
                FROM categories c
                LEFT JOIN categories p ON c.parent_id = p.id
                LEFT JOIN category_stats s ON c.id = s.category_id
                $whereClause
                ORDER BY c.sort_order ASC, c.name_ar ASC
            ";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $categories = $stmt->fetchAll();

            return [
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'total' => count($categories),
                    'query' => $query,
                    'filters' => $filters
                ]
            ];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في البحث: ' . $e->getMessage()];
        }
    }

    // Helper methods
    private function buildHierarchy($categories, $parentId = null)
    {
        $hierarchy = [];
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['children'] = $this->buildHierarchy($categories, $category['id']);
                $hierarchy[] = $category;
            }
        }
        return $hierarchy;
    }

    private function generateSlug($text)
    {
        // Simple slug generation - convert Arabic to English transliteration
        $arabicToEnglish = [
            'ا' => 'a',
            'ب' => 'b',
            'ت' => 't',
            'ث' => 'th',
            'ج' => 'j',
            'ح' => 'h',
            'خ' => 'kh',
            'د' => 'd',
            'ذ' => 'th',
            'ر' => 'r',
            'ز' => 'z',
            'س' => 's',
            'ش' => 'sh',
            'ص' => 's',
            'ض' => 'd',
            'ط' => 't',
            'ظ' => 'z',
            'ع' => 'a',
            'غ' => 'gh',
            'ف' => 'f',
            'ق' => 'q',
            'ك' => 'k',
            'ل' => 'l',
            'م' => 'm',
            'ن' => 'n',
            'ه' => 'h',
            'و' => 'w',
            'ي' => 'y',
            'ة' => 'h',
            'ى' => 'a',
            'ء' => 'a',
            'أ' => 'a',
            'إ' => 'i',
            'آ' => 'a',
            'ؤ' => 'o',
            'ئ' => 'e'
        ];

        $slug = str_replace(array_keys($arabicToEnglish), array_values($arabicToEnglish), $text);
        $slug = strtolower(trim($slug));
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        return trim($slug, '-');
    }

    private function generateUniqueSlug($baseSlug)
    {
        $counter = 1;
        $newSlug = $baseSlug;

        while ($this->slugExists($newSlug)) {
            $newSlug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $newSlug;
    }

    private function slugExists($slug, $excludeId = null)
    {
        $sql = "SELECT COUNT(*) FROM categories WHERE slug = ?";
        $params = [$slug];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchColumn() > 0;
    }

    private function initializeCategoryStats($categoryId)
    {
        $stmt = $this->pdo->prepare("
            INSERT INTO category_stats (category_id, products_count, subcategories_count, views_count)
            VALUES (?, 0, 0, 0)
        ");
        $stmt->execute([$categoryId]);
    }

    private function getCategoryPath($categoryId)
    {
        $path = [];
        $currentId = $categoryId;

        while ($currentId) {
            $stmt = $this->pdo->prepare("SELECT id, name_ar, parent_id FROM categories WHERE id = ?");
            $stmt->execute([$currentId]);
            $category = $stmt->fetch();

            if ($category) {
                array_unshift($path, $category);
                $currentId = $category['parent_id'];
            } else {
                break;
            }
        }

        return $path;
    }

    private function getCategoryProductsCount($categoryId)
    {
        // Check if products table exists
        try {
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
            $stmt->execute([$categoryId]);
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            // Products table doesn't exist yet
            return 0;
        }
    }
}

// Handle requests
$manager = new CategoriesManager($pdo);
$action = $_GET['action'] ?? $_POST['action'] ?? '';

// Debug logging
error_log("Categories API - Action: $action, Method: " . $_SERVER['REQUEST_METHOD']);
error_log("Categories API - GET: " . json_encode($_GET));
error_log("Categories API - POST: " . json_encode($_POST));

switch ($action) {
    case 'get_all':
        $includeInactive = isset($_GET['include_inactive']) && $_GET['include_inactive'] === 'true';
        echo json_encode($manager->getAllCategories($includeInactive));
        break;

    case 'get_by_id':
        $id = $_GET['id'] ?? 0;
        echo json_encode($manager->getCategoryById($id));
        break;

    case 'create':
        $data = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        echo json_encode($manager->createCategory($data));
        break;

    case 'update':
        $id = $_POST['id'] ?? $_GET['id'] ?? 0;
        $data = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        echo json_encode($manager->updateCategory($id, $data));
        break;

    case 'delete':
        $id = $_POST['id'] ?? $_GET['id'] ?? 0;
        echo json_encode($manager->deleteCategory($id));
        break;

    case 'toggle_status':
        $id = $_POST['id'] ?? $_GET['id'] ?? 0;
        echo json_encode($manager->toggleCategoryStatus($id));
        break;

    case 'search':
        $query = $_GET['q'] ?? '';
        $filters = [
            'is_active' => $_GET['is_active'] ?? null,
            'parent_id' => $_GET['parent_id'] ?? null
        ];
        echo json_encode($manager->searchCategories($query, array_filter($filters, function ($v) {
            return $v !== null;
        })));
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
        break;
}
