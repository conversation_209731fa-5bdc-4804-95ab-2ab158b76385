<?php

/**
 * Payment Settings API
 * API إعدادات الدفع
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../config/db_env.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                getPaymentMethod($_GET['id']);
            } elseif ($action === 'stats') {
                getPaymentStats();
            } elseif ($action === 'test') {
                testPaymentMethod($_GET['method_id'] ?? null);
            } else {
                getPaymentMethods();
            }
            break;
        case 'POST':
            createPaymentMethod();
            break;
        case 'PUT':
            updatePaymentMethod();
            break;
        case 'DELETE':
            if (isset($_GET['id'])) {
                deletePaymentMethod($_GET['id']);
            }
            break;
        default:
            throw new Exception('Method not allowed');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getPaymentMethods()
{
    global $pdo;

    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $search = $_GET['search'] ?? '';
    $type = $_GET['type'] ?? '';
    $status = $_GET['status'] ?? '';

    $offset = ($page - 1) * $limit;

    // Build WHERE clause
    $where = [];
    $params = [];

    if ($search) {
        $where[] = "(name LIKE ? OR name_ar LIKE ? OR provider LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if ($type) {
        $where[] = "type = ?";
        $params[] = $type;
    }

    if ($status !== '') {
        $where[] = "is_active = ?";
        $params[] = $status === 'active' ? 1 : 0;
    }

    $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

    // Get total count
    $countSql = "SELECT COUNT(*) FROM payment_methods $whereClause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetchColumn();

    // Get payment methods
    $sql = "
        SELECT
            id, name, name_ar, type, provider, fees_percentage, fees_fixed,
            min_amount, max_amount, supported_currencies, is_active, sort_order,
            created_at, updated_at
        FROM payment_methods
        $whereClause
        ORDER BY sort_order ASC, name ASC
        LIMIT ? OFFSET ?
    ";

    $params[] = $limit;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format data
    foreach ($methods as &$method) {
        $method['is_active'] = (bool) $method['is_active'];
        $method['fees_percentage'] = (float) $method['fees_percentage'];
        $method['fees_fixed'] = (float) $method['fees_fixed'];
        $method['min_amount'] = (float) $method['min_amount'];
        $method['max_amount'] = $method['max_amount'] ? (float) $method['max_amount'] : null;
        $method['supported_currencies'] = json_decode($method['supported_currencies'], true) ?: [];
        $method['sort_order'] = (int) $method['sort_order'];
    }

    echo json_encode([
        'success' => true,
        'data' => $methods,
        'pagination' => [
            'current_page' => (int) $page,
            'per_page' => (int) $limit,
            'total' => (int) $total,
            'total_pages' => ceil($total / $limit)
        ]
    ]);
}

function getPaymentMethod($id)
{
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM payment_methods WHERE id = ?");
    $stmt->execute([$id]);
    $method = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$method) {
        throw new Exception('Payment method not found');
    }

    // Format data
    $method['is_active'] = (bool) $method['is_active'];
    $method['fees_percentage'] = (float) $method['fees_percentage'];
    $method['fees_fixed'] = (float) $method['fees_fixed'];
    $method['min_amount'] = (float) $method['min_amount'];
    $method['max_amount'] = $method['max_amount'] ? (float) $method['max_amount'] : null;
    $method['supported_currencies'] = json_decode($method['supported_currencies'], true) ?: [];
    $method['config'] = json_decode($method['config'], true) ?: [];

    echo json_encode([
        'success' => true,
        'data' => $method
    ]);
}

function createPaymentMethod()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid input data');
    }

    // Validate required fields
    $required = ['name', 'name_ar', 'type'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }

    // Validate type
    $validTypes = ['credit_card', 'bank_transfer', 'cash_on_delivery', 'digital_wallet', 'cryptocurrency'];
    if (!in_array($input['type'], $validTypes)) {
        throw new Exception('Invalid payment method type');
    }

    $stmt = $pdo->prepare("
        INSERT INTO payment_methods (
            name, name_ar, type, provider, config, fees_percentage, fees_fixed,
            min_amount, max_amount, supported_currencies, is_active, sort_order
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $input['name'],
        $input['name_ar'],
        $input['type'],
        $input['provider'] ?? null,
        json_encode($input['config'] ?? []),
        $input['fees_percentage'] ?? 0.00,
        $input['fees_fixed'] ?? 0.00,
        $input['min_amount'] ?? 0.00,
        $input['max_amount'] ?? null,
        json_encode($input['supported_currencies'] ?? ['DZD']),
        $input['is_active'] ?? true,
        $input['sort_order'] ?? 0
    ]);

    $methodId = $pdo->lastInsertId();

    echo json_encode([
        'success' => true,
        'message' => 'Payment method created successfully',
        'data' => ['id' => $methodId]
    ]);
}

function updatePaymentMethod()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['id'])) {
        throw new Exception('Invalid input data');
    }

    $id = $input['id'];

    // Check if method exists
    $stmt = $pdo->prepare("SELECT id FROM payment_methods WHERE id = ?");
    $stmt->execute([$id]);
    if (!$stmt->fetch()) {
        throw new Exception('Payment method not found');
    }

    // Build update query
    $fields = [];
    $params = [];

    $allowedFields = [
        'name',
        'name_ar',
        'type',
        'provider',
        'config',
        'fees_percentage',
        'fees_fixed',
        'min_amount',
        'max_amount',
        'supported_currencies',
        'is_active',
        'sort_order'
    ];

    foreach ($allowedFields as $field) {
        if (array_key_exists($field, $input)) {
            $fields[] = "$field = ?";

            if (in_array($field, ['config', 'supported_currencies'])) {
                $params[] = json_encode($input[$field]);
            } else {
                $params[] = $input[$field];
            }
        }
    }

    if (empty($fields)) {
        throw new Exception('No fields to update');
    }

    $params[] = $id;

    $stmt = $pdo->prepare("
        UPDATE payment_methods
        SET " . implode(', ', $fields) . "
        WHERE id = ?
    ");

    $stmt->execute($params);

    echo json_encode([
        'success' => true,
        'message' => 'Payment method updated successfully'
    ]);
}

function deletePaymentMethod($id)
{
    global $pdo;

    // Check if method is used in orders (if orders table exists)
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE payment_method_id = ?");
        $stmt->execute([$id]);
        $ordersCount = $stmt->fetchColumn();

        if ($ordersCount > 0) {
            throw new Exception('Cannot delete payment method with existing orders');
        }
    } catch (PDOException $e) {
        // Orders table doesn't exist, continue
    }

    $stmt = $pdo->prepare("DELETE FROM payment_methods WHERE id = ?");
    $stmt->execute([$id]);

    echo json_encode([
        'success' => true,
        'message' => 'Payment method deleted successfully'
    ]);
}

function getPaymentStats()
{
    global $pdo;

    $stats = [];

    // Total methods
    $stmt = $pdo->query("SELECT COUNT(*) FROM payment_methods");
    $stats['total'] = $stmt->fetchColumn();

    // Active methods
    $stmt = $pdo->query("SELECT COUNT(*) FROM payment_methods WHERE is_active = 1");
    $stats['active'] = $stmt->fetchColumn();

    // Methods by type
    $stmt = $pdo->query("
        SELECT type, COUNT(*) as count
        FROM payment_methods
        GROUP BY type
    ");
    $stats['by_type'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // Average fees
    $stmt = $pdo->query("
        SELECT
            AVG(fees_percentage) as avg_percentage,
            AVG(fees_fixed) as avg_fixed
        FROM payment_methods
        WHERE is_active = 1
    ");
    $fees = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['average_fees'] = [
        'percentage' => round($fees['avg_percentage'], 2),
        'fixed' => round($fees['avg_fixed'], 2)
    ];

    echo json_encode([
        'success' => true,
        'data' => $stats
    ]);
}

function testPaymentMethod($methodId)
{
    global $pdo;

    if (!$methodId) {
        throw new Exception('Payment method ID is required');
    }

    $stmt = $pdo->prepare("SELECT * FROM payment_methods WHERE id = ? AND is_active = 1");
    $stmt->execute([$methodId]);
    $method = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$method) {
        throw new Exception('Payment method not found or inactive');
    }

    $config = json_decode($method['config'], true) ?: [];
    $testResults = [];

    // Basic validation tests
    $testResults['basic_validation'] = [
        'name_exists' => !empty($method['name']),
        'type_valid' => in_array($method['type'], ['credit_card', 'bank_transfer', 'cash_on_delivery', 'digital_wallet', 'cryptocurrency']),
        'currencies_configured' => !empty(json_decode($method['supported_currencies'], true))
    ];

    // Configuration tests based on type
    switch ($method['type']) {
        case 'credit_card':
            $testResults['config_validation'] = [
                'api_key_exists' => !empty($config['api_key']),
                'secret_key_exists' => !empty($config['secret_key']),
                'endpoint_configured' => !empty($config['endpoint'])
            ];
            break;

        case 'bank_transfer':
            $testResults['config_validation'] = [
                'bank_name_exists' => !empty($config['bank_name']),
                'account_number_exists' => !empty($config['account_number']),
                'iban_exists' => !empty($config['iban'])
            ];
            break;

        case 'digital_wallet':
            $testResults['config_validation'] = [
                'wallet_id_exists' => !empty($config['wallet_id']),
                'api_credentials_exist' => !empty($config['api_key'])
            ];
            break;

        default:
            $testResults['config_validation'] = ['basic_config' => true];
    }

    // Calculate overall status
    $allTests = array_merge($testResults['basic_validation'], $testResults['config_validation']);
    $passedTests = array_filter($allTests);
    $testResults['overall'] = [
        'status' => count($passedTests) === count($allTests) ? 'passed' : 'failed',
        'passed' => count($passedTests),
        'total' => count($allTests),
        'percentage' => round((count($passedTests) / count($allTests)) * 100, 2)
    ];

    echo json_encode([
        'success' => true,
        'data' => [
            'method' => $method['name_ar'],
            'type' => $method['type'],
            'test_results' => $testResults
        ]
    ]);
}
