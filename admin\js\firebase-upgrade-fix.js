/**
 * Firebase Upgrade and Compatibility Fix
 * Mise à jour et amélioration de la compatibilité Firebase
 * 
 * Objectifs :
 * - Mise à jour vers Firebase SDK 10.8.0+ (depuis 10.7.1)
 * - Amélioration de la gestion des erreurs réseau
 * - Optimisation des performances Firestore
 * - Gestion avancée du mode hors ligne
 */

// Configuration de mise à jour Firebase
const FIREBASE_UPGRADE_CONFIG = {
    TARGET_VERSION: '10.8.0',
    CURRENT_VERSION: '10.7.1',
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_ENHANCED_LOGGING: true,
    FORCE_LONG_POLLING: true,
    NETWORK_TIMEOUT: 10000 // 10 secondes
};

/**
 * Gestionnaire de mise à jour Firebase
 */
class FirebaseUpgradeManager {
    constructor() {
        this.isUpgraded = false;
        this.networkStatus = navigator.onLine;
        this.performanceMetrics = new Map();
        this.initializeUpgrade();
    }

    /**
     * Initialise la mise à jour Firebase
     */
    async initializeUpgrade() {
        console.log('🚀 Initialisation de la mise à jour Firebase...');
        
        try {
            // Vérifier la version actuelle
            await this.checkCurrentVersion();
            
            // Appliquer les améliorations de compatibilité
            this.applyCompatibilityFixes();
            
            // Optimiser les performances
            this.optimizePerformance();
            
            // Améliorer la gestion réseau
            this.enhanceNetworkHandling();
            
            // Activer le monitoring
            if (FIREBASE_UPGRADE_CONFIG.ENABLE_PERFORMANCE_MONITORING) {
                this.enablePerformanceMonitoring();
            }
            
            this.isUpgraded = true;
            console.log('✅ Mise à jour Firebase terminée avec succès');
            
        } catch (error) {
            console.error('❌ Erreur lors de la mise à jour Firebase:', error);
        }
    }

    /**
     * Vérifie la version actuelle de Firebase
     */
    async checkCurrentVersion() {
        try {
            // Tenter de détecter la version Firebase chargée
            const scripts = document.querySelectorAll('script[src*="firebase"]');
            let detectedVersion = FIREBASE_UPGRADE_CONFIG.CURRENT_VERSION;
            
            scripts.forEach(script => {
                const src = script.src;
                const versionMatch = src.match(/firebasejs\/(\d+\.\d+\.\d+)/);
                if (versionMatch) {
                    detectedVersion = versionMatch[1];
                }
            });
            
            console.log(`📦 Version Firebase détectée: ${detectedVersion}`);
            
            if (this.compareVersions(detectedVersion, FIREBASE_UPGRADE_CONFIG.TARGET_VERSION) < 0) {
                console.warn(`⚠️ Version Firebase obsolète: ${detectedVersion} < ${FIREBASE_UPGRADE_CONFIG.TARGET_VERSION}`);
                this.suggestUpgrade();
            }
            
        } catch (error) {
            console.debug('Impossible de détecter la version Firebase:', error.message);
        }
    }

    /**
     * Compare deux versions
     */
    compareVersions(version1, version2) {
        const v1parts = version1.split('.').map(Number);
        const v2parts = version2.split('.').map(Number);
        
        for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
            const v1part = v1parts[i] || 0;
            const v2part = v2parts[i] || 0;
            
            if (v1part < v2part) return -1;
            if (v1part > v2part) return 1;
        }
        
        return 0;
    }

    /**
     * Suggère une mise à jour
     */
    suggestUpgrade() {
        console.log(`
🔄 MISE À JOUR RECOMMANDÉE
` +
                   `Version actuelle: ${FIREBASE_UPGRADE_CONFIG.CURRENT_VERSION}\n` +
                   `Version recommandée: ${FIREBASE_UPGRADE_CONFIG.TARGET_VERSION}\n` +
                   `\nPour mettre à jour, modifiez les URLs dans vos scripts :\n` +
                   `Remplacez: firebasejs/${FIREBASE_UPGRADE_CONFIG.CURRENT_VERSION}/\n` +
                   `Par: firebasejs/${FIREBASE_UPGRADE_CONFIG.TARGET_VERSION}/\n`);
    }

    /**
     * Applique les corrections de compatibilité
     */
    applyCompatibilityFixes() {
        console.log('🔧 Application des corrections de compatibilité...');
        
        // Fix pour les erreurs de session WebChannel
        this.fixWebChannelErrors();
        
        // Fix pour les timeouts réseau
        this.fixNetworkTimeouts();
        
        // Fix pour les erreurs de synchronisation
        this.fixSyncErrors();
        
        // Fix pour les problèmes de cache
        this.fixCacheIssues();
    }

    /**
     * Corrige les erreurs WebChannel
     */
    fixWebChannelErrors() {
        // Intercepter les erreurs WebChannel spécifiques
        const originalWebSocket = window.WebSocket;
        
        window.WebSocket = function(url, protocols) {
            const ws = new originalWebSocket(url, protocols);
            
            // Gérer les erreurs de connexion WebSocket
            ws.addEventListener('error', (event) => {
                if (url.includes('firestore.googleapis.com')) {
                    console.debug('🔄 WebSocket Firestore error, falling back to HTTP');
                    // Force le fallback vers HTTP long polling
                    if (window.firestoreErrorHandler) {
                        window.firestoreErrorHandler.resetFirebaseSession();
                    }
                }
            });
            
            return ws;
        };
    }

    /**
     * Corrige les timeouts réseau
     */
    fixNetworkTimeouts() {
        // Augmenter les timeouts pour les requêtes lentes
        const originalFetch = window.fetch;
        
        window.fetch = function(url, options = {}) {
            if (typeof url === 'string' && url.includes('firestore.googleapis.com')) {
                // Ajouter un timeout personnalisé
                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    controller.abort();
                }, FIREBASE_UPGRADE_CONFIG.NETWORK_TIMEOUT);
                
                options.signal = controller.signal;
                
                return originalFetch(url, options)
                    .finally(() => clearTimeout(timeoutId))
                    .catch(error => {
                        if (error.name === 'AbortError') {
                            console.warn('⏰ Firestore request timeout, retrying...');
                            // Retry sans timeout
                            return originalFetch(url, { ...options, signal: undefined });
                        }
                        throw error;
                    });
            }
            
            return originalFetch(url, options);
        };
    }

    /**
     * Corrige les erreurs de synchronisation
     */
    fixSyncErrors() {
        // Gérer les erreurs de synchronisation Firestore
        window.addEventListener('unhandledrejection', (event) => {
            const error = event.reason;
            
            if (error && error.code === 'failed-precondition') {
                console.debug('🔄 Firestore sync error, attempting recovery...');
                event.preventDefault();
                
                // Tenter une récupération automatique
                setTimeout(() => {
                    if (window.firebaseAuth && window.firebaseAuth.currentUser) {
                        window.firebaseAuth.loadUserProfile(window.firebaseAuth.currentUser.uid);
                    }
                }, 2000);
            }
        });
    }

    /**
     * Corrige les problèmes de cache
     */
    fixCacheIssues() {
        // Nettoyer le cache IndexedDB en cas de corruption
        if ('indexedDB' in window) {
            const cleanupCache = () => {
                try {
                    const deleteReq = indexedDB.deleteDatabase('firestore_cache');
                    deleteReq.onsuccess = () => {
                        console.log('🧹 Cache Firestore nettoyé');
                    };
                } catch (error) {
                    console.debug('Cache cleanup error:', error.message);
                }
            };
            
            // Nettoyer en cas d'erreur de quota
            window.addEventListener('error', (event) => {
                if (event.error && event.error.name === 'QuotaExceededError') {
                    console.warn('💾 Quota de stockage dépassé, nettoyage du cache...');
                    cleanupCache();
                }
            });
        }
    }

    /**
     * Optimise les performances
     */
    optimizePerformance() {
        console.log('⚡ Optimisation des performances Firebase...');
        
        // Précharger les ressources critiques
        this.preloadCriticalResources();
        
        // Optimiser les requêtes Firestore
        this.optimizeFirestoreQueries();
        
        // Réduire la latence réseau
        this.reduceNetworkLatency();
    }

    /**
     * Précharge les ressources critiques
     */
    preloadCriticalResources() {
        const criticalUrls = [
            'https://firestore.googleapis.com',
            'https://firebase.googleapis.com'
        ];
        
        criticalUrls.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'dns-prefetch';
            link.href = url;
            document.head.appendChild(link);
        });
    }

    /**
     * Optimise les requêtes Firestore
     */
    optimizeFirestoreQueries() {
        // Implémenter un cache de requêtes intelligent
        const queryCache = new Map();
        const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
        
        window.optimizedFirestoreGet = async (docRef) => {
            const cacheKey = docRef.path;
            const cached = queryCache.get(cacheKey);
            
            if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
                console.debug('📋 Utilisation du cache pour:', cacheKey);
                return cached.data;
            }
            
            try {
                const result = await window.getDocumentWithRetry(docRef);
                queryCache.set(cacheKey, {
                    data: result,
                    timestamp: Date.now()
                });
                return result;
            } catch (error) {
                // Retourner les données en cache même expirées en cas d'erreur
                if (cached) {
                    console.debug('🔄 Utilisation du cache expiré en cas d\'erreur');
                    return cached.data;
                }
                throw error;
            }
        };
    }

    /**
     * Réduit la latence réseau
     */
    reduceNetworkLatency() {
        // Utiliser HTTP/2 Server Push si disponible
        if ('serviceWorker' in navigator) {
            this.setupServiceWorkerOptimizations();
        }
        
        // Optimiser les en-têtes de requête
        this.optimizeRequestHeaders();
    }

    /**
     * Configure les optimisations Service Worker
     */
    async setupServiceWorkerOptimizations() {
        try {
            const registration = await navigator.serviceWorker.getRegistration();
            if (registration) {
                console.log('🔧 Service Worker détecté, optimisations activées');
                // Envoyer des instructions d'optimisation au SW
                if (registration.active) {
                    registration.active.postMessage({
                        type: 'OPTIMIZE_FIREBASE',
                        config: FIREBASE_UPGRADE_CONFIG
                    });
                }
            }
        } catch (error) {
            console.debug('Service Worker optimization error:', error.message);
        }
    }

    /**
     * Optimise les en-têtes de requête
     */
    optimizeRequestHeaders() {
        const originalFetch = window.fetch;
        
        window.fetch = function(url, options = {}) {
            if (typeof url === 'string' && url.includes('googleapis.com')) {
                // Ajouter des en-têtes d'optimisation
                options.headers = {
                    ...options.headers,
                    'Cache-Control': 'max-age=300', // 5 minutes
                    'Connection': 'keep-alive'
                };
            }
            
            return originalFetch(url, options);
        };
    }

    /**
     * Améliore la gestion réseau
     */
    enhanceNetworkHandling() {
        console.log('🌐 Amélioration de la gestion réseau...');
        
        // Surveiller la qualité de la connexion
        this.monitorConnectionQuality();
        
        // Adapter le comportement selon le type de connexion
        this.adaptToConnectionType();
        
        // Implémenter une reconnexion intelligente
        this.setupIntelligentReconnection();
    }

    /**
     * Surveille la qualité de la connexion
     */
    monitorConnectionQuality() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            const logConnectionInfo = () => {
                console.log('📶 Qualité de connexion:', {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt,
                    saveData: connection.saveData
                });
                
                // Adapter le comportement selon la connexion
                if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                    console.log('🐌 Connexion lente détectée, optimisations activées');
                    this.enableSlowConnectionMode();
                }
            };
            
            connection.addEventListener('change', logConnectionInfo);
            logConnectionInfo();
        }
    }

    /**
     * Active le mode connexion lente
     */
    enableSlowConnectionMode() {
        // Réduire la fréquence des requêtes
        window.FIREBASE_SLOW_MODE = true;
        
        // Augmenter les timeouts
        FIREBASE_UPGRADE_CONFIG.NETWORK_TIMEOUT = 30000; // 30 secondes
        
        console.log('🐌 Mode connexion lente activé');
    }

    /**
     * Adapte le comportement selon le type de connexion
     */
    adaptToConnectionType() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            if (connection.saveData) {
                console.log('💾 Mode économie de données détecté');
                // Désactiver les fonctionnalités non essentielles
                window.FIREBASE_DATA_SAVER_MODE = true;
            }
        }
    }

    /**
     * Configure une reconnexion intelligente
     */
    setupIntelligentReconnection() {
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        
        const handleReconnection = () => {
            if (navigator.onLine && reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                console.log(`🔄 Tentative de reconnexion ${reconnectAttempts}/${maxReconnectAttempts}`);
                
                // Réinitialiser la session Firebase
                if (window.firestoreErrorHandler) {
                    window.firestoreErrorHandler.resetFirebaseSession();
                }
                
                // Reset du compteur en cas de succès
                setTimeout(() => {
                    if (navigator.onLine) {
                        reconnectAttempts = 0;
                    }
                }, 10000);
            }
        };
        
        window.addEventListener('online', handleReconnection);
    }

    /**
     * Active le monitoring des performances
     */
    enablePerformanceMonitoring() {
        console.log('📊 Activation du monitoring des performances...');
        
        // Surveiller les temps de réponse
        this.monitorResponseTimes();
        
        // Surveiller les erreurs
        this.monitorErrors();
        
        // Générer des rapports périodiques
        this.setupPerformanceReporting();
    }

    /**
     * Surveille les temps de réponse
     */
    monitorResponseTimes() {
        const originalFetch = window.fetch;
        
        window.fetch = function(url, options) {
            const startTime = performance.now();
            
            return originalFetch(url, options)
                .then(response => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    if (typeof url === 'string' && url.includes('firestore.googleapis.com')) {
                        console.debug(`⏱️ Firestore request: ${duration.toFixed(2)}ms`);
                        
                        // Enregistrer les métriques
                        if (window.firebaseUpgradeManager) {
                            window.firebaseUpgradeManager.recordMetric('firestore_response_time', duration);
                        }
                    }
                    
                    return response;
                })
                .catch(error => {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    if (typeof url === 'string' && url.includes('firestore.googleapis.com')) {
                        console.debug(`❌ Firestore error after ${duration.toFixed(2)}ms:`, error.message);
                    }
                    
                    throw error;
                });
        };
    }

    /**
     * Surveille les erreurs
     */
    monitorErrors() {
        let errorCount = 0;
        const errorThreshold = 10;
        
        window.addEventListener('error', (event) => {
            if (event.error && event.error.message.includes('firebase')) {
                errorCount++;
                this.recordMetric('firebase_errors', errorCount);
                
                if (errorCount >= errorThreshold) {
                    console.warn(`⚠️ Seuil d'erreurs Firebase atteint: ${errorCount}`);
                    // Déclencher une action corrective
                    this.triggerErrorRecovery();
                }
            }
        });
    }

    /**
     * Déclenche une récupération d'erreur
     */
    async triggerErrorRecovery() {
        console.log('🔧 Déclenchement de la récupération d\'erreur...');
        
        try {
            // Réinitialiser la session
            if (window.firestoreErrorHandler) {
                await window.firestoreErrorHandler.resetFirebaseSession();
            }
            
            // Nettoyer le cache
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                await Promise.all(
                    cacheNames.map(name => caches.delete(name))
                );
            }
            
            // Recharger la page en dernier recours
            setTimeout(() => {
                if (this.performanceMetrics.get('firebase_errors') > 20) {
                    console.warn('🔄 Rechargement de la page nécessaire');
                    window.location.reload();
                }
            }, 30000);
            
        } catch (error) {
            console.error('❌ Erreur lors de la récupération:', error);
        }
    }

    /**
     * Configure les rapports de performance
     */
    setupPerformanceReporting() {
        setInterval(() => {
            this.generatePerformanceReport();
        }, 5 * 60 * 1000); // Toutes les 5 minutes
    }

    /**
     * Génère un rapport de performance
     */
    generatePerformanceReport() {
        const report = {
            timestamp: new Date().toISOString(),
            metrics: Object.fromEntries(this.performanceMetrics),
            networkStatus: navigator.onLine,
            connectionType: navigator.connection?.effectiveType || 'unknown'
        };
        
        console.log('📊 Rapport de performance Firebase:', report);
        
        // Envoyer le rapport si nécessaire
        if (typeof window.onFirebasePerformanceReport === 'function') {
            window.onFirebasePerformanceReport(report);
        }
    }

    /**
     * Enregistre une métrique
     */
    recordMetric(name, value) {
        if (!this.performanceMetrics.has(name)) {
            this.performanceMetrics.set(name, []);
        }
        
        const metrics = this.performanceMetrics.get(name);
        metrics.push({
            value,
            timestamp: Date.now()
        });
        
        // Garder seulement les 100 dernières valeurs
        if (metrics.length > 100) {
            metrics.splice(0, metrics.length - 100);
        }
    }

    /**
     * Obtient les statistiques d'une métrique
     */
    getMetricStats(name) {
        const metrics = this.performanceMetrics.get(name) || [];
        if (metrics.length === 0) return null;
        
        const values = metrics.map(m => m.value);
        return {
            count: values.length,
            min: Math.min(...values),
            max: Math.max(...values),
            avg: values.reduce((a, b) => a + b, 0) / values.length,
            latest: values[values.length - 1]
        };
    }
}

// Initialisation automatique
let firebaseUpgradeManager;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        firebaseUpgradeManager = new FirebaseUpgradeManager();
    });
} else {
    firebaseUpgradeManager = new FirebaseUpgradeManager();
}

// Export global
window.FirebaseUpgradeManager = FirebaseUpgradeManager;
window.firebaseUpgradeManager = firebaseUpgradeManager;

console.log('🚀 Firebase Upgrade Fix chargé');