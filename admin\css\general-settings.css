/**
 * General Settings Styles
 * أنماط الإعدادات العامة
 */

/* Settings Container */
.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Settings Header */
.settings-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.settings-header h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.settings-header p {
    color: #666;
    font-size: 1.1rem;
    margin: 0;
}

/* Settings Form */
.settings-form {
    width: 100%;
}

/* Settings Groups */
.settings-group {
    margin-bottom: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.settings-group-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.settings-group-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.settings-group-header i {
    margin-left: 10px;
    font-size: 1.1rem;
}

.settings-group-content {
    padding: 25px;
    background: #fafafa;
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.form-group .required {
    color: #dc3545;
    margin-right: 3px;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #ffffff;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: #ffffff;
}

.form-control:hover {
    border-color: #c1c9d2;
}

/* Textarea */
textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

/* Select */
select.form-control {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-left: 40px;
}

/* Checkbox */
.form-check {
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    margin-left: 10px;
    cursor: pointer;
    accent-color: #667eea;
}

.form-check-label {
    cursor: pointer;
    font-weight: 500;
    color: #333;
}

/* File Input */
input[type="file"].form-control {
    padding: 8px 12px;
    cursor: pointer;
}

.current-file {
    margin-top: 8px;
    padding: 8px 12px;
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    font-size: 0.9em;
    color: #1565c0;
}

.current-file a {
    color: #1565c0;
    text-decoration: none;
    font-weight: 500;
}

.current-file a:hover {
    text-decoration: underline;
}

/* Form Text */
.form-text {
    display: block;
    margin-top: 6px;
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Settings Actions */
.settings-actions {
    text-align: center;
    padding: 25px 20px;
    border-top: 2px solid #f0f0f0;
    margin-top: 30px;
    background: #f8f9fa;
}

.settings-actions .btn {
    margin: 0 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    box-shadow: 0 4px 6px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(108, 117, 125, 0.4);
}

.btn-info {
    background: #17a2b8;
    color: white;
    box-shadow: 0 4px 6px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(23, 162, 184, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.loading-spinner i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
    animation: spin 1s linear infinite;
}

.loading-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* Error State */
.error-state {
    text-align: center;
    padding: 60px 20px;
    color: #dc3545;
}

.error-icon i {
    font-size: 3rem;
    margin-bottom: 20px;
}

.error-state h4 {
    color: #dc3545;
    margin-bottom: 15px;
}

.error-state p {
    color: #666;
    margin-bottom: 20px;
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-container {
    animation: fadeIn 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        margin: 10px;
        padding: 15px;
    }
    
    .settings-header h2 {
        font-size: 1.5rem;
    }
    
    .settings-group-content {
        padding: 20px 15px;
    }
    
    .settings-actions {
        padding: 20px 15px;
    }
    
    .settings-actions .btn {
        margin: 5px;
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .form-control {
        padding: 10px 12px;
        font-size: 0.9rem;
    }
    
    select.form-control {
        padding-left: 35px;
    }
}

@media (max-width: 480px) {
    .settings-actions .btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
    
    .settings-header h2 {
        font-size: 1.3rem;
    }
    
    .settings-header p {
        font-size: 1rem;
    }
}
