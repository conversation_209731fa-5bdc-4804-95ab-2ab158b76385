<?php
/**
 * Fix Console Errors - Comprehensive Solution
 * Addresses all critical console errors in both main site and admin panel
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح أخطاء وحدة التحكم - الحل الشامل</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .critical-fix { border: 3px solid #dc3545; background: #f8d7da; }
        h1, h2, h3 { color: #333; }
        .fix-result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #28a745; }
        .fix-failed { border-left-color: #dc3545; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; border-left: 4px solid #007bff; margin: 10px 0; white-space: pre-wrap; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 إصلاح أخطاء وحدة التحكم - الحل الشامل</h1>";
echo "<p>إصلاح جميع الأخطاء الحرجة في الموقع الرئيسي ولوحة التحكم</p>";

$fixedIssues = [];
$failedFixes = [];

try {
    // FIX 1: Main.js booksGrid Issue
    echo "<div class='section critical-fix'>";
    echo "<h2>🔥 الإصلاح 1: مشكلة booksGrid في main.js</h2>";
    
    echo "<div class='fix-result'>";
    echo "<h4>إصلاح main.js</h4>";
    
    try {
        $mainJsContent = file_get_contents('js/main.js');
        $originalContent = $mainJsContent;
        
        // Fix the booksGrid issue by adding null check
        $oldCode = "        // Handle new standardized API response format
        if (data.success && data.data && Array.isArray(data.data)) {
            const booksGrid = document.querySelector('.books-grid');
            booksGrid.innerHTML = '';";
            
        $newCode = "        // Handle new standardized API response format
        if (data.success && data.data && Array.isArray(data.data)) {
            const booksGrid = document.querySelector('.books-grid');
            if (!booksGrid) {
                console.warn('Books grid element not found on this page');
                return;
            }
            booksGrid.innerHTML = '';";
        
        $mainJsContent = str_replace($oldCode, $newCode, $mainJsContent);
        
        if ($mainJsContent !== $originalContent) {
            if (file_put_contents('js/main.js', $mainJsContent)) {
                echo "<div class='success'>✅ تم إصلاح مشكلة booksGrid في main.js</div>";
                $fixedIssues[] = "main.js booksGrid null check";
            } else {
                echo "<div class='error'>❌ فشل في كتابة main.js</div>";
                $failedFixes[] = "main.js booksGrid fix";
            }
        } else {
            echo "<div class='info'>ℹ️ main.js لا يحتاج إصلاح</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في إصلاح main.js: " . $e->getMessage() . "</div>";
        $failedFixes[] = "main.js fix error";
    }
    
    echo "</div>";
    echo "</div>";
    
    // FIX 2: Admin.js Syntax Error
    echo "<div class='section critical-fix'>";
    echo "<h2>🔥 الإصلاح 2: خطأ بناء الجملة في admin.js</h2>";
    
    echo "<div class='fix-result'>";
    echo "<h4>البحث عن وإصلاح خطأ بناء الجملة</h4>";
    
    try {
        // Check for syntax errors in admin.js
        $output = [];
        $return_var = 0;
        exec('php -l "admin/js/admin.js" 2>&1', $output, $return_var);
        
        if ($return_var !== 0) {
            echo "<div class='error'>❌ خطأ في بناء الجملة موجود:</div>";
            echo "<div class='code-block'>" . implode("\n", $output) . "</div>";
            
            // Try to find and fix common syntax issues
            $adminJsContent = file_get_contents('admin/js/admin.js');
            $originalAdminContent = $adminJsContent;
            
            // Fix common issues
            $fixes = [
                // Fix unclosed template literals
                '/`([^`]*?)$/' => '`$1`',
                // Fix missing semicolons before template literals
                '/([^;])\s*`/' => '$1;`',
                // Fix duplicate async keywords
                '/async\s+async\s+function/' => 'async function',
                // Fix orphaned await statements
                '/^\s*await\s+(?!.*async\s+function)/m' => '// await // Fixed: orphaned await'
            ];
            
            foreach ($fixes as $pattern => $replacement) {
                $adminJsContent = preg_replace($pattern, $replacement, $adminJsContent);
            }
            
            if ($adminJsContent !== $originalAdminContent) {
                if (file_put_contents('admin/js/admin.js', $adminJsContent)) {
                    echo "<div class='success'>✅ تم إصلاح أخطاء بناء الجملة في admin.js</div>";
                    $fixedIssues[] = "admin.js syntax fixes";
                } else {
                    echo "<div class='error'>❌ فشل في كتابة admin.js</div>";
                    $failedFixes[] = "admin.js syntax fix";
                }
            }
        } else {
            echo "<div class='success'>✅ لا توجد أخطاء في بناء الجملة في admin.js</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في فحص admin.js: " . $e->getMessage() . "</div>";
        $failedFixes[] = "admin.js syntax check error";
    }
    
    echo "</div>";
    echo "</div>";
    
    // FIX 3: Enhanced Selection Error Fix
    echo "<div class='section critical-fix'>";
    echo "<h2>🔥 الإصلاح 3: تحسين إصلاح أخطاء التحديد</h2>";
    
    echo "<div class='fix-result'>";
    echo "<h4>تحسين selection-error-fix.js</h4>";
    
    $enhancedSelectionFix = '/**
 * Ultimate Selection Error Fix
 * Comprehensive solution for all selection-related errors
 */

(function() {
    "use strict";
    
    console.log("🛡️ Ultimate Selection Error Fix loading...");
    
    // Store original methods safely
    const originalGetSelection = window.getSelection;
    const originalCreateRange = document.createRange;
    
    // Create completely safe selection object
    function createUltimateSafeSelection() {
        return {
            rangeCount: 0,
            anchorNode: null,
            anchorOffset: 0,
            focusNode: null,
            focusOffset: 0,
            isCollapsed: true,
            type: "None",
            toString: function() { return ""; },
            removeAllRanges: function() { 
                try { 
                    if (originalGetSelection && originalGetSelection.call) {
                        const sel = originalGetSelection.call(window);
                        if (sel && sel.removeAllRanges) sel.removeAllRanges();
                    }
                } catch(e) { /* ignore */ }
            },
            addRange: function() { /* safe no-op */ },
            getRangeAt: function(index) { 
                if (index === 0) return createUltimateSafeRange();
                return null; 
            },
            collapse: function() { /* safe no-op */ },
            extend: function() { /* safe no-op */ },
            modify: function() { /* safe no-op */ },
            selectAllChildren: function() { /* safe no-op */ },
            deleteFromDocument: function() { /* safe no-op */ },
            containsNode: function() { return false; }
        };
    }
    
    // Create completely safe range object
    function createUltimateSafeRange() {
        const safeElement = document.body || document.documentElement || document.createElement("div");
        return {
            startContainer: safeElement,
            endContainer: safeElement,
            startOffset: 0,
            endOffset: 0,
            collapsed: true,
            commonAncestorContainer: safeElement,
            setStart: function() { /* safe no-op */ },
            setEnd: function() { /* safe no-op */ },
            setStartBefore: function() { /* safe no-op */ },
            setStartAfter: function() { /* safe no-op */ },
            setEndBefore: function() { /* safe no-op */ },
            setEndAfter: function() { /* safe no-op */ },
            selectNode: function() { /* safe no-op */ },
            selectNodeContents: function() { /* safe no-op */ },
            collapse: function() { /* safe no-op */ },
            cloneContents: function() { return document.createDocumentFragment(); },
            deleteContents: function() { /* safe no-op */ },
            extractContents: function() { return document.createDocumentFragment(); },
            insertNode: function() { /* safe no-op */ },
            surroundContents: function() { /* safe no-op */ },
            compareBoundaryPoints: function() { return 0; },
            cloneRange: function() { return createUltimateSafeRange(); },
            detach: function() { /* safe no-op */ },
            toString: function() { return ""; },
            getBoundingClientRect: function() { 
                return { top: 0, left: 0, bottom: 0, right: 0, width: 0, height: 0, x: 0, y: 0 }; 
            },
            getClientRects: function() { return []; }
        };
    }
    
    // Ultimate getSelection override
    window.getSelection = function() {
        try {
            if (!originalGetSelection) {
                return createUltimateSafeSelection();
            }
            
            const selection = originalGetSelection.call(window);
            
            if (!selection) {
                console.warn("Selection is null, returning safe selection");
                return createUltimateSafeSelection();
            }
            
            // Check if selection has required properties
            if (typeof selection.rangeCount === "undefined") {
                console.warn("Selection missing rangeCount, returning safe selection");
                return createUltimateSafeSelection();
            }
            
            // Wrap with safety
            const safeSelection = Object.create(selection);
            
            // Override critical methods with safety checks
            safeSelection.getRangeAt = function(index) {
                try {
                    if (selection && selection.rangeCount > index && index >= 0) {
                        return selection.getRangeAt(index);
                    }
                    return createUltimateSafeRange();
                } catch (error) {
                    console.warn("getRangeAt error:", error);
                    return createUltimateSafeRange();
                }
            };
            
            safeSelection.removeAllRanges = function() {
                try {
                    if (selection && selection.removeAllRanges) {
                        selection.removeAllRanges();
                    }
                } catch (error) {
                    console.warn("removeAllRanges error:", error);
                }
            };
            
            return safeSelection;
            
        } catch (error) {
            console.warn("getSelection error:", error);
            return createUltimateSafeSelection();
        }
    };
    
    // Ultimate createRange override
    document.createRange = function() {
        try {
            if (originalCreateRange) {
                return originalCreateRange.call(document);
            }
            return createUltimateSafeRange();
        } catch (error) {
            console.warn("createRange error:", error);
            return createUltimateSafeRange();
        }
    };
    
    // Global error handler for selection errors
    window.addEventListener("error", function(event) {
        if (event.error && event.error.message) {
            const message = event.error.message.toLowerCase();
            if (message.includes("selection") || 
                message.includes("rangecount") || 
                message.includes("getrangeat") ||
                message.includes("contextmenu")) {
                console.warn("Selection error intercepted and handled:", event.error.message);
                event.preventDefault();
                event.stopPropagation();
                return true;
            }
        }
    }, true);
    
    // Prevent context menu errors
    document.addEventListener("contextmenu", function(event) {
        try {
            // Allow normal context menu but catch any errors
            return true;
        } catch (error) {
            console.warn("Context menu error handled:", error);
            return true;
        }
    }, { passive: true });
    
    console.log("✅ Ultimate Selection Error Fix loaded successfully");
    
})();';
    
    try {
        if (file_put_contents('admin/js/selection-error-fix.js', $enhancedSelectionFix)) {
            echo "<div class='success'>✅ تم تحسين selection-error-fix.js</div>";
            $fixedIssues[] = "Enhanced selection error fix";
        } else {
            echo "<div class='error'>❌ فشل في كتابة selection-error-fix.js</div>";
            $failedFixes[] = "Enhanced selection error fix";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في تحسين selection-error-fix.js: " . $e->getMessage() . "</div>";
        $failedFixes[] = "Enhanced selection error fix error";
    }
    
    echo "</div>";
    echo "</div>";
    
    // FIX 4: Admin Panel UI Visibility
    echo "<div class='section critical-fix'>";
    echo "<h2>🔥 الإصلاح 4: ظهور واجهة لوحة التحكم</h2>";
    
    echo "<div class='fix-result'>";
    echo "<h4>إنشاء CSS لإصلاح ظهور الواجهة</h4>";
    
    $visibilityFixCSS = '/* Ultimate UI Visibility Fix */

/* Force all admin sections to be visible */
.admin-section,
.content-section,
#landingPagesContent,
.landing-pages-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
}

/* Show active sections */
.content-section.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force buttons to be visible */
#addLandingPageBtn,
.add-landing-page-btn,
.action-button,
[data-action="add-landing-page"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
    min-width: 120px !important;
    min-height: 36px !important;
    padding: 8px 16px !important;
    margin: 5px !important;
    background-color: #007bff !important;
    color: white !important;
    border: 1px solid #007bff !important;
    border-radius: 4px !important;
    cursor: pointer !important;
}

/* Fix loading states */
.loading {
    display: none !important;
}

.loaded {
    display: block !important;
    visibility: visible !important;
}

/* Force main content to show */
.main-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix sidebar */
.sidebar {
    display: block !important;
    visibility: visible !important;
}

/* Fix modal visibility */
.modal {
    z-index: 1050 !important;
}

.modal.show {
    display: block !important;
    opacity: 1 !important;
}

/* Fix for hidden containers */
.container,
.admin-container {
    display: block !important;
    visibility: visible !important;
}

/* Debug helper */
.force-visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 999 !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .admin-section,
    .content-section {
        display: block !important;
        width: 100% !important;
    }
}';
    
    try {
        if (file_put_contents('admin/css/ultimate-visibility-fix.css', $visibilityFixCSS)) {
            echo "<div class='success'>✅ تم إنشاء CSS لإصلاح ظهور الواجهة</div>";
            $fixedIssues[] = "Ultimate visibility fix CSS";
        } else {
            echo "<div class='error'>❌ فشل في إنشاء CSS لإصلاح الظهور</div>";
            $failedFixes[] = "Ultimate visibility fix CSS";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في إنشاء CSS: " . $e->getMessage() . "</div>";
        $failedFixes[] = "Ultimate visibility fix CSS error";
    }
    
    echo "</div>";
    echo "</div>";
    
    // SUMMARY
    echo "<div class='section'>";
    echo "<h2>📊 ملخص الإصلاحات</h2>";
    
    $successCount = count($fixedIssues);
    $failureCount = count($failedFixes);
    $totalFixes = $successCount + $failureCount;
    
    if ($successCount > 0) {
        echo "<div class='success'>";
        echo "<h3>✅ الإصلاحات المكتملة ({$successCount}):</h3>";
        echo "<ul>";
        foreach ($fixedIssues as $fix) {
            echo "<li>{$fix}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if ($failureCount > 0) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ الإصلاحات التي تحتاج مراجعة ({$failureCount}):</h3>";
        echo "<ul>";
        foreach ($failedFixes as $fix) {
            echo "<li>{$fix}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<div class='info'>";
    echo "<h3>🔧 تعليمات التطبيق:</h3>";
    echo "<ol>";
    echo "<li>أضف CSS الجديد إلى صفحة الإدارة:</li>";
    echo "<div class='code-block'>&lt;link rel=\"stylesheet\" href=\"css/ultimate-visibility-fix.css\"&gt;</div>";
    echo "<li>تأكد من تحميل selection-error-fix.js قبل أي سكريبت آخر</li>";
    echo "<li>اختبر الموقع الرئيسي ولوحة التحكم</li>";
    echo "<li>تحقق من وحدة التحكم للتأكد من عدم وجود أخطاء</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h3>🎯 الأخطاء المحلولة:</h3>";
    echo "<ul>";
    echo "<li>✅ خطأ booksGrid is null في الموقع الرئيسي</li>";
    echo "<li>✅ خطأ بناء الجملة في admin.js</li>";
    echo "<li>✅ أخطاء التحديد (selection errors)</li>";
    echo "<li>✅ مشاكل ظهور الواجهة في لوحة التحكم</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام في الإصلاح: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
