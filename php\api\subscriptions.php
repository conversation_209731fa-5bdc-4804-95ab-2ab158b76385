<?php

/**
 * Subscriptions Management API with Enhanced Error Handling
 * Handles CRUD operations for subscription plans and user subscriptions
 * Addresses JSON parsing errors and implements comprehensive error handling
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../ApiErrorHandler.php';

// Initialize error handler to catch any issues before JSON output
ApiErrorHandler::init();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $pdo = getPDOConnection();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    // Handle different HTTP methods and actions
    switch ($method) {
        case 'GET':
            if ($action === 'plans' || empty($action)) {
                handleGetPlans($pdo);
            } elseif ($action === 'stats') {
                handleGetSubscriptionStats($pdo);
            } elseif ($action === 'check_limits') {
                handleCheckLimits($pdo);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'POST':
            if ($action === 'create_plan') {
                handleCreatePlan($pdo);
            } elseif ($action === 'assign') {
                handleAssignSubscription($pdo);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'PUT':
            if ($action === 'update_plan') {
                handleUpdatePlan($pdo);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        case 'DELETE':
            if ($action === 'delete_plan') {
                handleDeletePlan($pdo);
            } else {
                throw new Exception('Invalid action');
            }
            break;

        default:
            ApiErrorHandler::sendError('طريقة الطلب غير مسموحة', 405, 'METHOD_NOT_ALLOWED');
    }
} catch (Exception $e) {
    ApiErrorHandler::sendError(
        'خطأ في API الاشتراكات: ' . $e->getMessage(),
        500,
        'SUBSCRIPTION_API_ERROR'
    );
}

/**
 * Get all subscription plans with user counts
 */
function handleGetPlans($pdo)
{
    try {
        $stmt = $pdo->prepare("
            SELECT
                sp.*,
                COUNT(u.id) as user_count
            FROM subscription_plans sp
            LEFT JOIN users u ON sp.id = u.subscription_id
            GROUP BY sp.id
            ORDER BY sp.sort_order ASC
        ");

        $stmt->execute();
        $plans = $stmt->fetchAll();

        // Format plans data
        $formattedPlans = array_map(function ($plan) {
            return [
                'id' => (int)$plan['id'],
                'name' => $plan['name'],
                'display_name_ar' => $plan['display_name_ar'],
                'display_name_en' => $plan['display_name_en'],
                'description_ar' => $plan['description_ar'],
                'description_en' => $plan['description_en'],
                'price' => (float)$plan['price'],
                'currency' => $plan['currency'],
                'duration_days' => (int)$plan['duration_days'],
                'limits' => [
                    'products' => (int)$plan['max_products'],
                    'landing_pages' => (int)$plan['max_landing_pages'],
                    'storage_mb' => (int)$plan['max_storage_mb'],
                    'templates' => (int)$plan['max_templates']
                ],
                'features' => json_decode($plan['features'] ?? '[]', true),
                'is_active' => (bool)$plan['is_active'],
                'sort_order' => (int)$plan['sort_order'],
                'user_count' => (int)$plan['user_count'],
                'created_at' => $plan['created_at'],
                'updated_at' => $plan['updated_at']
            ];
        }, $plans);

        echo json_encode([
            'success' => true,
            'plans' => $formattedPlans,
            'total' => count($formattedPlans)
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to fetch subscription plans: ' . $e->getMessage());
    }
}

/**
 * Get subscription statistics
 */
function handleGetSubscriptionStats($pdo)
{
    try {
        // Total subscriptions
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM users WHERE subscription_id IS NOT NULL");
        $stmt->execute();
        $totalSubscriptions = $stmt->fetch()['total'];

        // Revenue by plan
        $stmt = $pdo->prepare("
            SELECT
                sp.display_name_ar as plan_name,
                sp.price,
                COUNT(u.id) as user_count,
                (sp.price * COUNT(u.id)) as total_revenue
            FROM subscription_plans sp
            LEFT JOIN users u ON sp.id = u.subscription_id
            WHERE sp.price > 0
            GROUP BY sp.id, sp.display_name_ar, sp.price
            ORDER BY total_revenue DESC
        ");
        $stmt->execute();
        $revenueByPlan = $stmt->fetchAll();

        // Usage statistics
        $stmt = $pdo->prepare("
            SELECT
                sp.display_name_ar as plan_name,
                sp.max_products,
                sp.max_landing_pages,
                COUNT(u.id) as user_count
            FROM subscription_plans sp
            LEFT JOIN users u ON sp.id = u.subscription_id
            GROUP BY sp.id, sp.display_name_ar, sp.max_products, sp.max_landing_pages
            ORDER BY sp.sort_order ASC
        ");
        $stmt->execute();
        $usageStats = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'stats' => [
                'total_subscriptions' => (int)$totalSubscriptions,
                'revenue_by_plan' => $revenueByPlan,
                'usage_stats' => $usageStats
            ]
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to fetch subscription stats: ' . $e->getMessage());
    }
}

/**
 * Check subscription limits for a user
 */
function handleCheckLimits($pdo)
{
    try {
        $userId = $_GET['user_id'] ?? '';

        if (empty($userId)) {
            throw new Exception('User ID is required');
        }

        // Get user's subscription limits
        $stmt = $pdo->prepare("
            SELECT
                sp.max_products,
                sp.max_landing_pages,
                sp.max_storage_mb,
                sp.max_templates
            FROM users u
            JOIN subscription_plans sp ON u.subscription_id = sp.id
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        $limits = $stmt->fetch();

        if (!$limits) {
            throw new Exception('User or subscription not found');
        }

        // Get current usage
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM produits WHERE user_id = ?");
        $stmt->execute([$userId]);
        $currentProducts = $stmt->fetch()['count'] ?? 0;

        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM landing_pages lp JOIN produits p ON lp.produit_id = p.id WHERE p.user_id = ?");
        $stmt->execute([$userId]);
        $currentLandingPages = $stmt->fetch()['count'] ?? 0;

        // Calculate storage usage (simplified - you might want to implement actual file size calculation)
        $currentStorage = 0; // Placeholder

        echo json_encode([
            'success' => true,
            'limits' => [
                'products' => [
                    'max' => (int)$limits['max_products'],
                    'current' => (int)$currentProducts,
                    'remaining' => max(0, (int)$limits['max_products'] - (int)$currentProducts),
                    'unlimited' => (int)$limits['max_products'] === -1
                ],
                'landing_pages' => [
                    'max' => (int)$limits['max_landing_pages'],
                    'current' => (int)$currentLandingPages,
                    'remaining' => max(0, (int)$limits['max_landing_pages'] - (int)$currentLandingPages),
                    'unlimited' => (int)$limits['max_landing_pages'] === -1
                ],
                'storage_mb' => [
                    'max' => (int)$limits['max_storage_mb'],
                    'current' => (int)$currentStorage,
                    'remaining' => max(0, (int)$limits['max_storage_mb'] - (int)$currentStorage),
                    'unlimited' => (int)$limits['max_storage_mb'] === -1
                ]
            ]
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to check limits: ' . $e->getMessage());
    }
}

/**
 * Create a new subscription plan
 */
function handleCreatePlan($pdo)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            throw new Exception('Invalid input data');
        }

        // Validate required fields
        $required = ['name', 'display_name_ar', 'display_name_en', 'price'];
        foreach ($required as $field) {
            if (!isset($input[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Check if plan name already exists
        $stmt = $pdo->prepare("SELECT id FROM subscription_plans WHERE name = ?");
        $stmt->execute([$input['name']]);
        if ($stmt->fetch()) {
            throw new Exception('Plan name already exists');
        }

        // Insert plan
        $stmt = $pdo->prepare("
            INSERT INTO subscription_plans (
                name, display_name_ar, display_name_en, description_ar, description_en,
                price, currency, duration_days, max_products, max_landing_pages,
                max_storage_mb, max_templates, features, is_active, sort_order
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $features = json_encode($input['features'] ?? []);

        $stmt->execute([
            $input['name'],
            $input['display_name_ar'],
            $input['display_name_en'],
            $input['description_ar'] ?? '',
            $input['description_en'] ?? '',
            $input['price'],
            $input['currency'] ?? 'DZD',
            $input['duration_days'] ?? 30,
            $input['max_products'] ?? 5,
            $input['max_landing_pages'] ?? 2,
            $input['max_storage_mb'] ?? 100,
            $input['max_templates'] ?? 5,
            $features,
            $input['is_active'] ?? 1,
            $input['sort_order'] ?? 0
        ]);

        $planId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Subscription plan created successfully',
            'plan_id' => $planId
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to create subscription plan: ' . $e->getMessage());
    }
}

/**
 * Update an existing subscription plan
 */
function handleUpdatePlan($pdo)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || empty($input['id'])) {
            throw new Exception('Invalid input data or missing plan ID');
        }

        $planId = $input['id'];
        $updates = [];
        $params = [];

        // Build dynamic update query
        $allowedFields = [
            'name',
            'display_name_ar',
            'display_name_en',
            'description_ar',
            'description_en',
            'price',
            'currency',
            'duration_days',
            'max_products',
            'max_landing_pages',
            'max_storage_mb',
            'max_templates',
            'is_active',
            'sort_order'
        ];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updates[] = "$field = ?";
                $params[] = $input[$field];
            }
        }

        // Handle features separately
        if (isset($input['features'])) {
            $updates[] = "features = ?";
            $params[] = json_encode($input['features']);
        }

        if (empty($updates)) {
            throw new Exception('No fields to update');
        }

        $params[] = $planId;

        $sql = "UPDATE subscription_plans SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        echo json_encode([
            'success' => true,
            'message' => 'Subscription plan updated successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to update subscription plan: ' . $e->getMessage());
    }
}

/**
 * Delete a subscription plan
 */
function handleDeletePlan($pdo)
{
    try {
        $planId = $_GET['id'] ?? '';

        if (empty($planId)) {
            throw new Exception('Plan ID is required');
        }

        // Check if plan is in use
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE subscription_id = ?");
        $stmt->execute([$planId]);
        $userCount = $stmt->fetch()['count'];

        if ($userCount > 0) {
            throw new Exception("Cannot delete plan: $userCount users are subscribed to this plan");
        }

        $stmt = $pdo->prepare("DELETE FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);

        if ($stmt->rowCount() === 0) {
            throw new Exception('Plan not found');
        }

        echo json_encode([
            'success' => true,
            'message' => 'Subscription plan deleted successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to delete subscription plan: ' . $e->getMessage());
    }
}

/**
 * Assign subscription to user
 */
function handleAssignSubscription($pdo)
{
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || empty($input['user_id']) || empty($input['subscription_id'])) {
            throw new Exception('User ID and Subscription ID are required');
        }

        $stmt = $pdo->prepare("UPDATE users SET subscription_id = ? WHERE id = ?");
        $stmt->execute([$input['subscription_id'], $input['user_id']]);

        if ($stmt->rowCount() === 0) {
            throw new Exception('User not found');
        }

        echo json_encode([
            'success' => true,
            'message' => 'Subscription assigned successfully'
        ]);
    } catch (Exception $e) {
        throw new Exception('Failed to assign subscription: ' . $e->getMessage());
    }
}
