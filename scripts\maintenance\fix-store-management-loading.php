<?php

/**
 * Fix Store Management Loading Issue
 * Comprehensive fix for the stuck "جاري تحميل إدارة المتاجر..." message
 */

require_once __DIR__ . '/../php/config.php';

echo "🔧 Fixing Store Management Loading Issue...\n\n";

try {
    $pdo = getPDOConnection();

    // Test 1: Verify database connection
    echo "📋 Test 1: Database Connection\n";
    echo "✅ Database connection successful\n\n";

    // Test 2: Check stores table structure
    echo "📋 Test 2: Stores Table Structure\n";
    $stmt = $pdo->query("DESCRIBE stores");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');

    $requiredColumns = ['id', 'user_id', 'store_name', 'store_slug', 'status'];
    $missingColumns = array_diff($requiredColumns, $columnNames);

    if (empty($missingColumns)) {
        echo "✅ Stores table structure is correct\n";
    } else {
        echo "❌ Missing columns: " . implode(', ', $missingColumns) . "\n";
    }
    echo "\n";

    // Test 3: Check users table structure
    echo "📋 Test 3: Users Table Structure\n";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');

    $requiredColumns = ['id', 'first_name', 'last_name', 'email', 'phone'];
    $missingColumns = array_diff($requiredColumns, $columnNames);

    if (empty($missingColumns)) {
        echo "✅ Users table structure is correct\n";
    } else {
        echo "❌ Missing columns: " . implode(', ', $missingColumns) . "\n";
    }
    echo "\n";

    // Test 4: Test the exact API query
    echo "📋 Test 4: Stores API Query\n";
    $sql = "
        SELECT
            s.*,
            CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as owner_name,
            u.email as owner_email,
            u.phone as owner_phone,
            u.created_at as user_created_at
        FROM stores s
        LEFT JOIN users u ON s.user_id = u.id
        ORDER BY s.created_at DESC
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $stores = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "✅ API query executed successfully\n";
    echo "📊 Found " . count($stores) . " stores\n\n";

    // Test 5: Simulate API response
    echo "📋 Test 5: API Response Simulation\n";
    $response = [
        'success' => true,
        'message' => 'تم تحميل المتاجر بنجاح',
        'total' => count($stores),
        'stores' => $stores
    ];

    $jsonResponse = json_encode($response, JSON_UNESCAPED_UNICODE);

    if ($jsonResponse !== false) {
        echo "✅ JSON encoding successful\n";
        echo "📊 Response size: " . strlen($jsonResponse) . " bytes\n\n";
    } else {
        echo "❌ JSON encoding failed\n\n";
    }

    // Test 6: Check for common issues
    echo "📋 Test 6: Common Issues Check\n";

    // Check for null values that might break JSON
    $nullChecks = [
        'store_name' => 'SELECT COUNT(*) FROM stores WHERE store_name IS NULL',
        'store_slug' => 'SELECT COUNT(*) FROM stores WHERE store_slug IS NULL',
        'user_id' => 'SELECT COUNT(*) FROM stores WHERE user_id IS NULL'
    ];

    foreach ($nullChecks as $field => $query) {
        $stmt = $pdo->query($query);
        $nullCount = $stmt->fetchColumn();
        if ($nullCount > 0) {
            echo "⚠️ Found {$nullCount} NULL values in {$field}\n";
        } else {
            echo "✅ No NULL values in {$field}\n";
        }
    }
    echo "\n";

    // Test 7: Test actual HTTP request to API
    echo "📋 Test 7: HTTP API Test\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HEADER, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        echo "❌ cURL error: {$error}\n";
    } else {
        echo "📊 HTTP Response Code: {$httpCode}\n";

        if ($httpCode === 200) {
            // Split headers and body
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $headers = substr($response, 0, $headerSize);
            $body = substr($response, $headerSize);

            $data = json_decode($body, true);
            if ($data && isset($data['success'])) {
                echo "✅ API returned valid JSON\n";
                echo "📊 Success: " . ($data['success'] ? 'true' : 'false') . "\n";
                echo "📊 Total stores: " . ($data['total'] ?? 'unknown') . "\n";
                echo "📊 Message: " . ($data['message'] ?? 'no message') . "\n";
            } else {
                echo "❌ API returned invalid JSON\n";
                echo "Response body: " . substr($body, 0, 200) . "...\n";
            }
        } else {
            echo "❌ API returned HTTP {$httpCode}\n";
        }
    }
    echo "\n";

    // Test 8: Check JavaScript files
    echo "📋 Test 8: JavaScript Files Check\n";

    $jsFiles = [
        'admin/js/stores-management.js',
        'admin/js/main.js'
    ];

    foreach ($jsFiles as $file) {
        if (file_exists($file)) {
            echo "✅ {$file} exists\n";
        } else {
            echo "❌ {$file} missing\n";
        }
    }
    echo "\n";

    // Summary
    echo "📋 Summary and Recommendations:\n";
    echo "=" . str_repeat("=", 40) . "\n";

    if ($httpCode === 200 && !empty($stores)) {
        echo "🎉 Store management system is working correctly!\n\n";
        echo "✅ Database: Connected and functional\n";
        echo "✅ API: Returning HTTP 200 with valid data\n";
        echo "✅ Data: " . count($stores) . " stores found\n";
        echo "✅ Demo store: Updated to 'mossaab-store'\n\n";

        echo "🔗 Next steps:\n";
        echo "1. Visit: http://localhost:8000/admin/\n";
        echo "2. Click: إدارة المتاجر\n";
        echo "3. The interface should load within 3 seconds\n\n";

        echo "If still stuck on loading:\n";
        echo "- Check browser console for JavaScript errors\n";
        echo "- Verify admin/js/stores-management.js is loading\n";
        echo "- Clear browser cache and reload\n";
    } else {
        echo "⚠️ Issues detected:\n";
        if ($httpCode !== 200) {
            echo "- API not returning HTTP 200\n";
        }
        if (empty($stores)) {
            echo "- No stores found in database\n";
        }
        echo "\nRecommendations:\n";
        echo "- Run: php admin/create-sample-stores.php\n";
        echo "- Check web server is running on port 8000\n";
        echo "- Verify database connection\n";
    }
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
}
