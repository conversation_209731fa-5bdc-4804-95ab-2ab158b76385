<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل إعداد Firebase - خطوة بخطوة</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .method {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            transition: transform 0.3s ease;
        }
        
        .method:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .method-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .method-icon {
            font-size: 2rem;
            color: #667eea;
        }
        
        .method-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .method-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .steps {
            margin: 20px 0;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .step-description {
            color: #6c757d;
            line-height: 1.6;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-family: inherit;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .url-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            word-break: break-all;
        }
        
        .accounts-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .accounts-table th,
        .accounts-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }
        
        .accounts-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .accounts-table tr:last-child td {
            border-bottom: none;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .quick-link {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .quick-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .quick-link-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .quick-link-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .quick-link-url {
            font-size: 0.8rem;
            color: #6c757d;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 دليل إعداد Firebase</h1>
            <p>خطوات مفصلة لإضافة الحسابات إلى Firebase</p>
        </div>
        
        <div class="content">
            <!-- Overview -->
            <div class="info-box">
                <h3><i class="fas fa-info-circle"></i> نظرة عامة</h3>
                <p>لديك طريقتان لإضافة الحسابات إلى Firebase:</p>
                <ul style="margin-top: 10px; padding-right: 20px;">
                    <li><strong>الطريقة الأولى (الموصى بها):</strong> استخدام صفحة الإعداد التلقائي</li>
                    <li><strong>الطريقة الثانية:</strong> إنشاء الحسابات يدوياً عبر صفحات التسجيل</li>
                </ul>
            </div>

            <!-- Method 1: Automatic Setup -->
            <div class="method">
                <div class="method-header">
                    <div class="method-icon">🚀</div>
                    <div>
                        <div class="method-title">الطريقة الأولى: الإعداد التلقائي</div>
                        <div class="method-subtitle">الأسرع والأسهل - موصى بها</div>
                    </div>
                </div>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">افتح صفحة الإعداد التلقائي</div>
                            <div class="step-description">
                                انقر على الرابط أدناه لفتح صفحة إعداد المديرين التلقائية
                            </div>
                            <div class="url-box">
                                http://localhost:8000/admin/setup-firebase-admins.html
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">انقر على "إنشاء جميع الحسابات"</div>
                            <div class="step-description">
                                ستقوم الصفحة بإنشاء جميع حسابات المديرين تلقائياً في Firebase
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <div class="step-title">انتظر اكتمال العملية</div>
                            <div class="step-description">
                                ستظهر رسائل تأكيد لكل حساب يتم إنشاؤه بنجاح
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <div class="step-title">جرب تسجيل الدخول</div>
                            <div class="step-description">
                                استخدم أي من الحسابات المنشأة لتسجيل الدخول
                            </div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center;">
                    <a href="setup-firebase-admins.html" class="btn btn-success" target="_blank">
                        <i class="fas fa-rocket"></i> بدء الإعداد التلقائي
                    </a>
                </div>
            </div>

            <!-- Method 2: Manual Setup -->
            <div class="method">
                <div class="method-header">
                    <div class="method-icon">✋</div>
                    <div>
                        <div class="method-title">الطريقة الثانية: الإعداد اليدوي</div>
                        <div class="method-subtitle">إنشاء الحسابات يدوياً عبر صفحات التسجيل</div>
                    </div>
                </div>

                <div class="warning-box">
                    <strong>⚠️ ملاحظة مهمة:</strong> هذه الطريقة تتطلب إنشاء كل حساب يدوياً وتعيين الأدوار لاحقاً.
                </div>

                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">افتح صفحة التسجيل للمستخدمين العاديين</div>
                            <div class="step-description">
                                لإنشاء حسابات المستخدمين العاديين
                            </div>
                            <div class="url-box">
                                http://localhost:8000/register.html
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">افتح صفحة التسجيل للمديرين</div>
                            <div class="step-description">
                                لإنشاء حسابات المديرين (ستحتاج لتعيين الأدوار لاحقاً)
                            </div>
                            <div class="url-box">
                                http://localhost:8000/admin/firebase-login.html
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <div class="step-title">أنشئ الحسابات واحداً تلو الآخر</div>
                            <div class="step-description">
                                استخدم البيانات من الجدول أدناه لإنشاء كل حساب
                            </div>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <div class="step-title">عيّن الأدوار في إدارة المستخدمين</div>
                            <div class="step-description">
                                بعد إنشاء الحسابات، استخدم صفحة إدارة المستخدمين لتعيين الأدوار
                            </div>
                            <div class="url-box">
                                http://localhost:8000/admin/firebase-users.html
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Accounts Table -->
            <div class="success-box">
                <h3><i class="fas fa-users"></i> حسابات المديرين المطلوب إنشاؤها</h3>
            </div>

            <table class="accounts-table">
                <thead>
                    <tr>
                        <th>الدور</th>
                        <th>البريد الإلكتروني</th>
                        <th>كلمة المرور</th>
                        <th>الاسم</th>
                        <th>الصلاحيات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>مدير رئيسي</td>
                        <td><EMAIL></td>
                        <td>Admin123!@#</td>
                        <td>المدير الرئيسي</td>
                        <td>صلاحيات كاملة</td>
                    </tr>
                    <tr>
                        <td>مالك المتجر</td>
                        <td><EMAIL></td>
                        <td>Mossaab2024!</td>
                        <td>مصعب - مالك المتجر</td>
                        <td>إدارة المتجر والمنتجات</td>
                    </tr>
                    <tr>
                        <td>مدير</td>
                        <td><EMAIL></td>
                        <td>Manager123!</td>
                        <td>مدير المتجر</td>
                        <td>إدارة محدودة</td>
                    </tr>
                    <tr>
                        <td>تجريبي</td>
                        <td><EMAIL></td>
                        <td>Demo123!</td>
                        <td>حساب تجريبي</td>
                        <td>عرض فقط</td>
                    </tr>
                </tbody>
            </table>

            <!-- Quick Links -->
            <div class="success-box">
                <h3><i class="fas fa-link"></i> روابط سريعة</h3>
            </div>

            <div class="quick-links">
                <a href="setup-firebase-admins.html" class="quick-link" target="_blank">
                    <div class="quick-link-icon">🚀</div>
                    <div class="quick-link-title">إعداد المديرين التلقائي</div>
                    <div class="quick-link-url">setup-firebase-admins.html</div>
                </a>

                <a href="../register.html" class="quick-link" target="_blank">
                    <div class="quick-link-icon">📝</div>
                    <div class="quick-link-title">تسجيل مستخدم جديد</div>
                    <div class="quick-link-url">register.html</div>
                </a>

                <a href="../login.html" class="quick-link" target="_blank">
                    <div class="quick-link-icon">🔐</div>
                    <div class="quick-link-title">تسجيل دخول المستخدمين</div>
                    <div class="quick-link-url">login.html</div>
                </a>

                <a href="firebase-login.html" class="quick-link" target="_blank">
                    <div class="quick-link-icon">👑</div>
                    <div class="quick-link-title">تسجيل دخول المديرين</div>
                    <div class="quick-link-url">admin/firebase-login.html</div>
                </a>

                <a href="firebase-users.html" class="quick-link" target="_blank">
                    <div class="quick-link-icon">👥</div>
                    <div class="quick-link-title">إدارة المستخدمين</div>
                    <div class="quick-link-url">admin/firebase-users.html</div>
                </a>

                <a href="index.html" class="quick-link" target="_blank">
                    <div class="quick-link-icon">📊</div>
                    <div class="quick-link-title">لوحة التحكم</div>
                    <div class="quick-link-url">admin/index.html</div>
                </a>
            </div>

            <!-- Final Instructions -->
            <div class="info-box">
                <h3><i class="fas fa-lightbulb"></i> نصائح مهمة</h3>
                <ul style="margin-top: 10px; padding-right: 20px;">
                    <li><strong>ابدأ بالطريقة الأولى:</strong> الإعداد التلقائي أسرع وأكثر دقة</li>
                    <li><strong>تأكد من الاتصال بالإنترنت:</strong> Firebase يحتاج اتصال للعمل</li>
                    <li><strong>احفظ كلمات المرور:</strong> ستحتاجها لتسجيل الدخول</li>
                    <li><strong>جرب تسجيل الدخول:</strong> تأكد من عمل الحسابات بعد الإنشاء</li>
                    <li><strong>استخدم Google Sign-In:</strong> للوصول السريع والآمن</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="setup-firebase-admins.html" class="btn btn-success" target="_blank">
                    <i class="fas fa-play"></i> ابدأ الآن - إعداد تلقائي
                </a>
                <a href="firebase-guide.html" class="btn btn-primary" target="_blank">
                    <i class="fas fa-book"></i> الدليل الكامل
                </a>
            </div>
        </div>
    </div>
</body>
</html>
