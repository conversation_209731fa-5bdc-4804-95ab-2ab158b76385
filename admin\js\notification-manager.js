/**
 * Notification Manager
 * مدير الإشعارات
 */

const notificationManager = {
    container: null,

    init() {
        this.createContainer();
    },

    createContainer() {
        if (this.container) return;

        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(this.container);
    },

    show(message, type = 'info', duration = 5000) {
        this.createContainer();

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };

        notification.style.cssText = `
            background: white;
            border: 1px solid ${colors[type]};
            border-right: 4px solid ${colors[type]};
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 12px;
            animation: slideIn 0.3s ease-out;
            direction: rtl;
            font-family: 'Noto Sans Arabic', sans-serif;
        `;

        notification.innerHTML = `
            <i class="${icons[type]}" style="color: ${colors[type]}; font-size: 18px;"></i>
            <span style="flex: 1; color: #333;">${message}</span>
            <button onclick="this.parentElement.remove()" style="
                background: none;
                border: none;
                color: #999;
                cursor: pointer;
                font-size: 16px;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            ">×</button>
        `;

        // Add CSS animation if not exists
        if (!document.getElementById('notification-styles')) {
            const notificationStyle = document.createElement('style');
            notificationStyle.id = 'notification-styles';
            notificationStyle.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }

                @keyframes slideOut {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(notificationStyle);
        }

        this.container.appendChild(notification);

        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);
        }

        return notification;
    },

    showSuccess(message, duration = 5000) {
        return this.show(message, 'success', duration);
    },

    showError(message, duration = 8000) {
        return this.show(message, 'error', duration);
    },

    showWarning(message, duration = 6000) {
        return this.show(message, 'warning', duration);
    },

    showInfo(message, duration = 5000) {
        return this.show(message, 'info', duration);
    },

    clear() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
};

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
    notificationManager.init();
});

// Expose to global scope to prevent redeclaration conflicts
window.notificationManager = notificationManager;

// Global functions for backward compatibility
function showNotification(message, type = 'info', duration = 5000) {
    return notificationManager.show(message, type, duration);
}

function showLoadingState() {
    const loadingElements = document.querySelectorAll('.loading-state');
    loadingElements.forEach(el => el.style.display = 'block');

    const contentElements = document.querySelectorAll('.content-state');
    contentElements.forEach(el => el.style.display = 'none');
}

function hideLoadingState() {
    const loadingElements = document.querySelectorAll('.loading-state');
    loadingElements.forEach(el => el.style.display = 'none');

    const contentElements = document.querySelectorAll('.content-state');
    contentElements.forEach(el => el.style.display = 'block');
}

function showErrorState(message) {
    hideLoadingState();
    notificationManager.showError(message);
}

// Expose notificationManager globally to prevent redeclaration issues
window.notificationManager = notificationManager;
