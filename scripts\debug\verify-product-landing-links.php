<?php
/**
 * Verify Product-Landing Page URL Links
 * Ensure landing page URLs are properly linked to products
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق من روابط المنتجات وصفحات الهبوط</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border-radius: 12px;
        }
        .verification-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .url-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        .url-link:hover {
            text-decoration: underline;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-linked {
            background: #d4edda;
            color: #155724;
        }
        .status-unlinked {
            background: #f8d7da;
            color: #721c24;
        }
        .stats-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            color: #11998e;
        }
        .stats-label {
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 التحقق من روابط المنتجات وصفحات الهبوط</h1>
            <p>فحص شامل للتأكد من ربط صفحات الهبوط بالمنتجات بشكل صحيح</p>
        </div>

        <?php
        $verificationResults = [];
        $totalProducts = 0;
        $linkedProducts = 0;
        $validUrls = 0;

        try {
            require_once '../php/config.php';
            
            // Verification 1: Database Structure Check
            echo '<div class="verification-section">';
            echo '<h3>🗄️ فحص 1: هيكل قاعدة البيانات</h3>';
            
            if (isset($conn) && $conn instanceof PDO) {
                echo '<div class="result pass">✅ اتصال قاعدة البيانات متاح</div>';
                
                // Check tables exist
                $tables = ['produits', 'landing_pages'];
                $existingTables = [];
                
                foreach ($tables as $table) {
                    $stmt = $conn->query("SHOW TABLES LIKE '$table'");
                    if ($stmt->rowCount() > 0) {
                        $existingTables[] = $table;
                        echo '<div class="result pass">✅ جدول ' . $table . ' موجود</div>';
                    } else {
                        echo '<div class="result fail">❌ جدول ' . $table . ' غير موجود</div>';
                    }
                }
                
                // Check foreign key relationship
                if (in_array('produits', $existingTables) && in_array('landing_pages', $existingTables)) {
                    $stmt = $conn->query("
                        SELECT COUNT(*) as count
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_NAME = 'landing_pages' 
                        AND COLUMN_NAME = 'produit_id'
                        AND REFERENCED_TABLE_NAME = 'produits'
                    ");
                    $fkExists = $stmt->fetch()['count'] > 0;
                    
                    if ($fkExists) {
                        echo '<div class="result pass">✅ العلاقة الخارجية (Foreign Key) موجودة</div>';
                    } else {
                        echo '<div class="result warning">⚠️ العلاقة الخارجية غير موجودة</div>';
                    }
                }
                
            } else {
                echo '<div class="result fail">❌ اتصال قاعدة البيانات غير متاح</div>';
                exit;
            }
            echo '</div>';

            // Verification 2: Product-Landing Page Linking Analysis
            echo '<div class="verification-section">';
            echo '<h3>📊 فحص 2: تحليل ربط المنتجات بصفحات الهبوط</h3>';
            
            // Get comprehensive data
            $stmt = $conn->query("
                SELECT 
                    p.id as product_id,
                    p.titre as product_title,
                    p.type as product_type,
                    p.prix as product_price,
                    p.stock as product_stock,
                    lp.id as landing_page_id,
                    lp.titre as landing_page_title,
                    lp.lien_url as landing_page_url,
                    lp.created_at as landing_created,
                    lp.updated_at as landing_updated
                FROM produits p
                LEFT JOIN landing_pages lp ON p.id = lp.produit_id
                ORDER BY p.id
            ");
            $productData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $totalProducts = count($productData);
            $linkedProducts = 0;
            $validUrls = 0;
            
            // Count statistics
            foreach ($productData as $row) {
                if (!empty($row['landing_page_id'])) {
                    $linkedProducts++;
                    if (!empty($row['landing_page_url'])) {
                        $validUrls++;
                    }
                }
            }
            
            // Display statistics
            echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';
            
            echo '<div class="stats-card">';
            echo '<div class="stats-number">' . $totalProducts . '</div>';
            echo '<div class="stats-label">إجمالي المنتجات</div>';
            echo '</div>';
            
            echo '<div class="stats-card">';
            echo '<div class="stats-number">' . $linkedProducts . '</div>';
            echo '<div class="stats-label">منتجات مربوطة بصفحات هبوط</div>';
            echo '</div>';
            
            echo '<div class="stats-card">';
            echo '<div class="stats-number">' . $validUrls . '</div>';
            echo '<div class="stats-label">روابط صالحة</div>';
            echo '</div>';
            
            $linkingRate = $totalProducts > 0 ? ($linkedProducts / $totalProducts) * 100 : 0;
            echo '<div class="stats-card">';
            echo '<div class="stats-number">' . round($linkingRate, 1) . '%</div>';
            echo '<div class="stats-label">معدل الربط</div>';
            echo '</div>';
            
            echo '</div>';
            
            if ($linkingRate >= 50) {
                echo '<div class="result pass">✅ معدل ربط جيد: ' . round($linkingRate, 1) . '%</div>';
            } elseif ($linkingRate >= 25) {
                echo '<div class="result warning">⚠️ معدل ربط متوسط: ' . round($linkingRate, 1) . '%</div>';
            } else {
                echo '<div class="result fail">❌ معدل ربط منخفض: ' . round($linkingRate, 1) . '%</div>';
            }
            
            echo '</div>';

            // Verification 3: Detailed Product-Landing Page Table
            echo '<div class="verification-section">';
            echo '<h3>📋 فحص 3: تفاصيل ربط المنتجات وصفحات الهبوط</h3>';
            
            if (count($productData) > 0) {
                echo '<table class="data-table">';
                echo '<tr>';
                echo '<th>ID المنتج</th>';
                echo '<th>اسم المنتج</th>';
                echo '<th>النوع</th>';
                echo '<th>السعر</th>';
                echo '<th>حالة الربط</th>';
                echo '<th>ID صفحة الهبوط</th>';
                echo '<th>رابط صفحة الهبوط</th>';
                echo '<th>تاريخ الإنشاء</th>';
                echo '</tr>';
                
                foreach ($productData as $row) {
                    $hasLanding = !empty($row['landing_page_id']);
                    $hasValidUrl = !empty($row['landing_page_url']);
                    
                    echo '<tr>';
                    echo '<td>' . $row['product_id'] . '</td>';
                    echo '<td>' . htmlspecialchars($row['product_title']) . '</td>';
                    echo '<td>' . $row['product_type'] . '</td>';
                    echo '<td>' . number_format($row['product_price'], 2) . ' دج</td>';
                    
                    if ($hasLanding) {
                        echo '<td><span class="status-badge status-linked">مربوط</span></td>';
                        echo '<td>' . $row['landing_page_id'] . '</td>';
                        
                        if ($hasValidUrl) {
                            $fullUrl = 'http://localhost:8000' . $row['landing_page_url'];
                            echo '<td><a href="' . $fullUrl . '" target="_blank" class="url-link">' . htmlspecialchars($row['landing_page_url']) . '</a></td>';
                        } else {
                            echo '<td><span style="color: #dc3545;">رابط غير صالح</span></td>';
                        }
                        
                        echo '<td>' . date('Y-m-d', strtotime($row['landing_created'])) . '</td>';
                    } else {
                        echo '<td><span class="status-badge status-unlinked">غير مربوط</span></td>';
                        echo '<td>-</td>';
                        echo '<td>-</td>';
                        echo '<td>-</td>';
                    }
                    
                    echo '</tr>';
                }
                
                echo '</table>';
            } else {
                echo '<div class="result warning">⚠️ لا توجد منتجات في قاعدة البيانات</div>';
            }
            
            echo '</div>';

            // Verification 4: URL Validation
            echo '<div class="verification-section">';
            echo '<h3>🔍 فحص 4: التحقق من صحة الروابط</h3>';
            
            $stmt = $conn->query("
                SELECT id, lien_url, titre 
                FROM landing_pages 
                WHERE lien_url IS NOT NULL AND lien_url != ''
            ");
            $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $validUrlCount = 0;
            $invalidUrlCount = 0;
            
            foreach ($landingPages as $page) {
                $url = $page['lien_url'];
                
                // Check URL format
                if (strpos($url, '/landing-page-template.php?id=') === 0) {
                    $validUrlCount++;
                    echo '<div class="result pass">✅ رابط صالح: ' . htmlspecialchars($url) . '</div>';
                } else {
                    $invalidUrlCount++;
                    echo '<div class="result warning">⚠️ رابط غير قياسي: ' . htmlspecialchars($url) . '</div>';
                }
            }
            
            echo '<div class="result info">📊 روابط صالحة: ' . $validUrlCount . ' | روابط غير قياسية: ' . $invalidUrlCount . '</div>';
            
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ في التحقق</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }

        // Summary and Actions
        echo '<div class="verification-section">';
        echo '<h3>📊 ملخص التحقق والإجراءات</h3>';
        
        if ($totalProducts > 0) {
            if ($linkingRate >= 75) {
                echo '<div class="result pass">🎉 ممتاز! معظم المنتجات مربوطة بصفحات هبوط</div>';
            } elseif ($linkingRate >= 50) {
                echo '<div class="result warning">⚠️ جيد، لكن يمكن تحسين معدل الربط</div>';
            } else {
                echo '<div class="result fail">❌ معدل ربط منخفض، يحتاج تحسين</div>';
            }
        }
        
        echo '<h4>🛠️ الإجراءات المتاحة:</h4>';
        echo '<a href="landing-pages-management.html" class="fix-button">🚀 إدارة صفحات الهبوط</a>';
        echo '<a href="products-management.html" class="fix-button">📚 إدارة المنتجات</a>';
        echo '<a href="fix-javascript-and-database-linking.php" class="fix-button">🔧 إصلاح شامل</a>';
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<a href="index.html" class="fix-button">🏠 لوحة التحكم</a>';
        echo '<a href="../landing-page-template.php?id=1" class="fix-button" target="_blank">👁️ معاينة صفحة هبوط</a>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Add interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for URL links
            const urlLinks = document.querySelectorAll('.url-link');
            urlLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log('🔗 Opening landing page:', this.href);
                });
            });
            
            // Add statistics animation
            const statsNumbers = document.querySelectorAll('.stats-number');
            statsNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = Math.ceil(finalValue / 20);
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    stat.textContent = currentValue + (stat.textContent.includes('%') ? '%' : '');
                }, 50);
            });
        });
    </script>
</body>
</html>
