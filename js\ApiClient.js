/**
 * Enhanced API Client for Mossaab Landing Page
 * Addresses JSON parsing errors and provides robust error handling
 * 
 * Fixes:
 * - "Failed to execute 'json' on 'Response': Unexpected end of JSON input"
 * - "JSON.parse: unexpected character at line 1 column 1"
 * - Inconsistent error handling across API calls
 */

class ApiClient {
    constructor() {
        this.baseUrl = '';
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        this.requestTimeout = 30000; // 30 seconds
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
    }

    /**
     * Make a safe API call with comprehensive error handling
     */
    async call(url, options = {}) {
        const requestId = this.generateRequestId();
        console.log(`🌐 API Call [${requestId}]: ${url}`);

        // Merge default options
        const mergedOptions = {
            method: 'GET',
            headers: { ...this.defaultHeaders },
            timeout: this.requestTimeout,
            ...options
        };

        // Merge headers properly
        if (options.headers) {
            mergedOptions.headers = { ...this.defaultHeaders, ...options.headers };
        }

        let lastError = null;
        
        // Retry logic
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                console.log(`🔄 Attempt ${attempt}/${this.retryAttempts} [${requestId}]`);
                
                const response = await this.makeRequest(url, mergedOptions, requestId);
                const data = await this.parseResponse(response, requestId);
                
                console.log(`✅ Success [${requestId}]:`, data);
                return data;
                
            } catch (error) {
                lastError = error;
                console.error(`❌ Attempt ${attempt} failed [${requestId}]:`, error.message);
                
                // Don't retry on certain errors
                if (this.shouldNotRetry(error)) {
                    break;
                }
                
                // Wait before retry (except on last attempt)
                if (attempt < this.retryAttempts) {
                    await this.delay(this.retryDelay * attempt);
                }
            }
        }

        // All attempts failed
        console.error(`💥 All attempts failed [${requestId}]:`, lastError);
        throw lastError;
    }

    /**
     * Make the actual HTTP request
     */
    async makeRequest(url, options, requestId) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), options.timeout);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            
            console.log(`📡 Response [${requestId}]: ${response.status} ${response.statusText}`);
            
            return response;
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new ApiError('انتهت مهلة الطلب', 'TIMEOUT', 408);
            }
            
            throw new ApiError('خطأ في الشبكة: ' + error.message, 'NETWORK_ERROR', 0);
        }
    }

    /**
     * Parse response with robust JSON handling
     */
    async parseResponse(response, requestId) {
        try {
            // Get response text first
            const responseText = await response.text();
            console.log(`📥 Raw response [${requestId}] (${responseText.length} chars):`, 
                responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

            // Check if response is empty
            if (!responseText.trim()) {
                throw new ApiError('استجابة فارغة من الخادم', 'EMPTY_RESPONSE', response.status);
            }

            // Try to parse JSON
            let data;
            try {
                // Look for JSON in the response, even if there are PHP warnings/errors before it
                const jsonMatch = responseText.match(/\{.*\}$/s);
                if (jsonMatch) {
                    data = JSON.parse(jsonMatch[0]);
                    
                    // Log PHP warnings if found
                    const beforeJson = responseText.replace(jsonMatch[0], '').trim();
                    if (beforeJson) {
                        console.warn(`⚠️ PHP output before JSON [${requestId}]:`, beforeJson);
                    }
                } else {
                    // Try parsing the entire response
                    data = JSON.parse(responseText);
                }
            } catch (parseError) {
                console.error(`❌ JSON Parse Error [${requestId}]:`, parseError.message);
                console.error(`❌ Response was [${requestId}]:`, responseText);
                
                // Check if it's an HTML error page
                if (responseText.includes('<html>') || responseText.includes('<!DOCTYPE')) {
                    throw new ApiError('الخادم أرجع صفحة HTML بدلاً من JSON', 'HTML_RESPONSE', response.status);
                }
                
                // Check for common PHP errors
                if (responseText.includes('Fatal error') || responseText.includes('Parse error')) {
                    throw new ApiError('خطأ في كود PHP على الخادم', 'PHP_ERROR', response.status);
                }
                
                throw new ApiError('استجابة غير صالحة من الخادم (ليست JSON)', 'INVALID_JSON', response.status);
            }

            // Check HTTP status
            if (!response.ok) {
                const errorMessage = data?.error?.message || data?.message || `HTTP ${response.status}`;
                const errorCode = data?.error?.code || 'HTTP_ERROR';
                throw new ApiError(errorMessage, errorCode, response.status);
            }

            // Validate response structure
            if (typeof data !== 'object' || data === null) {
                throw new ApiError('تنسيق استجابة غير صالح', 'INVALID_RESPONSE_FORMAT', response.status);
            }

            // Check for API-level errors
            if (data.success === false) {
                const errorMessage = data.error?.message || data.message || 'خطأ غير محدد';
                const errorCode = data.error?.code || 'API_ERROR';
                throw new ApiError(errorMessage, errorCode, response.status);
            }

            return data;

        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            
            throw new ApiError('خطأ في معالجة الاستجابة: ' + error.message, 'RESPONSE_PROCESSING_ERROR', response.status);
        }
    }

    /**
     * Check if error should not be retried
     */
    shouldNotRetry(error) {
        // Don't retry on client errors (4xx) except timeout
        if (error.status >= 400 && error.status < 500 && error.status !== 408) {
            return true;
        }
        
        // Don't retry on certain error codes
        const noRetryErrors = ['INVALID_JSON', 'HTML_RESPONSE', 'PHP_ERROR'];
        return noRetryErrors.includes(error.code);
    }

    /**
     * Generate unique request ID for tracking
     */
    generateRequestId() {
        return Math.random().toString(36).substr(2, 9);
    }

    /**
     * Delay utility for retries
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Convenience methods
    async get(url, options = {}) {
        return this.call(url, { ...options, method: 'GET' });
    }

    async post(url, data, options = {}) {
        return this.call(url, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async put(url, data, options = {}) {
        return this.call(url, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    async delete(url, data = null, options = {}) {
        const requestOptions = {
            ...options,
            method: 'DELETE'
        };
        
        if (data) {
            requestOptions.body = JSON.stringify(data);
        }
        
        return this.call(url, requestOptions);
    }
}

/**
 * Custom API Error class
 */
class ApiError extends Error {
    constructor(message, code = 'UNKNOWN_ERROR', status = 0) {
        super(message);
        this.name = 'ApiError';
        this.code = code;
        this.status = status;
    }
}

// Create global instance
const apiClient = new ApiClient();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiClient, ApiError, apiClient };
}

// Global error handler for unhandled API errors
window.addEventListener('unhandledrejection', function(event) {
    if (event.reason instanceof ApiError) {
        console.error('🚨 Unhandled API Error:', event.reason);
        
        // Show user-friendly error message
        if (typeof showNotification === 'function') {
            showNotification(event.reason.message, 'error');
        } else {
            alert('خطأ في الاتصال: ' + event.reason.message);
        }
        
        // Prevent default browser error handling
        event.preventDefault();
    }
});

console.log('✅ Enhanced API Client loaded successfully');
