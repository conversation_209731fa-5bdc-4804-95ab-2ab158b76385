<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات العامة - إعدادات النظام</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/system-settings.css">
    <style>
        /* General Settings specific styles */
        .settings-category {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .category-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .category-actions {
            display: flex;
            gap: 10px;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .setting-field {
            position: relative;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .setting-field:hover {
            background: #f1f3f4;
            border-color: #3498db;
        }
        
        .setting-field .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: block;
        }
        
        .setting-field .form-control {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .setting-info {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
        }
        
        .setting-info small {
            color: #6c757d;
            font-size: 0.75rem;
        }
        
        .setting-key {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .setting-default {
            color: #28a745;
        }
        
        .setting-actions {
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .setting-field:hover .setting-actions {
            opacity: 1;
        }
        
        .color-input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .color-input {
            width: 50px;
            height: 40px;
            padding: 0;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .color-text {
            flex: 1;
            font-family: 'Courier New', monospace;
        }
        
        .json-editor {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .json-editor.is-invalid {
            border-color: #e74c3c;
            background-color: #fdf2f2;
        }
        
        .form-check {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 5px;
        }
        
        .form-check input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #3498db;
        }
        
        .form-check label {
            margin: 0;
            cursor: pointer;
            font-weight: 500;
        }
        
        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .settings-filters {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .settings-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .backup-restore {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .file-input {
            display: none;
        }
        
        .btn-warning.has-changes {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(243, 156, 18, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(243, 156, 18, 0); }
            100% { box-shadow: 0 0 0 0 rgba(243, 156, 18, 0); }
        }
        
        .auto-save-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }
        
        .auto-save-indicator.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="system-settings-container">
        <!-- Header -->
        <header class="system-header">
            <h1>
                <i class="fas fa-cogs"></i>
                الإعدادات العامة
            </h1>
            <p>تكوين الإعدادات الأساسية والعامة للنظام</p>
        </header>

        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <a href="../index.html"><i class="fas fa-home"></i> لوحة التحكم</a>
            <span class="separator">/</span>
            <a href="index.html">إعدادات النظام</a>
            <span class="separator">/</span>
            <span>الإعدادات العامة</span>
        </nav>

        <!-- Settings Header -->
        <div class="settings-header">
            <div class="settings-filters">
                <div class="filter-group">
                    <label for="categoryFilter">الفئة</label>
                    <select id="categoryFilter" class="enhanced-select">
                        <option value="">جميع الفئات</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="settingsSearch">البحث</label>
                    <input type="text" id="settingsSearch" class="enhanced-input" placeholder="البحث في الإعدادات...">
                </div>
            </div>

            <div class="settings-actions">
                <div class="backup-restore">
                    <button class="btn btn-secondary" onclick="GeneralManager.backupSettings()">
                        <i class="fas fa-download"></i>
                        نسخ احتياطي
                    </button>
                    <button class="btn btn-secondary" onclick="document.getElementById('restoreFile').click()">
                        <i class="fas fa-upload"></i>
                        استعادة
                    </button>
                    <input type="file" id="restoreFile" class="file-input" accept=".json" 
                           onchange="GeneralManager.restoreSettings(this.files[0])">
                </div>
                
                <button class="btn btn-primary" onclick="GeneralManager.showAddModal()">
                    <i class="fas fa-plus"></i>
                    إضافة إعداد
                </button>
                
                <button id="saveAllBtn" class="btn btn-success" onclick="GeneralManager.saveAllSettings()">
                    <i class="fas fa-save"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </div>

        <!-- Settings Container -->
        <div class="settings-container">
            <div id="settingsContainer">
                <!-- Settings will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Auto Save Indicator -->
    <div class="auto-save-indicator" id="autoSaveIndicator">
        <i class="fas fa-check"></i>
        تم الحفظ التلقائي
    </div>

    <!-- Setting Modal -->
    <div class="modal" id="settingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة إعداد جديد</h3>
                <button class="modal-close" onclick="GeneralManager.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="settingForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="setting_key">مفتاح الإعداد *</label>
                            <input type="text" id="setting_key" name="setting_key" class="form-control" required>
                            <small class="form-text">استخدم أحرف إنجليزية وشرطات سفلية فقط</small>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="data_type">نوع البيانات *</label>
                            <select id="data_type" name="data_type" class="form-control" required>
                                <option value="">اختر النوع</option>
                                <option value="string">نص</option>
                                <option value="number">رقم</option>
                                <option value="boolean">منطقي (صح/خطأ)</option>
                                <option value="email">بريد إلكتروني</option>
                                <option value="url">رابط</option>
                                <option value="color">لون</option>
                                <option value="json">JSON</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="category">الفئة *</label>
                            <select id="category" name="category" class="form-control" required>
                                <option value="">اختر الفئة</option>
                                <option value="site">إعدادات الموقع</option>
                                <option value="email">إعدادات البريد الإلكتروني</option>
                                <option value="sms">إعدادات الرسائل النصية</option>
                                <option value="social">وسائل التواصل الاجتماعي</option>
                                <option value="seo">تحسين محركات البحث</option>
                                <option value="analytics">التحليلات والإحصائيات</option>
                                <option value="maintenance">الصيانة والنسخ الاحتياطي</option>
                                <option value="localization">التوطين واللغة</option>
                                <option value="api">إعدادات API</option>
                                <option value="system">إعدادات النظام</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="setting_value">القيمة *</label>
                            <input type="text" id="setting_value" name="setting_value" class="form-control" required>
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label" for="description_ar">الوصف بالعربية *</label>
                            <input type="text" id="description_ar" name="description_ar" class="form-control" required>
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label" for="description_en">الوصف بالإنجليزية</label>
                            <input type="text" id="description_en" name="description_en" class="form-control">
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label" for="validation_rules">قواعد التحقق (JSON)</label>
                            <textarea id="validation_rules" name="validation_rules" class="form-control" rows="3" 
                                      placeholder='{"min": 0, "max": 100}'></textarea>
                            <small class="form-text">مثال: {"min_length": 3, "max_length": 50}</small>
                        </div>
                    </div>
                    
                    <!-- Public Setting -->
                    <div class="form-check">
                        <input type="checkbox" id="is_public" name="is_public">
                        <label for="is_public">إعداد عام (يمكن الوصول إليه من الواجهة الأمامية)</label>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="GeneralManager.closeModal()">
                    إلغاء
                </button>
                <button type="submit" form="settingForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/core.js"></script>
    <script src="js/general.js"></script>
    
    <script>
        // Show auto-save indicator
        function showAutoSaveIndicator() {
            const indicator = document.getElementById('autoSaveIndicator');
            if (indicator) {
                indicator.classList.add('show');
                setTimeout(() => {
                    indicator.classList.remove('show');
                }, 2000);
            }
        }

        // Override the auto-save function to show indicator
        const originalSaveAllSettings = GeneralManager.saveAllSettings;
        GeneralManager.saveAllSettings = async function(isAutoSave = false) {
            const result = await originalSaveAllSettings.call(this, isAutoSave);
            if (isAutoSave) {
                showAutoSaveIndicator();
            }
            return result;
        };
    </script>
</body>
</html>
