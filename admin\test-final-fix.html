<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Correction safeRedirect</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f8f9fa;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Final - Correction de l'erreur safeRedirect</h1>
        <p>Ce test vérifie que l'erreur <code>window.safeRedirect is not a function</code> est corrigée.</p>

        <div class="test-section">
            <h3>📋 Statut des Fonctions</h3>
            <div id="status"></div>
        </div>

        <div class="test-section">
            <h3>🔧 Tests de Fonctionnement</h3>
            <button onclick="testSafeRedirect()">Test safeRedirect</button>
            <button onclick="testAuthFunctions()">Test fonctions auth</button>
            <button onclick="simulateLoginProcess()">Simuler processus de connexion</button>
            <button onclick="clearLogs()">Effacer les logs</button>
        </div>

        <div class="test-section">
            <h3>📝 Logs de Test</h3>
            <div id="logs" class="log"></div>
        </div>
    </div>

    <!-- Auth Fix Script (load FIRST) -->
    <script src="auth-fix.js"></script>

    <!-- Fallback redirect function -->
    <script>
        if (!window.safeRedirect) {
            window.safeRedirect = function (url) {
                console.log("🔄 Using fallback redirect to:", url);
                addLog(`🔄 Fallback redirect appelé vers: ${url}`, 'info');
                // Simulation de redirection pour le test
                addLog(`✅ Redirection simulée vers ${url}`, 'success');
            };
        }
    </script>

    <!-- Firebase Configuration -->
    <script type="module" src="js/firebase-config.js"></script>

    <script>
        function addLog(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `<strong>${timestamp}:</strong> ${message}`;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        function checkFunctionStatus() {
            const status = document.getElementById('status');
            const functions = [
                { name: 'safeRedirect', func: window.safeRedirect },
                { name: 'onFirebaseUserSignedIn', func: window.onFirebaseUserSignedIn },
                { name: 'onFirebaseUserSignedOut', func: window.onFirebaseUserSignedOut },
                { name: 'authFix', func: window.authFix },
                { name: 'firebaseAuth', func: window.firebaseAuth }
            ];

            let html = '';
            functions.forEach(item => {
                const available = typeof item.func === 'function' || typeof item.func === 'object';
                const statusClass = available ? 'success' : 'error';
                const statusIcon = available ? '✅' : '❌';
                html += `<div class="${statusClass}">${statusIcon} ${item.name}: ${typeof item.func}</div>`;
            });

            status.innerHTML = html;
        }

        function testSafeRedirect() {
            addLog('=== TEST SAFEREDIRECT ===', 'info');
            
            try {
                if (typeof window.safeRedirect === 'function') {
                    addLog('✅ window.safeRedirect est disponible', 'success');
                    
                    // Test d'appel sans erreur
                    window.safeRedirect('test-page.html');
                    addLog('✅ Appel de safeRedirect réussi sans erreur', 'success');
                } else {
                    addLog('❌ window.safeRedirect n\'est pas une fonction', 'error');
                    addLog(`Type actuel: ${typeof window.safeRedirect}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erreur lors du test safeRedirect: ${error.message}`, 'error');
            }
        }

        function testAuthFunctions() {
            addLog('=== TEST FONCTIONS AUTH ===', 'info');
            
            const authFunctions = [
                'onFirebaseUserSignedIn',
                'onFirebaseUserSignedOut',
                'safeAuthCheck'
            ];

            authFunctions.forEach(funcName => {
                const func = window[funcName];
                if (typeof func === 'function') {
                    addLog(`✅ ${funcName} est disponible`, 'success');
                } else {
                    addLog(`❌ ${funcName} n'est pas disponible (${typeof func})`, 'error');
                }
            });
        }

        function simulateLoginProcess() {
            addLog('=== SIMULATION PROCESSUS DE CONNEXION ===', 'info');
            
            try {
                // Simuler un utilisateur connecté
                const mockUser = {
                    uid: 'test-uid-123',
                    email: '<EMAIL>',
                    displayName: 'Test User'
                };

                const mockProfile = {
                    role: 'admin',
                    loginPageMode: false
                };

                addLog('👤 Simulation d\'un utilisateur connecté...', 'info');
                addLog(`📧 Email: ${mockUser.email}`, 'info');
                
                // Marquer comme soumission de formulaire
                window.loginFormSubmitted = true;
                addLog('✅ loginFormSubmitted défini sur true', 'success');

                // Tester onFirebaseUserSignedIn si disponible
                if (typeof window.onFirebaseUserSignedIn === 'function') {
                    addLog('🔄 Appel de onFirebaseUserSignedIn...', 'info');
                    window.onFirebaseUserSignedIn(mockUser, mockProfile);
                    addLog('✅ onFirebaseUserSignedIn appelé avec succès', 'success');
                } else {
                    addLog('❌ onFirebaseUserSignedIn non disponible', 'error');
                }

            } catch (error) {
                addLog(`❌ Erreur lors de la simulation: ${error.message}`, 'error');
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 Page de test chargée', 'info');
            checkFunctionStatus();
            
            // Vérifier périodiquement le statut
            setInterval(checkFunctionStatus, 2000);
        });

        // Override console.error pour capturer les erreurs
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('safeRedirect')) {
                addLog(`🚨 ERREUR DÉTECTÉE: ${message}`, 'error');
            }
            originalConsoleError.apply(console, args);
        };

        // Capturer les erreurs JavaScript
        window.addEventListener('error', function(event) {
            if (event.message.includes('safeRedirect')) {
                addLog(`🚨 ERREUR JS: ${event.message} (${event.filename}:${event.lineno})`, 'error');
            }
        });
    </script>
</body>
</html>