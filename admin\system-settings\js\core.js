/**
 * System Settings Core JavaScript
 * الوظائف الأساسية لإعدادات النظام
 */

// Global System Settings Object
const SystemSettings = {
    // Configuration
    config: {
        apiBase: '../../api/system/',
        notificationDuration: 5000,
        loadingTimeout: 30000
    },

    // State Management
    state: {
        currentSection: null,
        isLoading: false,
        hasUnsavedChanges: false
    },

    // Initialize the system
    async init() {
        console.log('🚀 Initializing System Settings...');
        
        try {
            // Initialize notification system
            this.initNotifications();
            
            // Setup global event listeners
            this.setupGlobalEvents();
            
            // Load initial data
            await this.loadInitialData();
            
            // Setup auto-save
            this.setupAutoSave();
            
            console.log('✅ System Settings initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize System Settings:', error);
            this.showNotification('فشل في تهيئة النظام: ' + error.message, 'error');
        }
    },

    // Notification System
    initNotifications() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    },

    showNotification(message, type = 'info', duration = null) {
        const container = document.getElementById('notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            warning: '#f39c12',
            info: '#3498db'
        };

        notification.style.cssText = `
            background: white;
            border: 1px solid ${colors[type]};
            border-right: 4px solid ${colors[type]};
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 12px;
            animation: slideInRight 0.3s ease-out;
            direction: rtl;
            font-family: 'Noto Sans Arabic', sans-serif;
            pointer-events: auto;
            backdrop-filter: blur(10px);
        `;

        notification.innerHTML = `
            <i class="${icons[type]}" style="color: ${colors[type]}; font-size: 18px;"></i>
            <span style="flex: 1; color: #2c3e50; font-weight: 500;">${message}</span>
            <button onclick="this.parentElement.remove()" style="
                background: none;
                border: none;
                color: #95a5a6;
                cursor: pointer;
                font-size: 18px;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s;
            " onmouseover="this.style.background='#ecf0f1'" onmouseout="this.style.background='none'">×</button>
        `;

        // Add animation styles if not exists
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        container.appendChild(notification);

        // Auto remove
        const autoRemoveDuration = duration || this.config.notificationDuration;
        if (autoRemoveDuration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease-in';
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, autoRemoveDuration);
        }

        return notification;
    },

    // Loading States
    showLoading(target = 'body', message = 'جاري التحميل...') {
        this.state.isLoading = true;
        
        const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
        if (!targetElement) return;

        // Remove existing loading
        this.hideLoading(target);

        const loading = document.createElement('div');
        loading.className = 'system-loading';
        loading.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: inherit;
        `;

        loading.innerHTML = `
            <div style="
                width: 50px;
                height: 50px;
                border: 4px solid #ecf0f1;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 15px;
            "></div>
            <p style="
                color: #7f8c8d;
                font-weight: 500;
                font-family: 'Noto Sans Arabic', sans-serif;
            ">${message}</p>
        `;

        // Add spin animation if not exists
        if (!document.getElementById('loading-styles')) {
            const style = document.createElement('style');
            style.id = 'loading-styles';
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // Make target relative if not already positioned
        const computedStyle = window.getComputedStyle(targetElement);
        if (computedStyle.position === 'static') {
            targetElement.style.position = 'relative';
        }

        targetElement.appendChild(loading);

        // Auto-hide after timeout
        setTimeout(() => {
            this.hideLoading(target);
            if (this.state.isLoading) {
                this.showNotification('انتهت مهلة التحميل', 'warning');
            }
        }, this.config.loadingTimeout);
    },

    hideLoading(target = 'body') {
        this.state.isLoading = false;
        
        const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
        if (!targetElement) return;

        const loading = targetElement.querySelector('.system-loading');
        if (loading) {
            loading.remove();
        }
    },

    // API Helper Functions
    async apiCall(endpoint, options = {}) {
        const url = this.config.apiBase + endpoint;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'فشل في العملية');
            }

            return data;
        } catch (error) {
            console.error('API Call Error:', error);
            throw error;
        }
    },

    // Global Event Handlers
    setupGlobalEvents() {
        // Handle unsaved changes warning
        window.addEventListener('beforeunload', (e) => {
            if (this.state.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
                return e.returnValue;
            }
        });

        // Handle form changes
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, select, textarea')) {
                this.state.hasUnsavedChanges = true;
                this.updateSaveIndicator();
            }
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl+S to save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveCurrentSection();
            }
        });
    },

    // Auto-save functionality
    setupAutoSave() {
        setInterval(() => {
            if (this.state.hasUnsavedChanges && this.state.currentSection) {
                this.autoSave();
            }
        }, 30000); // Auto-save every 30 seconds
    },

    async autoSave() {
        try {
            await this.saveCurrentSection(true);
            this.showNotification('تم الحفظ التلقائي', 'info', 2000);
        } catch (error) {
            console.warn('Auto-save failed:', error);
        }
    },

    // Save current section
    async saveCurrentSection(isAutoSave = false) {
        if (!this.state.currentSection) return;

        try {
            // Implementation will be added by specific section modules
            const saveFunction = window[`save${this.state.currentSection}`];
            if (typeof saveFunction === 'function') {
                await saveFunction(isAutoSave);
                this.state.hasUnsavedChanges = false;
                this.updateSaveIndicator();
            }
        } catch (error) {
            if (!isAutoSave) {
                this.showNotification('فشل في الحفظ: ' + error.message, 'error');
            }
            throw error;
        }
    },

    // Update save indicator
    updateSaveIndicator() {
        const indicator = document.querySelector('.save-indicator');
        if (indicator) {
            if (this.state.hasUnsavedChanges) {
                indicator.textContent = 'تغييرات غير محفوظة';
                indicator.className = 'save-indicator unsaved';
            } else {
                indicator.textContent = 'محفوظ';
                indicator.className = 'save-indicator saved';
            }
        }
    },

    // Load initial data
    async loadInitialData() {
        try {
            // Load system status
            const status = await this.apiCall('system-status.php');
            this.updateSystemStatus(status);
        } catch (error) {
            console.warn('Failed to load initial data:', error);
        }
    },

    // Update system status
    updateSystemStatus(status) {
        // Update status indicators throughout the interface
        const statusElements = document.querySelectorAll('[data-status]');
        statusElements.forEach(element => {
            const statusType = element.dataset.status;
            if (status[statusType] !== undefined) {
                element.textContent = status[statusType];
            }
        });
    },

    // Utility Functions
    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    formatNumber(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    },

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    SystemSettings.init();
});

// Make SystemSettings globally available
window.SystemSettings = SystemSettings;
