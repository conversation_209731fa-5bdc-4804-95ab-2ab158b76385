/**
 * Interactive Categories Management
 * إدارة الفئات التفاعلية
 */

console.log('📂 تحميل ملف categories-interactive.js...');

// Global variables
let categoriesData = {};
let editingCategoryId = null;

/**
 * Load interactive categories management
 */
function loadInteractiveCategoriesContent() {
    console.log('📂 بدء تحميل إدارة الفئات التفاعلية...');

    const container = document.getElementById('categoriesManagementContent');
    if (!container) {
        console.error('❌ لم يتم العثور على حاوي إدارة الفئات');
        return;
    }

    // Show loading
    showCategoriesLoadingState(container);

    // Fetch categories data
    fetchCategoriesData()
        .then(data => {
            console.log('📦 تم استلام بيانات الفئات:', data);

            if (data.success) {
                categoriesData = data.data;
                renderInteractiveCategoriesInterface(container, data.data);
            } else {
                throw new Error(data.message || 'فشل في تحميل الفئات');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل الفئات:', error);
            showCategoriesErrorState(container, error.message);
        });
}

/**
 * Fetch categories data
 */
async function fetchCategoriesData() {
    try {
        const response = await fetch('php/categories.php?action=get_all&include_inactive=true');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        throw new Error('خطأ في الاتصال بالخادم: ' + error.message);
    }
}

/**
 * Show loading state
 */
function showCategoriesLoadingState(container) {
    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
            <p style="color: #666;">جاري تحميل إدارة الفئات التفاعلية...</p>
        </div>
    `;
}

/**
 * Show error state
 */
function showCategoriesErrorState(container, message) {
    container.innerHTML = `
        <div style="text-align: center; padding: 60px 20px; color: #dc3545;">
            <i class="fas fa-exclamation-triangle" style="font-size: 4rem; margin-bottom: 20px; opacity: 0.7;"></i>
            <h3>خطأ في تحميل إدارة الفئات</h3>
            <p>${message}</p>
            <button onclick="loadInteractiveCategoriesContent()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                <i class="fas fa-redo"></i> إعادة المحاولة
            </button>
        </div>
    `;
}

/**
 * Render interactive categories interface
 */
function renderInteractiveCategoriesInterface(container, data) {
    const categories = data.categories || [];
    const mainCategories = categories.filter(c => c.parent_id === null);
    const subCategories = categories.filter(c => c.parent_id !== null);
    const featuredCategories = categories.filter(c => c.is_featured == 1);

    // Store categories data globally for modal use
    window.allCategoriesData = categories;
    window.mainCategoriesData = mainCategories;

    const html = `
        <div style="max-width: 1400px; margin: 0 auto; padding: 20px;">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px; text-align: center;">
                <h2 style="margin: 0;"><i class="fas fa-sitemap"></i> إدارة الفئات التفاعلية</h2>
                <p style="margin: 10px 0 0 0;">إضافة وتعديل وحذف الفئات بسهولة</p>
                <div style="margin-top: 20px;">
                    <button onclick="showAddCategoryModal()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-plus"></i> إضافة فئة جديدة
                    </button>
                    <button onclick="loadInteractiveCategoriesContent()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;"><i class="fas fa-folder"></i></div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${categories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666;">إجمالي الفئات</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;"><i class="fas fa-folder-open"></i></div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${mainCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666;">الفئات الرئيسية</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;"><i class="fas fa-layer-group"></i></div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${subCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666;">الفئات الفرعية</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;"><i class="fas fa-star"></i></div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333;">${featuredCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666;">الفئات المميزة</p>
                </div>
            </div>

            <!-- Categories Table -->
            <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; background: #f8f9fa; display: flex; justify-content: space-between; align-items: center;">
                    <h3 style="margin: 0; color: #333;"><i class="fas fa-list"></i> قائمة الفئات</h3>
                    <button onclick="showAddCategoryModal()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-plus"></i> إضافة فئة
                    </button>
                </div>

                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 15px; text-align: right; border-bottom: 1px solid #e0e0e0; font-weight: 600;">الفئة</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #e0e0e0; font-weight: 600;">النوع</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #e0e0e0; font-weight: 600;">الحالة</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #e0e0e0; font-weight: 600;">مميزة</th>
                                <th style="padding: 15px; text-align: center; border-bottom: 1px solid #e0e0e0; font-weight: 600;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${renderCategoriesTableRows(categories)}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Success Message -->
            <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; text-align: center;">
                <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة الفئات التفاعلية بنجاح!</strong>
                <br>يمكنك الآن إضافة وتعديل وحذف الفئات بسهولة.
            </div>
        </div>

        <!-- Category Modal - Will be created dynamically -->
        <div id="categoryModal" style="display: none;"></div>
    `;

    container.innerHTML = html;
}

/**
 * Render categories table rows
 */
function renderCategoriesTableRows(categories) {
    if (!categories || categories.length === 0) {
        return `
            <tr>
                <td colspan="5" style="padding: 40px; text-align: center; color: #666;">
                    <i class="fas fa-folder-open" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                    <p style="margin: 0; font-size: 1.1rem;">لا توجد فئات للعرض</p>
                </td>
            </tr>
        `;
    }

    const mainCategories = categories.filter(c => c.parent_id === null);
    let html = '';

    mainCategories.forEach(mainCat => {
        const subCategories = categories.filter(c => c.parent_id == mainCat.id);

        // Main category row
        html += `
            <tr style="border-bottom: 1px solid #f0f0f0;">
                <td style="padding: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <i class="${mainCat.icon || 'fas fa-folder'}" style="color: ${mainCat.color || '#667eea'}; font-size: 1.2rem;"></i>
                        <div>
                            <strong style="color: #333; font-size: 1.1rem;">${mainCat.name_ar || 'فئة بدون اسم'}</strong>
                            <p style="margin: 2px 0 0 0; color: #666; font-size: 0.9em;">${mainCat.description_ar || 'لا يوجد وصف'}</p>
                        </div>
                    </div>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <span style="padding: 4px 8px; background: #667eea; color: white; border-radius: 12px; font-size: 0.8rem;">رئيسية</span>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <span style="padding: 4px 8px; background: ${mainCat.is_active == 1 ? '#28a745' : '#dc3545'}; color: white; border-radius: 12px; font-size: 0.8rem;">
                        ${mainCat.is_active == 1 ? 'نشطة' : 'معطلة'}
                    </span>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <button onclick="toggleFeatured(${mainCat.id})" style="padding: 4px 8px; background: ${mainCat.is_featured == 1 ? '#ffc107' : '#e0e0e0'}; color: ${mainCat.is_featured == 1 ? '#333' : '#666'}; border: none; border-radius: 12px; font-size: 0.8rem; cursor: pointer;">
                        ${mainCat.is_featured == 1 ? '⭐ مميزة' : '☆ عادية'}
                    </button>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <div style="display: flex; gap: 5px; justify-content: center;">
                        <button onclick="editCategory(${mainCat.id})" style="padding: 6px 10px; background: #ffc107; color: #333; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteCategory(${mainCat.id})" style="padding: 6px 10px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;

        // Sub categories rows
        subCategories.forEach(subCat => {
            html += `
                <tr style="border-bottom: 1px solid #f0f0f0; background: #f8f9fa;">
                    <td style="padding: 15px; padding-right: 40px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-arrow-turn-down" style="color: #999; font-size: 0.8rem;"></i>
                            <i class="${subCat.icon || 'fas fa-folder'}" style="color: ${subCat.color || '#17a2b8'}; font-size: 1rem;"></i>
                            <div>
                                <strong style="color: #333;">${subCat.name_ar || 'فئة فرعية'}</strong>
                                <p style="margin: 2px 0 0 0; color: #666; font-size: 0.85em;">${subCat.description_ar || 'لا يوجد وصف'}</p>
                            </div>
                        </div>
                    </td>
                    <td style="padding: 15px; text-align: center;">
                        <span style="padding: 4px 8px; background: #17a2b8; color: white; border-radius: 12px; font-size: 0.8rem;">فرعية</span>
                    </td>
                    <td style="padding: 15px; text-align: center;">
                        <span style="padding: 4px 8px; background: ${subCat.is_active == 1 ? '#28a745' : '#dc3545'}; color: white; border-radius: 12px; font-size: 0.8rem;">
                            ${subCat.is_active == 1 ? 'نشطة' : 'معطلة'}
                        </span>
                    </td>
                    <td style="padding: 15px; text-align: center;">
                        <button onclick="toggleFeatured(${subCat.id})" style="padding: 4px 8px; background: ${subCat.is_featured == 1 ? '#ffc107' : '#e0e0e0'}; color: ${subCat.is_featured == 1 ? '#333' : '#666'}; border: none; border-radius: 12px; font-size: 0.8rem; cursor: pointer;">
                            ${subCat.is_featured == 1 ? '⭐ مميزة' : '☆ عادية'}
                        </button>
                    </td>
                    <td style="padding: 15px; text-align: center;">
                        <div style="display: flex; gap: 5px; justify-content: center;">
                            <button onclick="editCategory(${subCat.id})" style="padding: 6px 10px; background: #ffc107; color: #333; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteCategory(${subCat.id})" style="padding: 6px 10px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.8rem;" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
    });

    return html;
}

/**
 * Create and populate parent category dropdown
 */
function createParentCategoryOptions(selectedParentId = null, excludeCategoryId = null) {
    const mainCategories = window.mainCategoriesData || [];
    let options = '<option value="">فئة رئيسية</option>';

    if (mainCategories.length === 0) {
        // No main categories available
        options += '<option value="" disabled>لا توجد فئات رئيسية متاحة</option>';
    } else {
        mainCategories.forEach(cat => {
            // Exclude the category being edited to prevent circular references
            if (excludeCategoryId && cat.id == excludeCategoryId) {
                return;
            }

            const selected = selectedParentId && cat.id == selectedParentId ? 'selected' : '';
            const categoryName = cat.name_ar || 'فئة بدون اسم';
            options += `<option value="${cat.id}" ${selected}>${categoryName}</option>`;
        });
    }

    return options;
}

/**
 * Check for circular parent-child references
 */
function checkCircularReference(categoryId, proposedParentId, allCategories) {
    // Find all children of the current category
    const findAllChildren = (parentId, visited = new Set()) => {
        if (visited.has(parentId)) {
            return []; // Prevent infinite loops
        }
        visited.add(parentId);

        const children = allCategories.filter(cat => cat.parent_id == parentId);
        let allChildren = [...children];

        children.forEach(child => {
            allChildren = allChildren.concat(findAllChildren(child.id, visited));
        });

        return allChildren;
    };

    const allChildren = findAllChildren(categoryId);
    return allChildren.some(child => child.id == proposedParentId);
}

/**
 * Create modal HTML dynamically
 */
function createCategoryModal(isEdit = false, categoryData = null) {
    const modalTitle = isEdit ? 'تعديل الفئة' : 'إضافة فئة جديدة';
    const saveButtonText = isEdit ? 'تحديث' : 'حفظ';

    // Get parent category options
    const parentOptions = createParentCategoryOptions(
        categoryData?.parent_id,
        categoryData?.id
    );

    return `
        <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; border-radius: 12px; box-shadow: 0 10px 25px rgba(0,0,0,0.2); max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
                <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; display: flex; justify-content: space-between; align-items: center;">
                    <h3 id="modalTitle" style="margin: 0; color: #333;">${modalTitle}</h3>
                    <button onclick="closeCategoryModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666; padding: 0; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div style="padding: 20px;">
                    <form id="categoryForm" style="display: grid; gap: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الاسم العربي *</label>
                                <input type="text" id="nameAr" name="name_ar" required value="${categoryData?.name_ar || ''}" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الاسم الإنجليزي</label>
                                <input type="text" id="nameEn" name="name_en" value="${categoryData?.name_en || ''}" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                        </div>

                        <div>
                            <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الوصف العربي</label>
                            <textarea id="descriptionAr" name="description_ar" rows="3" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem; resize: vertical;">${categoryData?.description_ar || ''}</textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الفئة الأب</label>
                                <select id="parentId" name="parent_id" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                                    ${parentOptions}
                                </select>
                            </div>
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">اللون</label>
                                <input type="color" id="color" name="color" value="${categoryData?.color || '#667eea'}" style="width: 100%; height: 42px; border: 2px solid #e0e0e0; border-radius: 8px; cursor: pointer;">
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">الأيقونة</label>
                                <input type="text" id="icon" name="icon" value="${categoryData?.icon || 'fas fa-folder'}" placeholder="fas fa-folder" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                            <div>
                                <label style="display: block; font-weight: 600; color: #333; margin-bottom: 5px;">ترتيب العرض</label>
                                <input type="number" id="sortOrder" name="sort_order" value="${categoryData?.sort_order || 0}" min="0" style="width: 100%; padding: 10px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 1rem;">
                            </div>
                        </div>

                        <div style="display: flex; gap: 20px;">
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="isActive" name="is_active" ${categoryData?.is_active == 1 ? 'checked' : 'checked'} style="width: 18px; height: 18px; accent-color: #667eea;">
                                <span>فئة نشطة</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                                <input type="checkbox" id="isFeatured" name="is_featured" ${categoryData?.is_featured == 1 ? 'checked' : ''} style="width: 18px; height: 18px; accent-color: #667eea;">
                                <span>فئة مميزة</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div style="padding: 20px; border-top: 1px solid #e0e0e0; display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="saveCategory()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-save"></i> ${saveButtonText}
                    </button>
                    <button onclick="closeCategoryModal()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        </div>
    `;
}

/**
 * Refresh parent category dropdown in an open modal
 */
function refreshParentDropdown(selectedParentId = null, excludeCategoryId = null) {
    const parentSelect = document.getElementById('parentId');
    if (parentSelect) {
        parentSelect.innerHTML = createParentCategoryOptions(selectedParentId, excludeCategoryId);
    }
}

// Modal functions
function showAddCategoryModal() {
    editingCategoryId = null;
    const modalContainer = document.getElementById('categoryModal');
    modalContainer.innerHTML = createCategoryModal(false);
    modalContainer.style.display = 'block';
}

function closeCategoryModal() {
    const modalContainer = document.getElementById('categoryModal');
    modalContainer.style.display = 'none';
    modalContainer.innerHTML = '';
    editingCategoryId = null;
}

// Action functions
async function saveCategory() {
    const form = document.getElementById('categoryForm');
    if (!form) {
        alert('خطأ: لم يتم العثور على النموذج');
        return;
    }

    // Validate required fields
    const nameAr = document.getElementById('nameAr').value.trim();
    if (!nameAr) {
        alert('الاسم العربي مطلوب');
        document.getElementById('nameAr').focus();
        return;
    }

    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // Convert checkboxes
    data.is_active = document.getElementById('isActive').checked ? 1 : 0;
    data.is_featured = document.getElementById('isFeatured').checked ? 1 : 0;

    // Handle empty parent_id
    if (!data.parent_id) {
        data.parent_id = null;
    }

    // Validate parent-child relationship to prevent circular references
    if (editingCategoryId && data.parent_id) {
        if (data.parent_id == editingCategoryId) {
            alert('خطأ: لا يمكن أن تكون الفئة أب لنفسها');
            return;
        }

        // Check if the selected parent is a child of the current category
        const allCategories = window.allCategoriesData || [];
        const isCircular = checkCircularReference(editingCategoryId, data.parent_id, allCategories);
        if (isCircular) {
            alert('خطأ: لا يمكن إنشاء علاقة دائرية بين الفئات. الفئة المختارة كأب هي فئة فرعية للفئة الحالية.');
            return;
        }
    }

    console.log('💾 بيانات الفئة للحفظ:', data);
    console.log('🔄 نوع العملية:', editingCategoryId ? 'تحديث' : 'إضافة');

    try {
        const url = editingCategoryId ?
            `php/categories.php?action=update&id=${editingCategoryId}` :
            'php/categories.php?action=create';

        console.log('📡 إرسال طلب إلى:', url);

        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });

        console.log('📡 استجابة الخادم:', response.status, response.statusText);

        const result = await response.json();
        console.log('📦 نتيجة العملية:', result);

        if (result.success) {
            const message = editingCategoryId ? 'تم تحديث الفئة بنجاح!' : 'تم إضافة الفئة بنجاح!';
            alert(message);
            closeCategoryModal();

            // Reload the categories list
            console.log('🔄 إعادة تحميل قائمة الفئات...');
            loadInteractiveCategoriesContent();
        } else {
            console.error('❌ خطأ في العملية:', result.message);
            alert('خطأ: ' + (result.message || 'حدث خطأ غير معروف'));
        }
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error);
        alert('خطأ في الاتصال: ' + error.message);
    }
}

async function editCategory(id) {
    try {
        console.log('🔄 جاري تحميل بيانات الفئة للتعديل...', id);

        const response = await fetch(`php/categories.php?action=get_by_id&id=${id}`);
        const result = await response.json();

        console.log('📦 استجابة API للفئة:', result);

        if (result.success && result.data) {
            // The API returns data.category, not data directly
            const category = result.data.category || result.data;
            editingCategoryId = id;

            console.log('✅ تم جلب بيانات الفئة بنجاح:', category);

            // Create modal with category data
            const modalContainer = document.getElementById('categoryModal');
            modalContainer.innerHTML = createCategoryModal(true, category);
            modalContainer.style.display = 'block';

            console.log('✅ تم عرض نموذج التعديل');
        } else {
            console.error('❌ خطأ في البيانات:', result);
            alert('خطأ في جلب بيانات الفئة: ' + (result.message || 'بيانات غير صحيحة'));
        }
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error);
        alert('خطأ في الاتصال: ' + error.message);
    }
}

async function deleteCategory(id) {
    if (!confirm('هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع الفئات الفرعية أيضاً.')) {
        return;
    }

    try {
        const response = await fetch(`php/categories.php?action=delete&id=${id}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (result.success) {
            alert('تم حذف الفئة بنجاح!');
            loadInteractiveCategoriesContent();
        } else {
            alert('خطأ: ' + result.message);
        }
    } catch (error) {
        alert('خطأ في الاتصال: ' + error.message);
    }
}

async function toggleFeatured(id) {
    try {
        const response = await fetch(`php/categories.php?action=toggle_status&id=${id}`, {
            method: 'POST'
        });
        const result = await response.json();

        if (result.success) {
            loadInteractiveCategoriesContent();
        } else {
            alert('خطأ: ' + result.message);
        }
    } catch (error) {
        alert('خطأ في الاتصال: ' + error.message);
    }
}

// Make functions globally available
window.loadInteractiveCategoriesContent = loadInteractiveCategoriesContent;
window.showAddCategoryModal = showAddCategoryModal;
window.closeCategoryModal = closeCategoryModal;
window.saveCategory = saveCategory;
window.editCategory = editCategory;
window.deleteCategory = deleteCategory;
window.toggleFeatured = toggleFeatured;

console.log('✅ تم تحميل ملف categories-interactive.js بنجاح');
console.log('🔧 الدوال المتاحة:', {
    loadInteractiveCategoriesContent: typeof loadInteractiveCategoriesContent,
    showAddCategoryModal: typeof showAddCategoryModal,
    editCategory: typeof editCategory,
    saveCategory: typeof saveCategory,
    deleteCategory: typeof deleteCategory
});
