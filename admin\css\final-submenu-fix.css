/**
 * Final Submenu Fix CSS
 * الإصلاح النهائي لضمان ظهور عناصر القائمة الفرعية
 */

/* FORCE SUBMENU TO BE VISIBLE - HIGHEST PRIORITY */
.admin-settings-submenu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    width: 100% !important;
    z-index: 10 !important;
    padding: 12px 0 16px 0 !important;
    margin: 0 !important;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.15) 100%) !important;
    border-radius: 0 0 12px 12px !important;
    list-style: none !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
}

/* FORCE ALL SUBMENU ITEMS TO BE VISIBLE */
.admin-settings-submenu li {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    width: calc(100% - 24px) !important;
    height: auto !important;
    min-height: 50px !important;
    max-height: none !important;
    margin: 4px 12px !important;
    padding: 14px 18px !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 10px !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    cursor: pointer !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex-direction: row !important;
    box-sizing: border-box !important;
    z-index: 11 !important;
    overflow: visible !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
    transition: all 0.3s ease !important;
}

/* FORCE SPECIFIC ITEMS TO BE VISIBLE */
.admin-settings-submenu li[data-section="securitySettings"],
.admin-settings-submenu li[data-section="subscriptionsManagement"] {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    width: calc(100% - 24px) !important;
    height: auto !important;
    min-height: 50px !important;
    margin: 4px 12px !important;
    padding: 14px 18px !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%) !important;
    color: rgba(255, 255, 255, 0.95) !important;
    border-radius: 10px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    cursor: pointer !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex-direction: row !important;
    box-sizing: border-box !important;
    z-index: 12 !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
}

/* FORCE ICONS AND TEXT TO BE VISIBLE */
.admin-settings-submenu li i {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 1.1rem !important;
    width: 24px !important;
    height: auto !important;
    margin-left: 12px !important;
    margin-right: 0 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-align: center !important;
    flex-shrink: 0 !important;
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    z-index: 13 !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
}

.admin-settings-submenu li span {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    text-align: right !important;
    flex: 1 !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
    padding: 0 !important;
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    width: auto !important;
    height: auto !important;
    z-index: 13 !important;
    pointer-events: auto !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
    white-space: nowrap !important;
    overflow: visible !important;
    text-overflow: clip !important;
}

/* HOVER EFFECTS */
.admin-settings-submenu li:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    color: #ffffff !important;
    transform: translateX(-4px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

.admin-settings-submenu li:hover i,
.admin-settings-submenu li:hover span {
    color: #ffffff !important;
}

/* ACTIVE STATE */
.admin-settings-submenu li.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.12) 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4) !important;
    transform: translateX(-2px) !important;
}

.admin-settings-submenu li.active i,
.admin-settings-submenu li.active span {
    color: #ffffff !important;
}

/* FORCE ADMIN SETTINGS MENU TO BE EXPANDED */
.admin-settings-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.admin-settings-menu .admin-settings-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* FORCE ARROW TO SHOW EXPANDED STATE */
.admin-settings-arrow {
    transform: rotate(180deg) !important;
}

/* OVERRIDE ANY CONFLICTING STYLES FROM OTHER CSS FILES */
.admin-nav .admin-settings-submenu,
.sidebar .admin-settings-submenu,
nav .admin-settings-submenu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
    overflow: visible !important;
}

.admin-nav .admin-settings-submenu li,
.sidebar .admin-settings-submenu li,
nav .admin-settings-submenu li {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* MOBILE RESPONSIVE */
@media (max-width: 768px) {
    .admin-settings-submenu li {
        margin: 3px 8px !important;
        padding: 12px 14px !important;
        width: calc(100% - 16px) !important;
        min-height: 45px !important;
    }
    
    .admin-settings-submenu li i {
        font-size: 1rem !important;
        width: 20px !important;
        margin-left: 10px !important;
    }
    
    .admin-settings-submenu li span {
        font-size: 0.85rem !important;
    }
}

/* PRINT STYLES */
@media print {
    .admin-settings-submenu {
        display: block !important;
        visibility: visible !important;
    }
    
    .admin-settings-submenu li {
        display: flex !important;
        visibility: visible !important;
    }
}

/* HIGH CONTRAST MODE */
@media (prefers-contrast: high) {
    .admin-settings-submenu li {
        border: 2px solid currentColor !important;
    }
}

/* REDUCED MOTION */
@media (prefers-reduced-motion: reduce) {
    .admin-settings-submenu li {
        transition: none !important;
    }
    
    .admin-settings-submenu li:hover {
        transform: none !important;
    }
}

/* DEBUG STYLES - UNCOMMENT TO SEE BOUNDARIES */
/*
.admin-settings-submenu {
    border: 3px solid red !important;
    background: rgba(255, 0, 0, 0.2) !important;
}

.admin-settings-submenu li {
    border: 2px solid green !important;
    background: rgba(0, 255, 0, 0.2) !important;
}

.admin-settings-submenu li[data-section="securitySettings"] {
    border: 3px solid blue !important;
    background: rgba(0, 0, 255, 0.3) !important;
}

.admin-settings-submenu li[data-section="subscriptionsManagement"] {
    border: 3px solid orange !important;
    background: rgba(255, 165, 0, 0.3) !important;
}
*/
