// Firebase User Management for Admin Panel
import { getFirestore, collection, getDocs, doc, updateDoc, deleteDoc, query, orderBy, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

class FirebaseUserManager {
    constructor() {
        this.db = getFirestore();
        this.users = [];
        this.currentPage = 1;
        this.usersPerPage = 10;
        this.totalUsers = 0;
    }

    // Load all users from Firestore
    async loadUsers(filters = {}) {
        try {
            console.log('📊 Loading users from Firebase...');
            
            let q = collection(this.db, 'users');
            
            // Apply filters
            if (filters.role) {
                q = query(q, where('role', '==', filters.role));
            }
            
            if (filters.isActive !== undefined) {
                q = query(q, where('isActive', '==', filters.isActive));
            }
            
            // Order by creation date
            q = query(q, orderBy('createdAt', 'desc'));
            
            const querySnapshot = await getDocs(q);
            this.users = [];
            
            querySnapshot.forEach((doc) => {
                this.users.push({
                    id: doc.id,
                    ...doc.data()
                });
            });
            
            this.totalUsers = this.users.length;
            console.log(`✅ Loaded ${this.totalUsers} users`);
            
            return {
                success: true,
                users: this.users,
                total: this.totalUsers
            };
            
        } catch (error) {
            console.error('❌ Error loading users:', error);
            return {
                success: false,
                error: error.message,
                users: [],
                total: 0
            };
        }
    }

    // Get paginated users
    getPaginatedUsers(page = 1, perPage = 10) {
        const startIndex = (page - 1) * perPage;
        const endIndex = startIndex + perPage;
        
        return {
            users: this.users.slice(startIndex, endIndex),
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(this.totalUsers / perPage),
                totalUsers: this.totalUsers,
                perPage: perPage,
                hasNext: endIndex < this.totalUsers,
                hasPrev: page > 1
            }
        };
    }

    // Update user role
    async updateUserRole(userId, newRole) {
        try {
            const userRef = doc(this.db, 'users', userId);
            await updateDoc(userRef, {
                role: newRole,
                updatedAt: new Date().toISOString()
            });
            
            // Update local data
            const userIndex = this.users.findIndex(u => u.id === userId);
            if (userIndex !== -1) {
                this.users[userIndex].role = newRole;
                this.users[userIndex].updatedAt = new Date().toISOString();
            }
            
            console.log(`✅ User role updated: ${userId} -> ${newRole}`);
            return { success: true };
            
        } catch (error) {
            console.error('❌ Error updating user role:', error);
            return { success: false, error: error.message };
        }
    }

    // Toggle user active status
    async toggleUserStatus(userId) {
        try {
            const user = this.users.find(u => u.id === userId);
            if (!user) {
                throw new Error('User not found');
            }
            
            const newStatus = !user.isActive;
            const userRef = doc(this.db, 'users', userId);
            
            await updateDoc(userRef, {
                isActive: newStatus,
                updatedAt: new Date().toISOString()
            });
            
            // Update local data
            user.isActive = newStatus;
            user.updatedAt = new Date().toISOString();
            
            console.log(`✅ User status updated: ${userId} -> ${newStatus ? 'Active' : 'Inactive'}`);
            return { success: true, newStatus };
            
        } catch (error) {
            console.error('❌ Error updating user status:', error);
            return { success: false, error: error.message };
        }
    }

    // Delete user (soft delete by deactivating)
    async deleteUser(userId) {
        try {
            const userRef = doc(this.db, 'users', userId);
            await updateDoc(userRef, {
                isActive: false,
                deletedAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
            
            // Remove from local data
            this.users = this.users.filter(u => u.id !== userId);
            this.totalUsers--;
            
            console.log(`✅ User soft deleted: ${userId}`);
            return { success: true };
            
        } catch (error) {
            console.error('❌ Error deleting user:', error);
            return { success: false, error: error.message };
        }
    }

    // Get user statistics
    getUserStats() {
        const stats = {
            total: this.totalUsers,
            active: this.users.filter(u => u.isActive).length,
            inactive: this.users.filter(u => !u.isActive).length,
            roles: {}
        };
        
        // Count by roles
        this.users.forEach(user => {
            const role = user.role || 'user';
            stats.roles[role] = (stats.roles[role] || 0) + 1;
        });
        
        return stats;
    }

    // Search users
    searchUsers(searchTerm) {
        if (!searchTerm) return this.users;
        
        const term = searchTerm.toLowerCase();
        return this.users.filter(user => 
            user.email?.toLowerCase().includes(term) ||
            user.displayName?.toLowerCase().includes(term) ||
            user.role?.toLowerCase().includes(term)
        );
    }

    // Format user data for display
    formatUserForDisplay(user) {
        return {
            id: user.id,
            email: user.email,
            displayName: user.displayName || user.email?.split('@')[0] || 'غير محدد',
            role: this.getRoleDisplayName(user.role),
            roleValue: user.role,
            isActive: user.isActive,
            statusText: user.isActive ? 'نشط' : 'غير نشط',
            statusClass: user.isActive ? 'status-active' : 'status-inactive',
            createdAt: this.formatDate(user.createdAt),
            lastLogin: this.formatDate(user.lastLogin),
            photoURL: user.photoURL || null,
            provider: user.provider || 'email'
        };
    }

    // Get role display name in Arabic
    getRoleDisplayName(role) {
        const roleNames = {
            'super_admin': 'مدير رئيسي',
            'admin': 'مدير',
            'owner': 'مالك',
            'manager': 'مدير متجر',
            'user': 'مستخدم',
            'demo': 'تجريبي'
        };
        
        return roleNames[role] || 'مستخدم';
    }

    // Format date for display
    formatDate(dateString) {
        if (!dateString) return 'غير محدد';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return 'تاريخ غير صالح';
        }
    }

    // Generate user management HTML
    generateUserManagementHTML(users, pagination) {
        const userRows = users.map(user => {
            const formattedUser = this.formatUserForDisplay(user);
            
            return `
                <tr data-user-id="${user.id}">
                    <td>
                        <div class="user-info">
                            ${formattedUser.photoURL ? 
                                `<img src="${formattedUser.photoURL}" alt="Avatar" class="user-avatar">` : 
                                `<div class="user-avatar-placeholder">${formattedUser.displayName.charAt(0)}</div>`
                            }
                            <div class="user-details">
                                <div class="user-name">${formattedUser.displayName}</div>
                                <div class="user-email">${formattedUser.email}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <select class="role-select" data-user-id="${user.id}" data-current-role="${formattedUser.roleValue}">
                            <option value="user" ${formattedUser.roleValue === 'user' ? 'selected' : ''}>مستخدم</option>
                            <option value="manager" ${formattedUser.roleValue === 'manager' ? 'selected' : ''}>مدير متجر</option>
                            <option value="admin" ${formattedUser.roleValue === 'admin' ? 'selected' : ''}>مدير</option>
                            <option value="owner" ${formattedUser.roleValue === 'owner' ? 'selected' : ''}>مالك</option>
                            <option value="super_admin" ${formattedUser.roleValue === 'super_admin' ? 'selected' : ''}>مدير رئيسي</option>
                        </select>
                    </td>
                    <td>
                        <span class="status-badge ${formattedUser.statusClass}">
                            ${formattedUser.statusText}
                        </span>
                    </td>
                    <td>${formattedUser.createdAt}</td>
                    <td>${formattedUser.lastLogin}</td>
                    <td>
                        <div class="user-actions">
                            <button class="btn btn-sm btn-toggle" onclick="toggleUserStatus('${user.id}')" 
                                    title="${user.isActive ? 'تعطيل' : 'تفعيل'}">
                                <i class="fas ${user.isActive ? 'fa-pause' : 'fa-play'}"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUser('${user.id}')" 
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        const paginationHTML = this.generatePaginationHTML(pagination);

        return `
            <div class="users-table-container">
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>آخر دخول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${userRows}
                    </tbody>
                </table>
            </div>
            ${paginationHTML}
        `;
    }

    // Generate pagination HTML
    generatePaginationHTML(pagination) {
        if (pagination.totalPages <= 1) return '';

        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (pagination.hasPrev) {
            paginationHTML += `<button class="pagination-btn" onclick="loadUsersPage(${pagination.currentPage - 1})">السابق</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= pagination.totalPages; i++) {
            const isActive = i === pagination.currentPage;
            paginationHTML += `<button class="pagination-btn ${isActive ? 'active' : ''}" onclick="loadUsersPage(${i})">${i}</button>`;
        }
        
        // Next button
        if (pagination.hasNext) {
            paginationHTML += `<button class="pagination-btn" onclick="loadUsersPage(${pagination.currentPage + 1})">التالي</button>`;
        }
        
        paginationHTML += '</div>';
        return paginationHTML;
    }
}

// Create global instance
window.firebaseUserManager = new FirebaseUserManager();

// Export for module usage
export { FirebaseUserManager };
