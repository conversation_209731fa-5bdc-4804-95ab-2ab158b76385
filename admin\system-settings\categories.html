<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفئات - إعدادات النظام</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/system-settings.css">
</head>
<body>
    <div class="system-settings-container">
        <!-- Header -->
        <header class="system-header">
            <h1>
                <i class="fas fa-tags"></i>
                إدارة الفئات
            </h1>
            <p>إدارة فئات المنتجات وتصنيفاتها</p>
        </header>

        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <a href="../index.html"><i class="fas fa-home"></i> لوحة التحكم</a>
            <span class="separator">/</span>
            <a href="index.html">إعدادات النظام</a>
            <span class="separator">/</span>
            <span>إدارة الفئات</span>
        </nav>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="stat-card">
                <div class="stat-number" id="totalCategories">0</div>
                <div class="stat-label">إجمالي الفئات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeCategories">0</div>
                <div class="stat-label">الفئات النشطة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="parentCategories">0</div>
                <div class="stat-label">الفئات الرئيسية</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="childCategories">0</div>
                <div class="stat-label">الفئات الفرعية</div>
            </div>
        </div>

        <!-- Categories Header -->
        <div class="categories-header">
            <div class="categories-filters">
                <div class="filter-group">
                    <label for="categorySearch">البحث</label>
                    <input type="text" id="categorySearch" class="enhanced-input" placeholder="البحث في الفئات...">
                </div>
                
                <div class="filter-group">
                    <label for="statusFilter">الحالة</label>
                    <select id="statusFilter" class="enhanced-select">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="parentFilter">الفئة الأب</label>
                    <select id="parentFilter" class="enhanced-select">
                        <option value="">جميع الفئات</option>
                        <option value="0">الفئات الرئيسية فقط</option>
                    </select>
                </div>
            </div>

            <div style="display: flex; gap: 15px; align-items: center;">
                <!-- View Mode Toggle -->
                <div class="view-mode-toggle">
                    <button class="view-mode-btn active" data-mode="list">
                        <i class="fas fa-th-large"></i>
                        شبكة
                    </button>
                    <button class="view-mode-btn" data-mode="tree">
                        <i class="fas fa-sitemap"></i>
                        شجرة
                    </button>
                </div>

                <!-- Add Button -->
                <button class="btn btn-primary" onclick="CategoriesManager.showAddModal()">
                    <i class="fas fa-plus"></i>
                    إضافة فئة
                </button>
            </div>
        </div>

        <!-- Categories Container -->
        <div class="categories-container">
            <div id="categoriesContainer">
                <!-- Categories will be loaded here -->
            </div>
            
            <!-- Pagination -->
            <div id="paginationContainer"></div>
        </div>
    </div>

    <!-- Category Modal -->
    <div class="modal" id="categoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة فئة جديدة</h3>
                <button class="modal-close" onclick="CategoriesManager.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="categoryForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="name_ar">الاسم بالعربية *</label>
                            <input type="text" id="name_ar" name="name_ar" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="name">الاسم بالإنجليزية *</label>
                            <input type="text" id="name" name="name" class="form-control" required>
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label" for="description_ar">الوصف بالعربية</label>
                            <textarea id="description_ar" name="description_ar" class="form-control" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label" for="description">الوصف بالإنجليزية</label>
                            <textarea id="description" name="description" class="form-control" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="parentId">الفئة الأب</label>
                            <select id="parentId" name="parentId" class="form-control">
                                <option value="">فئة رئيسية</option>
                                <!-- Parent categories will be loaded here -->
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="sortOrder">ترتيب العرض</label>
                            <input type="number" id="sortOrder" name="sortOrder" class="form-control" value="0" min="0">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">اللون والأيقونة</label>
                            <div class="color-icon-group">
                                <div class="color-picker-group">
                                    <input type="color" id="color" name="color" class="form-control" value="#3498db">
                                    <div class="color-preview" id="colorPreview"></div>
                                </div>
                                <div class="icon-picker-group">
                                    <select id="icon" name="icon" class="form-control">
                                        <option value="fas fa-tag">🏷️ تاغ</option>
                                        <option value="fas fa-folder">📁 مجلد</option>
                                        <option value="fas fa-box">📦 صندوق</option>
                                        <option value="fas fa-shopping-bag">🛍️ حقيبة تسوق</option>
                                        <option value="fas fa-tshirt">👕 ملابس</option>
                                        <option value="fas fa-mobile-alt">📱 جوال</option>
                                        <option value="fas fa-laptop">💻 لابتوب</option>
                                        <option value="fas fa-home">🏠 منزل</option>
                                        <option value="fas fa-car">🚗 سيارة</option>
                                        <option value="fas fa-book">📚 كتاب</option>
                                        <option value="fas fa-gamepad">🎮 ألعاب</option>
                                        <option value="fas fa-utensils">🍽️ أدوات مطبخ</option>
                                        <option value="fas fa-dumbbell">🏋️ رياضة</option>
                                        <option value="fas fa-paint-brush">🎨 فن</option>
                                        <option value="fas fa-music">🎵 موسيقى</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- SEO Settings -->
                    <div class="form-group full-width">
                        <label class="form-label" for="metaTitle">عنوان SEO</label>
                        <input type="text" id="metaTitle" name="metaTitle" class="form-control" maxlength="200">
                    </div>
                    
                    <div class="form-group full-width">
                        <label class="form-label" for="metaDescription">وصف SEO</label>
                        <textarea id="metaDescription" name="metaDescription" class="form-control" rows="2" maxlength="300"></textarea>
                    </div>
                    
                    <!-- Active Status -->
                    <div class="form-check">
                        <input type="checkbox" id="isActive" name="isActive" checked>
                        <label for="isActive">فئة نشطة</label>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="CategoriesManager.closeModal()">
                    إلغاء
                </button>
                <button type="submit" form="categoryForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/core.js"></script>
    <script src="js/categories.js"></script>
    
    <script>
        // Update color preview when color changes
        document.addEventListener('DOMContentLoaded', () => {
            const colorInput = document.getElementById('color');
            const colorPreview = document.getElementById('colorPreview');
            
            if (colorInput && colorPreview) {
                const updateColorPreview = () => {
                    colorPreview.style.backgroundColor = colorInput.value;
                };
                
                colorInput.addEventListener('input', updateColorPreview);
                updateColorPreview(); // Initial update
            }
        });
    </script>
</body>
</html>
