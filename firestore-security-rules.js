// Firestore Security Rules for Production Firebase Authentication System
// Copy these rules to Firebase Console -> Firestore Database -> Rules

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Users collection - users can read/write their own profile
    match /users/{userId} {
      // Allow users to read and write their own profile
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Allow admin users to read all user profiles
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin', 'owner'];

      // Allow admin users to update user roles
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin', 'owner'];
    }

    // System collection - for connectivity tests and system data
    match /system/{document} {
      // Allow authenticated users (including anonymous) to read/write system documents
      // This enables connectivity testing
      allow read, write: if request.auth != null;

      // Special rule for connectivity_test document - explicitly allow anonymous users
      match /connectivity_test {
        allow read, write: if request.auth != null;
        // Additional explicit rule for anonymous users
        allow read, write: if request.auth != null && request.auth.token.firebase.sign_in_provider == 'anonymous';
      }
    }

    // Test collection - for development and testing purposes
    match /test/{document} {
      // Allow authenticated users to read/write test documents
      allow read, write: if request.auth != null;
    }

    // Admin-only collections (categories, products, settings, etc.)
    match /categories/{document} {
      // Only admin users can manage categories
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin', 'owner'];
    }

    match /products/{document} {
      // Only admin users can manage products
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin', 'owner'];
    }

    match /settings/{document} {
      // Only admin users can manage settings
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin', 'owner'];
    }

    match /orders/{document} {
      // Only admin users can manage orders
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin', 'owner'];
    }

    // Default rule - deny all other access
    match /{document=**} {
      // Only admin users can access other collections
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin', 'owner'];
    }
  }
}

/*
INSTRUCTIONS FOR APPLYING THESE RULES:

1. Go to Firebase Console (https://console.firebase.google.com/)
2. Select your project: landingpage-a7491
3. Navigate to Firestore Database
4. Click on "Rules" tab
5. Replace the existing rules with the content above
6. Click "Publish" to apply the rules

KEY FEATURES OF THESE RULES:

✅ User Profile Management:
   - Users can read/write their own profiles
   - Admins can read/write all user profiles

✅ Connectivity Testing:
   - /system/connectivity_test allows authenticated access
   - Enables the testFirestoreConnection() method to work

✅ Admin Access Control:
   - Only admin/super_admin/owner roles can access admin collections
   - Secure role-based access control

✅ Development Support:
   - /test collection allows testing during development
   - Anonymous authentication supported for connectivity tests

✅ Production Security:
   - All access requires authentication
   - Role-based permissions for sensitive data
   - Default deny rule for unknown collections

TESTING THE RULES:

After applying these rules, your Firestore connectivity test should pass because:
1. Anonymous authentication is allowed
2. /system/connectivity_test document allows read/write for authenticated users
3. The testFirestoreConnection() method can successfully test the connection

If you encounter issues, check the Firebase Console -> Firestore -> Rules playground
to test specific operations and debug rule matching.
*/
