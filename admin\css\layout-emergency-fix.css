/**
 * Emergency Layout Fix for Admin Panel
 * Fixes content being hidden behind the right sidebar
 */

/* Critical Layout Fix - Override any conflicting styles */
@media (min-width: 769px) {
  .main-content {
    margin-right: 350px !important;
    padding: 40px !important;
    padding-right: 80px !important;
    width: calc(100% - 350px) !important;
    max-width: calc(100vw - 350px) !important;
    box-sizing: border-box !important;
    overflow-x: visible !important;
    position: relative !important;
  }

  .sidebar {
    width: 320px !important;
    position: fixed !important;
    right: 0 !important;
    top: 0 !important;
    height: 100vh !important;
    z-index: 100 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: -5px 0 15px rgba(0,0,0,0.1) !important;
  }

  /* Ensure all content sections are properly contained */
  .content-section {
    width: 100% !important;
    max-width: none !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
    overflow: visible !important;
  }

  /* Fix for tables and wide content */
  .content-section > div[style*="overflow-x: auto"],
  .table-responsive {
    margin-right: 0 !important;
    width: 100% !important;
    overflow-x: auto !important;
  }

  /* Fix for grid layouts */
  [style*="display: grid"],
  [style*="display: flex"] {
    margin-right: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix for specific sections that might be problematic */
  #dashboard,
  #books,
  #orders,
  #landingPages,
  #reportsContent,
  #generalSettings,
  #paymentSettings,
  #categories,
  #usersManagement,
  #rolesManagement,
  #showcaseDemo,
  #storeSettings,
  #storesManagementContent,
  #securitySettings,
  #subscriptionsManagement {
    width: 100% !important;
    max-width: none !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
  }

  /* Fix for tables specifically */
  table {
    width: 100% !important;
    max-width: none !important;
    table-layout: auto !important;
  }

  /* Fix for cards and statistics */
  .dashboard-stats,
  .stats-grid,
  [style*="grid-template-columns"] {
    margin-right: 0 !important;
    padding-right: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix for headers and titles */
  .content-header,
  .dashboard-header,
  [style*="background: linear-gradient"] {
    margin-right: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Ensure buttons and controls are visible */
  .section-header,
  .action-buttons,
  [style*="display: flex"][style*="justify-content"] {
    margin-right: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .main-content {
    margin-right: 0 !important;
    padding: 20px !important;
    width: 100% !important;
    max-width: 100vw !important;
  }

  .sidebar {
    width: 280px !important;
    right: -280px !important;
    transition: right 0.3s ease !important;
  }

  .sidebar.mobile-open {
    right: 0 !important;
  }
}

/* Additional safety measures */
* {
  box-sizing: border-box;
}

.admin-container {
  overflow-x: hidden !important;
}

/* Ensure no element extends beyond the viewport */
.main-content * {
  max-width: 100% !important;
}

/* Fix for any remaining overflow issues */
.main-content {
  position: relative !important;
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  right: -20px;
  width: 20px;
  height: 100%;
  background: transparent;
  pointer-events: none;
  z-index: -1;
}

/* Debug helper - remove in production */
.debug-layout {
  border: 2px solid red !important;
  background: rgba(255, 0, 0, 0.1) !important;
}

/* Force visibility for hidden content */
.content-section.active {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure proper stacking order */
.main-content {
  z-index: 1 !important;
}

.sidebar {
  z-index: 100 !important;
}

/* Final override for any stubborn elements */
.main-content > section,
.main-content > div,
.content-section > div {
  margin-right: 0 !important;
  padding-right: 0 !important;
  width: 100% !important;
  max-width: none !important;
  box-sizing: border-box !important;
}
