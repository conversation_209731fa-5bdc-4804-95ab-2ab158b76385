<?php

/**
 * Users Management Tables Setup
 * إعداد جداول إدارة المستخدمين
 */

// Load configuration
require_once '../../config/config.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد جداول إدارة المستخدمين</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { color: #28a745; margin: 10px 0; }
        .error { color: #dc3545; margin: 10px 0; }
        .info { color: #17a2b8; margin: 10px 0; }
        .warning { color: #ffc107; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: right; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>👥 إعداد جداول إدارة المستخدمين</h1>";

try {
    // Connect to database
    $dbConfig = Config::getDbConfig();
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $dbConfig['host'],
        $dbConfig['port'],
        $dbConfig['database']
    );

    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];

    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

    // Drop existing tables if they exist
    echo "<h2>حذف الجداول الموجودة (إن وجدت):</h2>";

    $tablesToDrop = [
        'user_sessions',
        'user_permissions',
        'role_permissions',
        'user_roles',
        'users',
        'roles',
        'permissions'
    ];

    foreach ($tablesToDrop as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS `$table`");
            echo "<div class='warning'>🗑️ تم حذف الجدول $table</div>";
        } catch (Exception $e) {
            echo "<div class='info'>ℹ️ الجدول $table غير موجود</div>";
        }
    }

    // Create permissions table
    echo "<h2>إنشاء جدول permissions:</h2>";

    $createPermissions = "
    CREATE TABLE `permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `display_name_ar` varchar(200) NOT NULL,
        `display_name_en` varchar(200),
        `description_ar` text,
        `description_en` text,
        `category` varchar(50) NOT NULL DEFAULT 'general',
        `is_system` tinyint(1) DEFAULT 0,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `name` (`name`),
        KEY `category` (`category`),
        KEY `is_system` (`is_system`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createPermissions);
    echo "<div class='success'>✅ تم إنشاء جدول permissions بنجاح</div>";

    // Create roles table
    echo "<h2>إنشاء جدول roles:</h2>";

    $createRoles = "
    CREATE TABLE `roles` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `display_name_ar` varchar(200) NOT NULL,
        `display_name_en` varchar(200),
        `description_ar` text,
        `description_en` text,
        `color` varchar(7) DEFAULT '#667eea',
        `icon` varchar(50) DEFAULT 'fas fa-user',
        `is_system` tinyint(1) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        `sort_order` int(11) DEFAULT 0,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `name` (`name`),
        KEY `is_active` (`is_active`),
        KEY `sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createRoles);
    echo "<div class='success'>✅ تم إنشاء جدول roles بنجاح</div>";

    // Create users table
    echo "<h2>إنشاء جدول users:</h2>";

    $createUsers = "
    CREATE TABLE `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(100) NOT NULL,
        `email` varchar(255) NOT NULL,
        `password_hash` varchar(255) NOT NULL,
        `first_name` varchar(100),
        `last_name` varchar(100),
        `full_name` varchar(200),
        `phone` varchar(20),
        `avatar` varchar(255),
        `bio` text,
        `date_of_birth` date,
        `gender` enum('male','female','other') DEFAULT NULL,
        `country` varchar(100),
        `city` varchar(100),
        `address` text,
        `is_active` tinyint(1) DEFAULT 1,
        `is_verified` tinyint(1) DEFAULT 0,
        `email_verified_at` timestamp NULL DEFAULT NULL,
        `last_login_at` timestamp NULL DEFAULT NULL,
        `last_login_ip` varchar(45),
        `login_attempts` int(11) DEFAULT 0,
        `locked_until` timestamp NULL DEFAULT NULL,
        `preferences` json,
        `metadata` json,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `username` (`username`),
        UNIQUE KEY `email` (`email`),
        KEY `is_active` (`is_active`),
        KEY `is_verified` (`is_verified`),
        KEY `last_login_at` (`last_login_at`),
        KEY `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createUsers);
    echo "<div class='success'>✅ تم إنشاء جدول users بنجاح</div>";

    // Create role_permissions table
    echo "<h2>إنشاء جدول role_permissions:</h2>";

    $createRolePermissions = "
    CREATE TABLE `role_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `role_id` int(11) NOT NULL,
        `permission_id` int(11) NOT NULL,
        `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `granted_by` int(11),
        PRIMARY KEY (`id`),
        UNIQUE KEY `role_permission` (`role_id`, `permission_id`),
        KEY `role_id` (`role_id`),
        KEY `permission_id` (`permission_id`),
        FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
        FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
        FOREIGN KEY (`granted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createRolePermissions);
    echo "<div class='success'>✅ تم إنشاء جدول role_permissions بنجاح</div>";

    // Create user_roles table
    echo "<h2>إنشاء جدول user_roles:</h2>";

    $createUserRoles = "
    CREATE TABLE `user_roles` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `role_id` int(11) NOT NULL,
        `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `assigned_by` int(11),
        `expires_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_role` (`user_id`, `role_id`),
        KEY `user_id` (`user_id`),
        KEY `role_id` (`role_id`),
        KEY `expires_at` (`expires_at`),
        FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
        FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createUserRoles);
    echo "<div class='success'>✅ تم إنشاء جدول user_roles بنجاح</div>";

    // Create user_permissions table (for direct user permissions)
    echo "<h2>إنشاء جدول user_permissions:</h2>";

    $createUserPermissions = "
    CREATE TABLE `user_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `permission_id` int(11) NOT NULL,
        `granted` tinyint(1) DEFAULT 1,
        `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `granted_by` int(11),
        `expires_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_permission` (`user_id`, `permission_id`),
        KEY `user_id` (`user_id`),
        KEY `permission_id` (`permission_id`),
        KEY `expires_at` (`expires_at`),
        FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
        FOREIGN KEY (`granted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createUserPermissions);
    echo "<div class='success'>✅ تم إنشاء جدول user_permissions بنجاح</div>";

    // Create user_sessions table
    echo "<h2>إنشاء جدول user_sessions:</h2>";

    $createUserSessions = "
    CREATE TABLE `user_sessions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `session_token` varchar(255) NOT NULL,
        `ip_address` varchar(45),
        `user_agent` text,
        `device_info` json,
        `location_info` json,
        `is_active` tinyint(1) DEFAULT 1,
        `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        `expires_at` timestamp NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `session_token` (`session_token`),
        KEY `user_id` (`user_id`),
        KEY `is_active` (`is_active`),
        KEY `expires_at` (`expires_at`),
        KEY `last_activity` (`last_activity`),
        FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createUserSessions);
    echo "<div class='success'>✅ تم إنشاء جدول user_sessions بنجاح</div>";

    // Insert default permissions
    echo "<h2>إدراج الصلاحيات الافتراضية:</h2>";

    $defaultPermissions = [
        // User Management
        ['users.view', 'عرض المستخدمين', 'View Users', 'عرض قائمة المستخدمين', 'View users list', 'users'],
        ['users.create', 'إنشاء مستخدم', 'Create User', 'إنشاء مستخدم جديد', 'Create new user', 'users'],
        ['users.edit', 'تعديل المستخدمين', 'Edit Users', 'تعديل بيانات المستخدمين', 'Edit user data', 'users'],
        ['users.delete', 'حذف المستخدمين', 'Delete Users', 'حذف المستخدمين', 'Delete users', 'users'],
        ['users.manage_roles', 'إدارة أدوار المستخدمين', 'Manage User Roles', 'تعيين وإزالة أدوار المستخدمين', 'Assign and remove user roles', 'users'],

        // Role Management
        ['roles.view', 'عرض الأدوار', 'View Roles', 'عرض قائمة الأدوار', 'View roles list', 'roles'],
        ['roles.create', 'إنشاء دور', 'Create Role', 'إنشاء دور جديد', 'Create new role', 'roles'],
        ['roles.edit', 'تعديل الأدوار', 'Edit Roles', 'تعديل الأدوار الموجودة', 'Edit existing roles', 'roles'],
        ['roles.delete', 'حذف الأدوار', 'Delete Roles', 'حذف الأدوار', 'Delete roles', 'roles'],
        ['roles.manage_permissions', 'إدارة صلاحيات الأدوار', 'Manage Role Permissions', 'تعيين صلاحيات للأدوار', 'Assign permissions to roles', 'roles'],

        // Store Management
        ['store.view', 'عرض إعدادات المتجر', 'View Store Settings', 'عرض إعدادات المتجر', 'View store settings', 'store'],
        ['store.edit', 'تعديل إعدادات المتجر', 'Edit Store Settings', 'تعديل إعدادات المتجر', 'Edit store settings', 'store'],
        ['store.manage_categories', 'إدارة الفئات', 'Manage Categories', 'إدارة فئات المنتجات', 'Manage product categories', 'store'],

        // Content Management
        ['content.view', 'عرض المحتوى', 'View Content', 'عرض المحتوى والمقالات', 'View content and articles', 'content'],
        ['content.create', 'إنشاء محتوى', 'Create Content', 'إنشاء محتوى جديد', 'Create new content', 'content'],
        ['content.edit', 'تعديل المحتوى', 'Edit Content', 'تعديل المحتوى الموجود', 'Edit existing content', 'content'],
        ['content.delete', 'حذف المحتوى', 'Delete Content', 'حذف المحتوى', 'Delete content', 'content'],
        ['content.publish', 'نشر المحتوى', 'Publish Content', 'نشر وإلغاء نشر المحتوى', 'Publish and unpublish content', 'content'],

        // System Administration
        ['system.admin', 'إدارة النظام', 'System Administration', 'إدارة كاملة للنظام', 'Full system administration', 'system', 1],
        ['system.settings', 'إعدادات النظام', 'System Settings', 'تعديل إعدادات النظام', 'Modify system settings', 'system'],
        ['system.logs', 'عرض السجلات', 'View Logs', 'عرض سجلات النظام', 'View system logs', 'system'],
        ['system.backup', 'النسخ الاحتياطي', 'System Backup', 'إنشاء واستعادة النسخ الاحتياطية', 'Create and restore backups', 'system'],
    ];

    $insertPermissionStmt = $pdo->prepare("
        INSERT INTO permissions
        (name, display_name_ar, display_name_en, description_ar, description_en, category, is_system)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");

    $insertedPermissions = 0;
    foreach ($defaultPermissions as $permission) {
        try {
            $isSystem = isset($permission[6]) ? $permission[6] : 0;
            $insertPermissionStmt->execute([
                $permission[0],
                $permission[1],
                $permission[2],
                $permission[3],
                $permission[4],
                $permission[5],
                $isSystem
            ]);
            echo "<div class='success'>✅ {$permission[1]}</div>";
            $insertedPermissions++;
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في إدراج {$permission[1]}: " . $e->getMessage() . "</div>";
        }
    }

    echo "<div class='success'>✅ تم إدراج $insertedPermissions صلاحية من أصل " . count($defaultPermissions) . "</div>";

    // Insert default roles
    echo "<h2>إدراج الأدوار الافتراضية:</h2>";

    $defaultRoles = [
        ['super_admin', 'مدير عام', 'Super Administrator', 'مدير عام بصلاحيات كاملة', 'Super administrator with full permissions', '#dc3545', 'fas fa-crown', 1, 1, 1],
        ['admin', 'مدير', 'Administrator', 'مدير بصلاحيات إدارية', 'Administrator with management permissions', '#667eea', 'fas fa-user-shield', 0, 1, 2],
        ['editor', 'محرر', 'Editor', 'محرر محتوى', 'Content editor', '#28a745', 'fas fa-edit', 0, 1, 3],
        ['moderator', 'مشرف', 'Moderator', 'مشرف على المحتوى', 'Content moderator', '#ffc107', 'fas fa-user-check', 0, 1, 4],
        ['customer', 'عميل', 'Customer', 'عميل عادي', 'Regular customer', '#17a2b8', 'fas fa-user', 0, 1, 5],
    ];

    $insertRoleStmt = $pdo->prepare("
        INSERT INTO roles
        (name, display_name_ar, display_name_en, description_ar, description_en, color, icon, is_system, is_active, sort_order)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $insertedRoles = 0;
    foreach ($defaultRoles as $role) {
        try {
            $insertRoleStmt->execute($role);
            echo "<div class='success'>✅ {$role[1]}</div>";
            $insertedRoles++;
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في إدراج {$role[1]}: " . $e->getMessage() . "</div>";
        }
    }

    echo "<div class='success'>✅ تم إدراج $insertedRoles دور من أصل " . count($defaultRoles) . "</div>";

    // Create default admin user and sample users
    echo "<h2>إنشاء المستخدمين الافتراضيين:</h2>";

    $defaultUsers = [
        ['admin', '<EMAIL>', 'admin123', 'مدير', 'النظام', 'مدير النظام', 1, 1, 'super_admin'],
        ['editor1', '<EMAIL>', 'editor123', 'محمد', 'أحمد', 'محمد أحمد', 1, 1, 'editor'],
        ['moderator1', '<EMAIL>', 'mod123', 'فاطمة', 'علي', 'فاطمة علي', 1, 1, 'moderator'],
        ['customer1', '<EMAIL>', 'customer123', 'عبدالله', 'محمد', 'عبدالله محمد', 1, 1, 'customer'],
        ['customer2', '<EMAIL>', 'customer123', 'نورا', 'سالم', 'نورا سالم', 1, 0, 'customer'],
    ];

    $insertUserStmt = $pdo->prepare("
        INSERT INTO users
        (username, email, password_hash, first_name, last_name, full_name, is_active, is_verified, email_verified_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $userIds = [];
    foreach ($defaultUsers as $userData) {
        try {
            $passwordHash = password_hash($userData[2], PASSWORD_DEFAULT);
            $emailVerifiedAt = $userData[7] ? date('Y-m-d H:i:s') : null;

            $insertUserStmt->execute([
                $userData[0], // username
                $userData[1], // email
                $passwordHash, // password_hash
                $userData[3], // first_name
                $userData[4], // last_name
                $userData[5], // full_name
                $userData[6], // is_active
                $userData[7], // is_verified
                $emailVerifiedAt // email_verified_at
            ]);

            $userId = $pdo->lastInsertId();
            $userIds[$userData[0]] = ['id' => $userId, 'role' => $userData[8]];
            echo "<div class='success'>✅ تم إنشاء مستخدم {$userData[5]} (ID: $userId)</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في إنشاء مستخدم {$userData[5]}: " . $e->getMessage() . "</div>";
        }
    }

    // Assign roles to users
    echo "<h3>تعيين الأدوار للمستخدمين:</h3>";

    $getRoleStmt = $pdo->prepare("SELECT id FROM roles WHERE name = ?");
    $assignRoleStmt = $pdo->prepare("
        INSERT INTO user_roles (user_id, role_id, assigned_by)
        VALUES (?, ?, ?)
    ");

    foreach ($userIds as $username => $userInfo) {
        try {
            $getRoleStmt->execute([$userInfo['role']]);
            $role = $getRoleStmt->fetch();

            if ($role) {
                $assignRoleStmt->execute([$userInfo['id'], $role['id'], $userIds['admin']['id']]);
                echo "<div class='success'>✅ تم تعيين دور {$userInfo['role']} للمستخدم $username</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في تعيين دور للمستخدم $username: " . $e->getMessage() . "</div>";
        }
    }

    // Assign all permissions to super_admin role
    echo "<h2>تعيين الصلاحيات للأدوار:</h2>";

    $superAdminRoleStmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'super_admin'");
    $superAdminRoleStmt->execute();
    $superAdminRole = $superAdminRoleStmt->fetch();

    if ($superAdminRole) {
        $allPermissionsStmt = $pdo->query("SELECT id FROM permissions");
        $assignPermissionStmt = $pdo->prepare("
            INSERT INTO role_permissions (role_id, permission_id, granted_by)
            VALUES (?, ?, ?)
        ");

        $assignedPermissions = 0;
        while ($permission = $allPermissionsStmt->fetch()) {
            try {
                $assignPermissionStmt->execute([$superAdminRole['id'], $permission['id'], $adminUserId]);
                $assignedPermissions++;
            } catch (Exception $e) {
                // Permission already assigned, skip
            }
        }

        echo "<div class='success'>✅ تم تعيين $assignedPermissions صلاحية لدور المدير العام</div>";
    }

    // Final verification
    echo "<h2>التحقق من النتائج النهائية:</h2>";

    $tables = ['permissions', 'roles', 'users', 'role_permissions', 'user_roles', 'user_permissions', 'user_sessions'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $count = $stmt->fetch()['count'];
        echo "<div class='success'>✅ جدول $table: $count سجل</div>";
    }

    // Show sample data
    echo "<h2>عينة من البيانات المدرجة:</h2>";

    echo "<h3>المستخدمين:</h3>";
    $usersStmt = $pdo->query("
        SELECT u.id, u.username, u.email, u.full_name, u.is_active, u.created_at,
               GROUP_CONCAT(r.display_name_ar SEPARATOR ', ') as roles
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id
        GROUP BY u.id
        ORDER BY u.id
    ");

    echo "<table>
            <tr>
                <th>ID</th>
                <th>اسم المستخدم</th>
                <th>البريد الإلكتروني</th>
                <th>الاسم الكامل</th>
                <th>الأدوار</th>
                <th>نشط</th>
            </tr>";

    while ($user = $usersStmt->fetch()) {
        $active = $user['is_active'] ? '✅' : '❌';
        echo "<tr>
                <td>{$user['id']}</td>
                <td>{$user['username']}</td>
                <td>{$user['email']}</td>
                <td>{$user['full_name']}</td>
                <td>{$user['roles']}</td>
                <td>$active</td>
              </tr>";
    }
    echo "</table>";

    echo "<h3>الأدوار والصلاحيات:</h3>";
    $rolesStmt = $pdo->query("
        SELECT r.id, r.display_name_ar, r.color, r.icon,
               COUNT(rp.permission_id) as permissions_count,
               COUNT(ur.user_id) as users_count
        FROM roles r
        LEFT JOIN role_permissions rp ON r.id = rp.role_id
        LEFT JOIN user_roles ur ON r.id = ur.role_id
        GROUP BY r.id
        ORDER BY r.sort_order
    ");

    echo "<table>
            <tr>
                <th>ID</th>
                <th>الدور</th>
                <th>اللون</th>
                <th>الأيقونة</th>
                <th>عدد الصلاحيات</th>
                <th>عدد المستخدمين</th>
            </tr>";

    while ($role = $rolesStmt->fetch()) {
        echo "<tr>
                <td>{$role['id']}</td>
                <td style='color: {$role['color']};'><i class='{$role['icon']}'></i> {$role['display_name_ar']}</td>
                <td><span style='background: {$role['color']}; color: white; padding: 2px 6px; border-radius: 3px;'>{$role['color']}</span></td>
                <td><i class='{$role['icon']}'></i></td>
                <td>{$role['permissions_count']}</td>
                <td>{$role['users_count']}</td>
              </tr>";
    }
    echo "</table>";

    echo "<div class='success'><h2>🎉 تم إنشاء جداول إدارة المستخدمين بنجاح!</h2></div>";
    echo "<p>تم إنشاء " . count($tables) . " جداول مع البيانات الافتراضية.</p>";
    echo "<p><strong>بيانات تسجيل الدخول للمدير:</strong></p>";
    echo "<ul>
            <li><strong>اسم المستخدم:</strong> admin</li>
            <li><strong>كلمة المرور:</strong> admin123</li>
            <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
          </ul>";
} catch (Exception $e) {
    echo "<div class='error'><h2>❌ خطأ في إنشاء الجداول:</h2>";
    echo "<p>" . $e->getMessage() . "</p></div>";
}

echo "
        <a href='../index.html' class='back-link'>← العودة إلى لوحة الإدارة</a>
        <a href='../php/users_management.php?action=get_all' class='back-link' style='background: #28a745;'>🔍 اختبار API</a>
    </div>
</body>
</html>";
