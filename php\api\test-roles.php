<?php

/**
 * Simple Roles Test API
 * Tests roles table and returns basic role data
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Error handling
ini_set('display_errors', 0); // Don't display errors in JSON response
error_reporting(E_ALL);

try {
    // Load environment configuration
    require_once __DIR__ . '/../config/env-loader.php';

    // Create PDO connection using .env settings
    $pdo = EnvLoader::createDatabaseConnection();

    // Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() == 0) {
        // Table doesn't exist
        $response = [
            'success' => false,
            'message' => 'Roles table does not exist',
            'roles' => [],
            'total' => 0,
            'table_exists' => false,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        http_response_code(404);
        echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }

    // Get roles data
    $stmt = $pdo->query("
        SELECT
            id,
            name,
            display_name,
            display_name_ar,
            description,
            is_active,
            created_at
        FROM roles
        ORDER BY id ASC
    ");

    $roles = $stmt->fetchAll();

    // Format roles data
    $formattedRoles = array_map(function ($role) {
        return [
            'id' => (int)$role['id'],
            'name' => $role['name'],
            'display_name' => $role['display_name'] ?? $role['name'],
            'display_name_ar' => $role['display_name_ar'] ?? $role['name'],
            'description' => $role['description'] ?? '',
            'is_active' => (bool)($role['is_active'] ?? 1),
            'created_at' => $role['created_at'] ?? null
        ];
    }, $roles);

    // Success response
    $response = [
        'success' => true,
        'message' => 'Roles retrieved successfully',
        'roles' => $formattedRoles,
        'total' => count($formattedRoles),
        'table_exists' => true,
        'timestamp' => date('Y-m-d H:i:s')
    ];

    http_response_code(200);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} catch (PDOException $e) {
    // Database connection error
    $response = [
        'success' => false,
        'message' => 'Database connection failed',
        'error' => $e->getMessage(),
        'roles' => [],
        'total' => 0,
        'timestamp' => date('Y-m-d H:i:s')
    ];

    http_response_code(500);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} catch (Exception $e) {
    // General error
    $response = [
        'success' => false,
        'message' => 'An error occurred',
        'error' => $e->getMessage(),
        'roles' => [],
        'total' => 0,
        'timestamp' => date('Y-m-d H:i:s')
    ];

    http_response_code(500);
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
