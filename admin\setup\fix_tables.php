<?php
/**
 * Fix Tables Script
 * سكريپت إصلاح الجداول
 */

echo "<h2>إصلاح جداول الإعدادات العامة</h2>\n";

// Load configuration manually
$envFile = '../../.env';
if (!file_exists($envFile)) {
    die("ملف .env غير موجود في: $envFile");
}

// Load configuration
$config = [];
$lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach ($lines as $line) {
    if (strpos(trim($line), '#') === 0) {
        continue;
    }
    list($key, $value) = explode('=', $line, 2) + [NULL, NULL];
    if (!empty($key)) {
        $config[trim($key)] = trim($value ?? '');
    }
}

// Check required database settings
$required = ['DB_HOST', 'DB_PORT', 'DB_USERNAME', 'DB_DATABASE'];
$missing = [];
foreach ($required as $key) {
    if (empty($config[$key])) {
        $missing[] = $key;
    }
}

if (!empty($missing)) {
    die('إعدادات قاعدة البيانات المفقودة: ' . implode(', ', $missing));
}

try {
    // Connect to database
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $config['DB_HOST'],
        $config['DB_PORT'],
        $config['DB_DATABASE']
    );
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, $config['DB_USERNAME'], $config['DB_PASSWORD'] ?? '', $options);
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    
    // Drop existing tables if they exist
    echo "<h3>حذف الجداول الموجودة (إن وجدت):</h3>\n";
    
    $dropTables = [
        "DROP TABLE IF EXISTS `settings_history`",
        "DROP TABLE IF EXISTS `general_settings`"
    ];
    
    foreach ($dropTables as $dropSQL) {
        $pdo->exec($dropSQL);
        echo "<p style='color: orange;'>🗑️ تم حذف الجدول</p>\n";
    }
    
    // Create general_settings table with correct structure
    echo "<h3>إنشاء جدول general_settings الجديد:</h3>\n";
    $createGeneralSettings = "
    CREATE TABLE `general_settings` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `setting_key` varchar(100) NOT NULL,
      `setting_value` text DEFAULT NULL,
      `setting_type` varchar(20) DEFAULT 'text',
      `setting_group` varchar(50) DEFAULT 'general',
      `setting_label_ar` varchar(255) DEFAULT NULL,
      `setting_label_en` varchar(255) DEFAULT NULL,
      `setting_description_ar` text DEFAULT NULL,
      `setting_description_en` text DEFAULT NULL,
      `is_required` tinyint(1) DEFAULT 0,
      `sort_order` int(11) DEFAULT 0,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `setting_key` (`setting_key`),
      KEY `idx_setting_group` (`setting_group`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createGeneralSettings);
    echo "<p style='color: green;'>✅ تم إنشاء جدول general_settings بنجاح</p>\n";
    
    // Create settings_history table
    echo "<h3>إنشاء جدول settings_history:</h3>\n";
    $createSettingsHistory = "
    CREATE TABLE `settings_history` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `setting_key` varchar(100) NOT NULL,
      `old_value` text DEFAULT NULL,
      `new_value` text DEFAULT NULL,
      `changed_by` int(11) DEFAULT NULL,
      `changed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `ip_address` varchar(45) DEFAULT NULL,
      `user_agent` text DEFAULT NULL,
      PRIMARY KEY (`id`),
      KEY `idx_setting_key` (`setting_key`),
      KEY `idx_changed_by` (`changed_by`),
      KEY `idx_changed_at` (`changed_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createSettingsHistory);
    echo "<p style='color: green;'>✅ تم إنشاء جدول settings_history بنجاح</p>\n";
    
    // Insert default settings
    echo "<h3>إدراج الإعدادات الافتراضية:</h3>\n";
    
    $defaultSettings = [
        // Site settings
        ['site_name', 'متجر الكتب الإلكترونية', 'text', 'site', 'اسم الموقع', 'Site Name', 'اسم الموقع الذي يظهر في العنوان والرأس', 'Site name displayed in title and header', 1, 1],
        ['site_description', 'أفضل متجر للكتب الإلكترونية والمنتجات الرقمية', 'textarea', 'site', 'وصف الموقع', 'Site Description', 'وصف مختصر للموقع يظهر في محركات البحث', 'Brief site description for search engines', 1, 2],
        ['site_keywords', 'كتب إلكترونية, منتجات رقمية, تسوق أونلاين', 'textarea', 'site', 'الكلمات المفتاحية', 'Site Keywords', 'الكلمات المفتاحية للموقع مفصولة بفواصل', 'Site keywords separated by commas', 0, 3],
        ['site_logo', '', 'file', 'site', 'شعار الموقع', 'Site Logo', 'شعار الموقع (PNG, JPG, SVG)', 'Site logo (PNG, JPG, SVG)', 0, 4],
        ['site_favicon', '', 'file', 'site', 'أيقونة الموقع', 'Site Favicon', 'أيقونة الموقع الصغيرة (ICO, PNG)', 'Site favicon (ICO, PNG)', 0, 5],
        ['site_url', 'http://localhost:8000', 'url', 'site', 'رابط الموقع', 'Site URL', 'الرابط الأساسي للموقع', 'Main site URL', 1, 6],
        
        // Localization settings
        ['default_language', 'ar', 'select', 'localization', 'اللغة الافتراضية', 'Default Language', 'اللغة الافتراضية للموقع', 'Default site language', 1, 10],
        ['timezone', 'Africa/Algiers', 'select', 'localization', 'المنطقة الزمنية', 'Timezone', 'المنطقة الزمنية للموقع', 'Site timezone', 1, 11],
        ['date_format', 'Y-m-d', 'select', 'localization', 'تنسيق التاريخ', 'Date Format', 'تنسيق عرض التاريخ', 'Date display format', 1, 12],
        ['currency', 'DZD', 'select', 'localization', 'العملة الافتراضية', 'Default Currency', 'العملة المستخدمة في الموقع', 'Site default currency', 1, 13],
        ['currency_symbol', 'دج', 'text', 'localization', 'رمز العملة', 'Currency Symbol', 'رمز العملة المعروض', 'Displayed currency symbol', 1, 14],
        
        // Email settings
        ['admin_email', '<EMAIL>', 'email', 'email', 'بريد المدير', 'Admin Email', 'البريد الإلكتروني للمدير', 'Administrator email address', 1, 20],
        ['smtp_host', '', 'text', 'email', 'خادم SMTP', 'SMTP Host', 'عنوان خادم البريد الإلكتروني', 'Email server host address', 0, 21],
        ['smtp_port', '587', 'number', 'email', 'منفذ SMTP', 'SMTP Port', 'منفذ خادم البريد الإلكتروني', 'Email server port', 0, 22],
        ['smtp_username', '', 'text', 'email', 'اسم مستخدم SMTP', 'SMTP Username', 'اسم المستخدم لخادم البريد', 'Email server username', 0, 23],
        ['smtp_password', '', 'text', 'email', 'كلمة مرور SMTP', 'SMTP Password', 'كلمة المرور لخادم البريد', 'Email server password', 0, 24],
        ['smtp_encryption', 'tls', 'select', 'email', 'تشفير SMTP', 'SMTP Encryption', 'نوع التشفير المستخدم', 'Encryption type used', 0, 25],
        
        // Notification settings
        ['enable_notifications', '1', 'boolean', 'notifications', 'تفعيل الإشعارات', 'Enable Notifications', 'تفعيل نظام الإشعارات', 'Enable notification system', 0, 30],
        ['email_notifications', '1', 'boolean', 'notifications', 'إشعارات البريد', 'Email Notifications', 'إرسال الإشعارات عبر البريد', 'Send notifications via email', 0, 31],
        ['sms_notifications', '0', 'boolean', 'notifications', 'إشعارات SMS', 'SMS Notifications', 'إرسال الإشعارات عبر الرسائل النصية', 'Send notifications via SMS', 0, 32],
        
        // Security settings
        ['maintenance_mode', '0', 'boolean', 'security', 'وضع الصيانة', 'Maintenance Mode', 'تفعيل وضع الصيانة للموقع', 'Enable site maintenance mode', 0, 40],
        ['registration_enabled', '1', 'boolean', 'security', 'السماح بالتسجيل', 'Registration Enabled', 'السماح للمستخدمين الجدد بالتسجيل', 'Allow new user registration', 0, 41],
        ['email_verification', '1', 'boolean', 'security', 'تأكيد البريد', 'Email Verification', 'طلب تأكيد البريد الإلكتروني', 'Require email verification', 0, 42],
        
        // Performance settings
        ['cache_enabled', '1', 'boolean', 'performance', 'تفعيل التخزين المؤقت', 'Cache Enabled', 'تفعيل نظام التخزين المؤقت', 'Enable caching system', 0, 50],
        ['compress_output', '1', 'boolean', 'performance', 'ضغط المخرجات', 'Compress Output', 'ضغط محتوى الصفحات', 'Compress page output', 0, 51],
        ['minify_css', '1', 'boolean', 'performance', 'ضغط CSS', 'Minify CSS', 'ضغط ملفات الأنماط', 'Minify CSS files', 0, 52],
        ['minify_js', '1', 'boolean', 'performance', 'ضغط JavaScript', 'Minify JavaScript', 'ضغط ملفات JavaScript', 'Minify JavaScript files', 0, 53]
    ];
    
    $insertStmt = $pdo->prepare("
        INSERT INTO general_settings 
        (setting_key, setting_value, setting_type, setting_group, setting_label_ar, setting_label_en, setting_description_ar, setting_description_en, is_required, sort_order) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $insertedCount = 0;
    foreach ($defaultSettings as $setting) {
        try {
            $insertStmt->execute($setting);
            $insertedCount++;
            echo "<p style='color: green; margin: 2px 0;'>✅ " . $setting[3] . " - " . $setting[4] . "</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: red; margin: 2px 0;'>❌ خطأ في إدراج " . $setting[4] . ": " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم إدراج $insertedCount إعداد افتراضي من أصل " . count($defaultSettings) . "</p>\n";
    
    // Verify tables and data
    echo "<h3>التحقق من النتائج النهائية:</h3>\n";
    
    $tables = ['general_settings', 'settings_history'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
        $count = $stmt->fetchColumn();
        echo "<p style='color: green;'>✅ جدول $table: $count سجل</p>\n";
    }
    
    // Show sample settings
    echo "<h3>عينة من الإعدادات المدرجة:</h3>\n";
    $stmt = $pdo->query("SELECT setting_key, setting_label_ar, setting_value, setting_group FROM general_settings ORDER BY setting_group, sort_order LIMIT 10");
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($settings) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f0f0f0;'><th>المجموعة</th><th>المفتاح</th><th>التسمية</th><th>القيمة</th></tr>\n";
        foreach ($settings as $setting) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($setting['setting_group']) . "</td>";
            echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
            echo "<td>" . htmlspecialchars($setting['setting_label_ar']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($setting['setting_value'], 0, 30)) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<div style='color: green; font-weight: bold; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "🎉 تم إصلاح وإنشاء جداول الإعدادات العامة بنجاح!<br>";
    echo "يمكنك الآن استخدام قسم الإعدادات العامة في لوحة الإدارة بدون مشاكل.";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; margin: 20px 0; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ خطأ في إصلاح الجداول: " . $e->getMessage();
    echo "</div>\n";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح جداول الإعدادات العامة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
            margin-top: 25px;
        }
        p {
            margin: 8px 0;
        }
        table {
            font-size: 0.9em;
        }
        th, td {
            padding: 8px 12px;
            text-align: right;
        }
        th {
            font-weight: bold;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .back-link:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="../index.html" class="back-link">← العودة إلى لوحة الإدارة</a>
        <a href="test_database_connection.php" class="back-link" style="background: #28a745;">🔍 اختبار الاتصال</a>
        <a href="../php/general_settings.php?action=get_all" class="back-link" style="background: #17a2b8;" target="_blank">📊 اختبار API</a>
    </div>
</body>
</html>
