/**
 * Admin Settings Layout Fix
 * إصلاح تخطيط قائمة إعدادات الإدارة لتظهر الروابط أسفل الرأس
 */

/* إعادة تعيين التخطيط الأساسي */
.admin-settings-menu {
    margin: 8px 15px !important;
    padding: 0 !important;
    background: none !important;
    border-radius: 12px !important;
    overflow: visible !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: block !important;
    width: calc(100% - 30px) !important;
    position: relative !important;
}

/* رأس القائمة */
.admin-settings-header {
    padding: 16px 20px !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    border-radius: 12px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%) !important;
    color: rgba(255, 255, 255, 0.95) !important;
    position: relative !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* محتوى الرأس */
.admin-settings-header-content {
    display: flex !important;
    align-items: center !important;
    flex: 1 !important;
}

.admin-settings-header-content i {
    font-size: 1.2rem !important;
    width: 28px !important;
    margin-left: 12px !important;
    flex-shrink: 0 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.admin-settings-header-content span {
    font-size: 1rem !important;
    font-weight: 600 !important;
    text-align: right !important;
    margin-right: 12px !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    letter-spacing: 0.5px !important;
    flex: 1 !important;
}

/* سهم التوسع */
.admin-settings-arrow {
    font-size: 0.9rem !important;
    width: 20px !important;
    margin-left: 0 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    flex-shrink: 0 !important;
    opacity: 0.8 !important;
}

.admin-settings-menu.expanded .admin-settings-arrow {
    transform: rotate(180deg) !important;
    opacity: 1 !important;
}

.admin-settings-menu.expanded .admin-settings-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* القائمة الفرعية */
.admin-settings-submenu {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.15) 100%) !important;
    border-radius: 0 0 12px 12px !important;
    backdrop-filter: blur(5px) !important;
    -webkit-backdrop-filter: blur(5px) !important;
    width: 100% !important;
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    display: block !important;
    box-sizing: border-box !important;
}

.admin-settings-menu.expanded .admin-settings-submenu {
    max-height: 400px !important;
    padding: 12px 0 16px 0 !important;
}

/* عناصر القائمة الفرعية */
.admin-settings-submenu li {
    margin: 4px 12px !important;
    padding: 14px 18px !important;
    cursor: pointer !important;
    border-radius: 10px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 0.9rem !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(3px) !important;
    -webkit-backdrop-filter: blur(3px) !important;
    width: calc(100% - 24px) !important;
    box-sizing: border-box !important;
}

.admin-settings-submenu li:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    color: #ffffff !important;
    transform: translateX(-4px) translateY(-1px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

.admin-settings-submenu li.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.12) 100%) !important;
    color: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: translateX(-2px) !important;
}

.admin-settings-submenu li.active::after {
    content: '' !important;
    position: absolute !important;
    right: -1px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 4px !important;
    height: 24px !important;
    background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%) !important;
    border-radius: 2px 0 0 2px !important;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3) !important;
}

.admin-settings-submenu li i {
    font-size: 1.1rem !important;
    width: 24px !important;
    margin-left: 12px !important;
    flex-shrink: 0 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    opacity: 0.9 !important;
}

.admin-settings-submenu li:hover i,
.admin-settings-submenu li.active i {
    opacity: 1 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
}

.admin-settings-submenu li span {
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    flex: 1 !important;
    text-align: right !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    letter-spacing: 0.3px !important;
}

/* تأثيرات الحوم للرأس */
.admin-settings-header:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%) !important;
    color: #ffffff !important;
    transform: translateX(-3px) !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
}

/* الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .admin-settings-menu {
        margin: 6px 10px !important;
        width: calc(100% - 20px) !important;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2) !important;
    }
    
    .admin-settings-header {
        padding: 14px 16px !important;
        font-size: 0.9rem !important;
    }
    
    .admin-settings-header-content i {
        font-size: 1.1rem !important;
        width: 24px !important;
        margin-left: 10px !important;
    }
    
    .admin-settings-header-content span {
        font-size: 0.9rem !important;
        font-weight: 500 !important;
    }
    
    .admin-settings-submenu {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.2) 100%) !important;
    }
    
    .admin-settings-submenu li {
        margin: 3px 8px !important;
        padding: 12px 14px !important;
        font-size: 0.85rem !important;
    }
    
    .admin-settings-submenu li i {
        font-size: 1rem !important;
        width: 20px !important;
        margin-left: 10px !important;
    }
    
    .admin-settings-submenu li span {
        font-size: 0.85rem !important;
    }
    
    .admin-settings-submenu li:hover {
        transform: translateX(-2px) translateY(-1px) !important;
    }
}

/* إصلاح التداخل مع عناصر أخرى */
.admin-nav ul li.admin-settings-menu {
    position: relative !important;
    z-index: 10 !important;
}

/* إصلاح العرض في الشريط الجانبي */
.sidebar .admin-settings-menu {
    width: calc(100% - 30px) !important;
}

/* تحسين الظهور */
.admin-settings-menu * {
    box-sizing: border-box !important;
}

/* إصلاح التخطيط العام */
.admin-nav ul {
    display: flex !important;
    flex-direction: column !important;
}

.admin-nav ul li {
    width: 100% !important;
    display: block !important;
}

.admin-nav ul li.admin-settings-menu {
    width: 100% !important;
    display: block !important;
    position: relative !important;
}
