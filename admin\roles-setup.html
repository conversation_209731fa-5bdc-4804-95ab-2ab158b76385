<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 إعداد جدول الأدوار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .setup-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .result-item {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid;
        }
        
        .result-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .result-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1 class="text-center mb-4">🔧 إعداد جدول الأدوار</h1>
        
        <div class="text-center mb-4">
            <button id="setup-roles" class="btn btn-primary btn-lg">🚀 إعداد جدول الأدوار</button>
            <button id="test-roles" class="btn btn-success btn-lg ms-2">🧪 اختبار الأدوار</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        // Setup roles table
        async function setupRoles() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner-border text-primary" role="status"></div><p>جاري إعداد جدول الأدوار...</p></div>';
            
            try {
                // Create roles table using direct SQL execution
                const setupData = {
                    action: 'setup_roles',
                    sql_commands: [
                        `CREATE TABLE IF NOT EXISTS roles (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            name VARCHAR(50) UNIQUE NOT NULL,
                            display_name VARCHAR(100) NOT NULL DEFAULT '',
                            display_name_ar VARCHAR(100) NOT NULL DEFAULT '',
                            description TEXT DEFAULT NULL,
                            permissions JSON DEFAULT NULL,
                            is_active TINYINT(1) DEFAULT 1,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,
                        
                        `INSERT IGNORE INTO roles (name, display_name, display_name_ar, description) VALUES
                        ('admin', 'Administrator', 'مدير النظام', 'Full system access'),
                        ('seller', 'Seller', 'بائع', 'Can manage own products and orders'),
                        ('user', 'User', 'مستخدم', 'Basic user access'),
                        ('moderator', 'Moderator', 'مشرف', 'Can moderate content'),
                        ('editor', 'Editor', 'محرر', 'Can edit content')`
                    ]
                };
                
                const response = await fetch('../php/api/database-setup.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(setupData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                displayResults(data, 'إعداد جدول الأدوار');
                
            } catch (error) {
                displayError('فشل في إعداد جدول الأدوار: ' + error.message);
            }
        }
        
        // Test roles functionality
        async function testRoles() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner-border text-success" role="status"></div><p>جاري اختبار الأدوار...</p></div>';
            
            const tests = [
                {
                    name: 'اختبار الاتصال بقاعدة البيانات',
                    url: '../php/api/database-status.php'
                },
                {
                    name: 'اختبار API الأدوار',
                    url: '../php/api/roles.php'
                },
                {
                    name: 'اختبار جدول الأدوار',
                    url: '../php/api/test-simple.php'
                }
            ];
            
            let results = [];
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    const data = await response.json();
                    
                    results.push({
                        name: test.name,
                        success: response.ok && (data.success !== false),
                        message: data.message || `HTTP ${response.status}`,
                        data: data
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        success: false,
                        message: error.message,
                        data: null
                    });
                }
            }
            
            displayTestResults(results);
        }
        
        // Display results
        function displayResults(data, title) {
            const resultsDiv = document.getElementById('results');
            let html = `<h4>${title}</h4>`;
            
            if (data.success) {
                html += `<div class="result-item result-success">
                    <h6>✅ نجح الإعداد</h6>
                    <p>${data.message}</p>
                </div>`;
                
                if (data.fixes && data.fixes.length > 0) {
                    data.fixes.forEach(fix => {
                        html += `<div class="result-item result-success">
                            <p>✅ ${fix}</p>
                        </div>`;
                    });
                }
            } else {
                html += `<div class="result-item result-error">
                    <h6>❌ فشل الإعداد</h6>
                    <p>${data.message}</p>
                </div>`;
                
                if (data.errors && data.errors.length > 0) {
                    data.errors.forEach(error => {
                        html += `<div class="result-item result-error">
                            <p>❌ ${error}</p>
                        </div>`;
                    });
                }
            }
            
            resultsDiv.innerHTML = html;
        }
        
        // Display test results
        function displayTestResults(results) {
            const resultsDiv = document.getElementById('results');
            let html = '<h4>نتائج الاختبارات</h4>';
            
            let successCount = 0;
            
            results.forEach(result => {
                const cssClass = result.success ? 'result-success' : 'result-error';
                const icon = result.success ? '✅' : '❌';
                
                if (result.success) successCount++;
                
                html += `<div class="result-item ${cssClass}">
                    <h6>${icon} ${result.name}</h6>
                    <p>${result.message}</p>
                </div>`;
            });
            
            // Summary
            const successRate = Math.round((successCount / results.length) * 100);
            const summaryClass = successRate >= 75 ? 'result-success' : successRate >= 50 ? 'result-warning' : 'result-error';
            
            html = `<div class="result-item ${summaryClass}">
                <h5>📊 ملخص النتائج</h5>
                <p>معدل النجاح: ${successRate}% (${successCount}/${results.length})</p>
            </div>` + html;
            
            resultsDiv.innerHTML = html;
        }
        
        // Display error
        function displayError(message) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="result-item result-error">
                <h6>❌ خطأ</h6>
                <p>${message}</p>
            </div>`;
        }
        
        // Event listeners
        document.getElementById('setup-roles').addEventListener('click', setupRoles);
        document.getElementById('test-roles').addEventListener('click', testRoles);
    </script>
</body>
</html>
