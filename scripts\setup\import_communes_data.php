<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🗺️ Importing Communes Data (Safe Method)\n\n";

require_once 'php/config.php';

try {
    // Check current state
    $stmt = $conn->query("SELECT COUNT(*) as count FROM communes");
    $current_communes = $stmt->fetch()['count'];
    echo "Current communes in database: $current_communes\n";
    
    if ($current_communes > 100) {
        echo "✅ Communes already populated. Skipping import.\n";
        exit(0);
    }
    
    echo "📥 Reading algeria_cities.sql file...\n";
    
    // Read the file line by line to avoid memory issues
    $file = fopen('sql/algeria_cities.sql', 'r');
    if (!$file) {
        throw new Exception("Could not open algeria_cities.sql file");
    }
    
    $conn->beginTransaction();
    
    // Prepare the insert statement for communes
    $insert_stmt = $conn->prepare("
        INSERT IGNORE INTO communes (commune_code, commune_name_ar, commune_name_fr, wilaya_code, is_active)
        VALUES (?, ?, ?, ?, 1)
    ");
    
    $imported_count = 0;
    $line_number = 0;
    
    while (($line = fgets($file)) !== false) {
        $line_number++;
        $line = trim($line);
        
        // Skip empty lines and CREATE TABLE statement
        if (empty($line) || strpos($line, 'CREATE TABLE') !== false || strpos($line, 'INSERT INTO algeria_cities') === false) {
            continue;
        }
        
        // Extract data from INSERT statement using regex
        if (preg_match("/INSERT INTO algeria_cities\(.*?\) VALUES \((\d+),'([^']+)','([^']+)','[^']*','[^']*','(\d+)','[^']*','[^']*'\);/u", $line, $matches)) {
            $id = $matches[1];
            $commune_name_ar = $matches[2];
            $commune_name_fr = $matches[3];
            $wilaya_code = str_pad($matches[4], 2, '0', STR_PAD_LEFT); // Ensure 2-digit format
            
            // Generate commune code (wilaya_code + commune_id)
            $commune_code = $wilaya_code . str_pad($id, 3, '0', STR_PAD_LEFT);
            
            try {
                $insert_stmt->execute([$commune_code, $commune_name_ar, $commune_name_fr, $wilaya_code]);
                $imported_count++;
                
                if ($imported_count % 100 == 0) {
                    echo "Imported $imported_count communes...\n";
                }
            } catch (PDOException $e) {
                echo "Warning: Could not import commune $commune_name_ar (line $line_number): " . $e->getMessage() . "\n";
            }
        } else {
            echo "Warning: Could not parse line $line_number: " . substr($line, 0, 100) . "...\n";
        }
    }
    
    fclose($file);
    $conn->commit();
    
    echo "✅ Successfully imported $imported_count communes\n";
    
    // Verify the result
    $stmt = $conn->query("SELECT COUNT(*) as count FROM communes");
    $final_count = $stmt->fetch()['count'];
    echo "🎉 Total communes in database: $final_count\n";
    
    // Test sample queries
    echo "\n🧪 Testing sample queries:\n";
    
    // Test communes for Algiers (16)
    $stmt = $conn->prepare("SELECT commune_name_ar, commune_code FROM communes WHERE wilaya_code = '16' LIMIT 5");
    $stmt->execute();
    $communes = $stmt->fetchAll();
    echo "Sample communes for Algiers (16):\n";
    foreach ($communes as $commune) {
        echo "- {$commune['commune_code']}: {$commune['commune_name_ar']}\n";
    }
    
    // Test communes for Oran (31)
    $stmt = $conn->prepare("SELECT commune_name_ar, commune_code FROM communes WHERE wilaya_code = '31' LIMIT 5");
    $stmt->execute();
    $communes = $stmt->fetchAll();
    echo "\nSample communes for Oran (31):\n";
    foreach ($communes as $commune) {
        echo "- {$commune['commune_code']}: {$commune['commune_name_ar']}\n";
    }
    
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
