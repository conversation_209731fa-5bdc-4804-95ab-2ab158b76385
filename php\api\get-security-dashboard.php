<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Prevent any output before JSON
ob_start();

try {
    // Database connection
    require_once '../config/database.php';
    
    $response = [
        'success' => true,
        'data' => [
            'security_score' => 85,
            'threat_level' => 'low',
            'active_sessions' => 12,
            'failed_logins_today' => 3,
            'blocked_ips' => 2,
            'recent_activities' => [
                [
                    'id' => 1,
                    'type' => 'login',
                    'user' => '<EMAIL>',
                    'ip' => '*************',
                    'timestamp' => date('Y-m-d H:i:s', strtotime('-5 minutes')),
                    'status' => 'success'
                ],
                [
                    'id' => 2,
                    'type' => 'failed_login',
                    'user' => '<EMAIL>',
                    'ip' => '*********',
                    'timestamp' => date('Y-m-d H:i:s', strtotime('-15 minutes')),
                    'status' => 'blocked'
                ],
                [
                    'id' => 3,
                    'type' => 'settings_change',
                    'user' => '<EMAIL>',
                    'ip' => '*************',
                    'timestamp' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                    'status' => 'success'
                ]
            ],
            'security_alerts' => [
                [
                    'id' => 1,
                    'type' => 'warning',
                    'title' => 'تحديث أمني متوفر',
                    'message' => 'يوجد تحديث أمني جديد للنظام',
                    'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours'))
                ]
            ],
            'system_health' => [
                'database' => 'healthy',
                'files' => 'healthy',
                'permissions' => 'warning',
                'ssl' => 'healthy'
            ]
        ],
        'message' => 'Security dashboard data retrieved successfully'
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => 'Failed to retrieve security dashboard data',
        'message' => $e->getMessage(),
        'data' => []
    ];
}

// Clear any output buffer and send JSON
ob_clean();
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;
?>
