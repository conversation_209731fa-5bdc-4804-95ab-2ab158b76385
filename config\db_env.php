<?php
/**
 * Database Configuration from .env file
 * إعدادات قاعدة البيانات من ملف .env
 */

// Function to load .env file
function loadEnv($path) {
    if (!file_exists($path)) {
        return [];
    }
    
    $env = [];
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    foreach ($lines as $line) {
        // <PERSON>p comments
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        // Parse key=value pairs
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            
            // Remove quotes if present
            if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
                (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
                $value = substr($value, 1, -1);
            }
            
            $env[$key] = $value;
        }
    }
    
    return $env;
}

// Load environment variables
$envPath = dirname(__DIR__) . '/.env';
$env = loadEnv($envPath);

// Database configuration from .env
$host = $env['DB_HOST'] ?? 'localhost';
$dbname = $env['DB_DATABASE'] ?? 'mossab-landing-page';
$username = $env['DB_USERNAME'] ?? 'root';
$password = $env['DB_PASSWORD'] ?? '';
$port = $env['DB_PORT'] ?? 3307;
$charset = $env['DB_CHARSET'] ?? 'utf8mb4';

// Create PDO connection
try {
    // First try to connect without database to create it if needed
    $pdo = new PDO("mysql:host=$host;port=$port;charset=$charset", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET $charset COLLATE {$charset}_unicode_ci");
    
    // Now connect to the specific database
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=$charset", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    // Log error
    error_log('Database connection failed: ' . $e->getMessage());
    
    // Return error response for API calls
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Database connection failed: ' . $e->getMessage(),
            'error_code' => 'DB_CONNECTION_ERROR',
            'details' => [
                'host' => $host,
                'port' => $port,
                'database' => $dbname,
                'username' => $username,
                'env_file_exists' => file_exists($envPath),
                'env_loaded' => !empty($env)
            ]
        ]);
        exit;
    }
    
    die('Database connection failed: ' . $e->getMessage());
}
?>
