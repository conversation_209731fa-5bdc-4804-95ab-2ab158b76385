/**
 * FINAL LAYOUT OVERRIDE - MAXIMUM PRIORITY
 * This file should be loaded LAST to ensure all styles are properly overridden
 */

/* CRITICAL: Force layout fix with maximum specificity */
html body .admin-container .main-content,
.admin-container .main-content,
.main-content {
    margin-right: 400px !important;
    padding: 40px !important;
    padding-right: 60px !important;
    width: calc(100% - 400px) !important;
    max-width: calc(100vw - 400px) !important;
    box-sizing: border-box !important;
    position: relative !important;
    left: 0 !important;
    right: auto !important;
    transform: none !important;
    overflow-x: visible !important;
    min-height: 100vh !important;
}

/* CRITICAL: Force sidebar positioning with maximum specificity */
html body .admin-container .sidebar,
.admin-container .sidebar,
.sidebar {
    width: 400px !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    bottom: auto !important;
    left: auto !important;
    height: 100vh !important;
    z-index: 1000 !important;
    box-shadow: -15px 0 40px rgba(0,0,0,0.4) !important;
    transform: none !important;
}

/* Force all content sections to respect the layout */
.content-section,
#dashboard,
#books,
#orders,
#landingPages,
#reportsContent,
#generalSettings,
#paymentSettings,
#categories,
#usersManagement,
#rolesManagement,
#showcaseDemo,
#storeSettings,
#storesManagementContent,
#securitySettings,
#subscriptionsManagement {
    width: 100% !important;
    max-width: none !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
    overflow-x: visible !important;
}

/* Force tables to fit within the available space */
table,
.table-responsive,
[style*="overflow-x: auto"] {
    width: 100% !important;
    max-width: none !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
}

/* Force grid layouts to respect boundaries */
[style*="display: grid"],
[style*="display: flex"],
.dashboard-stats,
.stats-grid {
    width: 100% !important;
    max-width: none !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
}

/* Force headers and titles to fit */
.content-header,
.dashboard-header,
[style*="background: linear-gradient"] {
    width: 100% !important;
    max-width: none !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
}

/* Ensure buttons and controls are visible */
.section-header,
.action-buttons,
[style*="justify-content"] {
    width: 100% !important;
    max-width: none !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
}

/* Mobile responsiveness override */
@media (max-width: 768px) {
    .main-content {
        margin-right: 0 !important;
        padding: 20px !important;
        width: 100% !important;
        max-width: 100vw !important;
    }
    
    .sidebar {
        width: 300px !important;
        right: -300px !important;
        transition: right 0.3s ease !important;
    }
    
    .sidebar.mobile-open {
        right: 0 !important;
    }
}

/* Debug helper - shows layout boundaries */
.debug-layout .main-content {
    border: 3px solid red !important;
    background: rgba(255, 0, 0, 0.1) !important;
}

.debug-layout .sidebar {
    border: 3px solid blue !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%) !important;
}

/* Force visibility for any hidden content */
.content-section.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Prevent any element from extending beyond viewport */
* {
    max-width: 100% !important;
}

/* Final safety net - ensure no horizontal overflow */
.admin-container {
    overflow-x: hidden !important;
}

/* Force proper stacking order */
.main-content {
    z-index: 1 !important;
}

.sidebar {
    z-index: 1000 !important;
}

/* Override any conflicting transforms or positions */
.main-content,
.main-content * {
    transform: none !important;
}

/* Ensure proper box model */
.main-content,
.main-content *,
.sidebar,
.sidebar * {
    box-sizing: border-box !important;
}

/* Force RTL layout consistency */
[dir="rtl"] .main-content {
    direction: rtl !important;
    text-align: right !important;
}

[dir="rtl"] .sidebar {
    direction: rtl !important;
    text-align: right !important;
}

/* Additional safety measures for specific screen sizes */
@media (min-width: 1200px) {
    .main-content {
        margin-right: 400px !important;
        padding-right: 60px !important;
    }
    
    .sidebar {
        width: 400px !important;
    }
}

@media (min-width: 1400px) {
    .main-content {
        margin-right: 400px !important;
        padding-right: 80px !important;
    }
    
    .sidebar {
        width: 400px !important;
    }
}

/* Ensure proper spacing on very large screens */
@media (min-width: 1600px) {
    .main-content {
        margin-right: 400px !important;
        padding-right: 100px !important;
    }
    
    .sidebar {
        width: 400px !important;
    }
}

/* ULTIMATE CONTENT ALIGNMENT FIX */
/* Force all dashboard content to respect the layout boundaries */
.main-content > *,
.content-section > *,
#dashboard > *,
#books > *,
#orders > *,
#landingPages > *,
#reportsContent > *,
#generalSettings > *,
#paymentSettings > *,
#categories > *,
#usersManagement > *,
#rolesManagement > *,
#showcaseDemo > *,
#storeSettings > *,
#storesManagementContent > *,
#securitySettings > *,
#subscriptionsManagement > * {
    max-width: 100% !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
    overflow-x: visible !important;
}

/* Fix for inline styles that might override our layout */
[style*="margin-right"],
[style*="padding-right"],
[style*="width: calc"] {
    margin-right: 0 !important;
    padding-right: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

/* Ensure tables and grids fit properly */
table,
.table-responsive,
[style*="display: grid"],
[style*="display: flex"],
.dashboard-stats,
.stats-grid {
    width: 100% !important;
    max-width: 100% !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
    overflow-x: auto !important;
}
    
.sidebar {
    width: 430px !important;
}

/* Force immediate application */
.layout-override-applied .main-content {
    margin-right: 420px !important;
    padding-right: 140px !important;
    width: calc(100% - 420px) !important;
}

.layout-override-applied .sidebar {
    width: 400px !important;
    position: fixed !important;
    right: 0 !important;
}

/* Critical override for any inline styles */
[style*="margin-right"] .main-content {
    margin-right: 420px !important;
}

[style*="width"] .sidebar {
    width: 400px !important;
}

/* Final nuclear option - override everything */
.main-content[style] {
    margin-right: 420px !important;
    padding-right: 140px !important;
    width: calc(100% - 420px) !important;
    max-width: calc(100vw - 420px) !important;
}

.sidebar[style] {
    width: 400px !important;
    position: fixed !important;
    right: 0 !important;
    top: 0 !important;
}
