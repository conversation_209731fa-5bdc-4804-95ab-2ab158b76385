<?php
/**
 * Users Bulk Operations API
 * API للعمليات المجمعة على المستخدمين
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action']) || !isset($input['users'])) {
        throw new Exception('Invalid input data');
    }
    
    $action = $input['action'];
    $userIds = $input['users'];
    
    if (!is_array($userIds) || empty($userIds)) {
        throw new Exception('No users selected');
    }
    
    // Validate user IDs
    $userIds = array_map('intval', $userIds);
    $userIds = array_filter($userIds, function($id) { return $id > 0; });
    
    if (empty($userIds)) {
        throw new Exception('Invalid user IDs');
    }
    
    $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
    
    switch ($action) {
        case 'activate':
            $stmt = $pdo->prepare("UPDATE users SET status = 'active', updated_at = NOW() WHERE id IN ($placeholders)");
            $stmt->execute($userIds);
            $message = 'تم تفعيل المستخدمين بنجاح';
            break;
            
        case 'deactivate':
            $stmt = $pdo->prepare("UPDATE users SET status = 'inactive', updated_at = NOW() WHERE id IN ($placeholders)");
            $stmt->execute($userIds);
            $message = 'تم إلغاء تفعيل المستخدمين بنجاح';
            break;
            
        case 'suspend':
            $stmt = $pdo->prepare("UPDATE users SET status = 'suspended', updated_at = NOW() WHERE id IN ($placeholders)");
            $stmt->execute($userIds);
            $message = 'تم تعليق المستخدمين بنجاح';
            break;
            
        case 'delete':
            $stmt = $pdo->prepare("DELETE FROM users WHERE id IN ($placeholders)");
            $stmt->execute($userIds);
            $message = 'تم حذف المستخدمين بنجاح';
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    $affectedRows = $stmt->rowCount();
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'affected_rows' => $affectedRows
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
