/**
 * Firefox Compatibility Fixes
 * Handles browser-specific issues for Firefox
 */

class FirefoxCompatibility {
    constructor() {
        this.isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
        this.init();
    }

    init() {
        if (this.isFirefox) {
            console.log('🦊 Firefox detected - applying compatibility fixes');
            this.fixFetchRequests();
            this.fixJSONParsing();
            this.fixCORSHeaders();
        }
    }

    /**
     * Fix fetch requests for Firefox
     */
    fixFetchRequests() {
        const originalFetch = window.fetch;

        window.fetch = function(url, options = {}) {
            // Add Firefox-specific headers
            const firefoxOptions = {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache',
                    'X-Requested-With': 'XMLHttpRequest',
                    ...options.headers
                },
                mode: 'cors',
                credentials: 'same-origin'
            };

            console.log('🦊 Firefox fetch:', url, firefoxOptions);

            return originalFetch(url, firefoxOptions)
                .then(response => {
                    console.log('🦊 Firefox response:', response.status, response.statusText);

                    // Return the original response object with all methods intact
                    // Don't try to parse JSON here - let the caller handle it
                    return response;
                })
                .catch(error => {
                    console.error('🦊 Firefox fetch error:', error);
                    throw error;
                });
        };
    }

    /**
     * Fix JSON parsing issues
     */
    fixJSONParsing() {
        const originalParse = JSON.parse;

        JSON.parse = function(text, reviver) {
            try {
                // Clean the text first
                const cleanText = text.trim();

                // Check if it's actually JSON
                if (!cleanText.startsWith('{') && !cleanText.startsWith('[')) {
                    console.warn('⚠️ Text does not appear to be JSON:', cleanText.substring(0, 100));
                    throw new Error('Invalid JSON format');
                }

                return originalParse(cleanText, reviver);
            } catch (error) {
                console.error('🦊 Firefox JSON parse error:', error);
                console.error('📄 Problematic text:', text.substring(0, 200));
                throw error;
            }
        };
    }

    /**
     * Fix CORS headers for Firefox
     */
    fixCORSHeaders() {
        // Add event listener for CORS errors
        window.addEventListener('error', (event) => {
            if (event.message && event.message.includes('CORS')) {
                console.error('🦊 Firefox CORS error detected:', event);
                this.showCORSError();
            }
        });
    }

    /**
     * Show CORS error message
     */
    showCORSError() {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-warning';
        errorDiv.innerHTML = `
            <h5>🦊 Firefox Compatibility Issue</h5>
            <p>Firefox has detected a CORS (Cross-Origin Resource Sharing) issue.</p>
            <p>This may affect some API calls. Please try refreshing the page.</p>
        `;

        const container = document.querySelector('.container-fluid') || document.body;
        container.insertBefore(errorDiv, container.firstChild);

        // Auto-hide after 10 seconds
        setTimeout(() => {
            errorDiv.remove();
        }, 10000);
    }

    /**
     * Enhanced API call function for Firefox
     */
    static async apiCall(url, options = {}) {
        try {
            console.log('🦊 Firefox API call to:', url);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                mode: 'cors',
                credentials: 'same-origin',
                ...options
            });

            console.log('🦊 Response status:', response.status, response.statusText);
            console.log('🦊 Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                // Try to get error details from response
                const errorText = await response.text();
                console.error('🦊 Error response:', errorText);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const text = await response.text();
            console.log('🦊 Raw response text:', text.substring(0, 200) + (text.length > 200 ? '...' : ''));

            // Check if response looks like JSON
            const trimmedText = text.trim();
            if (!trimmedText.startsWith('{') && !trimmedText.startsWith('[')) {
                console.error('🦊 Response does not appear to be JSON:', trimmedText.substring(0, 100));

                // Check if it's an HTML error page
                if (trimmedText.toLowerCase().includes('<html') || trimmedText.toLowerCase().includes('<!doctype')) {
                    throw new Error('Server returned HTML error page instead of JSON');
                }

                // Check if it's a PHP error
                if (trimmedText.includes('Fatal error') || trimmedText.includes('Parse error')) {
                    throw new Error('Server PHP error: ' + trimmedText.substring(0, 100));
                }

                throw new Error('Server returned non-JSON response');
            }

            try {
                const parsed = JSON.parse(trimmedText);
                console.log('🦊 Parsed JSON successfully:', parsed);
                return parsed;
            } catch (parseError) {
                console.error('🦊 JSON parse failed:', parseError);
                console.error('🦊 Problematic text:', trimmedText.substring(0, 500));
                throw new Error('Invalid JSON response from server: ' + parseError.message);
            }
        } catch (error) {
            console.error('🦊 Firefox API call failed:', error);
            throw error;
        }
    }
}

// Initialize Firefox compatibility
if (typeof window !== 'undefined') {
    window.firefoxCompat = new FirefoxCompatibility();

    // Export for use in other scripts
    window.FirefoxCompatibility = FirefoxCompatibility;
}
