<?php
/**
 * Demo Data Verification Script
 * Verifies that all demo data was created successfully
 */

require_once '../php/config.php';

// Set content type and charset
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق من البيانات التجريبية</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: right; }
        th { background: #f8f9fa; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; }
        .stat-label { font-size: 0.9rem; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 التحقق من البيانات التجريبية</h1>
            <p>فحص شامل للبيانات التجريبية المُنشأة</p>
        </div>

<?php
try {
    $pdo = getPDOConnection();
    echo '<div class="success">✅ تم الاتصال بقاعدة البيانات بنجاح</div>';

    // Statistics Overview
    echo '<div class="section">';
    echo '<h2>📊 إحصائيات عامة</h2>';
    
    // Count demo user
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = 'demo_user'");
    $stmt->execute();
    $demoUserCount = $stmt->fetch()['count'];
    
    // Count categories
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
    $categoriesCount = $stmt->fetch()['count'];
    
    // Count products
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
    $productsCount = $stmt->fetch()['count'];
    
    // Count landing pages
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM landing_pages");
    $landingPagesCount = $stmt->fetch()['count'];
    
    echo '<div class="stats-grid">';
    echo '<div class="stat-card"><div class="stat-number">' . $demoUserCount . '</div><div class="stat-label">مستخدم تجريبي</div></div>';
    echo '<div class="stat-card"><div class="stat-number">' . $categoriesCount . '</div><div class="stat-label">فئة</div></div>';
    echo '<div class="stat-card"><div class="stat-number">' . $productsCount . '</div><div class="stat-label">منتج</div></div>';
    echo '<div class="stat-card"><div class="stat-number">' . $landingPagesCount . '</div><div class="stat-label">صفحة هبوط</div></div>';
    echo '</div>';
    echo '</div>';

    // Demo User Details
    echo '<div class="section">';
    echo '<h2>👤 تفاصيل المستخدم التجريبي</h2>';
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = 'demo_user'");
    $stmt->execute();
    $demoUser = $stmt->fetch();
    
    if ($demoUser) {
        echo '<div class="success">✅ المستخدم التجريبي موجود</div>';
        echo '<table>';
        echo '<tr><th>المعرف</th><td>' . $demoUser['id'] . '</td></tr>';
        echo '<tr><th>اسم المستخدم</th><td>' . $demoUser['username'] . '</td></tr>';
        echo '<tr><th>البريد الإلكتروني</th><td>' . $demoUser['email'] . '</td></tr>';
        echo '<tr><th>الاسم الأول</th><td>' . $demoUser['first_name'] . '</td></tr>';
        echo '<tr><th>الاسم الأخير</th><td>' . $demoUser['last_name'] . '</td></tr>';
        echo '<tr><th>الهاتف</th><td>' . $demoUser['phone'] . '</td></tr>';
        echo '<tr><th>الحالة</th><td>' . $demoUser['status'] . '</td></tr>';
        echo '<tr><th>تاريخ الإنشاء</th><td>' . $demoUser['created_at'] . '</td></tr>';
        echo '</table>';
    } else {
        echo '<div class="error">❌ المستخدم التجريبي غير موجود</div>';
    }
    echo '</div>';

    // Categories List
    echo '<div class="section">';
    echo '<h2>📂 قائمة الفئات</h2>';
    
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY ordre");
    $categories = $stmt->fetchAll();
    
    if ($categories) {
        echo '<table>';
        echo '<tr><th>المعرف</th><th>الاسم العربي</th><th>الاسم الإنجليزي</th><th>الأيقونة</th><th>اللون</th><th>الحالة</th></tr>';
        foreach ($categories as $category) {
            echo '<tr>';
            echo '<td>' . $category['id'] . '</td>';
            echo '<td>' . $category['nom_ar'] . '</td>';
            echo '<td>' . $category['nom_en'] . '</td>';
            echo '<td><i class="' . $category['icone'] . '" style="color: ' . $category['couleur'] . ';"></i> ' . $category['icone'] . '</td>';
            echo '<td><span style="background: ' . $category['couleur'] . '; color: white; padding: 2px 8px; border-radius: 3px;">' . $category['couleur'] . '</span></td>';
            echo '<td>' . ($category['actif'] ? '✅ نشط' : '❌ غير نشط') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<div class="error">❌ لا توجد فئات</div>';
    }
    echo '</div>';

    // Products List
    echo '<div class="section">';
    echo '<h2>📦 قائمة المنتجات</h2>';
    
    $stmt = $pdo->query("
        SELECT p.*, c.nom_ar as category_name 
        FROM produits p 
        LEFT JOIN categories c ON p.category_id = c.id 
        ORDER BY p.id
    ");
    $products = $stmt->fetchAll();
    
    if ($products) {
        echo '<table>';
        echo '<tr><th>المعرف</th><th>العنوان</th><th>النوع</th><th>الفئة</th><th>السعر</th><th>المخزون</th><th>الحالة</th></tr>';
        foreach ($products as $product) {
            echo '<tr>';
            echo '<td>' . $product['id'] . '</td>';
            echo '<td>' . substr($product['titre'], 0, 50) . '...</td>';
            echo '<td>' . $product['type'] . '</td>';
            echo '<td>' . ($product['category_name'] ?? 'غير محدد') . '</td>';
            echo '<td>' . number_format($product['prix'], 2) . ' دج</td>';
            echo '<td>' . $product['stock'] . '</td>';
            echo '<td>' . ($product['actif'] ? '✅ نشط' : '❌ غير نشط') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<div class="error">❌ لا توجد منتجات</div>';
    }
    echo '</div>';

    // Landing Pages List
    echo '<div class="section">';
    echo '<h2>🌐 قائمة صفحات الهبوط</h2>';
    
    $stmt = $pdo->query("
        SELECT lp.*, p.titre as product_title 
        FROM landing_pages lp 
        LEFT JOIN produits p ON lp.produit_id = p.id 
        ORDER BY lp.id
    ");
    $landingPages = $stmt->fetchAll();
    
    if ($landingPages) {
        echo '<table>';
        echo '<tr><th>المعرف</th><th>العنوان</th><th>المنتج</th><th>القالب</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>';
        foreach ($landingPages as $page) {
            echo '<tr>';
            echo '<td>' . $page['id'] . '</td>';
            echo '<td>' . $page['titre'] . '</td>';
            echo '<td>' . ($page['product_title'] ?? 'غير محدد') . '</td>';
            echo '<td>' . $page['template_id'] . '</td>';
            echo '<td>' . ($page['actif'] ? '✅ نشط' : '❌ غير نشط') . '</td>';
            echo '<td>' . $page['date_creation'] . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<div class="error">❌ لا توجد صفحات هبوط</div>';
    }
    echo '</div>';

} catch (Exception $e) {
    echo '<div class="error">❌ خطأ: ' . $e->getMessage() . '</div>';
}
?>

        <div class="section">
            <h2>✅ تقرير التحقق</h2>
            <div class="success">
                <p>تم التحقق من جميع البيانات التجريبية بنجاح. النظام جاهز للاستخدام مع البيانات التجريبية الشاملة.</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="create-demo-data.php" class="btn">🔄 إعادة إنشاء البيانات</a>
                <a href="index.html" class="btn">⚙️ لوحة التحكم</a>
                <a href="../" class="btn">🛍️ عرض المتجر</a>
            </div>
        </div>
    </div>
</body>
</html>
