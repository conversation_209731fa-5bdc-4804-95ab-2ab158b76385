# Technical Implementation Guide - <PERSON><PERSON>b Landing Page

## Quick Start Setup

### 1. Environment Setup
Create a `.env` file in the root directory:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3307
DB_USERNAME=root
DB_PASSWORD=
DB_DATABASE=mossab-landing-page

# Application Settings
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000

# AI Provider Settings (Optional)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GEMINI_API_KEY=your_gemini_key_here

# Security Settings
SESSION_LIFETIME=120
CSRF_TOKEN_LIFETIME=3600
MAX_LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_TIME=900

# Upload Settings
MAX_UPLOAD_SIZE=10485760
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp

# Cache Settings
CACHE_ENABLED=true
CACHE_LIFETIME=3600

# Shipping Settings
DEFAULT_SHIPPING_COST=50
FREE_SHIPPING_THRESHOLD=1000
```

### 2. Database Setup
1. Create the database:
```sql
CREATE DATABASE `mossab-landing-page` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Import the schema:
```bash
mysql -u root -p mossab-landing-page < mossab-landing-page.sql
```

3. Create admin user:
```sql
INSERT INTO admins (nom_utilisateur, mot_de_passe) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');
```

### 3. File Permissions
Set proper permissions for upload directories:
```bash
chmod -R 755 uploads/
chmod -R 755 logs/
chown -R www-data:www-data uploads/
chown -R www-data:www-data logs/
```

## Core Architecture Patterns

### 1. Configuration Management
The system uses a centralized configuration approach:

```php
// config/config.php
class Config {
    private static $config = [];
    
    public static function get($key, $default = null) {
        return self::$config[$key] ?? $default;
    }
    
    public static function getDbConfig() {
        return [
            'host' => self::get('DB_HOST'),
            'port' => self::get('DB_PORT'),
            'username' => self::get('DB_USERNAME'),
            'password' => self::get('DB_PASSWORD'),
            'database' => self::get('DB_DATABASE')
        ];
    }
}
```

### 2. Database Connection Pattern
Singleton pattern for database connections:

```php
// config/database.php
class Database {
    private static $instance = null;
    private $pdo;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getPDO() {
        return $this->pdo;
    }
}
```

### 3. API Response Pattern
Consistent API response structure:

```php
// Standard success response
{
    "success": true,
    "data": [...],
    "message": "Operation completed successfully"
}

// Standard error response
{
    "success": false,
    "error": "Error message",
    "code": "ERROR_CODE"
}
```

## AI Integration Implementation

### 1. AI Manager Setup
The AIManager class handles multiple AI providers:

```php
class AIManager {
    private $providers = ['openai', 'anthropic', 'gemini'];
    
    public function generateContent($prompt, $provider = null) {
        $provider = $provider ?: $this->getActiveProvider();
        
        switch ($provider) {
            case 'openai':
                return $this->callOpenAI($prompt);
            case 'anthropic':
                return $this->callAnthropic($prompt);
            case 'gemini':
                return $this->callGemini($prompt);
        }
    }
}
```

### 2. Frontend AI Integration
Magic wand buttons for content generation:

```javascript
// admin/js/ai-magic-wand.js
class AIMagicWand {
    static async generateContent(type, context) {
        const response = await fetch('/api/ai-generate.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': getCSRFToken()
            },
            body: JSON.stringify({
                type: type,
                context: context
            })
        });
        
        return await response.json();
    }
}
```

## Landing Page System

### 1. Product Landing Management
The ProductLanding class handles landing page operations:

```php
class ProductLanding {
    public function createLandingPage($productId, $data) {
        $slug = $this->generateSlug($data['title'], $productId);
        
        $stmt = $this->pdo->prepare("
            INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $productId,
            $data['title'],
            $data['right_content'],
            $data['left_content'],
            $slug
        ]);
    }
}
```

### 2. Dynamic Content Blocks
Content blocks for flexible page layouts:

```php
public function saveContentBlock($productId, $title, $content, $sortOrder) {
    $stmt = $this->pdo->prepare("
        INSERT INTO product_content_blocks (product_id, title, content, sort_order)
        VALUES (?, ?, ?, ?)
    ");
    
    return $stmt->execute([$productId, $title, $content, $sortOrder]);
}
```

## Security Implementation

### 1. Authentication System
Secure login with attempt limiting:

```php
class Security {
    public static function authenticate($username, $password) {
        // Check login attempts
        if (self::isLockedOut($username)) {
            throw new Exception('Account temporarily locked');
        }
        
        // Verify credentials
        $user = self::getUserByUsername($username);
        if ($user && password_verify($password, $user['mot_de_passe'])) {
            self::resetLoginAttempts($username);
            return $user;
        }
        
        self::recordFailedAttempt($username);
        return false;
    }
}
```

### 2. CSRF Protection
Token-based CSRF protection:

```php
public static function generateCSRFToken() {
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    return $token;
}

public static function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && 
           hash_equals($_SESSION['csrf_token'], $token);
}
```

## Frontend Architecture

### 1. Lazy Loading Implementation
Intersection Observer for image lazy loading:

```javascript
class LazyLoadManager {
    static init() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy-load');
                    observer.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('.lazy-load').forEach(img => {
            observer.observe(img);
        });
    }
}
```

### 2. Cart Management
Client-side cart with localStorage:

```javascript
class CartManager {
    static addToCart(product) {
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        
        const existingItem = cart.find(item => item.id === product.id);
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({...product, quantity: 1});
        }
        
        localStorage.setItem('cart', JSON.stringify(cart));
        this.updateCartUI();
    }
}
```

## Performance Optimization

### 1. Image Optimization
WebP conversion with fallbacks:

```php
class ImageOptimizer {
    public function optimizeImage($sourcePath, $targetPath) {
        // Create WebP version
        $webpPath = str_replace('.jpg', '.webp', $targetPath);
        $this->createWebP($sourcePath, $webpPath);
        
        // Create thumbnail
        $thumbnailPath = str_replace('.jpg', '_thumb.jpg', $targetPath);
        $this->createThumbnail($sourcePath, $thumbnailPath, 300, 300);
        
        return [
            'original' => $targetPath,
            'webp' => $webpPath,
            'thumbnail' => $thumbnailPath
        ];
    }
}
```

### 2. Caching Strategy
File-based caching for API responses:

```php
class CacheManager {
    public function get($key) {
        $file = $this->getCacheFile($key);
        
        if (file_exists($file) && (time() - filemtime($file)) < $this->lifetime) {
            return unserialize(file_get_contents($file));
        }
        
        return null;
    }
    
    public function set($key, $data) {
        $file = $this->getCacheFile($key);
        file_put_contents($file, serialize($data));
    }
}
```

## Testing and Debugging

### 1. Error Handling
Comprehensive error logging:

```php
function handleException($e) {
    error_log($e->getMessage());
    
    if (Config::get('APP_DEBUG', false)) {
        echo 'Error: ' . $e->getMessage();
    } else {
        echo 'An application error occurred. Please try again later.';
    }
}
set_exception_handler('handleException');
```

### 2. API Testing
Test endpoints for validation:

```php
// test-api-endpoints.php
$endpoints = [
    '/api/get-ai-settings.php',
    '/php/api/products.php',
    '/php/api/orders.php'
];

foreach ($endpoints as $endpoint) {
    $response = file_get_contents($baseUrl . $endpoint);
    $data = json_decode($response, true);
    
    if ($data['success']) {
        echo "✓ $endpoint - OK\n";
    } else {
        echo "✗ $endpoint - Error: " . $data['error'] . "\n";
    }
}
```

This implementation guide provides the essential patterns and code examples needed to understand and extend the Mossaab Landing Page system.
