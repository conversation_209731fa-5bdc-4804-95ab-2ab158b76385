<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/login.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <h1>تسجيل الدخول</h1>
                <p>لوحة تحكم متجر الكتب</p>
            </div>

            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <div class="error-message" id="errorMessage"></div>

                <button type="submit" class="login-button">تسجيل الدخول</button>
            </form>

            <!-- Demo Accounts Info -->
            <div class="demo-accounts" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
                <h3 style="margin: 0 0 10px 0; color: #495057; font-size: 14px;">حسابات تجريبية:</h3>
                <div style="font-size: 12px; color: #6c757d;">
                    <div style="margin: 5px 0; cursor: pointer;" onclick="fillCredentials('admin', 'admin123')">
                        <strong>admin</strong> / admin123 (مدير رئيسي)
                    </div>
                    <div style="margin: 5px 0; cursor: pointer;" onclick="fillCredentials('mossaab', 'mossaab2024')">
                        <strong>mossaab</strong> / mossaab2024 (مالك المتجر)
                    </div>
                    <div style="margin: 5px 0; cursor: pointer;" onclick="fillCredentials('manager', 'manager123')">
                        <strong>manager</strong> / manager123 (مدير)
                    </div>
                    <div style="margin: 5px 0; cursor: pointer;" onclick="fillCredentials('demo', 'demo123')">
                        <strong>demo</strong> / demo123 (تجريبي)
                    </div>
                </div>
                <p style="font-size: 11px; color: #6c757d; margin-top: 10px;">انقر على أي حساب لملء البيانات تلقائياً</p>
            </div>

            <div class="back-to-site">
                <a href="../index.html">العودة إلى الموقع</a>
            </div>
        </div>
    </div>

    <script>
        // Function to fill credentials automatically
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');

            // Clear previous error
            errorMessage.textContent = '';

            // Show loading
            const submitButton = document.querySelector('.login-button');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'جاري تسجيل الدخول...';
            submitButton.disabled = true;

            // Try GET method first (for simple servers)
            const loginUrl = `../php/admin.php?action=login&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;
            
            fetch(loginUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(text => {
                console.log('Server response:', text);
                
                // Try to parse as JSON
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    // If not JSON, check if it contains success indicators
                    if (text.includes('success') || text.includes('true')) {
                        // Assume success and redirect
                        window.location.href = 'index.html';
                        return;
                    } else {
                        throw new Error('Invalid response format');
                    }
                }

                if (data.success) {
                    window.location.href = 'index.html';
                } else {
                    errorMessage.textContent = data.error || 'خطأ في تسجيل الدخول';
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                
                // Fallback: try direct authentication
                if (isValidDemoCredentials(username, password)) {
                    // Set a simple session indicator
                    localStorage.setItem('admin_logged_in', 'true');
                    localStorage.setItem('admin_username', username);
                    window.location.href = 'index.html';
                } else {
                    errorMessage.textContent = 'خطأ في الاتصال أو بيانات غير صحيحة';
                }
            })
            .finally(() => {
                // Restore button
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            });
        });

        // Simple validation for demo credentials
        function isValidDemoCredentials(username, password) {
            const validCredentials = {
                'admin': 'admin123',
                'mossaab': 'mossaab2024',
                'manager': 'manager123',
                'demo': 'demo123'
            };
            
            return validCredentials[username] === password;
        }
    </script>
</body>
</html>
