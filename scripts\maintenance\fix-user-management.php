<?php
/**
 * Fix User Management System
 * This script will fix the user management system by:
 * 1. Ensuring the user-auth.php file has the correct include path
 * 2. Updating the loadUserManagementContent function in admin.js
 * 3. Creating a proper user-management.html file if it doesn't exist
 */

header('Content-Type: text/html');
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Fix User Management System</h1>";

// Step 1: Fix user-auth.php include path
$userAuthFile = 'php/api/user-auth.php';
if (file_exists($userAuthFile)) {
    echo "<h2>Step 1: Fix user-auth.php include path</h2>";
    
    $content = file_get_contents($userAuthFile);
    if ($content !== false) {
        // Replace the include path
        $newContent = str_replace(
            "require_once '../config.php';",
            "require_once __DIR__ . '/../config.php';",
            $content
        );
        
        if ($newContent !== $content) {
            // Write the updated content back to the file
            if (file_put_contents($userAuthFile, $newContent)) {
                echo "<p>✅ Updated include path in user-auth.php</p>";
            } else {
                echo "<p>❌ Failed to write to user-auth.php</p>";
            }
        } else {
            echo "<p>✅ Include path in user-auth.php is already correct</p>";
        }
    } else {
        echo "<p>❌ Failed to read user-auth.php</p>";
    }
} else {
    echo "<p>❌ user-auth.php file not found</p>";
}

// Step 2: Create a proper user-management.html file if it doesn't exist
$userMgmtFile = 'admin/user-management.html';
if (!file_exists($userMgmtFile)) {
    echo "<h2>Step 2: Create user-management.html</h2>";
    
    $userMgmtContent = <<<HTML
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - لوحة التحكم</title>
</head>
<body>
    <!-- User Management Content -->
    <div class="user-management-content">
        <!-- Header Section -->
        <div class="user-management-header">
            <div class="section-title-wrapper">
                <div class="section-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="section-title">
                    <h3>إدارة المستخدمين</h3>
                    <p>إدارة حسابات المستخدمين والصلاحيات والأدوار</p>
                </div>
            </div>
            <div class="user-stats">
                <div class="stat-box">
                    <div class="stat-label">إجمالي المستخدمين:</div>
                    <div class="stat-value" id="totalUsers">0</div>
                </div>
                <div class="stat-box">
                    <div class="stat-label">المستخدمين النشطين:</div>
                    <div class="stat-value" id="activeUsers">0</div>
                </div>
                <div class="stat-box">
                    <div class="stat-label">المديرين:</div>
                    <div class="stat-value" id="adminUsers">0</div>
                </div>
            </div>
        </div>

        <!-- Tools Section -->
        <div class="user-management-tools">
            <div class="tools-header">
                <h4 class="tools-title">
                    <i class="fas fa-tools"></i>
                    أدوات إدارة المستخدمين
                </h4>
                <div class="tools-actions">
                    <button class="btn btn-primary" onclick="showAddUserModal()">
                        <i class="fas fa-user-plus"></i>
                        إضافة مستخدم جديد
                    </button>
                    <button class="btn btn-secondary" onclick="exportUsers()">
                        <i class="fas fa-file-export"></i>
                        تصدير المستخدمين
                    </button>
                </div>
            </div>
            <div class="tools-filters">
                <div class="filter-group">
                    <div class="filter-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <input type="text" id="userSearch" class="filter-input" placeholder="البحث">
                </div>
                <div class="filter-group">
                    <div class="filter-icon">
                        <i class="fas fa-user-tag"></i>
                    </div>
                    <select id="roleFilter" class="filter-select">
                        <option value="">جميع الأدوار</option>
                        <option value="مدير">مدير</option>
                        <option value="محرر">محرر</option>
                        <option value="عميل">عميل</option>
                    </select>
                </div>
                <div class="filter-group">
                    <div class="filter-icon">
                        <i class="fas fa-toggle-on"></i>
                    </div>
                    <select id="statusFilter" class="filter-select">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="suspended">معلق</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="users-table-container">
            <div class="table-header">
                <h4 class="table-title">
                    <i class="fas fa-list"></i>
                    قائمة المستخدمين
                </h4>
                <div class="table-info">
                    <span id="usersCount">0 مستخدم</span>
                </div>
            </div>

            <div class="table-responsive">
                <table class="enhanced-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllUsers" onchange="toggleSelectAll()">
                            </th>
                            <th>الصورة</th>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ التسجيل</th>
                            <th>آخر دخول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
            </div>

            <div class="table-pagination">
                <div class="pagination-info">
                    <span id="paginationInfo">عرض 0 من 0</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-sm" onclick="changePage(-1)">
                        <i class="fas fa-chevron-right"></i>
                        السابق
                    </button>
                    <button class="btn btn-sm" onclick="changePage(1)">
                        التالي
                        <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Modal -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="userModalTitle">إضافة مستخدم جديد</h3>
                <button class="close-btn" onclick="closeUserModal()">×</button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <div class="form-group">
                        <label for="userName">الاسم</label>
                        <input type="text" id="userName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="userEmail">البريد الإلكتروني</label>
                        <input type="email" id="userEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="userPassword">كلمة المرور</label>
                        <input type="password" id="userPassword" name="password">
                        <small>اتركه فارغاً للإبقاء على كلمة المرور الحالية</small>
                    </div>
                    <div class="form-group">
                        <label for="userRole">الدور</label>
                        <select id="userRole" name="role_id" required>
                            <option value="1">مدير</option>
                            <option value="2">صاحب متجر</option>
                            <option value="3">محرر</option>
                            <option value="4">عميل</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="userStatus">الحالة</label>
                        <select id="userStatus" name="status" required>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeUserModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="saveUser()">حفظ</button>
            </div>
        </div>
    </div>

    <!-- User Details Modal -->
    <div class="modal" id="userDetailsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="userDetailsTitle">تفاصيل المستخدم</h3>
                <button class="close-btn" onclick="closeUserDetailsModal()">×</button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- User details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeUserDetailsModal()">إغلاق</button>
                <button class="btn btn-primary" onclick="editUserFromDetails()">تعديل</button>
            </div>
        </div>
    </div>
</body>
</html>
HTML;

    if (file_put_contents($userMgmtFile, $userMgmtContent)) {
        echo "<p>✅ Created user-management.html file</p>";
    } else {
        echo "<p>❌ Failed to create user-management.html file</p>";
    }
} else {
    echo "<p>✅ user-management.html file already exists</p>";
}

// Step 3: Update loadUserManagementContent function in admin.js
$adminJsFile = 'admin/js/admin.js';
if (file_exists($adminJsFile)) {
    echo "<h2>Step 3: Update loadUserManagementContent function</h2>";
    
    $content = file_get_contents($adminJsFile);
    if ($content !== false) {
        // Find the loadUserManagementContent function
        $pattern = '/function loadUserManagementContent\(\) \{.*?\}/s';
        $replacement = <<<'JS'
async function loadUserManagementContent() {
    console.log('Loading User Management content...');

    const container = document.getElementById('userManagementContent');
    if (!container) {
        console.error('User management container not found');
        return;
    }

    try {
        // Load the enhanced user management HTML
        const response = await fetch('user-management.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // Extract the user management content
        const userManagementContent = doc.querySelector('.user-management-content');

        if (userManagementContent) {
            container.innerHTML = userManagementContent.innerHTML;

            // Also extract and append the user modals if they exist
            const userModal = doc.querySelector('#userModal');
            const userDetailsModal = doc.querySelector('#userDetailsModal');

            if (userModal && !document.getElementById('userModal')) {
                document.body.appendChild(userModal.cloneNode(true));
                console.log('✅ User modal added to document');
            }

            if (userDetailsModal && !document.getElementById('userDetailsModal')) {
                document.body.appendChild(userDetailsModal.cloneNode(true));
                console.log('✅ User details modal added to document');
            }
        } else {
            container.innerHTML = htmlContent;
        }

        console.log('User Management content loaded successfully');

        // Initialize the enhanced user management system
        if (typeof initializeUserManagement === 'function') {
            console.log('Initializing enhanced user management...');
            initializeUserManagement();
        } else {
            console.log('User Management script loaded successfully');
        }

    } catch (error) {
        console.error('Error loading User Management content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إدارة المستخدمين</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadUserManagementContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}
JS;

        $newContent = preg_replace($pattern, $replacement, $content);
        
        if ($newContent !== $content) {
            // Write the updated content back to the file
            if (file_put_contents($adminJsFile, $newContent)) {
                echo "<p>✅ Updated loadUserManagementContent function in admin.js</p>";
            } else {
                echo "<p>❌ Failed to write to admin.js</p>";
            }
        } else {
            echo "<p>❌ Failed to find and replace loadUserManagementContent function</p>";
        }
    } else {
        echo "<p>❌ Failed to read admin.js</p>";
    }
} else {
    echo "<p>❌ admin.js file not found</p>";
}

echo "<h2>Fix Complete</h2>";
echo "<p>The user management system should now be fixed. Please refresh the admin panel and check the User Management section.</p>";
?>
