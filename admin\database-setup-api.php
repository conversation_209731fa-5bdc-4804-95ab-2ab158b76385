<?php

/**
 * Database Setup API
 * Creates all necessary tables and sample data for the admin panel
 */

// Set proper headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// Database configuration from .env
$host = 'localhost';
$port = '3307';
$dbname = 'mossab-landing-page';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    $results = [];

    // 1. Create Categories Table
    $createCategoriesSQL = "
        CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            parent_id INT DEFAULT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createCategoriesSQL);
    $results['categories_table'] = 'Created successfully';

    // 2. Update Users Table (add missing columns if they don't exist)
    try {
        // Check if users table exists and get its structure
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            // Table exists, check for missing columns
            $columns = $pdo->query("SHOW COLUMNS FROM users")->fetchAll(PDO::FETCH_COLUMN);

            if (!in_array('full_name', $columns)) {
                $pdo->exec("ALTER TABLE users ADD COLUMN full_name VARCHAR(255) AFTER password");
            }
            if (!in_array('role', $columns)) {
                $pdo->exec("ALTER TABLE users ADD COLUMN role VARCHAR(100) DEFAULT 'user' AFTER full_name");
            }
            if (!in_array('status', $columns)) {
                $pdo->exec("ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' AFTER role");
            }
            if (!in_array('last_login', $columns)) {
                $pdo->exec("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL AFTER status");
            }
            $results['users_table'] = 'Updated with missing columns';
        } else {
            // Create new table
            $createUsersSQL = "
                CREATE TABLE users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(255) NOT NULL UNIQUE,
                    email VARCHAR(255) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    full_name VARCHAR(255),
                    role VARCHAR(100) DEFAULT 'user',
                    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                    last_login TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $pdo->exec($createUsersSQL);
            $results['users_table'] = 'Created successfully';
        }
    } catch (Exception $e) {
        $results['users_table'] = 'Error: ' . $e->getMessage();
    }

    // 3. Update Roles Table (add missing columns if they don't exist)
    try {
        // Check if roles table exists and get its structure
        $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
        if ($stmt->rowCount() > 0) {
            // Table exists, check for missing columns
            $columns = $pdo->query("SHOW COLUMNS FROM roles")->fetchAll(PDO::FETCH_COLUMN);

            if (!in_array('description', $columns)) {
                $pdo->exec("ALTER TABLE roles ADD COLUMN description TEXT AFTER name");
            }
            if (!in_array('permissions', $columns)) {
                $pdo->exec("ALTER TABLE roles ADD COLUMN permissions JSON AFTER description");
            }
            if (!in_array('status', $columns)) {
                $pdo->exec("ALTER TABLE roles ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active' AFTER permissions");
            }
            $results['roles_table'] = 'Updated with missing columns';
        } else {
            // Create new table
            $createRolesSQL = "
                CREATE TABLE roles (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL UNIQUE,
                    description TEXT,
                    permissions JSON,
                    status ENUM('active', 'inactive') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $pdo->exec($createRolesSQL);
            $results['roles_table'] = 'Created successfully';
        }
    } catch (Exception $e) {
        $results['roles_table'] = 'Error: ' . $e->getMessage();
    }

    // 4. Create Security Settings Table
    $createSecuritySQL = "
        CREATE TABLE IF NOT EXISTS security_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(255) NOT NULL UNIQUE,
            setting_value JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createSecuritySQL);
    $results['security_settings_table'] = 'Created successfully';

    // 5. Create Subscription Plans Table
    $createSubscriptionsSQL = "
        CREATE TABLE IF NOT EXISTS subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            duration_months INT NOT NULL DEFAULT 1,
            features JSON,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createSubscriptionsSQL);
    $results['subscription_plans_table'] = 'Created successfully';

    // 6. Create Products Table
    $createProductsSQL = "
        CREATE TABLE IF NOT EXISTS products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            category_id INT,
            user_id INT,
            status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
            stock_quantity INT DEFAULT 0,
            image_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_category_id (category_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createProductsSQL);
    $results['products_table'] = 'Created successfully';

    // Insert sample data
    $sampleDataResults = [];

    // Sample Categories
    $categoriesCount = $pdo->query("SELECT COUNT(*) as count FROM categories")->fetch()['count'];
    if ($categoriesCount == 0) {
        $sampleCategories = [
            ['name' => 'الإلكترونيات', 'description' => 'أجهزة إلكترونية متنوعة'],
            ['name' => 'الملابس', 'description' => 'ملابس رجالية ونسائية'],
            ['name' => 'الكتب', 'description' => 'كتب ومراجع متنوعة'],
            ['name' => 'المنزل والحديقة', 'description' => 'أدوات منزلية ومستلزمات الحديقة'],
            ['name' => 'الرياضة', 'description' => 'معدات ومستلزمات رياضية']
        ];

        $insertCategoryStmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        foreach ($sampleCategories as $category) {
            $insertCategoryStmt->execute([$category['name'], $category['description']]);
        }
        $sampleDataResults['categories'] = count($sampleCategories) . ' categories inserted';
    }

    // Sample Users
    $usersCount = $pdo->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
    if ($usersCount == 0) {
        $sampleUsers = [
            [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'full_name' => 'مدير النظام',
                'role' => 'admin'
            ],
            [
                'username' => 'ahmed_store',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'full_name' => 'أحمد محمد',
                'role' => 'store_owner'
            ],
            [
                'username' => 'fatima_user',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'full_name' => 'فاطمة أحمد',
                'role' => 'user'
            ]
        ];

        $insertUserStmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
        foreach ($sampleUsers as $user) {
            $insertUserStmt->execute([$user['username'], $user['email'], $user['password'], $user['full_name'], $user['role']]);
        }
        $sampleDataResults['users'] = count($sampleUsers) . ' users inserted';
    }

    // Sample Roles
    $rolesCount = $pdo->query("SELECT COUNT(*) as count FROM roles")->fetch()['count'];
    if ($rolesCount == 0) {
        $sampleRoles = [
            [
                'name' => 'مدير عام',
                'description' => 'صلاحيات كاملة للنظام',
                'permissions' => json_encode(['all'])
            ],
            [
                'name' => 'مدير المتاجر',
                'description' => 'إدارة المتاجر والمنتجات',
                'permissions' => json_encode(['stores', 'products', 'orders'])
            ],
            [
                'name' => 'مدير المحتوى',
                'description' => 'إدارة المحتوى والصفحات',
                'permissions' => json_encode(['content', 'pages', 'categories'])
            ]
        ];

        $insertRoleStmt = $pdo->prepare("INSERT INTO roles (name, description, permissions) VALUES (?, ?, ?)");
        foreach ($sampleRoles as $role) {
            $insertRoleStmt->execute([$role['name'], $role['description'], $role['permissions']]);
        }
        $sampleDataResults['roles'] = count($sampleRoles) . ' roles inserted';
    }

    // Sample Security Settings
    $securityCount = $pdo->query("SELECT COUNT(*) as count FROM security_settings")->fetch()['count'];
    if ($securityCount == 0) {
        $securitySettings = [
            'security_dashboard' => [
                'active_sessions' => 24,
                'failed_logins' => 3,
                'blocked_ips' => 2,
                'security_alerts' => 1,
                'last_scan' => date('Y-m-d H:i:s')
            ]
        ];

        $insertSecurityStmt = $pdo->prepare("INSERT INTO security_settings (setting_key, setting_value) VALUES (?, ?)");
        foreach ($securitySettings as $key => $value) {
            $insertSecurityStmt->execute([$key, json_encode($value)]);
        }
        $sampleDataResults['security_settings'] = count($securitySettings) . ' security settings inserted';
    }

    // Sample Subscription Plans
    $plansCount = $pdo->query("SELECT COUNT(*) as count FROM subscription_plans")->fetch()['count'];
    if ($plansCount == 0) {
        $samplePlans = [
            [
                'name' => 'الخطة الأساسية',
                'description' => 'خطة مناسبة للمبتدئين',
                'price' => 29.99,
                'duration_months' => 1,
                'features' => json_encode(['5 منتجات', 'دعم أساسي', 'تقارير شهرية'])
            ],
            [
                'name' => 'الخطة المتقدمة',
                'description' => 'خطة للشركات المتوسطة',
                'price' => 59.99,
                'duration_months' => 1,
                'features' => json_encode(['50 منتج', 'دعم متقدم', 'تقارير أسبوعية', 'تحليلات متقدمة'])
            ]
        ];

        $insertPlanStmt = $pdo->prepare("INSERT INTO subscription_plans (name, description, price, duration_months, features) VALUES (?, ?, ?, ?, ?)");
        foreach ($samplePlans as $plan) {
            $insertPlanStmt->execute([$plan['name'], $plan['description'], $plan['price'], $plan['duration_months'], $plan['features']]);
        }
        $sampleDataResults['subscription_plans'] = count($samplePlans) . ' subscription plans inserted';
    }

    echo json_encode([
        'success' => true,
        'message' => 'Database setup completed successfully',
        'data' => [
            'tables_created' => $results,
            'sample_data' => $sampleDataResults,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database setup failed: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
