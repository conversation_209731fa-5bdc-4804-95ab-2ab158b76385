/**
 * Store Settings Management JavaScript
 * إدارة إعدادات المتجر - JavaScript
 */

console.log('🏪 تحميل ملف store-settings-management.js...');

// Global variables
let storeSettingsData = {};
let currentSection = 'general';
let hasUnsavedChanges = false;

/**
 * Load store settings management content
 */
function loadStoreSettingsContent() {
    console.log('🏪 بدء تحميل إعدادات المتجر...');
    
    const container = document.getElementById('storeSettingsContent');
    if (!container) {
        console.error('❌ لم يتم العثور على حاوي إعدادات المتجر');
        return;
    }
    
    console.log('✅ تم العثور على الحاوي');
    
    // Show loading
    showLoadingState(container);
    
    // Fetch settings data
    fetchStoreSettings()
        .then(data => {
            console.log('📦 تم استلام بيانات الإعدادات:', data);
            
            if (data.success) {
                storeSettingsData = data.data;
                renderStoreSettingsInterface(container, data.data);
            } else {
                throw new Error(data.message || 'فشل في تحميل الإعدادات');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل الإعدادات:', error);
            showErrorState(container, error.message);
        });
}

/**
 * Fetch store settings from server
 */
async function fetchStoreSettings() {
    try {
        const url = 'php/store_settings.php?action=get_all&include_private=true';
        console.log('🌐 طلب البيانات من:', url);
        
        const response = await fetch(url);
        console.log('📡 استجابة الخادم:', response.status, response.statusText);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('📋 البيانات المستلمة:', data);
        
        return data;
    } catch (error) {
        console.error('❌ خطأ في جلب البيانات:', error);
        throw new Error('خطأ في الاتصال بالخادم: ' + error.message);
    }
}

/**
 * Show loading state
 */
function showLoadingState(container) {
    console.log('⏳ عرض حالة التحميل...');
    
    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div>
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
            </div>
            <p style="color: #666;">جاري تحميل إعدادات المتجر...</p>
            <p style="color: #999; font-size: 0.9em;">يتم الآن جلب البيانات من الخادم...</p>
        </div>
    `;
}

/**
 * Show error state
 */
function showErrorState(container, message) {
    console.log('❌ عرض حالة الخطأ:', message);
    
    container.innerHTML = `
        <div style="text-align: center; padding: 60px 20px; color: #dc3545;">
            <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.7;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 style="margin: 0 0 15px 0; color: #dc3545;">خطأ في تحميل إعدادات المتجر</h3>
            <p style="margin: 0 0 25px 0; color: #666; font-size: 1.1rem;">${message}</p>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button onclick="loadStoreSettingsContent()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
                <a href="php/store_settings.php?action=get_all" target="_blank" style="padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
                    <i class="fas fa-external-link-alt"></i> اختبار API مباشر
                </a>
            </div>
        </div>
    `;
}

/**
 * Render store settings interface
 */
function renderStoreSettingsInterface(container, data) {
    console.log('🎨 رسم واجهة إعدادات المتجر...');
    
    if (!data || !data.settings) {
        console.error('❌ بيانات غير صحيحة للرسم');
        showErrorState(container, 'بيانات الإعدادات غير صحيحة');
        return;
    }
    
    const categories = Object.keys(data.settings);
    const totalSettings = data.total_settings || 0;
    
    const html = `
        <div class="store-settings-container">
            <!-- Header -->
            <div class="store-settings-header">
                <h2><i class="fas fa-cog"></i> إعدادات المتجر</h2>
                <p>إدارة وتخصيص جميع إعدادات المتجر والنظام</p>
                <div class="header-actions">
                    <button class="header-btn primary" onclick="saveAllSettings()">
                        <i class="fas fa-save"></i> حفظ جميع الإعدادات
                    </button>
                    <button class="header-btn secondary" onclick="resetToDefaults()">
                        <i class="fas fa-undo"></i> استعادة الافتراضية
                    </button>
                    <button class="header-btn secondary" onclick="exportSettings()">
                        <i class="fas fa-download"></i> تصدير الإعدادات
                    </button>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="settings-stats">
                <div class="stat-card">
                    <div class="stat-icon general">
                        <i class="fas fa-cog"></i>
                    </div>
                    <h3 class="stat-number">${totalSettings}</h3>
                    <p class="stat-label">إجمالي الإعدادات</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon currency">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h3 class="stat-number">${categories.length}</h3>
                    <p class="stat-label">فئات الإعدادات</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon shipping">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="stat-number">${Object.values(data.settings).flat().filter(s => s.is_public == 1).length}</h3>
                    <p class="stat-label">الإعدادات العامة</p>
                </div>
                <div class="stat-card">
                    <div class="stat-icon email">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="stat-number">${Object.values(data.settings).flat().filter(s => s.is_public == 0).length}</h3>
                    <p class="stat-label">الإعدادات الخاصة</p>
                </div>
            </div>
            
            <!-- Navigation -->
            <div class="settings-nav">
                <div class="settings-nav-header">
                    <h3><i class="fas fa-list"></i> فئات الإعدادات</h3>
                </div>
                <ul class="nav-tabs">
                    ${categories.map(category => `
                        <li class="nav-tab">
                            <a href="#" class="nav-tab-link ${category === currentSection ? 'active' : ''}" 
                               onclick="switchSettingsSection('${category}')">
                                <span class="nav-tab-badge">${data.categories[category]?.count || 0}</span>
                                ${data.categories[category]?.name || category}
                            </a>
                        </li>
                    `).join('')}
                </ul>
            </div>
            
            <!-- Settings Content -->
            <div class="settings-content">
                ${categories.map(category => renderSettingsSection(category, data.settings[category], data.categories[category])).join('')}
            </div>
            
            <!-- Success Message -->
            <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724; text-align: center;">
                <i class="fas fa-check-circle"></i> <strong>تم تحميل إعدادات المتجر بنجاح!</strong>
                <br>تم عرض ${totalSettings} إعداد في ${categories.length} فئة.
            </div>
        </div>
    `;
    
    console.log('✅ تم إنشاء HTML للواجهة');
    container.innerHTML = html;
    
    // Initialize form handlers
    initializeFormHandlers();
    
    console.log('✅ تم تحديث محتوى الحاوي بنجاح');
}

/**
 * Render settings section
 */
function renderSettingsSection(category, settings, categoryInfo) {
    if (!settings || settings.length === 0) {
        return '';
    }
    
    const isActive = category === currentSection;
    
    return `
        <div class="settings-section ${isActive ? 'active' : ''}" id="section-${category}">
            <h3 class="settings-section-title">
                <i class="fas fa-${getCategoryIcon(category)}"></i>
                ${categoryInfo?.name || category}
                <span style="font-size: 0.8rem; color: #999; font-weight: normal;">(${settings.length} إعداد)</span>
            </h3>
            
            <form class="settings-form" id="form-${category}">
                ${settings.map(setting => renderSettingField(setting)).join('')}
                
                <div class="form-actions">
                    <button type="button" class="btn primary" onclick="saveSectionSettings('${category}')">
                        <i class="fas fa-save"></i> حفظ إعدادات ${categoryInfo?.name || category}
                    </button>
                    <button type="button" class="btn outline" onclick="resetSectionSettings('${category}')">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>
    `;
}

/**
 * Render individual setting field
 */
function renderSettingField(setting) {
    const fieldId = `setting_${setting.setting_key}`;
    const isRequired = setting.is_required == 1;
    const isPublic = setting.is_public == 1;
    
    let inputHtml = '';
    
    switch (setting.setting_type) {
        case 'boolean':
            inputHtml = `
                <label class="form-checkbox">
                    <input type="checkbox" id="${fieldId}" name="${setting.setting_key}" 
                           ${setting.setting_value == '1' ? 'checked' : ''} 
                           onchange="markAsChanged()">
                    <span>تفعيل هذا الإعداد</span>
                </label>
            `;
            break;
            
        case 'number':
            inputHtml = `
                <input type="number" class="form-input" id="${fieldId}" name="${setting.setting_key}" 
                       value="${setting.setting_value || ''}" 
                       placeholder="${setting.description_ar || ''}"
                       ${isRequired ? 'required' : ''}
                       onchange="markAsChanged()">
            `;
            break;
            
        case 'email':
            inputHtml = `
                <input type="email" class="form-input" id="${fieldId}" name="${setting.setting_key}" 
                       value="${setting.setting_value || ''}" 
                       placeholder="${setting.description_ar || ''}"
                       ${isRequired ? 'required' : ''}
                       onchange="markAsChanged()">
            `;
            break;
            
        case 'color':
            inputHtml = `
                <div class="color-input-wrapper">
                    <input type="color" class="color-input" id="${fieldId}_color" 
                           value="${setting.setting_value || '#667eea'}"
                           onchange="updateColorValue('${fieldId}'); markAsChanged();">
                    <input type="text" class="form-input color-value" id="${fieldId}" name="${setting.setting_key}" 
                           value="${setting.setting_value || '#667eea'}"
                           placeholder="#RRGGBB"
                           onchange="updateColorPicker('${fieldId}'); markAsChanged();">
                </div>
            `;
            break;
            
        case 'text':
        default:
            if (setting.setting_key.includes('password')) {
                inputHtml = `
                    <input type="password" class="form-input" id="${fieldId}" name="${setting.setting_key}" 
                           value="${setting.setting_value || ''}" 
                           placeholder="${setting.description_ar || ''}"
                           ${isRequired ? 'required' : ''}
                           onchange="markAsChanged()">
                `;
            } else if (setting.description_ar && setting.description_ar.length > 100) {
                inputHtml = `
                    <textarea class="form-input form-textarea" id="${fieldId}" name="${setting.setting_key}" 
                              placeholder="${setting.description_ar || ''}"
                              ${isRequired ? 'required' : ''}
                              onchange="markAsChanged()">${setting.setting_value || ''}</textarea>
                `;
            } else {
                inputHtml = `
                    <input type="text" class="form-input" id="${fieldId}" name="${setting.setting_key}" 
                           value="${setting.setting_value || ''}" 
                           placeholder="${setting.description_ar || ''}"
                           ${isRequired ? 'required' : ''}
                           onchange="markAsChanged()">
                `;
            }
            break;
    }
    
    return `
        <div class="form-group">
            <label for="${fieldId}" class="form-label ${isRequired ? 'required' : ''}">
                ${setting.display_name_ar}
                ${isPublic ? '<i class="fas fa-globe" title="إعداد عام"></i>' : '<i class="fas fa-lock" title="إعداد خاص"></i>'}
            </label>
            ${inputHtml}
            ${setting.description_ar ? `<p class="form-help">${setting.description_ar}</p>` : ''}
        </div>
    `;
}

/**
 * Get category icon
 */
function getCategoryIcon(category) {
    const icons = {
        'general': 'store',
        'currency': 'coins',
        'shipping': 'shipping-fast',
        'email': 'envelope',
        'appearance': 'palette'
    };
    
    return icons[category] || 'cog';
}

/**
 * Switch settings section
 */
function switchSettingsSection(category) {
    console.log('🔄 تبديل القسم إلى:', category);
    
    // Update active tab
    document.querySelectorAll('.nav-tab-link').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector(`[onclick="switchSettingsSection('${category}')"]`).classList.add('active');
    
    // Update active section
    document.querySelectorAll('.settings-section').forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById(`section-${category}`).classList.add('active');
    
    currentSection = category;
}

/**
 * Initialize form handlers
 */
function initializeFormHandlers() {
    console.log('🔧 تهيئة معالجات النماذج...');
}

/**
 * Mark form as changed
 */
function markAsChanged() {
    hasUnsavedChanges = true;
    console.log('📝 تم تعديل النموذج');
}

/**
 * Update color value from picker
 */
function updateColorValue(fieldId) {
    const colorPicker = document.getElementById(fieldId + '_color');
    const colorInput = document.getElementById(fieldId);
    colorInput.value = colorPicker.value;
}

/**
 * Update color picker from input
 */
function updateColorPicker(fieldId) {
    const colorInput = document.getElementById(fieldId);
    const colorPicker = document.getElementById(fieldId + '_color');
    if (colorInput.value.match(/^#[0-9A-F]{6}$/i)) {
        colorPicker.value = colorInput.value;
    }
}

// Placeholder functions for actions
function saveAllSettings() {
    console.log('💾 حفظ جميع الإعدادات...');
    alert('حفظ جميع الإعدادات - قيد التطوير');
}

function resetToDefaults() {
    console.log('🔄 استعادة الإعدادات الافتراضية...');
    if (confirm('هل أنت متأكد من استعادة جميع الإعدادات إلى القيم الافتراضية؟')) {
        alert('استعادة الإعدادات الافتراضية - قيد التطوير');
    }
}

function exportSettings() {
    console.log('📤 تصدير الإعدادات...');
    alert('تصدير الإعدادات - قيد التطوير');
}

function saveSectionSettings(category) {
    console.log('💾 حفظ إعدادات القسم:', category);
    alert(`حفظ إعدادات ${category} - قيد التطوير`);
}

function resetSectionSettings(category) {
    console.log('🔄 إعادة تعيين إعدادات القسم:', category);
    if (confirm(`هل أنت متأكد من إعادة تعيين إعدادات ${category}؟`)) {
        alert(`إعادة تعيين إعدادات ${category} - قيد التطوير`);
    }
}

// Make function globally available
window.loadStoreSettingsContent = loadStoreSettingsContent;

console.log('✅ تم تحميل ملف store-settings-management.js بنجاح');
console.log('🎯 الدالة الرئيسية متاحة:', typeof loadStoreSettingsContent);
