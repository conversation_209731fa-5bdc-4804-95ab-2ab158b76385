<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد المديرين - Firebase</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .admin-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .admin-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .admin-details h3 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .admin-details p {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .admin-credentials {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-family: inherit;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .links {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إعداد المديرين - Firebase</h1>
            <p>إنشاء حسابات المديرين في Firebase Authentication</p>
        </div>
        
        <div class="content">
            <div class="status info">
                <strong>📋 المطلوب:</strong>
                سيتم إنشاء حسابات المديرين التالية في Firebase Authentication مع تعيين الأدوار المناسبة في Firestore.
            </div>

            <!-- Progress Bar -->
            <div class="progress">
                <div class="progress-bar" id="progressBar">0%</div>
            </div>

            <!-- Status Messages -->
            <div id="statusMessages"></div>

            <!-- Admin Accounts -->
            <div id="adminAccounts">
                <div class="admin-card" data-admin="super-admin">
                    <div class="admin-info">
                        <div class="admin-details">
                            <h3>🔑 المدير الرئيسي</h3>
                            <p>صلاحيات كاملة لجميع أقسام النظام</p>
                        </div>
                        <button class="btn btn-primary" onclick="createAdmin('super-admin')">إنشاء</button>
                    </div>
                    <div class="admin-credentials">
                        <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                        <strong>كلمة المرور:</strong> Admin123!@#<br>
                        <strong>الدور:</strong> super_admin
                    </div>
                </div>

                <div class="admin-card" data-admin="owner">
                    <div class="admin-info">
                        <div class="admin-details">
                            <h3>👑 مالك المتجر</h3>
                            <p>مالك المتجر مع صلاحيات إدارية كاملة</p>
                        </div>
                        <button class="btn btn-primary" onclick="createAdmin('owner')">إنشاء</button>
                    </div>
                    <div class="admin-credentials">
                        <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                        <strong>كلمة المرور:</strong> Mossaab2024!<br>
                        <strong>الدور:</strong> owner
                    </div>
                </div>

                <div class="admin-card" data-admin="manager">
                    <div class="admin-info">
                        <div class="admin-details">
                            <h3>👨‍💼 مدير المتجر</h3>
                            <p>مدير مع صلاحيات محدودة</p>
                        </div>
                        <button class="btn btn-primary" onclick="createAdmin('manager')">إنشاء</button>
                    </div>
                    <div class="admin-credentials">
                        <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                        <strong>كلمة المرور:</strong> Manager123!<br>
                        <strong>الدور:</strong> manager
                    </div>
                </div>

                <div class="admin-card" data-admin="demo">
                    <div class="admin-info">
                        <div class="admin-details">
                            <h3>🧪 حساب تجريبي</h3>
                            <p>حساب للاستعراض والتجربة</p>
                        </div>
                        <button class="btn btn-primary" onclick="createAdmin('demo')">إنشاء</button>
                    </div>
                    <div class="admin-credentials">
                        <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                        <strong>كلمة المرور:</strong> Demo123!<br>
                        <strong>الدور:</strong> demo
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="btn btn-success" onclick="createAllAdmins()" id="createAllBtn">
                    <i class="fas fa-users"></i> إنشاء جميع الحسابات
                </button>
                <button class="btn btn-warning" onclick="resetProgress()" id="resetBtn">
                    <i class="fas fa-redo"></i> إعادة تعيين
                </button>
            </div>

            <!-- Links -->
            <div class="links">
                <a href="firebase-login.html" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> صفحة تسجيل الدخول
                </a>
                <a href="firebase-users.html" class="btn btn-primary">
                    <i class="fas fa-users"></i> إدارة المستخدمين
                </a>
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module" src="js/firebase-config.js"></script>

    <!-- Main Script -->
    <script type="module">
        const adminAccounts = {
            'super-admin': {
                email: '<EMAIL>',
                password: 'Admin123!@#',
                displayName: 'المدير الرئيسي',
                role: 'super_admin'
            },
            'owner': {
                email: '<EMAIL>',
                password: 'Mossaab2024!',
                displayName: 'مصعب - مالك المتجر',
                role: 'owner'
            },
            'manager': {
                email: '<EMAIL>',
                password: 'Manager123!',
                displayName: 'مدير المتجر',
                role: 'manager'
            },
            'demo': {
                email: '<EMAIL>',
                password: 'Demo123!',
                displayName: 'حساب تجريبي',
                role: 'demo'
            }
        };

        let createdCount = 0;
        const totalCount = Object.keys(adminAccounts).length;

        // Create single admin
        window.createAdmin = async (adminKey) => {
            const admin = adminAccounts[adminKey];
            const button = document.querySelector(`[data-admin="${adminKey}"] button`);
            const card = document.querySelector(`[data-admin="${adminKey}"]`);

            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';

            try {
                const result = await window.firebaseAuth.signUpWithEmail(
                    admin.email, 
                    admin.password, 
                    admin.displayName, 
                    admin.role
                );

                if (result.success) {
                    button.innerHTML = '<i class="fas fa-check"></i> تم الإنشاء';
                    button.className = 'btn btn-success';
                    card.style.background = '#d4edda';
                    
                    showStatus(`✅ تم إنشاء حساب ${admin.displayName} بنجاح`, 'success');
                    createdCount++;
                    updateProgress();
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                button.innerHTML = '<i class="fas fa-times"></i> فشل';
                button.className = 'btn btn-danger';
                button.disabled = false;
                
                showStatus(`❌ فشل في إنشاء حساب ${admin.displayName}: ${error.message}`, 'error');
            }
        };

        // Create all admins
        window.createAllAdmins = async () => {
            const createAllBtn = document.getElementById('createAllBtn');
            createAllBtn.disabled = true;
            createAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء جميع الحسابات...';

            for (const adminKey of Object.keys(adminAccounts)) {
                const button = document.querySelector(`[data-admin="${adminKey}"] button`);
                if (!button.classList.contains('btn-success')) {
                    await createAdmin(adminKey);
                    // Wait a bit between creations
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            createAllBtn.innerHTML = '<i class="fas fa-check"></i> تم إنشاء جميع الحسابات';
            createAllBtn.className = 'btn btn-success';
        };

        // Reset progress
        window.resetProgress = () => {
            createdCount = 0;
            updateProgress();
            
            document.querySelectorAll('.admin-card').forEach(card => {
                card.style.background = '#f8f9fa';
                const button = card.querySelector('button');
                button.disabled = false;
                button.innerHTML = 'إنشاء';
                button.className = 'btn btn-primary';
            });

            document.getElementById('createAllBtn').disabled = false;
            document.getElementById('createAllBtn').innerHTML = '<i class="fas fa-users"></i> إنشاء جميع الحسابات';
            document.getElementById('createAllBtn').className = 'btn btn-success';

            document.getElementById('statusMessages').innerHTML = '';
        };

        // Update progress bar
        function updateProgress() {
            const percentage = Math.round((createdCount / totalCount) * 100);
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '%';
        }

        // Show status message
        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `status ${type}`;
            messageDiv.innerHTML = message;
            statusDiv.appendChild(messageDiv);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // Initialize
        setTimeout(() => {
            if (!window.firebaseAuth) {
                showStatus('❌ Firebase لم يتم تحميله بعد. يرجى إعادة تحميل الصفحة.', 'error');
            } else {
                showStatus('✅ Firebase جاهز. يمكنك الآن إنشاء حسابات المديرين.', 'success');
            }
        }, 2000);
    </script>
</body>
</html>
