/**
 * Firebase Analytics Fix
 * Correction des erreurs Firebase et Google Analytics
 */

// Fonction pour gérer les erreurs de connexion Firebase
function handleFirebaseConnectionErrors() {
    console.log('🔧 Initialisation des corrections Firebase/Analytics...');
    
    // Intercepter les erreurs de réseau
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        return originalFetch.apply(this, args)
            .catch(error => {
                // Ignorer silencieusement les erreurs Google Analytics
                if (args[0] && args[0].includes('google-analytics.com')) {
                    console.debug('Analytics request failed (ignored):', error.message);
                    return Promise.resolve(new Response('{}', { status: 200 }));
                }
                
                // Ignorer silencieusement les erreurs Firebase
                if (args[0] && (args[0].includes('firestore.googleapis.com') || args[0].includes('firebase'))) {
                    console.debug('Firebase request failed (ignored):', error.message);
                    return Promise.resolve(new Response('{}', { status: 200 }));
                }
                
                // Re-lancer les autres erreurs
                throw error;
            });
    };
}

// Fonction pour gérer les erreurs Firestore hors ligne
function handleFirestoreOfflineErrors() {
    // Écouter les erreurs Firestore
    window.addEventListener('unhandledrejection', function(event) {
        const error = event.reason;
        
        // Ignorer les erreurs de connexion Firestore
        if (error && error.message && (
            error.message.includes('Failed to get document') ||
            error.message.includes('offline') ||
            error.message.includes('network') ||
            error.message.includes('UNAVAILABLE')
        )) {
            console.debug('Firestore offline error (ignored):', error.message);
            event.preventDefault();
            return;
        }
        
        // Ignorer les erreurs Google Analytics
        if (error && error.message && error.message.includes('google-analytics')) {
            console.debug('Google Analytics error (ignored):', error.message);
            event.preventDefault();
            return;
        }
    });
}

// Fonction pour corriger les erreurs de profil utilisateur
function handleUserProfileErrors() {
    // Surcharger les fonctions de profil utilisateur pour gérer les erreurs
    if (window.FirebaseAuthManager) {
        const originalLoadUserProfile = window.FirebaseAuthManager.prototype.loadUserProfile;
        
        if (originalLoadUserProfile) {
            window.FirebaseAuthManager.prototype.loadUserProfile = function(uid) {
                return originalLoadUserProfile.call(this, uid)
                    .catch(error => {
                        console.debug('User profile load failed (using fallback):', error.message);
                        
                        // Retourner un profil par défaut
                        return {
                            uid: uid,
                            email: '<EMAIL>',
                            displayName: 'Utilisateur',
                            role: 'user',
                            isAdmin: false
                        };
                    });
            };
        }
    }
}

// Fonction pour désactiver Google Analytics en mode développement
function disableAnalyticsInDev() {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('🚫 Désactivation de Google Analytics en mode développement');
        
        // Désactiver gtag
        window.gtag = function() {
            console.debug('gtag call ignored in development');
        };
        
        // Désactiver Google Analytics
        window.ga = function() {
            console.debug('ga call ignored in development');
        };
        
        // Empêcher le chargement des scripts Analytics
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(this, tagName);
            
            if (tagName.toLowerCase() === 'script') {
                const originalSetAttribute = element.setAttribute;
                element.setAttribute = function(name, value) {
                    if (name === 'src' && value && value.includes('google-analytics.com')) {
                        console.debug('Blocked Google Analytics script in development');
                        return;
                    }
                    return originalSetAttribute.call(this, name, value);
                };
            }
            
            return element;
        };
    }
}

// Fonction pour nettoyer les erreurs de console
function cleanConsoleErrors() {
    // Intercepter console.error pour filtrer les erreurs connues
    const originalConsoleError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        
        // Ignorer les erreurs connues
        if (
            message.includes('net::ERR_ABORTED') ||
            message.includes('google-analytics.com') ||
            message.includes('Failed to get document') ||
            message.includes('firestore.googleapis.com') ||
            message.includes('FIRESTORE (9.0.0) INTERNAL ASSERTION FAILED')
        ) {
            console.debug('Filtered error:', message);
            return;
        }
        
        // Afficher les autres erreurs normalement
        originalConsoleError.apply(console, args);
    };
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Initialisation des corrections Firebase/Analytics...');
    
    // Appliquer toutes les corrections
    handleFirebaseConnectionErrors();
    handleFirestoreOfflineErrors();
    handleUserProfileErrors();
    disableAnalyticsInDev();
    cleanConsoleErrors();
    
    console.log('✅ Corrections Firebase/Analytics appliquées');
});

// Appliquer immédiatement si le DOM est déjà chargé
if (document.readyState === 'loading') {
    // Le document est encore en cours de chargement
} else {
    // Le document est déjà chargé
    handleFirebaseConnectionErrors();
    handleFirestoreOfflineErrors();
    handleUserProfileErrors();
    disableAnalyticsInDev();
    cleanConsoleErrors();
    
    console.log('✅ Corrections Firebase/Analytics appliquées (DOM déjà chargé)');
}

// Exporter pour utilisation globale
window.firebaseAnalyticsFix = {
    handleFirebaseConnectionErrors,
    handleFirestoreOfflineErrors,
    handleUserProfileErrors,
    disableAnalyticsInDev,
    cleanConsoleErrors
};