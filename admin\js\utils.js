/**
 * Utility Functions for Admin Panel
 * Contains common functions used across the admin interface
 */

// Global utility functions
window.utils = window.utils || {};

/**
 * Make API calls with error handling and retry mechanism
 */
async function makeAPICall(url, options = {}) {
    const maxRetries = 3;
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`🔄 API Call attempt ${attempt}/${maxRetries}: ${url}`);
            
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log(`✅ API Call successful: ${url}`);
            return data;
            
        } catch (error) {
            console.error(`❌ API Call attempt ${attempt} failed:`, error);
            lastError = error;
            
            if (attempt < maxRetries) {
                const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
                console.log(`⏳ Retrying in ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    
    throw lastError;
}

// Make it globally available
window.makeAPICall = makeAPICall;

/**
 * Log errors consistently
 */
function logError(message, error = null) {
    console.error(`❌ ${message}`, error);
    if (error && error.stack) {
        console.error('Stack trace:', error.stack);
    }
}

window.logError = logError;

/**
 * Loading Manager
 */
const LoadingManager = {
    show: function(message = 'جاري التحميل...') {
        const existingLoader = document.getElementById('globalLoader');
        if (existingLoader) {
            existingLoader.remove();
        }
        
        const loader = document.createElement('div');
        loader.id = 'globalLoader';
        loader.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 30px; border-radius: 10px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                    <div style="margin-bottom: 20px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                    </div>
                    <div style="color: #333; font-size: 1.1rem;">${message}</div>
                </div>
            </div>
        `;
        document.body.appendChild(loader);
    },
    
    hide: function() {
        const loader = document.getElementById('globalLoader');
        if (loader) {
            loader.remove();
        }
    }
};

window.LoadingManager = LoadingManager;

/**
 * Lazy Load Manager for images
 */
const LazyLoadManager = {
    observer: null,
    
    init: function() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            });
            
            this.observeImages();
        } else {
            // Fallback for older browsers
            this.loadAllImages();
        }
    },
    
    observeImages: function() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.observer.observe(img);
        });
    },
    
    loadImage: function(img) {
        const src = img.getAttribute('data-src');
        if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        }
    },
    
    showImagePlaceholder: function(img) {
        img.style.backgroundColor = '#f0f0f0';
        img.style.minHeight = '200px';
    },
    
    hideImagePlaceholder: function(img) {
        img.style.backgroundColor = '';
        img.style.minHeight = '';
    },
    
    showImageError: function(img) {
        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
    },
    
    loadAllImages: function() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => this.loadImage(img));
    }
};

window.LazyLoadManager = LazyLoadManager;

/**
 * Debounce function
 */
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

window.debounce = debounce;

/**
 * Format currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-DZ', {
        style: 'currency',
        currency: 'DZD'
    }).format(amount || 0);
}

window.formatCurrency = formatCurrency;

/**
 * Format date
 */
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-DZ');
}

window.formatDate = formatDate;

/**
 * Show/Hide loading states
 */
function showLoading(message = 'جاري التحميل...') {
    LoadingManager.show(message);
}

function hideLoading() {
    LoadingManager.hide();
}

window.showLoading = showLoading;
window.hideLoading = hideLoading;

console.log('✅ Utils.js loaded successfully');