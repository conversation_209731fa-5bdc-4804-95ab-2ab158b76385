<?php

/**
 * Server Configuration Check
 * Diagnoses PHP execution and server configuration issues
 */

// Set proper headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$diagnostics = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
    'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
    'timestamp' => date('Y-m-d H:i:s'),
    'timezone' => date_default_timezone_get(),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'extensions' => [],
    'database_test' => null,
    'file_permissions' => [],
    'api_endpoints' => []
];

// Check required PHP extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'curl'];
foreach ($requiredExtensions as $ext) {
    $diagnostics['extensions'][$ext] = extension_loaded($ext);
}

// Test database connection
try {
    $host = 'localhost';
    $port = '3307';
    $dbname = 'mossab-landing-page';
    $username = 'root';
    $password = '';

    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $diagnostics['database_test'] = [
        'status' => 'success',
        'message' => 'Database connection successful'
    ];
} catch (PDOException $e) {
    $diagnostics['database_test'] = [
        'status' => 'error',
        'message' => 'Database connection failed: ' . $e->getMessage()
    ];
}

// Check file permissions
$checkPaths = [
    __DIR__ . '/php',
    __DIR__ . '/php/api',
    __DIR__ . '/js',
    __DIR__ . '/css'
];

foreach ($checkPaths as $path) {
    if (file_exists($path)) {
        $diagnostics['file_permissions'][$path] = [
            'exists' => true,
            'readable' => is_readable($path),
            'writable' => is_writable($path),
            'permissions' => substr(sprintf('%o', fileperms($path)), -4)
        ];
    } else {
        $diagnostics['file_permissions'][$path] = [
            'exists' => false,
            'readable' => false,
            'writable' => false,
            'permissions' => null
        ];
    }
}

// Test API endpoints
$apiEndpoints = [
    'categories-fixed.php',
    'roles-fixed.php',
    'users.php',
    'security-settings.php',
    'subscriptions-fixed.php'
];

foreach ($apiEndpoints as $endpoint) {
    $filePath = __DIR__ . '/php/api/' . $endpoint;
    $diagnostics['api_endpoints'][$endpoint] = [
        'file_exists' => file_exists($filePath),
        'file_readable' => file_exists($filePath) ? is_readable($filePath) : false,
        'file_size' => file_exists($filePath) ? filesize($filePath) : 0
    ];
}

// Check if we're running under a web server
$diagnostics['web_server'] = [
    'sapi_name' => php_sapi_name(),
    'is_cli' => php_sapi_name() === 'cli',
    'is_web' => !php_sapi_name() === 'cli',
    'server_name' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
    'server_port' => $_SERVER['SERVER_PORT'] ?? 'Unknown'
];

// Check for common issues
$diagnostics['issues'] = [];

if (php_sapi_name() === 'cli') {
    $diagnostics['issues'][] = 'Running in CLI mode - web server may not be configured properly';
}

if (!extension_loaded('pdo_mysql')) {
    $diagnostics['issues'][] = 'PDO MySQL extension not loaded - database connections will fail';
}

if (!file_exists(__DIR__ . '/php/api')) {
    $diagnostics['issues'][] = 'API directory does not exist';
}

if (empty($diagnostics['issues'])) {
    $diagnostics['issues'][] = 'No obvious issues detected';
}

// Return diagnostics
echo json_encode([
    'success' => true,
    'message' => 'Server diagnostics completed',
    'data' => $diagnostics
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
