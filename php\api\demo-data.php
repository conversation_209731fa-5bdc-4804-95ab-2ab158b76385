<?php
/**
 * Demo Data Management API
 * إدارة البيانات التجريبية
 */

require_once __DIR__ . '/../config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $pdo = getPDOConnection();
    $action = $_GET['action'] ?? $_POST['action'] ?? 'get_stats';

    switch ($action) {
        case 'get_stats':
            getDemoStats();
            break;
            
        case 'get_products':
            getDemoProducts();
            break;
            
        case 'get_landing_pages':
            getDemoLandingPages();
            break;
            
        case 'get_orders':
            getDemoOrders();
            break;
            
        case 'get_customers':
            getDemoCustomers();
            break;
            
        case 'install_demo':
            installDemoData();
            break;
            
        case 'reset_demo':
            resetDemoData();
            break;
            
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}

/**
 * Get Demo Statistics
 */
function getDemoStats()
{
    global $pdo;
    
    try {
        $stats = [];
        
        // Count sellers
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM sellers");
        $stats['sellers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count stores
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM stores");
        $stats['stores'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count products
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
        $stats['products'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count landing pages
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM landing_pages");
        $stats['landing_pages'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count customers
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customers");
        $stats['customers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count orders
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
        $stats['orders'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Get recent activity
        $stmt = $pdo->query("
            SELECT 'order' as type, customer_name as title, total_amount as value, created_at 
            FROM orders 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $stats['recent_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $stats,
            'message' => 'تم تحميل إحصائيات البيانات التجريبية بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل الإحصائيات: ' . $e->getMessage());
    }
}

/**
 * Get Demo Products
 */
function getDemoProducts()
{
    global $pdo;
    
    try {
        $stmt = $pdo->query("
            SELECT p.*, c.name as category_name, s.store_name
            FROM products p
            LEFT JOIN product_categories c ON p.category_id = c.id
            LEFT JOIN stores s ON p.store_id = s.id
            ORDER BY p.created_at DESC
        ");
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON fields
        foreach ($products as &$product) {
            $product['images'] = json_decode($product['images'], true);
            $product['specifications'] = json_decode($product['specifications'], true);
            $product['tags'] = json_decode($product['tags'], true);
        }
        
        echo json_encode([
            'success' => true,
            'data' => $products,
            'message' => 'تم تحميل المنتجات التجريبية بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل المنتجات: ' . $e->getMessage());
    }
}

/**
 * Get Demo Landing Pages
 */
function getDemoLandingPages()
{
    global $pdo;
    
    try {
        $stmt = $pdo->query("
            SELECT lp.*, s.store_name
            FROM landing_pages lp
            LEFT JOIN stores s ON lp.store_id = s.id
            ORDER BY lp.created_at DESC
        ");
        $landingPages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON fields
        foreach ($landingPages as &$page) {
            $page['hero_section'] = json_decode($page['hero_section'], true);
            $page['features_section'] = json_decode($page['features_section'], true);
            $page['testimonials_section'] = json_decode($page['testimonials_section'], true);
            $page['cta_section'] = json_decode($page['cta_section'], true);
            $page['seo_meta'] = json_decode($page['seo_meta'], true);
        }
        
        echo json_encode([
            'success' => true,
            'data' => $landingPages,
            'message' => 'تم تحميل صفحات الهبوط التجريبية بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل صفحات الهبوط: ' . $e->getMessage());
    }
}

/**
 * Get Demo Orders
 */
function getDemoOrders()
{
    global $pdo;
    
    try {
        $stmt = $pdo->query("
            SELECT o.*, s.store_name
            FROM orders o
            LEFT JOIN stores s ON o.store_id = s.id
            ORDER BY o.created_at DESC
        ");
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON fields
        foreach ($orders as &$order) {
            $order['order_items'] = json_decode($order['order_items'], true);
        }
        
        echo json_encode([
            'success' => true,
            'data' => $orders,
            'message' => 'تم تحميل الطلبات التجريبية بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل الطلبات: ' . $e->getMessage());
    }
}

/**
 * Get Demo Customers
 */
function getDemoCustomers()
{
    global $pdo;
    
    try {
        $stmt = $pdo->query("
            SELECT c.*, 
                   COUNT(o.id) as total_orders,
                   COALESCE(SUM(o.total_amount), 0) as total_spent
            FROM customers c
            LEFT JOIN orders o ON c.email = o.customer_email
            GROUP BY c.id
            ORDER BY c.created_at DESC
        ");
        $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $customers,
            'message' => 'تم تحميل العملاء التجريبيين بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل العملاء: ' . $e->getMessage());
    }
}

/**
 * Install Demo Data
 */
function installDemoData()
{
    try {
        // Execute the installation script
        $output = shell_exec('php ' . __DIR__ . '/../install_demo.php 2>&1');
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تثبيت البيانات التجريبية بنجاح',
            'output' => $output
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تثبيت البيانات التجريبية: ' . $e->getMessage());
    }
}

/**
 * Reset Demo Data
 */
function resetDemoData()
{
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Delete demo data in correct order (respecting foreign keys)
        $pdo->exec("DELETE FROM landing_page_analytics");
        $pdo->exec("DELETE FROM newsletter_subscriptions");
        $pdo->exec("DELETE FROM orders");
        $pdo->exec("DELETE FROM customers");
        $pdo->exec("DELETE FROM product_reviews");
        $pdo->exec("DELETE FROM landing_pages");
        $pdo->exec("DELETE FROM products");
        $pdo->exec("DELETE FROM product_categories");
        $pdo->exec("DELETE FROM stores");
        $pdo->exec("DELETE FROM sellers");
        
        // Reset auto increment
        $pdo->exec("ALTER TABLE sellers AUTO_INCREMENT = 1");
        $pdo->exec("ALTER TABLE stores AUTO_INCREMENT = 1");
        $pdo->exec("ALTER TABLE product_categories AUTO_INCREMENT = 1");
        $pdo->exec("ALTER TABLE products AUTO_INCREMENT = 1");
        $pdo->exec("ALTER TABLE landing_pages AUTO_INCREMENT = 1");
        $pdo->exec("ALTER TABLE customers AUTO_INCREMENT = 1");
        $pdo->exec("ALTER TABLE orders AUTO_INCREMENT = 1");
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف جميع البيانات التجريبية بنجاح'
        ]);
    } catch (Exception $e) {
        $pdo->rollback();
        throw new Exception('فشل في حذف البيانات التجريبية: ' . $e->getMessage());
    }
}
?>
