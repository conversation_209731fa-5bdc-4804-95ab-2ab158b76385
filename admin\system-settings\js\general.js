/**
 * General Settings Management JavaScript
 * إدارة الإعدادات العامة
 */

const GeneralManager = {
    // State
    state: {
        settings: {},
        categories: [],
        currentCategory: '',
        isLoading: false,
        hasChanges: false,
        autoSaveTimer: null
    },

    // Configuration
    config: {
        apiEndpoint: '../../api/system/general-settings.php',
        autoSaveDelay: 2000 // 2 seconds
    },

    // Category names in Arabic
    categoryNames: {
        'site': 'إعدادات الموقع',
        'email': 'إعدادات البريد الإلكتروني',
        'sms': 'إعدادات الرسائل النصية',
        'social': 'وسائل التواصل الاجتماعي',
        'seo': 'تحسين محركات البحث',
        'analytics': 'التحليلات والإحصائيات',
        'maintenance': 'الصيانة والنسخ الاحتياطي',
        'localization': 'التوطين واللغة',
        'api': 'إعدادات API',
        'system': 'إعدادات النظام'
    },

    // Initialize
    async init() {
        console.log('⚙️ Initializing General Settings Manager...');
        
        try {
            // Setup event listeners
            this.setupEventListeners();
            
            // Load categories
            await this.loadCategories();
            
            // Load initial settings
            await this.loadSettings();
            
            // Setup auto-save
            this.setupAutoSave();
            
            console.log('✅ General Settings Manager initialized');
            SystemSettings.state.currentSection = 'General';
            
        } catch (error) {
            console.error('❌ Failed to initialize General Settings Manager:', error);
            SystemSettings.showNotification('فشل في تحميل الإعدادات العامة: ' + error.message, 'error');
        }
    },

    // Setup Event Listeners
    setupEventListeners() {
        // Category filter
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.state.currentCategory = e.target.value;
                this.loadSettings();
            });
        }

        // Search input
        const searchInput = document.getElementById('settingsSearch');
        if (searchInput) {
            searchInput.addEventListener('input', SystemSettings.debounce(() => {
                this.loadSettings();
            }, 300));
        }

        // Form changes detection
        document.addEventListener('change', (e) => {
            if (e.target.closest('.settings-form')) {
                this.markAsChanged();
                this.scheduleAutoSave();
            }
        });

        document.addEventListener('input', (e) => {
            if (e.target.closest('.settings-form')) {
                this.markAsChanged();
                this.scheduleAutoSave();
            }
        });

        // Modal events
        const modal = document.getElementById('settingModal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal();
                }
            });
        }

        // Form submission
        const settingForm = document.getElementById('settingForm');
        if (settingForm) {
            settingForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveSetting();
            });
        }
    },

    // Load Categories
    async loadCategories() {
        try {
            const response = await fetch(`${this.config.apiEndpoint}?action=categories`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'فشل في تحميل فئات الإعدادات');
            }

            this.state.categories = data.data;
            this.renderCategoryFilter();

        } catch (error) {
            console.error('Error loading categories:', error);
            SystemSettings.showNotification('فشل في تحميل فئات الإعدادات: ' + error.message, 'error');
        }
    },

    // Render Category Filter
    renderCategoryFilter() {
        const categoryFilter = document.getElementById('categoryFilter');
        if (!categoryFilter) return;

        let html = '<option value="">جميع الفئات</option>';
        
        this.state.categories.forEach(category => {
            const name = this.categoryNames[category.category] || category.category;
            html += `<option value="${category.category}">${name} (${category.count})</option>`;
        });

        categoryFilter.innerHTML = html;
    },

    // Load Settings
    async loadSettings() {
        if (this.state.isLoading) return;
        
        this.state.isLoading = true;
        SystemSettings.showLoading('.settings-container', 'تحميل الإعدادات...');

        try {
            const params = new URLSearchParams({
                category: this.state.currentCategory,
                search: document.getElementById('settingsSearch')?.value || ''
            });

            const response = await fetch(`${this.config.apiEndpoint}?${params}`);
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.message || 'فشل في تحميل الإعدادات');
            }

            this.state.settings = data.data;
            this.renderSettings();

        } catch (error) {
            console.error('Error loading settings:', error);
            SystemSettings.showNotification('فشل في تحميل الإعدادات: ' + error.message, 'error');
        } finally {
            this.state.isLoading = false;
            SystemSettings.hideLoading('.settings-container');
        }
    },

    // Render Settings
    renderSettings() {
        const container = document.getElementById('settingsContainer');
        if (!container) return;

        if (Object.keys(this.state.settings).length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-cogs"></i>
                    <h3>لا توجد إعدادات</h3>
                    <p>لم يتم العثور على أي إعدادات</p>
                    <button class="btn btn-primary" onclick="GeneralManager.showAddModal()">
                        <i class="fas fa-plus"></i>
                        إضافة إعداد جديد
                    </button>
                </div>
            `;
            return;
        }

        let html = '<div class="settings-form">';

        Object.keys(this.state.settings).forEach(category => {
            const categoryName = this.categoryNames[category] || category;
            const settings = this.state.settings[category];

            html += `
                <div class="settings-category" data-category="${category}">
                    <div class="category-header">
                        <h3>
                            <i class="${this.getCategoryIcon(category)}"></i>
                            ${categoryName}
                        </h3>
                        <div class="category-actions">
                            <button class="btn btn-sm btn-secondary" onclick="GeneralManager.resetCategory('${category}')">
                                <i class="fas fa-undo"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </div>
                    
                    <div class="settings-grid">
                        ${settings.map(setting => this.renderSettingField(setting)).join('')}
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.innerHTML = html;

        // Setup field interactions
        this.setupFieldInteractions();
    },

    // Render Setting Field
    renderSettingField(setting) {
        const fieldId = `setting_${setting.setting_key}`;
        let fieldHtml = '';

        switch (setting.data_type) {
            case 'boolean':
                fieldHtml = `
                    <div class="form-check">
                        <input type="checkbox" id="${fieldId}" name="${setting.setting_key}" 
                               ${setting.setting_value ? 'checked' : ''}>
                        <label for="${fieldId}">${setting.description_ar}</label>
                    </div>
                `;
                break;

            case 'number':
                const rules = setting.validation_rules || {};
                fieldHtml = `
                    <label class="form-label" for="${fieldId}">${setting.description_ar}</label>
                    <input type="number" id="${fieldId}" name="${setting.setting_key}" 
                           class="form-control" value="${setting.setting_value}"
                           ${rules.min !== undefined ? `min="${rules.min}"` : ''}
                           ${rules.max !== undefined ? `max="${rules.max}"` : ''}
                           step="any">
                `;
                break;

            case 'email':
                fieldHtml = `
                    <label class="form-label" for="${fieldId}">${setting.description_ar}</label>
                    <input type="email" id="${fieldId}" name="${setting.setting_key}" 
                           class="form-control" value="${setting.setting_value}">
                `;
                break;

            case 'url':
                fieldHtml = `
                    <label class="form-label" for="${fieldId}">${setting.description_ar}</label>
                    <input type="url" id="${fieldId}" name="${setting.setting_key}" 
                           class="form-control" value="${setting.setting_value}">
                `;
                break;

            case 'color':
                fieldHtml = `
                    <label class="form-label" for="${fieldId}">${setting.description_ar}</label>
                    <div class="color-input-group">
                        <input type="color" id="${fieldId}" name="${setting.setting_key}" 
                               class="form-control color-input" value="${setting.setting_value}">
                        <input type="text" class="form-control color-text" 
                               value="${setting.setting_value}" readonly>
                    </div>
                `;
                break;

            case 'json':
                fieldHtml = `
                    <label class="form-label" for="${fieldId}">${setting.description_ar}</label>
                    <textarea id="${fieldId}" name="${setting.setting_key}" 
                              class="form-control json-editor" rows="4">${JSON.stringify(setting.setting_value, null, 2)}</textarea>
                `;
                break;

            default: // string
                const stringRules = setting.validation_rules || {};
                fieldHtml = `
                    <label class="form-label" for="${fieldId}">${setting.description_ar}</label>
                    <input type="text" id="${fieldId}" name="${setting.setting_key}" 
                           class="form-control" value="${setting.setting_value}"
                           ${stringRules.min_length ? `minlength="${stringRules.min_length}"` : ''}
                           ${stringRules.max_length ? `maxlength="${stringRules.max_length}"` : ''}>
                `;
        }

        return `
            <div class="setting-field" data-key="${setting.setting_key}" data-type="${setting.data_type}">
                ${fieldHtml}
                <div class="setting-info">
                    <small class="setting-key">المفتاح: ${setting.setting_key}</small>
                    ${setting.default_value ? `<small class="setting-default">الافتراضي: ${setting.default_value}</small>` : ''}
                </div>
                <div class="setting-actions">
                    <button type="button" class="btn btn-xs btn-secondary" onclick="GeneralManager.resetSetting('${setting.setting_key}')">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button type="button" class="btn btn-xs btn-primary" onclick="GeneralManager.editSetting('${setting.setting_key}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </div>
        `;
    },

    // Setup Field Interactions
    setupFieldInteractions() {
        // Color inputs
        document.querySelectorAll('.color-input').forEach(input => {
            input.addEventListener('change', (e) => {
                const textInput = e.target.parentNode.querySelector('.color-text');
                if (textInput) {
                    textInput.value = e.target.value;
                }
            });
        });

        // JSON validation
        document.querySelectorAll('.json-editor').forEach(textarea => {
            textarea.addEventListener('blur', (e) => {
                try {
                    JSON.parse(e.target.value);
                    e.target.classList.remove('is-invalid');
                } catch (error) {
                    e.target.classList.add('is-invalid');
                    SystemSettings.showNotification('تنسيق JSON غير صحيح', 'error');
                }
            });
        });
    },

    // Get Category Icon
    getCategoryIcon(category) {
        const icons = {
            'site': 'fas fa-globe',
            'email': 'fas fa-envelope',
            'sms': 'fas fa-sms',
            'social': 'fas fa-share-alt',
            'seo': 'fas fa-search',
            'analytics': 'fas fa-chart-bar',
            'maintenance': 'fas fa-tools',
            'localization': 'fas fa-language',
            'api': 'fas fa-code',
            'system': 'fas fa-cogs'
        };
        return icons[category] || 'fas fa-cog';
    },

    // Mark as Changed
    markAsChanged() {
        this.state.hasChanges = true;
        
        // Update save button
        const saveBtn = document.getElementById('saveAllBtn');
        if (saveBtn) {
            saveBtn.classList.add('btn-warning');
            saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات';
        }
    },

    // Schedule Auto Save
    scheduleAutoSave() {
        if (this.state.autoSaveTimer) {
            clearTimeout(this.state.autoSaveTimer);
        }

        this.state.autoSaveTimer = setTimeout(() => {
            this.saveAllSettings(true);
        }, this.config.autoSaveDelay);
    },

    // Setup Auto Save
    setupAutoSave() {
        // Save before page unload if there are changes
        window.addEventListener('beforeunload', (e) => {
            if (this.state.hasChanges) {
                e.preventDefault();
                e.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
            }
        });
    },

    // Save All Settings
    async saveAllSettings(isAutoSave = false) {
        const form = document.querySelector('.settings-form');
        if (!form) return;

        const formData = new FormData(form);
        const settings = {};

        // Collect all form data
        for (let [key, value] of formData.entries()) {
            const field = document.querySelector(`[name="${key}"]`);
            const dataType = field?.closest('.setting-field')?.dataset.type;

            // Convert value based on data type
            switch (dataType) {
                case 'boolean':
                    settings[key] = field.checked;
                    break;
                case 'number':
                    settings[key] = parseFloat(value) || 0;
                    break;
                case 'json':
                    try {
                        settings[key] = JSON.parse(value);
                    } catch {
                        settings[key] = value;
                    }
                    break;
                default:
                    settings[key] = value;
            }
        }

        try {
            if (!isAutoSave) {
                SystemSettings.showLoading('.settings-container', 'حفظ الإعدادات...');
            }

            const response = await fetch(`${this.config.apiEndpoint}?action=bulk`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ settings })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في حفظ الإعدادات');
            }

            this.state.hasChanges = false;
            
            // Update save button
            const saveBtn = document.getElementById('saveAllBtn');
            if (saveBtn) {
                saveBtn.classList.remove('btn-warning');
                saveBtn.innerHTML = '<i class="fas fa-check"></i> محفوظ';
                
                setTimeout(() => {
                    saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ الإعدادات';
                }, 2000);
            }

            if (!isAutoSave) {
                SystemSettings.showNotification(
                    `تم حفظ ${result.updated_count} إعداد بنجاح`,
                    'success'
                );
            }

            if (result.errors && result.errors.length > 0) {
                console.warn('Some settings had errors:', result.errors);
            }

        } catch (error) {
            console.error('Error saving settings:', error);
            if (!isAutoSave) {
                SystemSettings.showNotification('فشل في حفظ الإعدادات: ' + error.message, 'error');
            }
        } finally {
            if (!isAutoSave) {
                SystemSettings.hideLoading('.settings-container');
            }
        }
    },

    // Reset Category
    async resetCategory(category) {
        if (!confirm(`هل أنت متأكد من إعادة تعيين جميع إعدادات "${this.categoryNames[category] || category}" إلى القيم الافتراضية؟`)) {
            return;
        }

        try {
            const response = await fetch(`${this.config.apiEndpoint}?action=reset`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ category })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في إعادة تعيين الإعدادات');
            }

            SystemSettings.showNotification('تم إعادة تعيين الإعدادات بنجاح', 'success');
            await this.loadSettings();

        } catch (error) {
            console.error('Error resetting category:', error);
            SystemSettings.showNotification('فشل في إعادة تعيين الإعدادات: ' + error.message, 'error');
        }
    },

    // Reset Setting
    async resetSetting(settingKey) {
        const field = document.querySelector(`[name="${settingKey}"]`);
        const settingField = field?.closest('.setting-field');
        
        if (!settingField) return;

        // Find the setting data
        let settingData = null;
        Object.values(this.state.settings).forEach(categorySettings => {
            const found = categorySettings.find(s => s.setting_key === settingKey);
            if (found) settingData = found;
        });

        if (!settingData || !settingData.default_value) {
            SystemSettings.showNotification('لا توجد قيمة افتراضية لهذا الإعداد', 'warning');
            return;
        }

        // Reset field value
        switch (settingData.data_type) {
            case 'boolean':
                field.checked = settingData.default_value;
                break;
            case 'json':
                field.value = JSON.stringify(settingData.default_value, null, 2);
                break;
            default:
                field.value = settingData.default_value;
        }

        // Trigger change event
        field.dispatchEvent(new Event('change', { bubbles: true }));
    },

    // Show Add Modal
    showAddModal() {
        this.showModal('إضافة إعداد جديد');
        this.resetForm();
    },

    // Edit Setting
    editSetting(settingKey) {
        // Find the setting data
        let settingData = null;
        Object.values(this.state.settings).forEach(categorySettings => {
            const found = categorySettings.find(s => s.setting_key === settingKey);
            if (found) settingData = found;
        });

        if (!settingData) return;

        this.showModal('تعديل الإعداد');
        this.populateForm(settingData);
    },

    // Show Modal
    showModal(title) {
        const modal = document.getElementById('settingModal');
        const modalTitle = document.getElementById('modalTitle');
        
        if (modal && modalTitle) {
            modalTitle.textContent = title;
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    },

    // Close Modal
    closeModal() {
        const modal = document.getElementById('settingModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    },

    // Reset Form
    resetForm() {
        const form = document.getElementById('settingForm');
        if (form) {
            form.reset();
        }
    },

    // Populate Form
    populateForm(setting) {
        const fields = ['setting_key', 'data_type', 'category', 'description_ar', 'description_en'];
        
        fields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                element.value = setting[field] || '';
            }
        });

        // Set setting value based on type
        const valueField = document.getElementById('setting_value');
        if (valueField && setting.setting_value !== undefined) {
            if (setting.data_type === 'json') {
                valueField.value = JSON.stringify(setting.setting_value, null, 2);
            } else {
                valueField.value = setting.setting_value;
            }
        }

        // Set validation rules
        const rulesField = document.getElementById('validation_rules');
        if (rulesField && setting.validation_rules) {
            rulesField.value = JSON.stringify(setting.validation_rules, null, 2);
        }

        // Set public checkbox
        const publicField = document.getElementById('is_public');
        if (publicField) {
            publicField.checked = setting.is_public;
        }
    },

    // Save Setting
    async saveSetting() {
        const form = document.getElementById('settingForm');
        if (!form) return;

        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Convert validation rules from JSON
        if (data.validation_rules) {
            try {
                data.validation_rules = JSON.parse(data.validation_rules);
            } catch {
                SystemSettings.showNotification('تنسيق قواعد التحقق غير صحيح', 'error');
                return;
            }
        }

        // Convert checkbox
        data.is_public = document.getElementById('is_public').checked;

        try {
            SystemSettings.showLoading('.modal-content', 'حفظ الإعداد...');

            const response = await fetch(this.config.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في حفظ الإعداد');
            }

            SystemSettings.showNotification('تم حفظ الإعداد بنجاح', 'success');
            this.closeModal();
            await this.loadSettings();

        } catch (error) {
            console.error('Error saving setting:', error);
            SystemSettings.showNotification('فشل في حفظ الإعداد: ' + error.message, 'error');
        } finally {
            SystemSettings.hideLoading('.modal-content');
        }
    },

    // Backup Settings
    async backupSettings() {
        try {
            const response = await fetch(`${this.config.apiEndpoint}?action=backup`);
            
            if (response.ok) {
                // File download will be handled by the browser
                SystemSettings.showNotification('تم تنزيل النسخة الاحتياطية بنجاح', 'success');
            } else {
                throw new Error('فشل في إنشاء النسخة الاحتياطية');
            }

        } catch (error) {
            console.error('Error backing up settings:', error);
            SystemSettings.showNotification('فشل في إنشاء النسخة الاحتياطية: ' + error.message, 'error');
        }
    },

    // Restore Settings
    async restoreSettings(file) {
        const formData = new FormData();
        formData.append('backup_file', file);

        try {
            SystemSettings.showLoading('.settings-container', 'استعادة الإعدادات...');

            const response = await fetch(`${this.config.apiEndpoint}?action=restore`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'فشل في استعادة الإعدادات');
            }

            SystemSettings.showNotification('تم استعادة الإعدادات بنجاح', 'success');
            await this.loadSettings();

        } catch (error) {
            console.error('Error restoring settings:', error);
            SystemSettings.showNotification('فشل في استعادة الإعدادات: ' + error.message, 'error');
        } finally {
            SystemSettings.hideLoading('.settings-container');
        }
    }
};

// Global save function for SystemSettings
window.saveGeneral = async function(isAutoSave = false) {
    await GeneralManager.saveAllSettings(isAutoSave);
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('general-settings.html')) {
        GeneralManager.init();
    }
});

// Make globally available
window.GeneralManager = GeneralManager;
