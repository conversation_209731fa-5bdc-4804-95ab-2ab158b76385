<?php
header('Content-Type: text/plain; charset=utf-8');

echo "=== Web PHP Diagnostic ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Server: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "Current Time: " . date('Y-m-d H:i:s') . "\n";

echo "\n=== Environment Variables ===\n";
$envPath = __DIR__ . '/../../.env';
echo "Looking for .env at: $envPath\n";

if (file_exists($envPath)) {
    echo "✓ .env file found\n";
    $envContent = file_get_contents($envPath);
    $lines = explode("\n", $envContent);
    foreach ($lines as $line) {
        if (strpos(trim($line), 'DB_') === 0) {
            echo "  " . trim($line) . "\n";
        }
    }
} else {
    echo "✗ .env file not found\n";
}

echo "\n=== Database Test ===\n";
try {
    // Use values from .env
    $host = 'localhost';
    $port = '3307';
    $dbname = 'mossab-landing-page';
    $username = 'root';
    $password = '';
    
    echo "Testing connection: $username@$host:$port/$dbname\n";
    
    $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "✓ Connected to MySQL server\n";
    
    // Check database
    $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Database '$dbname' exists\n";
        
        $pdo->exec("USE `$dbname`");
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "✓ Tables (" . count($tables) . "): " . implode(', ', $tables) . "\n";
    } else {
        echo "! Database '$dbname' does not exist - creating...\n";
        $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✓ Database created successfully\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
