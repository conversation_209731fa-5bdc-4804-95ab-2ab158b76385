<?php
/**
 * Quick API Test - Direct endpoint testing
 */

header('Content-Type: text/html; charset=utf-8');

$endpoint = $_GET['endpoint'] ?? '';
$action = $_GET['action'] ?? 'get';

if (empty($endpoint)) {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>اختبار API سريع</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; direction: rtl; }
            .endpoint-list { list-style: none; padding: 0; }
            .endpoint-list li { margin: 10px 0; }
            .endpoint-list a { 
                display: block; 
                padding: 15px; 
                background: #f8f9fa; 
                border: 1px solid #ddd; 
                border-radius: 8px; 
                text-decoration: none; 
                color: #333;
                transition: all 0.3s ease;
            }
            .endpoint-list a:hover { 
                background: #e9ecef; 
                transform: translateX(-5px);
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 12px;
                text-align: center;
                margin-bottom: 30px;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔧 اختبار API سريع</h1>
            <p>اختر نقطة API للاختبار</p>
        </div>
        
        <ul class="endpoint-list">
            <li><a href="?endpoint=categories">📂 الفئات (Categories)</a></li>
            <li><a href="?endpoint=payment-settings">💳 إعدادات الدفع (Payment Settings)</a></li>
            <li><a href="?endpoint=general-settings">⚙️ الإعدادات العامة (General Settings)</a></li>
            <li><a href="?endpoint=users">👥 إدارة المستخدمين (Users)</a></li>
            <li><a href="?endpoint=roles">🔐 إدارة الأدوار (Roles)</a></li>
            <li><a href="?endpoint=subscriptions">📋 إدارة الاشتراكات (Subscriptions)</a></li>
            <li><a href="?endpoint=security-settings">🛡️ إعدادات الأمان (Security Settings)</a></li>
            <li><a href="?endpoint=dashboard-stats">📊 إحصائيات لوحة المعلومات (Dashboard Stats)</a></li>
        </ul>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="index.html" style="background: #6c757d; color: white; padding: 10px 20px; border-radius: 6px; text-decoration: none;">
                ← العودة للوحة التحكم
            </a>
        </div>
    </body>
    </html>
    <?php
    exit();
}

// Test the specific endpoint
$apiUrl = "../php/api/{$endpoint}.php";

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>اختبار $endpoint</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; direction: rtl; }
        .result { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; }
        .nav { margin: 20px 0; }
        .nav a { background: #007bff; color: white; padding: 8px 15px; border-radius: 4px; text-decoration: none; margin: 0 5px; }
    </style>
</head>
<body>";

echo "<div class='header'>";
echo "<h1>🔧 اختبار API: $endpoint</h1>";
echo "</div>";

echo "<div class='nav'>";
echo "<a href='?endpoint=$endpoint&action=get'>GET</a>";
echo "<a href='?endpoint=$endpoint&action=test'>TEST</a>";
echo "<a href='quick-api-test.php'>← العودة للقائمة</a>";
echo "</div>";

// Check if file exists
if (!file_exists($apiUrl)) {
    echo "<div class='result error'>";
    echo "<h3>❌ خطأ: الملف غير موجود</h3>";
    echo "<p>المسار: $apiUrl</p>";
    echo "</div>";
    echo "</body></html>";
    exit();
}

echo "<div class='result info'>";
echo "<h3>📁 معلومات الملف</h3>";
echo "<p><strong>المسار:</strong> $apiUrl</p>";
echo "<p><strong>الحجم:</strong> " . number_format(filesize($apiUrl)) . " بايت</p>";
echo "<p><strong>آخر تعديل:</strong> " . date('Y-m-d H:i:s', filemtime($apiUrl)) . "</p>";
echo "</div>";

// Test the endpoint
echo "<div class='result'>";
echo "<h3>🚀 اختبار الاستجابة</h3>";

try {
    // Start output buffering to capture any output
    ob_start();
    
    // Set up environment for the API call
    $_GET['action'] = $action;
    
    // Include the API file
    include $apiUrl;
    
    // Get the output
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo "<div class='success'>";
        echo "<h4>✅ نجح الاختبار - تم إرجاع استجابة</h4>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        echo "</div>";
        
        // Try to decode JSON
        $jsonData = json_decode($output, true);
        if ($jsonData !== null) {
            echo "<div class='success'>";
            echo "<h4>📋 البيانات المفكوكة (JSON)</h4>";
            echo "<pre>" . print_r($jsonData, true) . "</pre>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<h4>⚠️ تحذير: لم يتم إرجاع أي استجابة</h4>";
        echo "<p>قد يكون هناك خطأ في الكود أو إعادة توجيه</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h4>❌ خطأ في التنفيذ</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
} catch (Error $e) {
    echo "<div class='error'>";
    echo "<h4>❌ خطأ فادح</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Test database connection if it's a database-related endpoint
if (in_array($endpoint, ['general-settings', 'security-settings', 'categories', 'users', 'roles', 'subscriptions'])) {
    echo "<div class='result'>";
    echo "<h3>🗄️ اختبار قاعدة البيانات</h3>";
    
    try {
        require_once '../php/config.php';
        $pdo = getPDOConnection();
        
        $stmt = $pdo->query("SELECT 1");
        echo "<div class='success'>";
        echo "<h4>✅ اتصال قاعدة البيانات ناجح</h4>";
        echo "</div>";
        
        // Test specific tables based on endpoint
        $tables = [
            'general-settings' => 'general_settings',
            'security-settings' => 'security_settings',
            'categories' => 'categories',
            'users' => 'users',
            'roles' => 'user_roles',
            'subscriptions' => 'subscription_plans'
        ];
        
        if (isset($tables[$endpoint])) {
            $tableName = $tables[$endpoint];
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
                if ($stmt->rowCount() > 0) {
                    echo "<div class='success'>";
                    echo "<h4>✅ جدول $tableName موجود</h4>";
                    
                    // Count records
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM $tableName");
                    $count = $stmt->fetch()['count'];
                    echo "<p>عدد السجلات: $count</p>";
                    echo "</div>";
                } else {
                    echo "<div class='error'>";
                    echo "<h4>❌ جدول $tableName غير موجود</h4>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>";
                echo "<h4>❌ خطأ في فحص الجدول $tableName</h4>";
                echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h4>❌ فشل اتصال قاعدة البيانات</h4>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "</body></html>";
?>
