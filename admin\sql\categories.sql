-- Categories Management Tables
-- جداول إدارة الفئات

-- Create categories table
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name_ar` varchar(255) NOT NULL,
  `name_en` varchar(255) DEFAULT NULL,
  `name_fr` varchar(255) DEFAULT NULL,
  `slug` varchar(255) NOT NULL UNIQUE,
  `description_ar` text DEFAULT NULL,
  `description_en` text DEFAULT NULL,
  `description_fr` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `image` varchar(500) DEFAULT NULL,
  `icon` varchar(100) DEFAULT NULL,
  `color` varchar(7) DEFAULT '#667eea',
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `is_featured` tinyint(1) DEFAULT 0,
  `meta_title_ar` varchar(255) DEFAULT NULL,
  `meta_title_en` varchar(255) DEFAULT NULL,
  `meta_title_fr` varchar(255) DEFAULT NULL,
  `meta_description_ar` text DEFAULT NULL,
  `meta_description_en` text DEFAULT NULL,
  `meta_description_fr` text DEFAULT NULL,
  `meta_keywords_ar` text DEFAULT NULL,
  `meta_keywords_en` text DEFAULT NULL,
  `meta_keywords_fr` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_slug` (`slug`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_created_by` (`created_by`),
  FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL,
  FULLTEXT KEY `ft_search_ar` (`name_ar`, `description_ar`),
  FULLTEXT KEY `ft_search_en` (`name_en`, `description_en`),
  FULLTEXT KEY `ft_search_fr` (`name_fr`, `description_fr`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create category_translations table for additional language support
CREATE TABLE IF NOT EXISTS `category_translations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `language_code` varchar(5) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_category_language` (`category_id`, `language_code`),
  KEY `idx_language_code` (`language_code`),
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create category_hierarchy table for better hierarchy management
CREATE TABLE IF NOT EXISTS `category_hierarchy` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ancestor_id` int(11) NOT NULL,
  `descendant_id` int(11) NOT NULL,
  `depth` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_hierarchy` (`ancestor_id`, `descendant_id`),
  KEY `idx_ancestor` (`ancestor_id`),
  KEY `idx_descendant` (`descendant_id`),
  KEY `idx_depth` (`depth`),
  FOREIGN KEY (`ancestor_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`descendant_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create category_stats table for analytics
CREATE TABLE IF NOT EXISTS `category_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `products_count` int(11) DEFAULT 0,
  `subcategories_count` int(11) DEFAULT 0,
  `views_count` int(11) DEFAULT 0,
  `last_product_added` timestamp NULL DEFAULT NULL,
  `last_viewed` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_category_stats` (`category_id`),
  FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default categories
INSERT INTO `categories` (`name_ar`, `name_en`, `name_fr`, `slug`, `description_ar`, `description_en`, `description_fr`, `parent_id`, `icon`, `color`, `sort_order`, `is_active`, `is_featured`) VALUES
('الكتب الإلكترونية', 'E-Books', 'Livres Électroniques', 'e-books', 'مجموعة شاملة من الكتب الإلكترونية في مختلف المجالات', 'Comprehensive collection of e-books in various fields', 'Collection complète de livres électroniques dans divers domaines', NULL, 'fas fa-book', '#667eea', 1, 1, 1),
('الكتب التعليمية', 'Educational Books', 'Livres Éducatifs', 'educational-books', 'كتب تعليمية ومناهج دراسية', 'Educational books and curricula', 'Livres éducatifs et programmes scolaires', 1, 'fas fa-graduation-cap', '#28a745', 1, 1, 1),
('الكتب الأدبية', 'Literature Books', 'Livres Littéraires', 'literature-books', 'روايات وقصص وشعر', 'Novels, stories and poetry', 'Romans, histoires et poésie', 1, 'fas fa-feather-alt', '#dc3545', 2, 1, 1),
('الكتب العلمية', 'Scientific Books', 'Livres Scientifiques', 'scientific-books', 'كتب في العلوم والتكنولوجيا', 'Books in science and technology', 'Livres de science et technologie', 1, 'fas fa-flask', '#17a2b8', 3, 1, 1),
('كتب الأطفال', 'Children Books', 'Livres pour Enfants', 'children-books', 'كتب مخصصة للأطفال', 'Books designed for children', 'Livres conçus pour les enfants', 1, 'fas fa-child', '#ffc107', 4, 1, 1),
('المنتجات الرقمية', 'Digital Products', 'Produits Numériques', 'digital-products', 'منتجات رقمية متنوعة', 'Various digital products', 'Divers produits numériques', NULL, 'fas fa-laptop', '#6f42c1', 2, 1, 1),
('البرمجيات', 'Software', 'Logiciels', 'software', 'برامج وتطبيقات', 'Programs and applications', 'Programmes et applications', 6, 'fas fa-code', '#fd7e14', 1, 1, 0),
('القوالب والتصاميم', 'Templates & Designs', 'Modèles et Designs', 'templates-designs', 'قوالب مواقع وتصاميم جرافيك', 'Website templates and graphic designs', 'Modèles de sites web et designs graphiques', 6, 'fas fa-paint-brush', '#e83e8c', 2, 1, 0),
('الدورات التدريبية', 'Training Courses', 'Cours de Formation', 'training-courses', 'دورات تدريبية أونلاين', 'Online training courses', 'Cours de formation en ligne', NULL, 'fas fa-chalkboard-teacher', '#20c997', 3, 1, 1),
('دورات البرمجة', 'Programming Courses', 'Cours de Programmation', 'programming-courses', 'دورات تعلم البرمجة', 'Programming learning courses', 'Cours d\'apprentissage de la programmation', 9, 'fas fa-laptop-code', '#6610f2', 1, 1, 1),
('دورات التصميم', 'Design Courses', 'Cours de Design', 'design-courses', 'دورات تعلم التصميم الجرافيكي', 'Graphic design learning courses', 'Cours d\'apprentissage du design graphique', 9, 'fas fa-palette', '#fd7e14', 2, 1, 1),
('دورات التسويق', 'Marketing Courses', 'Cours de Marketing', 'marketing-courses', 'دورات التسويق الرقمي', 'Digital marketing courses', 'Cours de marketing numérique', 9, 'fas fa-bullhorn', '#dc3545', 3, 1, 1);

-- Insert category hierarchy data
INSERT INTO `category_hierarchy` (`ancestor_id`, `descendant_id`, `depth`) 
SELECT c1.id, c2.id, 
  CASE 
    WHEN c1.id = c2.id THEN 0
    WHEN c2.parent_id = c1.id THEN 1
    ELSE 2
  END as depth
FROM `categories` c1, `categories` c2
WHERE c1.id = c2.id 
   OR c2.parent_id = c1.id 
   OR (c2.parent_id IS NOT NULL AND c1.id IN (
     SELECT parent_id FROM `categories` WHERE id = c2.parent_id
   ));

-- Initialize category stats
INSERT INTO `category_stats` (`category_id`, `products_count`, `subcategories_count`, `views_count`)
SELECT 
  c.id,
  0 as products_count,
  (SELECT COUNT(*) FROM `categories` sub WHERE sub.parent_id = c.id) as subcategories_count,
  0 as views_count
FROM `categories` c;

-- Create indexes for better performance
CREATE INDEX `idx_categories_search` ON `categories` (`name_ar`, `name_en`, `name_fr`);
CREATE INDEX `idx_categories_active_featured` ON `categories` (`is_active`, `is_featured`, `sort_order`);
CREATE INDEX `idx_categories_parent_sort` ON `categories` (`parent_id`, `sort_order`);

-- Create triggers for automatic hierarchy management
DELIMITER ;;

CREATE TRIGGER `categories_after_insert` AFTER INSERT ON `categories`
FOR EACH ROW
BEGIN
  -- Insert self-reference
  INSERT INTO `category_hierarchy` (`ancestor_id`, `descendant_id`, `depth`) 
  VALUES (NEW.id, NEW.id, 0);
  
  -- Insert parent relationships if parent exists
  IF NEW.parent_id IS NOT NULL THEN
    INSERT INTO `category_hierarchy` (`ancestor_id`, `descendant_id`, `depth`)
    SELECT h.ancestor_id, NEW.id, h.depth + 1
    FROM `category_hierarchy` h
    WHERE h.descendant_id = NEW.parent_id;
  END IF;
  
  -- Update parent category stats
  IF NEW.parent_id IS NOT NULL THEN
    UPDATE `category_stats` 
    SET `subcategories_count` = `subcategories_count` + 1
    WHERE `category_id` = NEW.parent_id;
  END IF;
END;;

CREATE TRIGGER `categories_after_update` AFTER UPDATE ON `categories`
FOR EACH ROW
BEGIN
  -- Handle parent change
  IF OLD.parent_id != NEW.parent_id OR (OLD.parent_id IS NULL AND NEW.parent_id IS NOT NULL) OR (OLD.parent_id IS NOT NULL AND NEW.parent_id IS NULL) THEN
    -- Remove old hierarchy relationships (except self)
    DELETE FROM `category_hierarchy` 
    WHERE `descendant_id` = NEW.id AND `depth` > 0;
    
    -- Update old parent stats
    IF OLD.parent_id IS NOT NULL THEN
      UPDATE `category_stats` 
      SET `subcategories_count` = `subcategories_count` - 1
      WHERE `category_id` = OLD.parent_id;
    END IF;
    
    -- Insert new hierarchy relationships
    IF NEW.parent_id IS NOT NULL THEN
      INSERT INTO `category_hierarchy` (`ancestor_id`, `descendant_id`, `depth`)
      SELECT h.ancestor_id, NEW.id, h.depth + 1
      FROM `category_hierarchy` h
      WHERE h.descendant_id = NEW.parent_id;
      
      -- Update new parent stats
      UPDATE `category_stats` 
      SET `subcategories_count` = `subcategories_count` + 1
      WHERE `category_id` = NEW.parent_id;
    END IF;
  END IF;
END;;

CREATE TRIGGER `categories_after_delete` AFTER DELETE ON `categories`
FOR EACH ROW
BEGIN
  -- Update parent category stats
  IF OLD.parent_id IS NOT NULL THEN
    UPDATE `category_stats` 
    SET `subcategories_count` = `subcategories_count` - 1
    WHERE `category_id` = OLD.parent_id;
  END IF;
END;;

DELIMITER ;
