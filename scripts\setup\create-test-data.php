<?php
require_once 'php/config.php';

echo "<h1>Création de Données de Test</h1>";

try {
    // Vérifier si des produits existent
    $stmt = $conn->query("SELECT COUNT(*) as count FROM produits");
    $productCount = $stmt->fetch()['count'];
    
    if ($productCount == 0) {
        echo "<h2>📦 Création de produits de test...</h2>";
        
        $products = [
            ['titre' => 'كتاب التسويق الرقمي', 'description' => 'دليل شامل للتسويق الرقمي', 'prix' => 2500, 'actif' => 1],
            ['titre' => 'دورة البرمجة', 'description' => 'تعلم البرمجة من الصفر', 'prix' => 5000, 'actif' => 1],
            ['titre' => 'كتاب ريادة الأعمال', 'description' => 'كيف تبدأ مشروعك الخاص', 'prix' => 3000, 'actif' => 1],
            ['titre' => 'دورة التصميم', 'description' => 'أساسيات التصميم الجرافيكي', 'prix' => 4000, 'actif' => 0],
        ];
        
        $stmt = $conn->prepare("INSERT INTO produits (titre, description, prix, actif) VALUES (?, ?, ?, ?)");
        
        foreach ($products as $product) {
            $stmt->execute([$product['titre'], $product['description'], $product['prix'], $product['actif']]);
            echo "<p>✅ Produit créé : {$product['titre']}</p>";
        }
    } else {
        echo "<p>✅ $productCount produits existent déjà</p>";
    }
    
    // Vérifier si des commandes existent
    $stmt = $conn->query("SELECT COUNT(*) as count FROM commandes");
    $orderCount = $stmt->fetch()['count'];
    
    if ($orderCount == 0) {
        echo "<h2>🛒 Création de commandes de test...</h2>";
        
        $orders = [
            ['nom_client' => 'أحمد محمد', 'email_client' => '<EMAIL>', 'telephone_client' => '0123456789', 'montant_total' => 2500, 'statut' => 'payé'],
            ['nom_client' => 'فاطمة علي', 'email_client' => '<EMAIL>', 'telephone_client' => '0123456790', 'montant_total' => 5000, 'statut' => 'en_attente'],
            ['nom_client' => 'محمد حسن', 'email_client' => '<EMAIL>', 'telephone_client' => '0123456791', 'montant_total' => 3000, 'statut' => 'expédié'],
            ['nom_client' => 'عائشة سالم', 'email_client' => '<EMAIL>', 'telephone_client' => '0123456792', 'montant_total' => 4000, 'statut' => 'en_attente'],
        ];
        
        $stmt = $conn->prepare("INSERT INTO commandes (nom_client, email_client, telephone_client, montant_total, statut, date_commande) VALUES (?, ?, ?, ?, ?, NOW())");
        
        foreach ($orders as $order) {
            $stmt->execute([$order['nom_client'], $order['email_client'], $order['telephone_client'], $order['montant_total'], $order['statut']]);
            echo "<p>✅ Commande créée : {$order['nom_client']} - {$order['montant_total']} دج</p>";
        }
    } else {
        echo "<p>✅ $orderCount commandes existent déjà</p>";
    }
    
    // Vérifier si des landing pages existent
    $stmt = $conn->query("SELECT COUNT(*) as count FROM landing_pages");
    $landingPageCount = $stmt->fetch()['count'];
    
    if ($landingPageCount == 0) {
        echo "<h2>🌐 Création de landing pages de test...</h2>";
        
        // Récupérer les IDs des produits
        $stmt = $conn->query("SELECT id, titre FROM produits LIMIT 3");
        $products = $stmt->fetchAll();
        
        foreach ($products as $product) {
            $landingPage = [
                'produit_id' => $product['id'],
                'titre' => 'صفحة هبوط - ' . $product['titre'],
                'contenu_droit' => '<h2>اكتشف ' . $product['titre'] . '</h2><p>منتج رائع يستحق الاقتناء</p>',
                'contenu_gauche' => '<h3>لماذا تختار هذا المنتج؟</h3><ul><li>جودة عالية</li><li>سعر مناسب</li><li>دعم فني</li></ul>',
                'lien_url' => '/landing-page-template.php?id=' . $product['id']
            ];
            
            $stmt = $conn->prepare("INSERT INTO landing_pages (produit_id, titre, contenu_droit, contenu_gauche, lien_url) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$landingPage['produit_id'], $landingPage['titre'], $landingPage['contenu_droit'], $landingPage['contenu_gauche'], $landingPage['lien_url']]);
            
            $landingPageId = $conn->lastInsertId();
            
            // Ajouter quelques images de test
            $images = [
                '/uploads/test-image-1.jpg',
                '/uploads/test-image-2.jpg'
            ];
            
            $stmt = $conn->prepare("INSERT INTO landing_page_images (landing_page_id, image_url, ordre) VALUES (?, ?, ?)");
            foreach ($images as $index => $image) {
                $stmt->execute([$landingPageId, $image, $index + 1]);
            }
            
            echo "<p>✅ Landing page créée : {$landingPage['titre']}</p>";
        }
    } else {
        echo "<p>✅ $landingPageCount landing pages existent déjà</p>";
    }
    
    echo "<h2>🎉 Données de test créées avec succès !</h2>";
    echo "<p><a href='admin/index.html'>Aller au panneau d'administration</a></p>";
    echo "<p><a href='check-database.php'>Vérifier la base de données</a></p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ Erreur lors de la création des données de test</h2>";
    echo "<p>Erreur : " . $e->getMessage() . "</p>";
}
?>
