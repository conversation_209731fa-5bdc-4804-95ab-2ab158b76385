<?php

/**
 * Security Settings API
 * Handles security configuration and monitoring
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../config.php';

// Get PDO connection using the existing config
try {
    $pdo = getPDOConnection();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'فشل في الاتصال بقاعدة البيانات: ' . $e->getMessage()
    ]);
    exit();
}

/**
 * Enhanced Security Manager Class
 * إدارة شاملة للأمان مع ميزات متقدمة
 */
class SecurityManager
{
    private $pdo;

    public function __construct($pdo)
    {
        $this->pdo = $pdo;
    }

    /**
     * Get security setting by key
     */
    public function getSetting($key)
    {
        $stmt = $this->pdo->prepare("SELECT setting_value, setting_type FROM security_settings WHERE setting_key = ? AND is_active = 1");
        $stmt->execute([$key]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result) return null;

        // Convert value based on type
        switch ($result['setting_type']) {
            case 'boolean':
                return (bool)$result['setting_value'];
            case 'integer':
                return (int)$result['setting_value'];
            case 'json':
                return json_decode($result['setting_value'], true);
            default:
                return $result['setting_value'];
        }
    }

    /**
     * Update security setting
     */
    public function updateSetting($key, $value, $type = 'string')
    {
        $stmt = $this->pdo->prepare("
            INSERT INTO security_settings (setting_key, setting_value, setting_type, category)
            VALUES (?, ?, ?, 'custom')
            ON DUPLICATE KEY UPDATE
            setting_value = VALUES(setting_value),
            setting_type = VALUES(setting_type),
            updated_at = CURRENT_TIMESTAMP
        ");

        $valueToStore = $type === 'json' ? json_encode($value) : $value;
        return $stmt->execute([$key, $valueToStore, $type]);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent($action, $description, $severity = 'medium', $status = 'success', $metadata = null)
    {
        $stmt = $this->pdo->prepare("
            INSERT INTO security_audit_logs (action, description, ip_address, user_agent, severity, status, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        return $stmt->execute([
            $action,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $severity,
            $status,
            $metadata ? json_encode($metadata) : null
        ]);
    }

    /**
     * Generate 2FA QR Code
     */
    public function generate2FASecret($userId)
    {
        // Generate a random secret for 2FA
        $secret = $this->generateRandomSecret();

        // Store the secret in database
        $stmt = $this->pdo->prepare("
            INSERT INTO user_2fa (user_id, secret_key, is_enabled)
            VALUES (?, ?, 0)
            ON DUPLICATE KEY UPDATE
            secret_key = VALUES(secret_key),
            updated_at = CURRENT_TIMESTAMP
        ");
        $stmt->execute([$userId, $secret]);

        return $secret;
    }

    /**
     * Generate random secret for 2FA
     */
    private function generateRandomSecret($length = 32)
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';
        for ($i = 0; $i < $length; $i++) {
            $secret .= $chars[random_int(0, strlen($chars) - 1)];
        }
        return $secret;
    }

    /**
     * Get security dashboard data
     */
    public function getDashboardData()
    {
        // Get recent security events
        $stmt = $this->pdo->prepare("
            SELECT event_type as action, description, severity, 'success' as status, created_at
            FROM security_logs
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $stmt->execute();
        $recentEvents = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get security metrics for last 24 hours
        $stmt = $this->pdo->prepare("
            SELECT
                COUNT(*) as total_events,
                SUM(CASE WHEN severity IN ('high', 'critical') THEN 1 ELSE 0 END) as high_severity,
                COUNT(DISTINCT ip_address) as unique_ips
            FROM security_logs
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $stmt->execute();
        $metrics = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get blocked IPs count
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as blocked_ips FROM ip_management WHERE type = 'blacklist' AND is_active = 1");
        $stmt->execute();
        $blockedIps = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get active sessions count
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as active_sessions FROM active_sessions WHERE last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)");
        $stmt->execute();
        $activeSessions = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'recent_events' => $recentEvents,
            'metrics' => array_merge($metrics ?: [], $blockedIps ?: [], $activeSessions ?: []),
            'security_level' => $this->calculateSecurityLevel($metrics ?: [])
        ];
    }

    /**
     * Calculate overall security level
     */
    private function calculateSecurityLevel($metrics)
    {
        $score = 100;

        // Deduct points for high severity events
        $score -= min(($metrics['high_severity'] ?? 0) * 10, 40);

        if ($score >= 90) return 'عالي';
        if ($score >= 70) return 'متوسط';
        return 'منخفض';
    }
}

// Initialize Security Manager
$securityManager = new SecurityManager($pdo);

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? 'get';

    switch ($action) {
        case 'get':
        case 'get_settings':
            getSecuritySettings();
            break;

        case 'update':
            updateSecuritySettings();
            break;

        case 'save_authentication':
            saveAuthenticationSettings();
            break;

        case 'logs':
            getSecurityLogs();
            break;

        case 'test':
            testSecurity();
            break;

        case 'audit_logs':
            getAuditLogs();
            break;

        case 'active_sessions':
            getActiveSessions();
            break;

        case 'ip_whitelist':
            getIPWhitelist();
            break;

        case 'ip_blacklist':
            getIPBlacklist();
            break;

        case 'add_ip_whitelist':
            addIPToWhitelist();
            break;

        case 'add_ip_blacklist':
            addIPToBlacklist();
            break;

        case 'terminate_session':
            terminateSession();
            break;

        case 'generate_2fa_qr':
            generate2FAQRCode();
            break;

        case 'security_stats':
            getSecurityStatsAdvanced();
            break;

        case 'dashboard':
            getSecurityDashboard();
            break;

        case 'password_policy':
            getPasswordPolicy();
            break;

        case 'update_password_policy':
            updatePasswordPolicy();
            break;

        case 'threat_detection':
            getThreatDetection();
            break;

        case 'backup_settings':
            getBackupSettings();
            break;

        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}

/**
 * Get security settings
 */
function getSecuritySettings()
{
    global $pdo;

    try {
        // Create enhanced security settings table
        $pdo->exec("CREATE TABLE IF NOT EXISTS security_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(255) UNIQUE NOT NULL,
            setting_value TEXT,
            category VARCHAR(50) NOT NULL,
            description TEXT,
            is_encrypted BOOLEAN DEFAULT FALSE,
            last_modified_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_setting_key (setting_key)
        )");

        // Create comprehensive security logs table
        $pdo->exec("CREATE TABLE IF NOT EXISTS security_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            event_type VARCHAR(100) NOT NULL,
            severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
            description TEXT,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            request_data TEXT,
            geo_location TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_type (event_type),
            INDEX idx_severity (severity),
            INDEX idx_created_at (created_at)
        )");

        // Create active sessions table
        $pdo->exec("CREATE TABLE IF NOT EXISTS active_sessions (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            session_id VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            last_activity TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_session_id (session_id),
            INDEX idx_expires_at (expires_at)
        )");

        // Create IP whitelist/blacklist table
        $pdo->exec("CREATE TABLE IF NOT EXISTS ip_access_list (
            id INT PRIMARY KEY AUTO_INCREMENT,
            ip_address VARCHAR(45) NOT NULL,
            type ENUM('whitelist', 'blacklist') NOT NULL,
            reason TEXT,
            added_by INT,
            expires_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_ip (ip_address, type),
            INDEX idx_type (type),
            INDEX idx_expires_at (expires_at)
        )");

        // Create 2FA backup codes table
        $pdo->exec("CREATE TABLE IF NOT EXISTS two_factor_backup_codes (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            backup_code VARCHAR(255) NOT NULL,
            is_used BOOLEAN DEFAULT FALSE,
            used_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_code (user_id, backup_code),
            INDEX idx_user_id (user_id)
        )");

        // Insert default security settings
        $defaultSettings = [
            ['session_timeout', '3600', 'مهلة انتهاء الجلسة (بالثواني)'],
            ['max_login_attempts', '5', 'الحد الأقصى لمحاولات تسجيل الدخول'],
            ['password_min_length', '8', 'الحد الأدنى لطول كلمة المرور'],
            ['require_strong_password', '1', 'طلب كلمة مرور قوية'],
            ['enable_two_factor', '0', 'تفعيل المصادقة الثنائية'],
            ['auto_logout', '1', 'تسجيل الخروج التلقائي'],
            ['ip_whitelist', '', 'قائمة IP المسموحة'],
            ['enable_ssl', '1', 'إجبار استخدام SSL']
        ];

        foreach ($defaultSettings as $setting) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO security_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }

        // Get all settings
        $stmt = $pdo->query("SELECT * FROM security_settings ORDER BY setting_key");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Convert to key-value format
        $settingsData = [];
        foreach ($settings as $setting) {
            $settingsData[$setting['setting_key']] = [
                'value' => $setting['setting_value'],
                'description' => $setting['description'],
                'updated_at' => $setting['updated_at']
            ];
        }

        // Get security statistics
        $stats = getSecurityStats();

        echo json_encode([
            'success' => true,
            'data' => [
                'settings' => $settingsData,
                'stats' => $stats
            ],
            'message' => 'تم تحميل إعدادات الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل إعدادات الأمان: ' . $e->getMessage());
    }
}

/**
 * Update security settings
 */
function updateSecuritySettings()
{
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['settings'])) {
            throw new Exception('بيانات غير صحيحة');
        }

        $pdo->beginTransaction();

        foreach ($input['settings'] as $key => $value) {
            $stmt = $pdo->prepare("UPDATE security_settings SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$value, $key]);
        }

        // Log security settings change
        logSecurityEvent('settings_updated', 'تم تحديث إعدادات الأمان');

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث إعدادات الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception('فشل في تحديث إعدادات الأمان: ' . $e->getMessage());
    }
}

/**
 * Get security logs
 */
function getSecurityLogs()
{
    global $pdo;

    try {
        $limit = $_GET['limit'] ?? 50;
        $stmt = $pdo->prepare("SELECT * FROM security_logs ORDER BY created_at DESC LIMIT ?");
        $stmt->execute([$limit]);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $logs,
            'message' => 'تم تحميل سجلات الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل سجلات الأمان: ' . $e->getMessage());
    }
}

/**
 * Test security configuration
 */
function testSecurity()
{
    global $pdo;

    try {
        $tests = [
            'database_connection' => testDatabaseConnection(),
            'ssl_enabled' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
            'session_security' => session_status() === PHP_SESSION_ACTIVE,
            'file_permissions' => is_writable('../uploads/'),
        ];

        echo json_encode([
            'success' => true,
            'data' => $tests,
            'message' => 'تم اختبار الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في اختبار الأمان: ' . $e->getMessage());
    }
}

/**
 * Get security statistics
 */
function getSecurityStats()
{
    global $pdo;

    try {
        $stats = [];

        // Count recent login attempts
        $stmt = $pdo->query("SELECT COUNT(*) FROM security_logs WHERE event_type = 'login_attempt' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stats['login_attempts_24h'] = $stmt->fetchColumn();

        // Count failed logins
        $stmt = $pdo->query("SELECT COUNT(*) FROM security_logs WHERE event_type = 'login_failed' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stats['failed_logins_24h'] = $stmt->fetchColumn();

        // Security level calculation
        $securityLevel = calculateSecurityLevel();
        $stats['security_level'] = $securityLevel;

        return $stats;
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

/**
 * Log security event
 */
function logSecurityEvent($eventType, $description)
{
    global $pdo;

    try {
        $stmt = $pdo->prepare("INSERT INTO security_logs (event_type, description, ip_address, user_agent) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $eventType,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // Silent fail for logging
    }
}

/**
 * Test database connection
 */
function testDatabaseConnection()
{
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT 1");
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Calculate security level
 */
function calculateSecurityLevel()
{
    // Simple security level calculation
    $level = 'متوسط';

    // Check various security factors
    $factors = [
        isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
        session_status() === PHP_SESSION_ACTIVE,
        is_writable('../uploads/') === false, // Should not be writable for security
    ];

    $score = array_sum($factors);

    if ($score >= 2) {
        $level = 'عالي';
    } elseif ($score >= 1) {
        $level = 'متوسط';
    } else {
        $level = 'منخفض';
    }

    return $level;
}

/**
 * Save authentication settings
 */
function saveAuthenticationSettings()
{
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['settings'])) {
            throw new Exception('بيانات غير صالحة');
        }

        $settings = $input['settings'];

        // Validate settings
        if (!isset($settings['passwordMinLength']) || $settings['passwordMinLength'] < 6 || $settings['passwordMinLength'] > 32) {
            throw new Exception('طول كلمة المرور يجب أن يكون بين 6 و 32 حرف');
        }

        if (!isset($settings['sessionTimeout']) || $settings['sessionTimeout'] < 5 || $settings['sessionTimeout'] > 1440) {
            throw new Exception('مهلة الجلسة يجب أن تكون بين 5 و 1440 دقيقة');
        }

        if (!isset($settings['maxLoginAttempts']) || $settings['maxLoginAttempts'] < 3 || $settings['maxLoginAttempts'] > 10) {
            throw new Exception('عدد محاولات تسجيل الدخول يجب أن يكون بين 3 و 10');
        }

        // Prepare settings for database
        $settingsToSave = [
            'require_strong_password' => $settings['requireStrongPassword'] ? '1' : '0',
            'password_min_length' => (string)$settings['passwordMinLength'],
            'password_expiry' => (string)$settings['passwordExpiry'],
            'session_timeout' => (string)$settings['sessionTimeout'],
            'auto_logout' => $settings['autoLogout'] ? '1' : '0',
            'remember_me' => $settings['rememberMe'] ? '1' : '0',
            'enable_two_factor' => $settings['enableTwoFactor'] ? '1' : '0',
            'two_factor_method' => $settings['twoFactorMethod'],
            'max_login_attempts' => (string)$settings['maxLoginAttempts'],
            'lockout_duration' => (string)$settings['lockoutDuration'],
            'notify_failed_login' => $settings['notifyFailedLogin'] ? '1' : '0'
        ];

        // Save each setting
        foreach ($settingsToSave as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT INTO security_settings (setting_key, setting_value, updated_at)
                VALUES (?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                setting_value = VALUES(setting_value),
                updated_at = NOW()
            ");
            $stmt->execute([$key, $value]);
        }

        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ إعدادات المصادقة بنجاح'
        ]);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * Get audit logs
 */
function getAuditLogs()
{
    global $pdo;

    try {
        $limit = $_GET['limit'] ?? 50;
        $offset = $_GET['offset'] ?? 0;

        $stmt = $pdo->prepare("
            SELECT
                sl.id,
                sl.user_id,
                sl.event_type,
                sl.description,
                sl.ip_address,
                sl.user_agent,
                sl.created_at
            FROM security_logs sl
            ORDER BY sl.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $logs,
            'message' => 'تم تحميل سجلات التدقيق بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل سجلات التدقيق: ' . $e->getMessage());
    }
}

/**
 * Get active sessions
 */
function getActiveSessions()
{
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT
                as.id,
                as.user_id,
                as.session_id,
                as.ip_address,
                as.user_agent,
                as.last_activity,
                as.created_at
            FROM active_sessions as
            WHERE as.last_activity > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY as.last_activity DESC
        ");
        $stmt->execute();
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $sessions,
            'message' => 'تم تحميل الجلسات النشطة بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل الجلسات النشطة: ' . $e->getMessage());
    }
}

/**
 * Get IP whitelist
 */
function getIPWhitelist()
{
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT * FROM ip_access_list
            WHERE type = 'whitelist'
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        $whitelist = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $whitelist,
            'message' => 'تم تحميل القائمة البيضاء بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل القائمة البيضاء: ' . $e->getMessage());
    }
}

/**
 * Get IP blacklist
 */
function getIPBlacklist()
{
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT * FROM ip_access_list
            WHERE type = 'blacklist'
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        $blacklist = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'data' => $blacklist,
            'message' => 'تم تحميل القائمة السوداء بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل القائمة السوداء: ' . $e->getMessage());
    }
}

/**
 * Add IP to whitelist
 */
function addIPToWhitelist()
{
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['ip'])) {
            throw new Exception('عنوان IP مطلوب');
        }

        $ip = $input['ip'];
        $reason = $input['reason'] ?? '';

        $stmt = $pdo->prepare("
            INSERT INTO ip_access_list (ip_address, type, reason, created_at)
            VALUES (?, 'whitelist', ?, NOW())
        ");
        $stmt->execute([$ip, $reason]);

        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة IP إلى القائمة البيضاء بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في إضافة IP إلى القائمة البيضاء: ' . $e->getMessage());
    }
}

/**
 * Add IP to blacklist
 */
function addIPToBlacklist()
{
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['ip'])) {
            throw new Exception('عنوان IP مطلوب');
        }

        $ip = $input['ip'];
        $reason = $input['reason'] ?? '';

        $stmt = $pdo->prepare("
            INSERT INTO ip_access_list (ip_address, type, reason, created_at)
            VALUES (?, 'blacklist', ?, NOW())
        ");
        $stmt->execute([$ip, $reason]);

        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة IP إلى القائمة السوداء بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في إضافة IP إلى القائمة السوداء: ' . $e->getMessage());
    }
}

/**
 * Terminate session
 */
function terminateSession()
{
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['session_id'])) {
            throw new Exception('معرف الجلسة مطلوب');
        }

        $sessionId = $input['session_id'];

        $stmt = $pdo->prepare("DELETE FROM active_sessions WHERE session_id = ?");
        $stmt->execute([$sessionId]);

        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'تم إنهاء الجلسة بنجاح'
            ]);
        } else {
            throw new Exception('الجلسة غير موجودة');
        }
    } catch (Exception $e) {
        throw new Exception('فشل في إنهاء الجلسة: ' . $e->getMessage());
    }
}

/**
 * Generate 2FA QR Code
 */
function generate2FAQRCode()
{
    global $pdo;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['user_id'])) {
            throw new Exception('معرف المستخدم مطلوب');
        }

        $userId = $input['user_id'];

        // Generate a random secret
        $secret = bin2hex(random_bytes(16));

        // Store the secret in the database (you may need to add this column)
        $stmt = $pdo->prepare("
            UPDATE security_settings SET setting_value = ?
            WHERE setting_key = CONCAT('two_factor_secret_', ?)
        ");
        $stmt->execute([$secret, $userId]);

        // Generate QR code data
        $qrData = "otpauth://totp/SecurityApp:user{$userId}?secret={$secret}&issuer=SecurityApp";

        echo json_encode([
            'success' => true,
            'secret' => $secret,
            'qr_code' => $qrData,
            'message' => 'تم إنشاء رمز QR للمصادقة الثنائية بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في إنشاء رمز QR: ' . $e->getMessage());
    }
}

/**
 * Get advanced security statistics
 */
function getSecurityStatsAdvanced()
{
    global $pdo;

    try {
        $stats = [];

        // Login attempts in last 24 hours
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM security_logs
            WHERE event_type = 'login_attempt'
            AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $stmt->execute();
        $stats['login_attempts_24h'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Failed login attempts in last 24 hours
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM security_logs
            WHERE event_type = 'login_failed'
            AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $stmt->execute();
        $stats['failed_logins_24h'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Active sessions count
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM active_sessions
            WHERE last_activity > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute();
        $stats['active_sessions'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Blocked IPs count
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM ip_access_list
            WHERE type = 'blacklist'
        ");
        $stmt->execute();
        $stats['blocked_ips'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        // Security incidents in last 7 days
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM security_logs
            WHERE event_type IN ('suspicious_activity', 'brute_force_attempt', 'unauthorized_access')
            AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");
        $stmt->execute();
        $stats['security_incidents_7d'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        echo json_encode([
            'success' => true,
            'data' => $stats,
            'message' => 'تم تحميل إحصائيات الأمان المتقدمة بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل إحصائيات الأمان: ' . $e->getMessage());
    }
}

/**
 * Get Security Dashboard Data
 * لوحة معلومات الأمان الشاملة
 */
function getSecurityDashboard()
{
    global $securityManager;

    try {
        $dashboardData = $securityManager->getDashboardData();

        echo json_encode([
            'success' => true,
            'data' => $dashboardData,
            'message' => 'تم تحميل لوحة معلومات الأمان بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل لوحة معلومات الأمان: ' . $e->getMessage());
    }
}

/**
 * Get Password Policy Settings
 * إعدادات سياسة كلمات المرور
 */
function getPasswordPolicy()
{
    global $securityManager;

    try {
        $policy = [
            'min_length' => $securityManager->getSetting('password_min_length') ?? 8,
            'require_uppercase' => $securityManager->getSetting('password_require_uppercase') ?? true,
            'require_lowercase' => $securityManager->getSetting('password_require_lowercase') ?? true,
            'require_numbers' => $securityManager->getSetting('password_require_numbers') ?? true,
            'require_symbols' => $securityManager->getSetting('password_require_symbols') ?? true,
            'max_age_days' => $securityManager->getSetting('password_max_age_days') ?? 90,
            'history_count' => $securityManager->getSetting('password_history_count') ?? 5,
            'complexity_score' => $securityManager->getSetting('password_complexity_score') ?? 3
        ];

        echo json_encode([
            'success' => true,
            'data' => $policy,
            'message' => 'تم تحميل سياسة كلمات المرور بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل سياسة كلمات المرور: ' . $e->getMessage());
    }
}

/**
 * Update Password Policy Settings
 * تحديث سياسة كلمات المرور
 */
function updatePasswordPolicy()
{
    global $securityManager;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }

        // Update each policy setting
        $settings = [
            'password_min_length' => ['value' => $input['min_length'] ?? 8, 'type' => 'integer'],
            'password_require_uppercase' => ['value' => $input['require_uppercase'] ?? true, 'type' => 'boolean'],
            'password_require_lowercase' => ['value' => $input['require_lowercase'] ?? true, 'type' => 'boolean'],
            'password_require_numbers' => ['value' => $input['require_numbers'] ?? true, 'type' => 'boolean'],
            'password_require_symbols' => ['value' => $input['require_symbols'] ?? true, 'type' => 'boolean'],
            'password_max_age_days' => ['value' => $input['max_age_days'] ?? 90, 'type' => 'integer'],
            'password_history_count' => ['value' => $input['history_count'] ?? 5, 'type' => 'integer'],
            'password_complexity_score' => ['value' => $input['complexity_score'] ?? 3, 'type' => 'integer']
        ];

        foreach ($settings as $key => $setting) {
            $securityManager->updateSetting($key, $setting['value'], $setting['type']);
        }

        // Log the change
        $securityManager->logSecurityEvent(
            'password_policy_update',
            'تم تحديث سياسة كلمات المرور',
            'medium',
            'success'
        );

        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث سياسة كلمات المرور بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحديث سياسة كلمات المرور: ' . $e->getMessage());
    }
}

/**
 * Get Threat Detection Settings
 * إعدادات كشف التهديدات
 */
function getThreatDetection()
{
    global $securityManager;

    try {
        $settings = [
            'enabled' => $securityManager->getSetting('threat_detection_enabled') ?? true,
            'max_login_attempts' => $securityManager->getSetting('max_login_attempts') ?? 5,
            'lockout_duration' => $securityManager->getSetting('lockout_duration') ?? 30,
            'suspicious_activity_threshold' => $securityManager->getSetting('suspicious_activity_threshold') ?? 10,
            'auto_block_enabled' => $securityManager->getSetting('auto_block_enabled') ?? true,
            'email_alerts' => $securityManager->getSetting('threat_email_alerts') ?? true,
            'sms_alerts' => $securityManager->getSetting('threat_sms_alerts') ?? false
        ];

        echo json_encode([
            'success' => true,
            'data' => $settings,
            'message' => 'تم تحميل إعدادات كشف التهديدات بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل إعدادات كشف التهديدات: ' . $e->getMessage());
    }
}

/**
 * Get Backup Settings
 * إعدادات النسخ الاحتياطي
 */
function getBackupSettings()
{
    global $securityManager;

    try {
        $settings = [
            'auto_backup_enabled' => $securityManager->getSetting('auto_backup_enabled') ?? true,
            'backup_frequency' => $securityManager->getSetting('backup_frequency') ?? 'daily',
            'backup_retention_days' => $securityManager->getSetting('backup_retention_days') ?? 30,
            'backup_location' => $securityManager->getSetting('backup_location') ?? 'local',
            'encryption_enabled' => $securityManager->getSetting('backup_encryption_enabled') ?? true,
            'compression_enabled' => $securityManager->getSetting('backup_compression_enabled') ?? true
        ];

        echo json_encode([
            'success' => true,
            'data' => $settings,
            'message' => 'تم تحميل إعدادات النسخ الاحتياطي بنجاح'
        ]);
    } catch (Exception $e) {
        throw new Exception('فشل في تحميل إعدادات النسخ الاحتياطي: ' . $e->getMessage());
    }
}
