/**
 * Categories Management - Working Version
 * إدارة الفئات - نسخة تعمل
 */

console.log('🔧 تحميل ملف categories-working.js...');

// Simple global function to avoid conflicts
function loadCategoriesManagementContent() {
    console.log('🗂️ بدء تحميل إدارة الفئات...');
    
    const container = document.getElementById('categoriesManagementContent');
    if (!container) {
        console.error('❌ لم يتم العثور على حاوي إدارة الفئات');
        return;
    }
    
    console.log('✅ تم العثور على الحاوي');
    
    // Show loading immediately
    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div>
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
            </div>
            <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
            <p style="color: #999; font-size: 0.9em;">يتم الآن جلب البيانات من الخادم...</p>
        </div>
    `;
    
    // Try to fetch data
    console.log('📡 محاولة جلب البيانات...');
    
    fetch('php/categories.php?action=get_all')
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response.json();
        })
        .then(data => {
            console.log('📦 البيانات المستلمة:', data);
            
            if (data.success) {
                console.log('✅ نجح جلب البيانات');
                renderCategoriesInterface(container, data.data);
            } else {
                throw new Error(data.message || 'فشل في جلب البيانات');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في جلب البيانات:', error);
            showErrorInterface(container, error.message);
        });
}

function renderCategoriesInterface(container, data) {
    console.log('🎨 رسم واجهة إدارة الفئات...');
    console.log('📊 البيانات للرسم:', data);
    
    if (!data || !data.categories) {
        console.error('❌ بيانات غير صحيحة');
        showErrorInterface(container, 'بيانات الفئات غير صحيحة');
        return;
    }
    
    const categories = data.categories;
    const mainCategories = categories.filter(c => c.parent_id === null);
    const subCategories = categories.filter(c => c.parent_id !== null);
    const featuredCategories = categories.filter(c => c.is_featured == 1);
    
    console.log('📊 إحصائيات:', {
        total: data.total,
        main: mainCategories.length,
        sub: subCategories.length,
        featured: featuredCategories.length
    });
    
    const html = `
        <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 20px;">
                    <div>
                        <h2 style="margin: 0; font-size: 1.8rem;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة وتنظيم فئات المنتجات والمحتوى</p>
                    </div>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button onclick="showAddCategoryAlert()" style="padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-plus"></i> إضافة فئة جديدة
                        </button>
                        <button onclick="loadCategoriesManagementContent()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;">
                        <i class="fas fa-folder"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${data.total}</h3>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">إجمالي الفئات</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${mainCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات الرئيسية</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${subCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات الفرعية</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${featuredCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات المميزة</p>
                </div>
            </div>
            
            <!-- Categories List -->
            <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; background: #f8f9fa;">
                    <h3 style="margin: 0; color: #333; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-sitemap"></i> 
                        عرض هرمي للفئات
                        <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">${data.total}</span>
                    </h3>
                </div>
                
                <div style="padding: 25px;">
                    ${renderCategoriesList(data.hierarchy || [], categories)}
                </div>
            </div>
            
            <!-- Success Message -->
            <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
                <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة الفئات بنجاح!</strong>
                <br>تم عرض ${data.total} فئة مع الهيكل الهرمي الكامل.
            </div>
        </div>
    `;
    
    console.log('✅ تم إنشاء HTML');
    container.innerHTML = html;
    console.log('✅ تم تحديث الحاوي بنجاح');
}

function renderCategoriesList(hierarchy, allCategories, level = 0) {
    console.log('🌳 رسم قائمة الفئات، المستوى:', level);
    
    // If no hierarchy provided, build it from categories
    if (!hierarchy || hierarchy.length === 0) {
        console.log('🔄 بناء الهيكل الهرمي من الفئات...');
        const mainCategories = allCategories.filter(c => c.parent_id === null);
        return renderCategoriesFlat(mainCategories, allCategories);
    }
    
    let html = '<div>';
    
    hierarchy.forEach((category, index) => {
        const indent = level * 25;
        const statusIcon = category.is_active == 1 ? '✅' : '❌';
        const featuredIcon = category.is_featured == 1 ? '⭐' : '';
        
        html += `
            <div style="margin-right: ${indent}px; margin-bottom: 15px; padding: 20px; border: 1px solid #e0e0e0; border-radius: 10px; ${category.is_active == 1 ? 'background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);' : 'background: #f5f5f5; opacity: 0.8;'} transition: all 0.3s ease;">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                    <div style="display: flex; align-items: center; gap: 20px; flex: 1;">
                        <div style="color: ${category.color}; font-size: 2rem; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <i class="${category.icon || 'fas fa-folder'}"></i>
                        </div>
                        <div style="flex: 1;">
                            <h4 style="margin: 0 0 8px 0; color: #333; font-size: 1.3rem; font-weight: 600;">
                                ${category.name_ar} ${featuredIcon}
                                <span style="font-size: 0.8rem; color: #999; font-weight: normal;">(${category.slug})</span>
                            </h4>
                            <p style="margin: 0 0 12px 0; color: #666; font-size: 0.95rem; line-height: 1.4;">
                                ${category.description_ar || 'لا يوجد وصف'}
                            </p>
                            <div style="display: flex; gap: 20px; font-size: 0.85rem; color: #999; flex-wrap: wrap;">
                                <span style="display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-layer-group"></i>
                                    ${category.subcategories_count || 0} فئة فرعية
                                </span>
                                <span style="display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-box"></i>
                                    ${category.products_count || 0} منتج
                                </span>
                                <span style="display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-eye"></i>
                                    ${category.views_count || 0} مشاهدة
                                </span>
                                <span style="display: flex; align-items: center; gap: 5px;">
                                    ${statusIcon} ${category.is_active == 1 ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <button onclick="editCategoryAlert(${category.id}, '${category.name_ar}')" title="تعديل" style="padding: 10px 14px; border: none; background: #17a2b8; color: white; border-radius: 6px; cursor: pointer; transition: all 0.3s ease;">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleCategoryAlert(${category.id}, '${category.name_ar}', ${category.is_active})" title="${category.is_active == 1 ? 'إلغاء التفعيل' : 'تفعيل'}" style="padding: 10px 14px; border: none; background: ${category.is_active == 1 ? '#28a745' : '#6c757d'}; color: white; border-radius: 6px; cursor: pointer; transition: all 0.3s ease;">
                            <i class="fas fa-${category.is_active == 1 ? 'eye-slash' : 'eye'}"></i>
                        </button>
                        <button onclick="deleteCategoryAlert(${category.id}, '${category.name_ar}')" title="حذف" style="padding: 10px 14px; border: none; background: #dc3545; color: white; border-radius: 6px; cursor: pointer; transition: all 0.3s ease;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Render children
        if (category.children && category.children.length > 0) {
            html += renderCategoriesList(category.children, allCategories, level + 1);
        }
    });
    
    html += '</div>';
    return html;
}

function renderCategoriesFlat(mainCategories, allCategories) {
    console.log('📋 رسم الفئات بشكل مسطح...');
    
    let html = '<div>';
    
    mainCategories.forEach(mainCat => {
        const subCategories = allCategories.filter(c => c.parent_id == mainCat.id);
        
        html += `
            <div style="margin-bottom: 20px; padding: 20px; border: 2px solid ${mainCat.color}; border-radius: 12px; background: linear-gradient(135deg, ${mainCat.color}15 0%, #ffffff 100%);">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                    <div style="color: ${mainCat.color}; font-size: 2rem;">
                        <i class="${mainCat.icon || 'fas fa-folder'}"></i>
                    </div>
                    <div>
                        <h3 style="margin: 0; color: #333; font-size: 1.4rem;">${mainCat.name_ar}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">${mainCat.description_ar || 'لا يوجد وصف'}</p>
                    </div>
                </div>
                
                ${subCategories.length > 0 ? `
                    <div style="margin-right: 40px;">
                        <h4 style="color: #555; margin-bottom: 10px;">الفئات الفرعية (${subCategories.length}):</h4>
                        ${subCategories.map(subCat => `
                            <div style="margin-bottom: 10px; padding: 15px; background: white; border-radius: 8px; border-right: 4px solid ${subCat.color};">
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <i class="${subCat.icon || 'fas fa-folder'}" style="color: ${subCat.color};"></i>
                                    <strong>${subCat.name_ar}</strong>
                                    ${subCat.is_featured == 1 ? '⭐' : ''}
                                </div>
                                <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">${subCat.description_ar || 'لا يوجد وصف'}</p>
                            </div>
                        `).join('')}
                    </div>
                ` : '<p style="margin-right: 40px; color: #999; font-style: italic;">لا توجد فئات فرعية</p>'}
            </div>
        `;
    });
    
    html += '</div>';
    return html;
}

function showErrorInterface(container, message) {
    console.log('❌ عرض واجهة الخطأ:', message);
    
    container.innerHTML = `
        <div style="text-align: center; padding: 60px 20px; color: #dc3545;">
            <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.7;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 style="margin: 0 0 15px 0; color: #dc3545;">خطأ في تحميل إدارة الفئات</h3>
            <p style="margin: 0 0 25px 0; color: #666; font-size: 1.1rem;">${message}</p>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button onclick="loadCategoriesManagementContent()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
                <a href="php/categories.php?action=get_all" target="_blank" style="padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
                    <i class="fas fa-external-link-alt"></i> اختبار API مباشر
                </a>
            </div>
        </div>
    `;
}

// Action functions
function showAddCategoryAlert() {
    alert('🆕 إضافة فئة جديدة\n\nهذه الميزة قيد التطوير.\nسيتم إضافة نموذج كامل لإنشاء الفئات قريباً.');
}

function editCategoryAlert(id, name) {
    alert(`✏️ تعديل الفئة\n\nالفئة: ${name}\nالمعرف: ${id}\n\nهذه الميزة قيد التطوير.`);
}

function toggleCategoryAlert(id, name, isActive) {
    const action = isActive == 1 ? 'إلغاء تفعيل' : 'تفعيل';
    if (confirm(`🔄 ${action} الفئة\n\nهل تريد ${action} الفئة "${name}"؟`)) {
        alert(`تم ${action} الفئة "${name}" - هذه الميزة قيد التطوير.`);
    }
}

function deleteCategoryAlert(id, name) {
    if (confirm(`🗑️ حذف الفئة\n\nهل أنت متأكد من حذف الفئة "${name}"؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!`)) {
        alert(`تم حذف الفئة "${name}" - هذه الميزة قيد التطوير.`);
    }
}

console.log('✅ تم تحميل ملف categories-working.js بنجاح');
console.log('🎯 الدالة الرئيسية متاحة:', typeof loadCategoriesManagementContent);
