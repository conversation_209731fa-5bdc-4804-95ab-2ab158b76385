<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Prevent any output before JSON
ob_start();

try {
    // Database connection
    require_once '../config/database.php';
    
    $response = [
        'success' => true,
        'data' => [
            'two_factor_auth' => [
                'enabled' => false,
                'method' => 'app',
                'backup_codes' => 5
            ],
            'session_security' => [
                'timeout' => 3600,
                'concurrent_sessions' => 3,
                'secure_cookies' => true
            ],
            'ip_restrictions' => [
                'enabled' => false,
                'whitelist' => [],
                'blacklist' => []
            ],
            'login_attempts' => [
                'max_attempts' => 5,
                'lockout_duration' => 900,
                'enabled' => true
            ],
            'audit_logs' => [
                'enabled' => true,
                'retention_days' => 90,
                'log_level' => 'info'
            ],
            'threat_detection' => [
                'enabled' => true,
                'auto_block' => false,
                'sensitivity' => 'medium'
            ],
            'backup_settings' => [
                'auto_backup' => true,
                'frequency' => 'daily',
                'retention' => 30
            ]
        ],
        'message' => 'Security settings retrieved successfully'
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => 'Failed to retrieve security settings',
        'message' => $e->getMessage(),
        'data' => []
    ];
}

// Clear any output buffer and send JSON
ob_clean();
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;
?>
