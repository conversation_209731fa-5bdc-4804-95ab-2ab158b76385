<?php

/**
 * AI Settings API - Simplified Version
 */

header('Content-Type: application/json; charset=utf-8');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Prevent any output before JSON
ob_start();

try {
    // Try to load config files with error handling
    $configLoaded = false;
    $pdo = null;

    $configPaths = [
        __DIR__ . '/../config/config.php',
        __DIR__ . '/../php/config.php',
        __DIR__ . '/../config/database.php'
    ];

    foreach ($configPaths as $configPath) {
        if (file_exists($configPath)) {
            try {
                require_once $configPath;
                $configLoaded = true;
            } catch (Exception $e) {
                error_log("Warning: Could not load config file $configPath: " . $e->getMessage());
            }
        }
    }

    // Try to get database connection
    if (function_exists('getPDOConnection')) {
        $pdo = getPDOConnection();
    } elseif (class_exists('Database')) {
        $db = Database::getInstance();
        $pdo = $db->getPDO();
    }

    $providers = ['openai', 'anthropic', 'gemini'];
    $settings = [];

    // Try to get settings from database if available
    if ($pdo) {
        try {
            foreach ($providers as $provider) {
                $stmt = $pdo->prepare("
                    SELECT provider, enabled, api_key, model, status_message, updated_at
                    FROM ai_settings
                    WHERE provider = :provider
                ");
                $stmt->execute(['provider' => $provider]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($result) {
                    $settings[$provider] = [
                        'enabled' => (bool)$result['enabled'],
                        'api_key' => $result['api_key'] ?: '',
                        'model' => $result['model'] ?: getDefaultModel($provider),
                        'status' => $result['status_message'] ?: 'Ready',
                        'last_tested' => $result['updated_at']
                    ];
                } else {
                    $settings[$provider] = getDefaultSettings($provider);
                }
            }
        } catch (Exception $e) {
            // Database error, use defaults
            foreach ($providers as $provider) {
                $settings[$provider] = getDefaultSettings($provider);
            }
        }
    } else {
        // No database connection, use defaults
        foreach ($providers as $provider) {
            $settings[$provider] = getDefaultSettings($provider);
        }
    }

    // Clear any output buffer
    ob_clean();

    echo json_encode([
        'success' => true,
        'data' => $settings
    ]);
} catch (Exception $e) {
    // Clear any output buffer
    ob_clean();

    error_log("AI Settings Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function getDefaultModel($provider)
{
    $models = [
        'openai' => 'gpt-3.5-turbo',
        'anthropic' => 'claude-3-sonnet-20240229',
        'gemini' => 'gemini-pro'
    ];

    return $models[$provider] ?? 'default';
}

function getDefaultSettings($provider)
{
    return [
        'enabled' => false,
        'api_key' => '',
        'model' => getDefaultModel($provider),
        'status' => 'Not configured',
        'last_tested' => null
    ];
}
