<?php
/**
 * Database Fix Script for Admin Panel
 * This script fixes database issues that prevent admin panel sections from loading
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration
require_once '../php/config.php';

// Set content type to HTML for better display
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قاعدة البيانات - لوحة التحكم</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        .info { color: #3498db; }
        .step {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح قاعدة البيانات - لوحة التحكم</h1>
        <p>هذا السكريبت سيقوم بإصلاح مشاكل قاعدة البيانات التي تمنع تحميل أقسام لوحة التحكم بشكل صحيح.</p>

<?php

try {
    // Get database connection
    $pdo = getPDOConnection();
    if (!$pdo) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }

    echo '<div class="step">';
    echo '<h3>✅ تم الاتصال بقاعدة البيانات بنجاح</h3>';
    echo '</div>';

    // Step 1: Check current table structure
    echo '<div class="step">';
    echo '<h3>🔍 الخطوة 1: فحص هيكل الجداول الحالي</h3>';
    
    $tables = ['produits', 'categories', 'landing_pages'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>جدول $table:</h4>";
            echo "<pre>";
            foreach ($columns as $column) {
                echo "- {$column['Field']} ({$column['Type']}) - Default: {$column['Default']}\n";
            }
            echo "</pre>";
            
            // Check for 'actif' column specifically
            $hasActif = false;
            foreach ($columns as $column) {
                if ($column['Field'] === 'actif') {
                    $hasActif = true;
                    break;
                }
            }
            
            if ($hasActif) {
                echo '<div class="result success">✅ عمود "actif" موجود</div>';
            } else {
                echo '<div class="result warning">⚠️ عمود "actif" غير موجود - سيتم إضافته</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="result error">❌ خطأ في فحص جدول ' . $table . ': ' . $e->getMessage() . '</div>';
        }
    }
    echo '</div>';

    // Step 2: Run database migration
    echo '<div class="step">';
    echo '<h3>🔧 الخطوة 2: تطبيق إصلاحات قاعدة البيانات</h3>';
    
    $migrationFile = '../database/migrations/fix_admin_panel_database.sql';
    if (file_exists($migrationFile)) {
        $sql = file_get_contents($migrationFile);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($statements as $statement) {
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue; // Skip empty statements and comments
            }
            
            try {
                $pdo->exec($statement);
                $successCount++;
                echo '<div class="result success">✅ تم تنفيذ: ' . substr($statement, 0, 50) . '...</div>';
            } catch (Exception $e) {
                $errorCount++;
                echo '<div class="result error">❌ خطأ في: ' . substr($statement, 0, 50) . '... - ' . $e->getMessage() . '</div>';
            }
        }
        
        echo "<p><strong>النتيجة:</strong> تم تنفيذ $successCount عملية بنجاح، $errorCount خطأ</p>";
        
    } else {
        echo '<div class="result error">❌ ملف الإصلاح غير موجود: ' . $migrationFile . '</div>';
    }
    echo '</div>';

    // Step 3: Verify fixes
    echo '<div class="step">';
    echo '<h3>✅ الخطوة 3: التحقق من الإصلاحات</h3>';
    
    // Test 'actif' column in produits table
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits WHERE actif = 1");
        $activeProducts = $stmt->fetch()['count'];
        echo '<div class="result success">✅ المنتجات النشطة: ' . $activeProducts . '</div>';
    } catch (Exception $e) {
        echo '<div class="result error">❌ خطأ في اختبار عمود actif: ' . $e->getMessage() . '</div>';
    }
    
    // Test categories table
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE actif = 1");
        $activeCategories = $stmt->fetch()['count'];
        echo '<div class="result success">✅ الفئات النشطة: ' . $activeCategories . '</div>';
    } catch (Exception $e) {
        echo '<div class="result error">❌ خطأ في اختبار جدول الفئات: ' . $e->getMessage() . '</div>';
    }
    
    // Test admin_settings table
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_settings");
        $settingsCount = $stmt->fetch()['count'];
        echo '<div class="result success">✅ إعدادات الإدارة: ' . $settingsCount . ' إعداد</div>';
    } catch (Exception $e) {
        echo '<div class="result warning">⚠️ جدول إعدادات الإدارة: ' . $e->getMessage() . '</div>';
    }
    
    echo '</div>';

    // Step 4: Test API endpoints
    echo '<div class="step">';
    echo '<h3>🧪 الخطوة 4: اختبار نقاط API</h3>';
    
    // Test products API
    $testUrls = [
        '../php/api/products.php' => 'API المنتجات',
        '../php/api/categories.php' => 'API الفئات',
        '../php/admin.php' => 'API الإدارة'
    ];
    
    foreach ($testUrls as $url => $name) {
        if (file_exists($url)) {
            echo '<div class="result success">✅ ' . $name . ' موجود</div>';
        } else {
            echo '<div class="result error">❌ ' . $name . ' غير موجود: ' . $url . '</div>';
        }
    }
    
    echo '</div>';

    echo '<div class="step">';
    echo '<h3>🎉 اكتمل الإصلاح!</h3>';
    echo '<p>تم إصلاح مشاكل قاعدة البيانات. يمكنك الآن العودة إلى لوحة التحكم واختبار الأقسام التالية:</p>';
    echo '<ul>';
    echo '<li>إعدادات الذكاء الاصطناعي</li>';
    echo '<li>إدارة الفئات</li>';
    echo '<li>إعدادات الدفع</li>';
    echo '<li>إدارة المستخدمين</li>';
    echo '<li>إعدادات الأمان</li>';
    echo '</ul>';
    echo '<p><a href="index.html" style="background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">العودة إلى لوحة التحكم</a></p>';
    echo '</div>';

} catch (Exception $e) {
    echo '<div class="result error">';
    echo '<h3>❌ خطأ عام</h3>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '</div>';
}

?>

    </div>
</body>
</html>
