<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Redirect - Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .log-area {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Test de Redirection Login - Diagnostic</h1>
        
        <div class="status info">
            <strong>Objectif:</strong> Diagnostiquer pourquoi la redirection ne fonctionne pas après connexion réussie
        </div>

        <div>
            <button class="test-button" onclick="testLoginRedirect()">🧪 Tester la Redirection Login</button>
            <button class="test-button" onclick="checkAuthState()">🔐 Vérifier l'État d'Authentification</button>
            <button class="test-button" onclick="simulateManualLogin()">👤 Simuler Connexion Manuelle</button>
            <button class="test-button" onclick="clearLogs()">🗑️ Effacer les Logs</button>
        </div>

        <div class="log-area" id="logArea"></div>

        <div id="statusArea"></div>
    </div>

    <!-- Load auth-fix.js first -->
    <script src="auth-fix.js"></script>
    
    <!-- Fallback safeRedirect -->
    <script>
        if (!window.safeRedirect) {
            window.safeRedirect = function(url) {
                log('🔄 Using fallback redirect to: ' + url);
                window.location.href = url;
            };
        }
    </script>

    <!-- Firebase config -->
    <script type="module" src="js/firebase-config.js"></script>

    <script>
        let logCount = 0;
        
        function log(message) {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('logArea');
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logArea').innerHTML = '';
            logCount = 0;
        }

        function showStatus(message, type = 'info') {
            const statusArea = document.getElementById('statusArea');
            statusArea.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Override console methods to capture all logs
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        console.log = function(...args) {
            log('📝 LOG: ' + args.join(' '));
            originalConsoleLog.apply(console, args);
        };

        console.error = function(...args) {
            log('❌ ERROR: ' + args.join(' '));
            originalConsoleError.apply(console, args);
        };

        console.warn = function(...args) {
            log('⚠️ WARN: ' + args.join(' '));
            originalConsoleWarn.apply(console, args);
        };

        // Test functions
        async function testLoginRedirect() {
            log('🧪 === DÉBUT DU TEST DE REDIRECTION LOGIN ===');
            
            // Check if Firebase is loaded
            if (!window.firebaseAuth) {
                log('❌ Firebase Auth non disponible');
                showStatus('Firebase Auth non chargé', 'error');
                return;
            }

            log('✅ Firebase Auth disponible');
            
            // Check current auth state
            const currentUser = window.firebaseAuth.getCurrentUser();
            log('👤 Utilisateur actuel: ' + JSON.stringify(currentUser, null, 2));

            // Check if user is already authenticated
            const isAuth = window.firebaseAuth.isAuthenticated();
            log('🔐 État d\'authentification: ' + isAuth);

            if (isAuth) {
                log('✅ Utilisateur déjà connecté');
                
                // Test manual login flag
                log('🏁 Test du flag loginFormSubmitted...');
                window.loginFormSubmitted = true;
                log('✅ Flag loginFormSubmitted défini sur true');
                
                // Simulate onFirebaseUserSignedIn call
                log('🔄 Simulation de onFirebaseUserSignedIn...');
                if (window.onFirebaseUserSignedIn && currentUser.user && currentUser.profile) {
                    window.onFirebaseUserSignedIn(currentUser.user, currentUser.profile);
                    log('✅ onFirebaseUserSignedIn appelé avec succès');
                } else {
                    log('❌ Impossible d\'appeler onFirebaseUserSignedIn - données manquantes');
                }
            } else {
                log('❌ Utilisateur non connecté - connexion requise');
                showStatus('Utilisateur non connecté. Veuillez vous connecter d\'abord.', 'warning');
            }
        }

        async function checkAuthState() {
            log('🔍 === VÉRIFICATION DE L\'ÉTAT D\'AUTHENTIFICATION ===');
            
            // Check Firebase availability
            log('🔧 Vérification de Firebase...');
            log('- window.firebaseAuth: ' + (window.firebaseAuth ? 'Disponible' : 'Non disponible'));
            log('- window.onFirebaseUserSignedIn: ' + (window.onFirebaseUserSignedIn ? 'Disponible' : 'Non disponible'));
            log('- window.safeRedirect: ' + (window.safeRedirect ? 'Disponible' : 'Non disponible'));
            
            // Check current page
            log('📄 Page actuelle: ' + window.location.pathname);
            log('🌐 URL complète: ' + window.location.href);
            
            // Check login form submitted flag
            log('🏁 Flag loginFormSubmitted: ' + (window.loginFormSubmitted || 'Non défini'));
            
            // Check redirect count
            log('🔄 Compteur de redirections: ' + (sessionStorage.getItem('auth_redirect_count') || '0'));
            
            if (window.firebaseAuth) {
                const isAuth = window.firebaseAuth.isAuthenticated();
                const currentUser = window.firebaseAuth.getCurrentUser();
                
                log('🔐 Authentifié: ' + isAuth);
                if (currentUser.user) {
                    log('👤 Email utilisateur: ' + currentUser.user.email);
                    log('🎭 Rôle utilisateur: ' + (currentUser.profile ? currentUser.profile.role : 'Non défini'));
                    log('📱 Mode login page: ' + (currentUser.profile ? currentUser.profile.loginPageMode : 'Non défini'));
                }
            }
        }

        async function simulateManualLogin() {
            log('👤 === SIMULATION DE CONNEXION MANUELLE ===');
            
            if (!window.firebaseAuth) {
                log('❌ Firebase Auth non disponible');
                return;
            }

            // Set manual login flag
            window.loginFormSubmitted = true;
            log('✅ Flag loginFormSubmitted défini sur true');
            
            // Try to sign in with demo credentials
            log('🔑 Tentative de <NAME_EMAIL>...');
            
            try {
                const result = await window.firebaseAuth.signInWithEmail('<EMAIL>', 'Admin123!@#');
                
                if (result.success) {
                    log('✅ Connexion réussie!');
                    log('👤 Utilisateur: ' + JSON.stringify(result.user, null, 2));
                    showStatus('Connexion réussie! Vérifiez si la redirection se produit...', 'success');
                } else {
                    log('❌ Échec de la connexion: ' + result.error);
                    showStatus('Échec de la connexion: ' + result.error, 'error');
                }
            } catch (error) {
                log('❌ Erreur lors de la connexion: ' + error.message);
                showStatus('Erreur: ' + error.message, 'error');
            }
        }

        // Monitor page changes
        let currentUrl = window.location.href;
        setInterval(() => {
            if (window.location.href !== currentUrl) {
                log('🔄 Changement d\'URL détecté: ' + window.location.href);
                currentUrl = window.location.href;
            }
        }, 1000);

        // Initialize
        window.addEventListener('load', () => {
            log('🚀 Page de test chargée');
            log('📍 URL: ' + window.location.href);
            showStatus('Page de test prête. Cliquez sur les boutons pour diagnostiquer.', 'info');
        });

        // Monitor Firebase loading
        let firebaseCheckInterval = setInterval(() => {
            if (window.firebaseAuth) {
                log('✅ Firebase Auth chargé et disponible');
                clearInterval(firebaseCheckInterval);
                
                // Check initial auth state
                setTimeout(() => {
                    checkAuthState();
                }, 1000);
            }
        }, 500);
    </script>
</body>
</html>