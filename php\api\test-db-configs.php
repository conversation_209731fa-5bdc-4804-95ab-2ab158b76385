<?php

/**
 * Test Different Database Configurations
 * Tests various database connection settings to find the working one
 */

// Detect if running via CLI or web
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// Error handling
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Load environment configuration
try {
    require_once __DIR__ . '/../config/env-loader.php';
    $envConfig = EnvLoader::getDatabaseConfig();
} catch (Exception $e) {
    $envConfig = [
        'host' => 'localhost',
        'port' => '3307',
        'database' => 'mossab-landing-page',
        'username' => 'root',
        'password' => ''
    ];
}

// Different database configurations to test (based on .env)
$configs = [
    [
        'name' => 'Config 1: From .env file',
        'host' => $envConfig['host'],
        'port' => $envConfig['port'],
        'dbname' => $envConfig['database'],
        'username' => $envConfig['username'],
        'password' => $envConfig['password']
    ],
    [
        'name' => 'Config 2: .env with password "root"',
        'host' => $envConfig['host'],
        'port' => $envConfig['port'],
        'dbname' => $envConfig['database'],
        'username' => $envConfig['username'],
        'password' => 'root'
    ],
    [
        'name' => 'Config 3: .env on port 3306',
        'host' => $envConfig['host'],
        'port' => '3306',
        'dbname' => $envConfig['database'],
        'username' => $envConfig['username'],
        'password' => $envConfig['password']
    ],
    [
        'name' => 'Config 4: Default MySQL (port 3306, empty password)',
        'host' => 'localhost',
        'port' => '3306',
        'dbname' => $envConfig['database'],
        'username' => 'root',
        'password' => ''
    ]
];

$results = [];
$workingConfig = null;

foreach ($configs as $config) {
    $result = [
        'config' => $config['name'],
        'success' => false,
        'message' => '',
        'details' => []
    ];

    try {
        // Build DSN
        if ($config['port']) {
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset=utf8mb4";
        } else {
            $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4";
        }

        // Try connection
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);

        // Test basic query
        $stmt = $pdo->query("SELECT 1 as test, NOW() as current_time");
        $testResult = $stmt->fetch();

        // Get database info
        $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as version");
        $dbInfo = $stmt->fetch();

        // Check if roles table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
        $rolesExists = $stmt->rowCount() > 0;

        $result['success'] = true;
        $result['message'] = 'Connection successful!';
        $result['details'] = [
            'database' => $dbInfo['db_name'],
            'version' => $dbInfo['version'],
            'current_time' => $testResult['current_time'],
            'roles_table_exists' => $rolesExists,
            'dsn' => $dsn
        ];

        if (!$workingConfig) {
            $workingConfig = $config;
        }
    } catch (Exception $e) {
        $result['message'] = 'Connection failed: ' . $e->getMessage();
        $result['details'] = [
            'dsn' => $dsn ?? 'Failed to build DSN',
            'error_code' => $e->getCode()
        ];
    }

    $results[] = $result;
}

// Prepare final response
$response = [
    'timestamp' => date('Y-m-d H:i:s'),
    'total_configs_tested' => count($configs),
    'successful_connections' => count(array_filter($results, function ($r) {
        return $r['success'];
    })),
    'working_config' => $workingConfig,
    'test_results' => $results
];

// Output based on context
if ($isCLI) {
    echo "=== Database Configuration Test Results ===\n";
    echo "Timestamp: " . $response['timestamp'] . "\n";
    echo "Configs tested: " . $response['total_configs_tested'] . "\n";
    echo "Successful: " . $response['successful_connections'] . "\n\n";

    foreach ($results as $result) {
        $status = $result['success'] ? '✓' : '✗';
        echo "$status {$result['config']}\n";
        echo "   Message: {$result['message']}\n";
        if ($result['success'] && isset($result['details']['database'])) {
            echo "   Database: {$result['details']['database']}\n";
            echo "   Version: {$result['details']['version']}\n";
            echo "   Roles table: " . ($result['details']['roles_table_exists'] ? 'EXISTS' : 'MISSING') . "\n";
        }
        echo "\n";
    }

    if ($workingConfig) {
        echo "=== RECOMMENDED CONFIG ===\n";
        echo "Host: {$workingConfig['host']}\n";
        echo "Port: " . ($workingConfig['port'] ?? 'default') . "\n";
        echo "Database: {$workingConfig['dbname']}\n";
        echo "Username: {$workingConfig['username']}\n";
        echo "Password: " . (empty($workingConfig['password']) ? 'empty' : 'set') . "\n";
    } else {
        echo "❌ No working configuration found!\n";
        echo "Please check:\n";
        echo "1. MySQL/MariaDB is running\n";
        echo "2. Database 'poultraydz' exists\n";
        echo "3. User credentials are correct\n";
    }
} else {
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
