<?php
/**
 * Store Settings Tables Setup
 * إعداد جداول إعدادات المتجر
 */

// Load configuration
require_once '../../config/config.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد جداول إعدادات المتجر</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { color: #28a745; margin: 10px 0; }
        .error { color: #dc3545; margin: 10px 0; }
        .info { color: #17a2b8; margin: 10px 0; }
        .warning { color: #ffc107; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .back-link { display: inline-block; margin-top: 20px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🏪 إعداد جداول إعدادات المتجر</h1>";

try {
    // Connect to database
    $dbConfig = Config::getDbConfig();
    $dsn = sprintf(
        "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
        $dbConfig['host'],
        $dbConfig['port'],
        $dbConfig['database']
    );
    
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

    // Drop existing tables if they exist
    echo "<h2>حذف الجداول الموجودة (إن وجدت):</h2>";
    
    $tablesToDrop = ['store_settings', 'store_configurations', 'notification_settings'];
    
    foreach ($tablesToDrop as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS `$table`");
            echo "<div class='warning'>🗑️ تم حذف الجدول $table</div>";
        } catch (Exception $e) {
            echo "<div class='info'>ℹ️ الجدول $table غير موجود</div>";
        }
    }

    // Create store_settings table
    echo "<h2>إنشاء جدول store_settings:</h2>";
    
    $createStoreSettings = "
    CREATE TABLE `store_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text,
        `setting_type` enum('text','number','boolean','json','file','email','url','color','date') DEFAULT 'text',
        `category` varchar(50) NOT NULL DEFAULT 'general',
        `display_name_ar` varchar(200) NOT NULL,
        `display_name_en` varchar(200),
        `description_ar` text,
        `description_en` text,
        `is_required` tinyint(1) DEFAULT 0,
        `is_public` tinyint(1) DEFAULT 0,
        `sort_order` int(11) DEFAULT 0,
        `validation_rules` json,
        `default_value` text,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`),
        KEY `category` (`category`),
        KEY `is_public` (`is_public`),
        KEY `sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createStoreSettings);
    echo "<div class='success'>✅ تم إنشاء جدول store_settings بنجاح</div>";

    // Create store_configurations table (for complex configurations)
    echo "<h2>إنشاء جدول store_configurations:</h2>";
    
    $createStoreConfigurations = "
    CREATE TABLE `store_configurations` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `config_name` varchar(100) NOT NULL,
        `config_data` json NOT NULL,
        `config_type` varchar(50) NOT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `created_by` int(11),
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `config_name` (`config_name`),
        KEY `config_type` (`config_type`),
        KEY `is_active` (`is_active`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createStoreConfigurations);
    echo "<div class='success'>✅ تم إنشاء جدول store_configurations بنجاح</div>";

    // Create notification_settings table
    echo "<h2>إنشاء جدول notification_settings:</h2>";
    
    $createNotificationSettings = "
    CREATE TABLE `notification_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `notification_type` varchar(50) NOT NULL,
        `is_enabled` tinyint(1) DEFAULT 1,
        `email_enabled` tinyint(1) DEFAULT 1,
        `sms_enabled` tinyint(1) DEFAULT 0,
        `push_enabled` tinyint(1) DEFAULT 1,
        `template_ar` text,
        `template_en` text,
        `recipients` json,
        `settings` json,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `notification_type` (`notification_type`),
        KEY `is_enabled` (`is_enabled`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createNotificationSettings);
    echo "<div class='success'>✅ تم إنشاء جدول notification_settings بنجاح</div>";

    // Insert default store settings
    echo "<h2>إدراج الإعدادات الافتراضية:</h2>";
    
    $defaultSettings = [
        // General Settings
        ['store_name', 'متجر الكتب الإلكترونية', 'text', 'general', 'اسم المتجر', 'Store Name', 'اسم المتجر الذي يظهر للعملاء', 'Store name displayed to customers', 1, 1, 1],
        ['store_description', 'متجر متخصص في بيع الكتب الإلكترونية والدورات التدريبية', 'text', 'general', 'وصف المتجر', 'Store Description', 'وصف مختصر عن المتجر', 'Brief description of the store', 0, 1, 2],
        ['store_logo', '', 'file', 'general', 'شعار المتجر', 'Store Logo', 'شعار المتجر (PNG, JPG)', 'Store logo (PNG, JPG)', 0, 1, 3],
        ['store_favicon', '', 'file', 'general', 'أيقونة المتجر', 'Store Favicon', 'أيقونة المتجر في المتصفح', 'Store favicon for browser', 0, 1, 4],
        ['store_email', '<EMAIL>', 'email', 'general', 'البريد الإلكتروني', 'Store Email', 'البريد الإلكتروني الرسمي للمتجر', 'Official store email address', 1, 1, 5],
        ['store_phone', '+966501234567', 'text', 'general', 'رقم الهاتف', 'Store Phone', 'رقم هاتف المتجر', 'Store phone number', 0, 1, 6],
        ['store_address', 'الرياض، المملكة العربية السعودية', 'text', 'general', 'عنوان المتجر', 'Store Address', 'العنوان الفعلي للمتجر', 'Physical store address', 0, 1, 7],
        
        // Currency & Tax Settings
        ['default_currency', 'SAR', 'text', 'currency', 'العملة الافتراضية', 'Default Currency', 'العملة المستخدمة في المتجر', 'Currency used in the store', 1, 1, 10],
        ['currency_symbol', 'ر.س', 'text', 'currency', 'رمز العملة', 'Currency Symbol', 'رمز العملة المعروض', 'Currency symbol displayed', 1, 1, 11],
        ['tax_rate', '15', 'number', 'currency', 'معدل الضريبة (%)', 'Tax Rate (%)', 'معدل ضريبة القيمة المضافة', 'VAT tax rate percentage', 1, 1, 12],
        ['tax_included', '1', 'boolean', 'currency', 'الضريبة مشمولة في السعر', 'Tax Included in Price', 'هل الضريبة مشمولة في أسعار المنتجات', 'Whether tax is included in product prices', 0, 1, 13],
        
        // Shipping Settings
        ['shipping_enabled', '1', 'boolean', 'shipping', 'تفعيل الشحن', 'Enable Shipping', 'تفعيل خدمة الشحن', 'Enable shipping service', 0, 1, 20],
        ['free_shipping_threshold', '100', 'number', 'shipping', 'حد الشحن المجاني', 'Free Shipping Threshold', 'الحد الأدنى للشحن المجاني', 'Minimum amount for free shipping', 0, 1, 21],
        ['shipping_cost', '25', 'number', 'shipping', 'تكلفة الشحن', 'Shipping Cost', 'تكلفة الشحن الافتراضية', 'Default shipping cost', 0, 1, 22],
        
        // Email Settings
        ['smtp_host', 'smtp.gmail.com', 'text', 'email', 'خادم SMTP', 'SMTP Host', 'عنوان خادم البريد الإلكتروني', 'Email server host address', 0, 0, 30],
        ['smtp_port', '587', 'number', 'email', 'منفذ SMTP', 'SMTP Port', 'منفذ خادم البريد الإلكتروني', 'Email server port', 0, 0, 31],
        ['smtp_username', '', 'email', 'email', 'اسم مستخدم SMTP', 'SMTP Username', 'اسم المستخدم لخادم البريد', 'Email server username', 0, 0, 32],
        ['smtp_password', '', 'text', 'email', 'كلمة مرور SMTP', 'SMTP Password', 'كلمة المرور لخادم البريد', 'Email server password', 0, 0, 33],
        
        // Appearance Settings
        ['theme_color', '#667eea', 'color', 'appearance', 'لون القالب الرئيسي', 'Primary Theme Color', 'اللون الرئيسي للموقع', 'Primary color for the website', 0, 1, 40],
        ['secondary_color', '#764ba2', 'color', 'appearance', 'اللون الثانوي', 'Secondary Color', 'اللون الثانوي للموقع', 'Secondary color for the website', 0, 1, 41],
        ['items_per_page', '12', 'number', 'appearance', 'عدد العناصر في الصفحة', 'Items Per Page', 'عدد المنتجات المعروضة في كل صفحة', 'Number of products displayed per page', 0, 1, 42],
    ];
    
    $insertStmt = $pdo->prepare("
        INSERT INTO store_settings 
        (setting_key, setting_value, setting_type, category, display_name_ar, display_name_en, description_ar, description_en, is_required, is_public, sort_order) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $insertedCount = 0;
    foreach ($defaultSettings as $setting) {
        try {
            $insertStmt->execute($setting);
            echo "<div class='success'>✅ {$setting[4]}</div>";
            $insertedCount++;
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في إدراج {$setting[4]}: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div class='success'>✅ تم إدراج $insertedCount إعداد من أصل " . count($defaultSettings) . "</div>";

    // Insert default configurations
    echo "<h2>إدراج التكوينات الافتراضية:</h2>";
    
    $defaultConfigurations = [
        [
            'payment_methods',
            json_encode([
                'credit_card' => ['enabled' => true, 'name_ar' => 'بطاقة ائتمان', 'name_en' => 'Credit Card'],
                'paypal' => ['enabled' => true, 'name_ar' => 'باي بال', 'name_en' => 'PayPal'],
                'bank_transfer' => ['enabled' => true, 'name_ar' => 'تحويل بنكي', 'name_en' => 'Bank Transfer'],
                'cash_on_delivery' => ['enabled' => false, 'name_ar' => 'الدفع عند الاستلام', 'name_en' => 'Cash on Delivery']
            ]),
            'payment'
        ],
        [
            'social_media',
            json_encode([
                'facebook' => ['url' => '', 'enabled' => true],
                'twitter' => ['url' => '', 'enabled' => true],
                'instagram' => ['url' => '', 'enabled' => true],
                'linkedin' => ['url' => '', 'enabled' => true],
                'youtube' => ['url' => '', 'enabled' => true]
            ]),
            'social'
        ],
        [
            'seo_settings',
            json_encode([
                'meta_title' => 'متجر الكتب الإلكترونية',
                'meta_description' => 'متجر متخصص في بيع الكتب الإلكترونية والدورات التدريبية',
                'meta_keywords' => 'كتب إلكترونية، دورات تدريبية، تعليم، قراءة',
                'google_analytics' => '',
                'google_tag_manager' => '',
                'facebook_pixel' => ''
            ]),
            'seo'
        ]
    ];
    
    $insertConfigStmt = $pdo->prepare("
        INSERT INTO store_configurations (config_name, config_data, config_type) 
        VALUES (?, ?, ?)
    ");
    
    foreach ($defaultConfigurations as $config) {
        try {
            $insertConfigStmt->execute($config);
            echo "<div class='success'>✅ تكوين {$config[0]}</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في إدراج تكوين {$config[0]}: " . $e->getMessage() . "</div>";
        }
    }

    // Insert default notification settings
    echo "<h2>إدراج إعدادات الإشعارات الافتراضية:</h2>";
    
    $defaultNotifications = [
        ['new_order', 1, 1, 0, 1, 'طلب جديد رقم {order_id}', 'New order #{order_id}', '["<EMAIL>"]'],
        ['order_shipped', 1, 1, 1, 1, 'تم شحن طلبك رقم {order_id}', 'Your order #{order_id} has been shipped', '["customer"]'],
        ['order_delivered', 1, 1, 1, 1, 'تم تسليم طلبك رقم {order_id}', 'Your order #{order_id} has been delivered', '["customer"]'],
        ['new_user_registration', 1, 1, 0, 0, 'مستخدم جديد: {user_name}', 'New user registration: {user_name}', '["<EMAIL>"]'],
        ['low_stock_alert', 1, 1, 0, 0, 'تنبيه: مخزون منخفض للمنتج {product_name}', 'Alert: Low stock for {product_name}', '["<EMAIL>"]']
    ];
    
    $insertNotificationStmt = $pdo->prepare("
        INSERT INTO notification_settings 
        (notification_type, is_enabled, email_enabled, sms_enabled, push_enabled, template_ar, template_en, recipients) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($defaultNotifications as $notification) {
        try {
            $insertNotificationStmt->execute($notification);
            echo "<div class='success'>✅ إشعار {$notification[0]}</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في إدراج إشعار {$notification[0]}: " . $e->getMessage() . "</div>";
        }
    }

    // Final verification
    echo "<h2>التحقق من النتائج النهائية:</h2>";
    
    $tables = ['store_settings', 'store_configurations', 'notification_settings'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $count = $stmt->fetch()['count'];
        echo "<div class='success'>✅ جدول $table: $count سجل</div>";
    }

    // Show sample data
    echo "<h2>عينة من الإعدادات المدرجة:</h2>";
    $stmt = $pdo->query("
        SELECT setting_key, setting_value, category, display_name_ar, is_required, is_public 
        FROM store_settings 
        ORDER BY category, sort_order 
        LIMIT 10
    ");
    
    echo "<table border='1' style='width: 100%; border-collapse: collapse; margin: 10px 0;'>
            <tr style='background: #f8f9fa;'>
                <th style='padding: 8px;'>المفتاح</th>
                <th style='padding: 8px;'>القيمة</th>
                <th style='padding: 8px;'>الفئة</th>
                <th style='padding: 8px;'>الاسم</th>
                <th style='padding: 8px;'>مطلوب</th>
                <th style='padding: 8px;'>عام</th>
            </tr>";
    
    while ($row = $stmt->fetch()) {
        $required = $row['is_required'] ? '✅' : '';
        $public = $row['is_public'] ? '🌐' : '🔒';
        echo "<tr>
                <td style='padding: 8px;'>{$row['setting_key']}</td>
                <td style='padding: 8px;'>" . substr($row['setting_value'], 0, 30) . (strlen($row['setting_value']) > 30 ? '...' : '') . "</td>
                <td style='padding: 8px;'>{$row['category']}</td>
                <td style='padding: 8px;'>{$row['display_name_ar']}</td>
                <td style='padding: 8px; text-align: center;'>$required</td>
                <td style='padding: 8px; text-align: center;'>$public</td>
              </tr>";
    }
    echo "</table>";

    echo "<div class='success'><h2>🎉 تم إنشاء جداول إعدادات المتجر بنجاح!</h2></div>";
    echo "<p>تم إنشاء " . count($tables) . " جداول مع الإعدادات والتكوينات الافتراضية.</p>";
    echo "<p>يمكنك الآن استخدام قسم إعدادات المتجر في لوحة الإدارة.</p>";

} catch (Exception $e) {
    echo "<div class='error'><h2>❌ خطأ في إنشاء الجداول:</h2>";
    echo "<p>" . $e->getMessage() . "</p></div>";
}

echo "
        <a href='../index.html' class='back-link'>← العودة إلى لوحة الإدارة</a>
        <a href='../php/store_settings.php?action=get_all' class='back-link' style='background: #28a745;'>🔍 اختبار API</a>
    </div>
</body>
</html>";
?>
