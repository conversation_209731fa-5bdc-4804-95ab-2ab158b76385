/**
 * User Management System
 * نظام إدارة المستخدمين
 */

const userManagement = {
    // User data
    users: [],
    filteredUsers: [],
    selectedUsers: new Set(),
    editingUserId: null,
    
    // Pagination settings
    pagination: {
        currentPage: 1,
        itemsPerPage: 10,
        totalItems: 0
    },

    // User roles configuration
    roles: {
        super_admin: {
            name: 'مدير النظام',
            permissions: ['all'],
            color: 'danger',
            icon: 'fas fa-crown'
        },
        admin: {
            name: 'مدير',
            permissions: ['manage_users', 'manage_content', 'manage_settings'],
            color: 'warning',
            icon: 'fas fa-user-shield'
        },
        editor: {
            name: 'محرر',
            permissions: ['manage_content'],
            color: 'info',
            icon: 'fas fa-edit'
        },
        customer: {
            name: 'عميل',
            permissions: ['view_content'],
            color: 'success',
            icon: 'fas fa-user'
        }
    },

// Subscription limits configuration
const SUBSCRIPTION_LIMITS = {
    'free': {
        name: 'مجاني',
        products: 5,
        landing_pages: 2,
        storage: 100, // MB
        templates: 3
    },
    'basic': {
        name: 'أساسي',
        products: 50,
        landing_pages: 10,
        storage: 1000, // MB
        templates: 10
    },
    'premium': {
        name: 'مميز',
        products: 500,
        landing_pages: 50,
        storage: 5000, // MB
        templates: 50
    },
    'unlimited': {
        name: 'غير محدود',
        products: -1, // unlimited
        landing_pages: -1,
        storage: -1,
        templates: -1
    }
};

    // Initialize the user management system
    async initialize() {
        console.log('🔧 Initializing user management...');
        
        try {
            // Load initial user data
            await this.loadUsers();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initialize search and filters
            this.initializeSearchAndFilters();
            
            console.log('✅ User management initialized');
        } catch (error) {
            console.error('❌ Error initializing user management:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل بيانات المستخدمين');
        }
    },

    // Load users from API
    async loadUsers(page = 1, filters = {}) {
        try {
            showLoadingState();
            const queryParams = new URLSearchParams({
                page: page,
                limit: this.pagination.itemsPerPage,
                ...filters
            });

            const response = await fetch(`../api/users.php?${queryParams}`);
            if (!response.ok) throw new Error('Network response was not ok');
            
            const data = await response.json();
            if (!data.success) throw new Error(data.message || 'Failed to load users');
            
            this.users = data.users || [];
            this.filteredUsers = [...this.users];
            this.renderUsers();
            this.updatePagination(data.total);
            this.updateSummary(data.stats);
            
            hideLoadingState();
        } catch (error) {
            console.error('Error loading users:', error);
            showErrorState('خطأ في تحميل المستخدمين: ' + error.message);
            this.loadSampleUsers(); // Fallback to sample data
        }
    },

    // Load sample users as fallback
    loadSampleUsers() {
        this.users = [
            {
                id: 1,
                name: 'مصعب (المدير العام)',
                email: '<EMAIL>',
                role: 'admin',
                status: 'active',
                avatar: 'https://via.placeholder.com/40',
                phone: '+213 555 123 456',
                registeredAt: '2024-01-01',
                lastLogin: new Date().toISOString().slice(0, 16).replace('T', ' '),
                notes: 'مدير النظام الرئيسي - صلاحيات كاملة',
                subscription: 'unlimited',
                store_id: 1
            },
            {
                id: 2,
                name: 'فاطمة علي',
                email: '<EMAIL>',
                role: 'store_owner',
                status: 'active',
                avatar: 'https://via.placeholder.com/40',
                phone: '+213 555 789 012',
                registeredAt: '2024-01-10',
                lastLogin: '2024-01-19 09:15',
                notes: 'صاحبة متجر إلكتروني',
                subscription: 'premium',
                store_id: 2
            },
            {
                id: 3,
                name: 'محمد حسن',
                email: '<EMAIL>',
                role: 'customer',
                status: 'inactive',
                avatar: 'https://via.placeholder.com/40',
                phone: '+213 555 345 678',
                registeredAt: '2024-01-05',
                lastLogin: '2024-01-18 16:45',
                notes: 'عميل مميز',
                subscription: 'basic',
                store_id: null
            }
        ];

        this.filteredUsers = [...this.users];
        this.renderUsers();
        this.updateSummary({
            total: this.users.length,
            active: this.users.filter(u => u.status === 'active').length,
            admin: this.users.filter(u => u.role === 'admin').length
        });
    },

    // Render users table
    renderUsers() {
        const tbody = document.getElementById('usersTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';
        this.filteredUsers.forEach(user => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>
                    <input type="checkbox" class="user-select" value="${user.id}"
                           ${this.selectedUsers.has(user.id) ? 'checked' : ''}>
                </td>
                <td>
                    <img src="${user.avatar || '../assets/images/default-avatar.png'}" 
                         alt="${user.name}" 
                         class="user-avatar">
                    ${user.name}
                </td>
                <td>${user.email}</td>
                <td>
                    <span class="role-badge ${user.role}">
                        ${this.roles[user.role]?.name || user.role}
                    </span>
                </td>
                <td>
                    <span class="status-badge ${user.status}">
                        ${this.getStatusText(user.status)}
                    </span>
                </td>
                <td>${new Date(user.registeredAt).toLocaleDateString('ar-DZ')}</td>
                <td>${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-DZ') : '--'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-edit" onclick="userManagement.showUserModal(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-delete" onclick="userManagement.deleteUser(${user.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(tr);
        });

        this.updateBulkActions();
    },

    // Get status text in Arabic
    getStatusText(status) {
        const statusMap = {
            active: 'نشط',
            inactive: 'غير نشط',
            suspended: 'معلق'
        };
        return statusMap[status] || status;
    },

    // Update pagination
    updatePagination(total) {
        this.pagination.totalItems = total;
        const totalPages = Math.ceil(total / this.pagination.itemsPerPage);

        const paginationInfo = document.getElementById('paginationInfo');
        if (paginationInfo) {
            paginationInfo.textContent = 
                `عرض ${(this.pagination.currentPage - 1) * this.pagination.itemsPerPage + 1} - ${Math.min(this.pagination.currentPage * this.pagination.itemsPerPage, total)} من ${total}`;
        }

        const pageNumbers = document.getElementById('pageNumbers');
        if (pageNumbers) {
            pageNumbers.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                const button = document.createElement('button');
                button.className = `btn btn-sm ${i === this.pagination.currentPage ? 'active' : ''}`;
                button.textContent = i;
                button.onclick = () => this.changePage(i);
                pageNumbers.appendChild(button);
            }
        }

        const prevPage = document.getElementById('prevPage');
        const nextPage = document.getElementById('nextPage');
        if (prevPage) prevPage.disabled = this.pagination.currentPage === 1;
        if (nextPage) nextPage.disabled = this.pagination.currentPage === totalPages;
    },

    // Change page
    async changePage(page) {
        this.pagination.currentPage = page;
        await this.loadUsers(page, this.getCurrentFilters());
    },

    // Update summary statistics
    updateSummary(stats) {
        const elements = {
            total: document.getElementById('totalUsers'),
            active: document.getElementById('activeUsers'),
            admin: document.getElementById('adminUsers')
        };

        if (elements.total) elements.total.textContent = stats.total || 0;
        if (elements.active) elements.active.textContent = stats.active || 0;
        if (elements.admin) elements.admin.textContent = stats.admin || 0;
    },

    // Setup event listeners
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('userSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => {
                this.pagination.currentPage = 1;
                this.loadUsers(1, this.getCurrentFilters());
            }, 300));
        }

        // Filters
        ['roleFilter', 'statusFilter'].forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', () => {
                    this.pagination.currentPage = 1;
                    this.loadUsers(1, this.getCurrentFilters());
                });
            }
        });

        // Select all checkbox
        const selectAll = document.getElementById('selectAllUsers');
        if (selectAll) {
            selectAll.addEventListener('change', () => {
                const checkboxes = document.querySelectorAll('.user-select');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAll.checked;
                    const userId = parseInt(checkbox.value);
                    if (selectAll.checked) {
                        this.selectedUsers.add(userId);
                    } else {
                        this.selectedUsers.delete(userId);
                    }
                });
                this.updateBulkActions();
            });
        }

        // Individual user checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('user-select')) {
                const userId = parseInt(e.target.value);
                if (e.target.checked) {
                    this.selectedUsers.add(userId);
                } else {
                    this.selectedUsers.delete(userId);
                }
                this.updateBulkActions();
            }
        });

        // Bulk action buttons
        const bulkActionButtons = document.querySelectorAll('.bulk-action-btn');
        bulkActionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const action = button.dataset.action;
                if (action) this.bulkAction(action);
            });
        });

        // Export users button
        const exportBtn = document.getElementById('exportUsers');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportUsers());
        }
    },

    // Initialize search and filters
    initializeSearchAndFilters() {
        // Populate role filter
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            Object.entries(this.roles).forEach(([value, role]) => {
                const option = document.createElement('option');
                option.value = value;
                option.textContent = role.name;
                roleFilter.appendChild(option);
            });
        }

        // Initialize status filter
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            const statuses = [
                { value: 'active', text: 'نشط' },
                { value: 'inactive', text: 'غير نشط' },
                { value: 'suspended', text: 'معلق' }
            ];

            statuses.forEach(status => {
                const option = document.createElement('option');
                option.value = status.value;
                option.textContent = status.text;
                statusFilter.appendChild(option);
            });
        }
    },

    // Get current filters
    getCurrentFilters() {
        return {
            search: document.getElementById('userSearch')?.value || '',
            role: document.getElementById('roleFilter')?.value || '',
            status: document.getElementById('statusFilter')?.value || ''
        };
    },

    // Update bulk actions visibility
    updateBulkActions() {
        const selectedUsers = document.querySelectorAll('.user-select:checked');
        const bulkActions = document.getElementById('bulkActions');
        const selectedCount = document.getElementById('selectedCount');

        if (bulkActions && selectedCount) {
            bulkActions.style.display = selectedUsers.length > 0 ? 'flex' : 'none';
            selectedCount.textContent = selectedUsers.length;
        }
    },

    // Show add/edit user modal
    async showUserModal(userId = null) {
        const modal = document.getElementById('userModal');
        const title = document.getElementById('modalTitle');
        const form = document.getElementById('userForm');

        if (!modal || !title || !form) return;

        if (userId) {
            title.textContent = 'تعديل المستخدم';
            await this.loadUserData(userId);
        } else {
            title.textContent = 'إضافة مستخدم جديد';
            form.reset();
            this.editingUserId = null;
        }

        modal.style.display = 'block';
    },

    // Load user data for editing
    async loadUserData(userId) {
        try {
            const response = await fetch(`../api/users.php?id=${userId}`);
            if (!response.ok) throw new Error('Network response was not ok');
            
            const data = await response.json();
            if (!data.success) throw new Error(data.message || 'Failed to load user data');
            
            const user = data.user;
            this.editingUserId = user.id;

            // Populate form fields
            const fields = ['name', 'email', 'phone', 'role', 'status'];
            fields.forEach(field => {
                const element = document.getElementById(`user${field.charAt(0).toUpperCase() + field.slice(1)}`);
                if (element) element.value = user[field] || '';
            });
        } catch (error) {
            console.error('Error loading user data:', error);
            notificationManager.showError('خطأ في تحميل بيانات المستخدم');
        }
    },

    // Save user
    async saveUser(event) {
        event.preventDefault();
        const form = event.target;
        const formData = new FormData(form);
        const userData = Object.fromEntries(formData.entries());

        try {
            const response = await fetch('../api/users.php', {
                method: this.editingUserId ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ...userData,
                    id: this.editingUserId
                })
            });

            const data = await response.json();
            if (!data.success) throw new Error(data.message || 'Failed to save user');

            notificationManager.showSuccess(this.editingUserId ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح');
            this.closeUserModal();
            this.loadUsers(this.pagination.currentPage, this.getCurrentFilters());
        } catch (error) {
            console.error('Error saving user:', error);
            notificationManager.showError('خطأ في حفظ بيانات المستخدم');
        }
    },

    // Delete user
    async deleteUser(userId) {
        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;

        try {
            const response = await fetch(`../api/users.php?id=${userId}`, {
                method: 'DELETE'
            });

            const data = await response.json();
            if (!data.success) throw new Error(data.message || 'Failed to delete user');

            notificationManager.showSuccess('تم حذف المستخدم بنجاح');
            this.loadUsers(this.pagination.currentPage, this.getCurrentFilters());
        } catch (error) {
            console.error('Error deleting user:', error);
            notificationManager.showError('خطأ في حذف المستخدم');
        }
    },

    // Perform bulk action
    async bulkAction(action) {
        const selectedUsers = Array.from(document.querySelectorAll('.user-select:checked'))
            .map(checkbox => parseInt(checkbox.value));

        if (selectedUsers.length === 0) return;

        const confirmMessages = {
            activate: 'هل أنت متأكد من تفعيل المستخدمين المحددين؟',
            deactivate: 'هل أنت متأكد من إلغاء تفعيل المستخدمين المحددين؟',
            delete: 'هل أنت متأكد من حذف المستخدمين المحددين؟'
        };

        if (!confirm(confirmMessages[action])) return;

        try {
            const response = await fetch('../api/users-bulk.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    users: selectedUsers
                })
            });

            const data = await response.json();
            if (!data.success) throw new Error(data.message || 'Failed to perform bulk action');

            notificationManager.showSuccess('تم تنفيذ الإجراء بنجاح');
            this.selectedUsers.clear();
            this.loadUsers(this.pagination.currentPage, this.getCurrentFilters());
        } catch (error) {
            console.error('Error performing bulk action:', error);
            notificationManager.showError('خطأ في تنفيذ الإجراء');
        }
    },

    // Export users
    async exportUsers() {
        try {
            const response = await fetch('../api/users-export.php');
            if (!response.ok) throw new Error('Network response was not ok');
            
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'users-export.csv';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            a.remove();

            notificationManager.showSuccess('تم تصدير المستخدمين بنجاح');
        } catch (error) {
            console.error('Error exporting users:', error);
            notificationManager.showError('خطأ في تصدير المستخدمين');
        }
    },

    // Close user modal
    closeUserModal() {
        const modal = document.getElementById('userModal');
        if (modal) modal.style.display = 'none';
    },

    // Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    userManagement.initialize();
});
    initializeSubscriptionManagement();

    // Initialize role-based access control
    initializeRoleBasedAccess();
}

/**
 * Initialize bulk actions
 */
function initializeBulkActions() {
    const bulkActionSelect = document.getElementById('bulkAction');
    const applyBulkBtn = document.getElementById('applyBulkAction');

    if (applyBulkBtn) {
        applyBulkBtn.addEventListener('click', handleBulkAction);
    }
}

/**
 * Initialize user analytics
 */
function initializeUserAnalytics() {
    // This would initialize charts and analytics
    console.log('User analytics initialized');
}

/**
 * Initialize subscription management
 */
function initializeSubscriptionManagement() {
    // Initialize subscription-related functionality
    console.log('Subscription management initialized');
}

/**
 * Initialize role-based access control
 */
function initializeRoleBasedAccess() {
    // Initialize RBAC functionality
    console.log('Role-based access control initialized');
}

/**
 * Update enhanced user summary statistics
 */
function updateUserSummary() {
    const totalUsers = users.length;
    const activeUsers = users.filter(user => user.status === 'active').length;
    const adminUsers = users.filter(user => user.role === 'admin' || user.role === 'super_admin').length;
    const storeOwners = users.filter(user => user.role === 'store_owner').length;
    const customers = users.filter(user => user.role === 'customer').length;

    // Update basic statistics
    const totalUsersEl = document.getElementById('totalUsers');
    const activeUsersEl = document.getElementById('activeUsers');
    const adminUsersEl = document.getElementById('adminUsers');

    if (totalUsersEl) totalUsersEl.textContent = totalUsers;
    if (activeUsersEl) activeUsersEl.textContent = activeUsers;
    if (adminUsersEl) adminUsersEl.textContent = adminUsers;

    // Update additional statistics
    const storeOwnersEl = document.getElementById('storeOwners');
    const customersEl = document.getElementById('customers');
    const lastUpdatedEl = document.getElementById('lastUpdated');

    if (storeOwnersEl) storeOwnersEl.textContent = storeOwners;
    if (customersEl) customersEl.textContent = customers;
    if (lastUpdatedEl) lastUpdatedEl.textContent = new Date().toLocaleString('ar-DZ');

    // Update subscription statistics
    updateSubscriptionStatistics();
}

/**
 * Update subscription statistics
 */
function updateSubscriptionStatistics() {
    const subscriptionCounts = {};

    // Count users by subscription type
    users.forEach(user => {
        const subscription = user.subscription || 'free';
        subscriptionCounts[subscription] = (subscriptionCounts[subscription] || 0) + 1;
    });

    // Update subscription elements
    Object.keys(SUBSCRIPTION_LIMITS).forEach(type => {
        const element = document.getElementById(`${type}Subscribers`);
        if (element) {
            element.textContent = subscriptionCounts[type] || 0;
        }
    });
}

/**
 * Load and display users
 */
function loadUsers() {
    // Apply filters
    applyFilters();

    // Calculate pagination
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const pageUsers = filteredUsers.slice(startIndex, endIndex);

    // Display users
    displayUsers(pageUsers);

    // Update pagination
    updatePagination(totalPages);

    // Update users count
    document.getElementById('usersCount').textContent = `${filteredUsers.length} مستخدم`;
}

/**
 * Apply search and filter criteria
 */
function applyFilters() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    filteredUsers = users.filter(user => {
        const matchesSearch = user.name.toLowerCase().includes(searchTerm) ||
                            user.email.toLowerCase().includes(searchTerm);
        const matchesRole = !roleFilter || user.role === roleFilter;
        const matchesStatus = !statusFilter || user.status === statusFilter;

        return matchesSearch && matchesRole && matchesStatus;
    });

    // Reset to first page when filters change
    currentPage = 1;
}

/**
 * Display users in table
 */
function displayUsers(pageUsers) {
    const tbody = document.getElementById('usersTableBody');

    if (pageUsers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 2rem; color: #6c757d;">
                    <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>لا توجد مستخدمين مطابقين للبحث</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = pageUsers.map(user => `
        <tr class="user-row" data-user-id="${user.id}">
            <td>
                <input type="checkbox" class="user-checkbox" value="${user.id}"
                       onchange="toggleUserSelection(${user.id}, this.checked)">
            </td>
            <td>
                <div class="user-avatar-container">
                    <img src="${user.avatar || 'https://via.placeholder.com/40'}" alt="${user.name}"
                         class="user-avatar">
                    <div class="user-status-indicator ${user.status}"></div>
                </div>
            </td>
            <td>
                <div class="user-info">
                    <div class="user-name">${user.name}</div>
                    <div class="user-id">ID: ${user.id}</div>
                    ${user.phone ? `<div class="user-phone"><i class="fas fa-phone"></i> ${user.phone}</div>` : ''}
                </div>
            </td>
            <td>
                <div class="user-email">${user.email}</div>
                ${user.store_id ? `<div class="user-store">متجر #${user.store_id}</div>` : ''}
            </td>
            <td>
                <div class="role-container">
                    <span class="badge badge-${getRoleBadgeClass(user.role)}">
                        <i class="${USER_ROLES[user.role]?.icon || 'fas fa-user'}"></i>
                        ${getRoleDisplayName(user.role)}
                    </span>
                </div>
            </td>
            <td>
                <div class="subscription-container">
                    <span class="badge badge-subscription badge-${getSubscriptionBadgeClass(user.subscription)}">
                        ${getSubscriptionDisplayName(user.subscription)}
                    </span>
                    <div class="subscription-limits">
                        ${getSubscriptionLimitsDisplay(user.subscription)}
                    </div>
                </div>
            </td>
            <td>
                <span class="badge badge-${getStatusBadgeClass(user.status)}">
                    ${getStatusDisplayName(user.status)}
                </span>
            </td>
            <td>
                <div class="date-info">
                    <div class="registered-date">${formatDate(user.registeredAt)}</div>
                    <div class="last-login">${user.lastLogin ? formatDateTime(user.lastLogin) : 'لم يسجل دخول'}</div>
                </div>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary" onclick="viewUser(${user.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editUser(${user.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="manageSubscription(${user.id})" title="إدارة الاشتراك">
                        <i class="fas fa-crown"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * Get role badge CSS class
 */
function getRoleBadgeClass(role) {
    return USER_ROLES[role]?.color || 'secondary';
}

/**
 * Get subscription badge CSS class
 */
function getSubscriptionBadgeClass(subscription) {
    const classes = {
        'free': 'secondary',
        'basic': 'info',
        'premium': 'warning',
        'unlimited': 'success'
    };
    return classes[subscription] || 'secondary';
}

/**
 * Get subscription display name
 */
function getSubscriptionDisplayName(subscription) {
    return SUBSCRIPTION_LIMITS[subscription]?.name || 'غير محدد';
}

/**
 * Get subscription limits display
 */
function getSubscriptionLimitsDisplay(subscription) {
    const limits = SUBSCRIPTION_LIMITS[subscription];
    if (!limits) return '';

    const formatLimit = (value) => value === -1 ? '∞' : value;

    return `
        <small class="subscription-details">
            منتجات: ${formatLimit(limits.products)} |
            صفحات: ${formatLimit(limits.landing_pages)} |
            تخزين: ${formatLimit(limits.storage)}MB
        </small>
    `;
}

/**
 * Format date and time
 */
function formatDateTime(dateTime) {
    if (!dateTime) return '--';

    try {
        const date = new Date(dateTime);
        return date.toLocaleString('ar-DZ', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateTime;
    }
}

/**
 * Get role display name
 */
function getRoleDisplayName(role) {
    const names = {
        'admin': 'مدير عام',
        'super_admin': 'مدير عام',
        'store_owner': 'صاحب متجر',
        'editor': 'محرر',
        'customer': 'عميل'
    };
    return names[role] || role;
}

/**
 * Get status badge CSS class
 */
function getStatusBadgeClass(status) {
    const classes = {
        'active': 'success',
        'inactive': 'secondary',
        'suspended': 'danger'
    };
    return classes[status] || 'secondary';
}

/**
 * Get status display name
 */
function getStatusDisplayName(status) {
    const names = {
        'active': 'نشط',
        'inactive': 'غير نشط',
        'suspended': 'معلق'
    };
    return names[status] || status;
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Update pagination controls
 */
function updatePagination(totalPages) {
    const paginationInfo = document.getElementById('paginationInfo');
    const pageNumbers = document.getElementById('pageNumbers');
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');

    // Update info
    const startIndex = (currentPage - 1) * usersPerPage + 1;
    const endIndex = Math.min(currentPage * usersPerPage, filteredUsers.length);
    paginationInfo.textContent = `عرض ${startIndex}-${endIndex} من ${filteredUsers.length}`;

    // Update buttons
    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages;

    // Update page numbers
    pageNumbers.innerHTML = '';
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            const button = document.createElement('button');
            button.textContent = i;
            button.className = i === currentPage ? 'active' : '';
            button.onclick = () => goToPage(i);
            pageNumbers.appendChild(button);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            const span = document.createElement('span');
            span.textContent = '...';
            span.style.padding = '0 0.5rem';
            pageNumbers.appendChild(span);
        }
    }
}

/**
 * Change page
 */
function changePage(direction) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        loadUsers();
    }
}

/**
 * Go to specific page
 */
function goToPage(page) {
    currentPage = page;
    loadUsers();
}

/**
 * Toggle user selection
 */
function toggleUserSelection(userId, selected) {
    if (selected) {
        selectedUsers.add(userId);
    } else {
        selectedUsers.delete(userId);
    }

    updateBulkActions();
    updateSelectAllCheckbox();
}

/**
 * Toggle select all users
 */
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllUsers');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');

    userCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
        toggleUserSelection(parseInt(checkbox.value), checkbox.checked);
    });
}

/**
 * Update select all checkbox state
 */
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllUsers');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');

    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === userCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

/**
 * Update bulk actions visibility
 */
function updateBulkActions() {
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedUsers.size > 0) {
        bulkActions.style.display = 'block';
        selectedCount.textContent = selectedUsers.size;
    } else {
        bulkActions.style.display = 'none';
    }
}

/**
 * Add event listeners
 */
function addEventListeners() {
    // Search and filter inputs
    document.getElementById('userSearch').addEventListener('input', debounce(loadUsers, 300));
    document.getElementById('roleFilter').addEventListener('change', loadUsers);
    document.getElementById('statusFilter').addEventListener('change', loadUsers);
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Show add user modal
 */
function showAddUserModal() {
    editingUserId = null;

    // Check if modal elements exist
    const modalTitle = document.getElementById('modalTitle');
    const userForm = document.getElementById('userForm');
    const userModal = document.getElementById('userModal');

    if (!userModal) {
        console.error('User modal not found');
        console.log('Available modals:', document.querySelectorAll('[id*="modal"]'));

        // Try to reload user management content
        if (typeof loadUserManagementContent === 'function') {
            console.log('Attempting to reload user management content...');
            loadUserManagementContent().then(() => {
                // Retry after reload
                setTimeout(() => showAddUserModal(), 500);
            });
            return;
        }

        alert('خطأ: لم يتم العثور على نافذة إضافة المستخدم. يرجى إعادة تحميل الصفحة.');
        return;
    }

    if (modalTitle) {
        modalTitle.textContent = 'إضافة مستخدم جديد';
    }

    if (userForm) {
        userForm.reset();
    }

    userModal.classList.add('show');
}

/**
 * Close user modal
 */
function closeUserModal() {
    const userModal = document.getElementById('userModal');
    if (userModal) {
        userModal.classList.remove('show');
    }
    editingUserId = null;
}

/**
 * Edit user
 */
function editUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    editingUserId = userId;

    // Check if modal elements exist
    const modalTitle = document.getElementById('modalTitle');
    const userModal = document.getElementById('userModal');

    if (!userModal) {
        console.error('User modal not found');
        alert('خطأ: لم يتم العثور على نافذة تعديل المستخدم');
        return;
    }

    if (modalTitle) {
        modalTitle.textContent = 'تعديل المستخدم';
    }

    // Fill form with user data (with null checks)
    const userName = document.getElementById('userName');
    if (userName) userName.value = user.name;

    const userEmail = document.getElementById('userEmail');
    if (userEmail) userEmail.value = user.email;

    const userRole = document.getElementById('userRole');
    if (userRole) userRole.value = user.role;

    const userStatus = document.getElementById('userStatus');
    if (userStatus) userStatus.value = user.status;

    const userPhone = document.getElementById('userPhone');
    if (userPhone) userPhone.value = user.phone || '';

    const userNotes = document.getElementById('userNotes');
    if (userNotes) userNotes.value = user.notes || '';

    userModal.classList.add('show');
}

/**
 * Save user (add or edit)
 */
async function saveUser() {
    const formData = {
        username: document.getElementById('userName').value,
        email: document.getElementById('userEmail').value,
        role_id: parseInt(document.getElementById('userRole').value),
        status: document.getElementById('userStatus').value,
        phone: document.getElementById('userPhone').value,
        notes: document.getElementById('userNotes').value,
        password: document.getElementById('userPassword').value,
        subscription_id: parseInt(document.getElementById('userSubscription')?.value || 1)
    };

    // Split name into first and last name
    const nameParts = formData.username.split(' ');
    formData.first_name = nameParts[0] || '';
    formData.last_name = nameParts.slice(1).join(' ') || '';

    // Validate required fields
    if (!formData.username || !formData.email || !formData.role_id) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    try {
        let response;

        if (editingUserId) {
            // Update existing user
            formData.id = editingUserId;
            response = await fetch('../php/api/users.php?action=update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        } else {
            // Add new user
            response = await fetch('../php/api/users.php?action=create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
        }

        const result = await response.json();

        if (result.success) {
            showNotification(editingUserId ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح', 'success');

            // Refresh users list
            await loadUsersFromAPI();
            closeUserModal();
        } else {
            throw new Error(result.message || 'فشل في حفظ المستخدم');
        }

    } catch (error) {
        console.error('Error saving user:', error);
        showNotification('خطأ في حفظ المستخدم: ' + error.message, 'error');
    }
}

/**
 * View user details
 */
function viewUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const detailsContent = document.getElementById('userDetailsContent');
    detailsContent.innerHTML = `
        <div class="user-details">
            <div class="user-avatar-section">
                <img src="${user.avatar}" alt="${user.name}" style="width: 80px; height: 80px; border-radius: 50%;">
                <h4>${user.name}</h4>
                <span class="badge badge-${getStatusBadgeClass(user.status)}">${getStatusDisplayName(user.status)}</span>
            </div>
            <div class="user-info-grid">
                <div class="info-item">
                    <label>البريد الإلكتروني:</label>
                    <span>${user.email}</span>
                </div>
                <div class="info-item">
                    <label>الدور:</label>
                    <span class="badge badge-${getRoleBadgeClass(user.role)}">${getRoleDisplayName(user.role)}</span>
                </div>
                <div class="info-item">
                    <label>رقم الهاتف:</label>
                    <span>${user.phone || '--'}</span>
                </div>
                <div class="info-item">
                    <label>تاريخ التسجيل:</label>
                    <span>${formatDate(user.registeredAt)}</span>
                </div>
                <div class="info-item">
                    <label>آخر دخول:</label>
                    <span>${user.lastLogin || '--'}</span>
                </div>
                <div class="info-item">
                    <label>الملاحظات:</label>
                    <span>${user.notes || '--'}</span>
                </div>
            </div>
        </div>
    `;

    document.getElementById('userDetailsModal').classList.add('show');
}

/**
 * Close user details modal
 */
function closeUserDetailsModal() {
    document.getElementById('userDetailsModal').classList.remove('show');
}

/**
 * Delete user
 */
function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        users = users.filter(u => u.id !== userId);
        updateUserSummary();
        loadUsers();
        showNotification('تم حذف المستخدم بنجاح', 'success');
    }
}

/**
 * Bulk actions
 */
function bulkAction(action) {
    if (selectedUsers.size === 0) return;

    const selectedArray = Array.from(selectedUsers);
    let message = '';

    switch (action) {
        case 'activate':
            selectedArray.forEach(userId => {
                const user = users.find(u => u.id === userId);
                if (user) user.status = 'active';
            });
            message = 'تم تفعيل المستخدمين المحددين';
            break;

        case 'deactivate':
            selectedArray.forEach(userId => {
                const user = users.find(u => u.id === userId);
                if (user) user.status = 'inactive';
            });
            message = 'تم إلغاء تفعيل المستخدمين المحددين';
            break;

        case 'delete':
            if (confirm(`هل أنت متأكد من حذف ${selectedUsers.size} مستخدم؟`)) {
                users = users.filter(u => !selectedUsers.has(u.id));
                message = 'تم حذف المستخدمين المحددين';
            } else {
                return;
            }
            break;
    }

    // Clear selection
    selectedUsers.clear();
    updateBulkActions();

    // Refresh display
    updateUserSummary();
    loadUsers();
    showNotification(message, 'success');
}

/**
 * Export users
 */
function exportUsers() {
    try {
        // Show loading state
        const exportBtn = document.querySelector('button[onclick="exportUsers()"]');
        if (exportBtn) {
            exportBtn.disabled = true;
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التصدير...';
        }

        if (users.length === 0) {
            showNotification('لا توجد بيانات مستخدمين للتصدير', 'warning');
            return;
        }

        const csvContent = [
            ['الاسم', 'البريد الإلكتروني', 'الدور', 'الحالة', 'الهاتف', 'تاريخ التسجيل'],
            ...users.map(user => [
                user.name,
                user.email,
                getRoleDisplayName(user.role),
                getStatusDisplayName(user.status),
                user.phone || '',
                user.registeredAt
            ])
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `users-export-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        showNotification(`تم تصدير ${users.length} مستخدم بنجاح`, 'success');

    } catch (error) {
        console.error('Error exporting users:', error);
        showNotification('خطأ في تصدير المستخدمين', 'error');
    } finally {
        // Reset button state
        setTimeout(() => {
            const exportBtn = document.querySelector('button[onclick="exportUsers()"]');
            if (exportBtn) {
                exportBtn.disabled = false;
                exportBtn.innerHTML = '<i class="fas fa-download"></i> تصدير المستخدمين';
            }
        }, 1000);
    }
}

/**
 * Toggle password visibility
 */
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Enhanced user management functions
 */

/**
 * Manage user subscription
 */
async function manageSubscription(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const modal = document.getElementById('subscriptionModal');
    if (!modal) {
        createSubscriptionModal();
        return manageSubscription(userId);
    }

    // Populate modal with user data
    document.getElementById('subscriptionUserId').value = userId;
    document.getElementById('subscriptionUserName').textContent = user.name;
    document.getElementById('currentSubscription').value = user.subscription || 'free';

    // Show current limits
    updateSubscriptionLimitsPreview(user.subscription || 'free');

    // Show modal
    modal.style.display = 'block';
}

/**
 * Create subscription management modal
 */
function createSubscriptionModal() {
    const modalHTML = `
        <div id="subscriptionModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>إدارة الاشتراك</h3>
                    <button type="button" class="modal-close" onclick="closeSubscriptionModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="subscriptionForm" class="subscription-form">
                    <input type="hidden" id="subscriptionUserId">

                    <div class="form-group">
                        <label>المستخدم:</label>
                        <span id="subscriptionUserName" class="user-name-display"></span>
                    </div>

                    <div class="form-group">
                        <label for="currentSubscription">نوع الاشتراك:</label>
                        <select id="currentSubscription" name="subscription" onchange="updateSubscriptionLimitsPreview(this.value)">
                            ${Object.keys(SUBSCRIPTION_LIMITS).map(type =>
                                `<option value="${type}">${SUBSCRIPTION_LIMITS[type].name}</option>`
                            ).join('')}
                        </select>
                    </div>

                    <div id="subscriptionLimitsPreview" class="subscription-preview"></div>

                    <div class="form-actions">
                        <button type="submit" class="action-button">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                        <button type="button" class="action-button" style="background: #6c757d;" onclick="closeSubscriptionModal()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add form submission handler
    document.getElementById('subscriptionForm').addEventListener('submit', handleSubscriptionUpdate);
}

/**
 * Update subscription limits preview
 */
function updateSubscriptionLimitsPreview(subscriptionType) {
    const limits = SUBSCRIPTION_LIMITS[subscriptionType];
    const preview = document.getElementById('subscriptionLimitsPreview');

    if (!limits || !preview) return;

    const formatLimit = (value) => value === -1 ? 'غير محدود' : value;

    preview.innerHTML = `
        <div class="limits-preview">
            <h4>حدود الاشتراك:</h4>
            <div class="limits-grid">
                <div class="limit-item">
                    <i class="fas fa-box"></i>
                    <span>المنتجات: ${formatLimit(limits.products)}</span>
                </div>
                <div class="limit-item">
                    <i class="fas fa-file-alt"></i>
                    <span>صفحات الهبوط: ${formatLimit(limits.landing_pages)}</span>
                </div>
                <div class="limit-item">
                    <i class="fas fa-hdd"></i>
                    <span>التخزين: ${formatLimit(limits.storage)} ميجابايت</span>
                </div>
                <div class="limit-item">
                    <i class="fas fa-layer-group"></i>
                    <span>القوالب: ${formatLimit(limits.templates)}</span>
                </div>
            </div>
        </div>
    `;
}

/**
 * Handle subscription update
 */
async function handleSubscriptionUpdate(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const userId = document.getElementById('subscriptionUserId').value;
    const newSubscription = formData.get('subscription');

    try {
        // Show loading state
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        submitBtn.disabled = true;

        // Update user subscription (API call would go here)
        const user = users.find(u => u.id == userId);
        if (user) {
            user.subscription = newSubscription;

            // Refresh display
            loadUsers();
            updateUserSummary();

            showNotification('تم تحديث الاشتراك بنجاح', 'success');
            closeSubscriptionModal();
        }

    } catch (error) {
        console.error('Error updating subscription:', error);
        showNotification('خطأ في تحديث الاشتراك', 'error');
    }
}

/**
 * Close subscription modal
 */
function closeSubscriptionModal() {
    const modal = document.getElementById('subscriptionModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Handle bulk actions
 */
async function handleBulkAction() {
    const selectedUserIds = Array.from(selectedUsers);
    const bulkAction = document.getElementById('bulkAction')?.value;

    if (selectedUserIds.length === 0) {
        showNotification('يرجى اختيار مستخدم واحد على الأقل', 'warning');
        return;
    }

    if (!bulkAction) {
        showNotification('يرجى اختيار إجراء', 'warning');
        return;
    }

    const confirmMessage = `هل أنت متأكد من تطبيق "${bulkAction}" على ${selectedUserIds.length} مستخدم؟`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        // Apply bulk action based on selection
        switch (bulkAction) {
            case 'activate':
                await bulkUpdateUserStatus(selectedUserIds, 'active');
                break;
            case 'deactivate':
                await bulkUpdateUserStatus(selectedUserIds, 'inactive');
                break;
            case 'delete':
                await bulkDeleteUsers(selectedUserIds);
                break;
            case 'export':
                exportSelectedUsers(selectedUserIds);
                break;
        }

        // Clear selection
        selectedUsers.clear();
        updateBulkActions();

    } catch (error) {
        console.error('Bulk action error:', error);
        showNotification('خطأ في تنفيذ الإجراء المجمع', 'error');
    }
}

/**
 * Bulk update user status
 */
async function bulkUpdateUserStatus(userIds, status) {
    // Update users in memory (API call would go here)
    users.forEach(user => {
        if (userIds.includes(user.id)) {
            user.status = status;
        }
    });

    // Refresh display
    loadUsers();
    updateUserSummary();

    showNotification(`تم تحديث حالة ${userIds.length} مستخدم بنجاح`, 'success');
}

/**
 * Bulk delete users
 */
async function bulkDeleteUsers(userIds) {
    // Remove users from memory (API call would go here)
    users = users.filter(user => !userIds.includes(user.id));
    filteredUsers = filteredUsers.filter(user => !userIds.includes(user.id));

    // Refresh display
    loadUsers();
    updateUserSummary();

    showNotification(`تم حذف ${userIds.length} مستخدم بنجاح`, 'success');
}

/**
 * Export selected users
 */
function exportSelectedUsers(userIds) {
    const selectedUsersData = users.filter(user => userIds.includes(user.id));
    const dataStr = JSON.stringify(selectedUsersData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `users_export_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);

    showNotification(`تم تصدير ${userIds.length} مستخدم بنجاح`, 'success');
}

/**
 * Show loading state
 */
function showLoadingState() {
    const tbody = document.getElementById('usersTableBody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 3rem;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                    <p style="margin-top: 1rem; color: #666;">جاري تحميل المستخدمين...</p>
                </td>
            </tr>
        `;
    }
}

/**
 * Show error state
 */
function showErrorState(message) {
    const tbody = document.getElementById('usersTableBody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 3rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #dc2626;"></i>
                    <p style="margin-top: 1rem; color: #dc2626;">${message}</p>
                    <button onclick="loadUsersFromAPI()" class="retry-btn" style="margin-top: 1rem;">إعادة المحاولة</button>
                </td>
            </tr>
        `;
    }
}

// Make functions globally available
window.initializeUserManagement = initializeUserManagement;
window.showAddUserModal = showAddUserModal;
window.closeUserModal = closeUserModal;
window.exportUsers = exportUsers;
window.bulkAction = bulkAction;
window.changePage = changePage;
window.toggleSelectAll = toggleSelectAll;
window.toggleUserSelection = toggleUserSelection;
window.viewUser = viewUser;
window.editUser = editUser;
window.deleteUser = deleteUser;
window.manageSubscription = manageSubscription;
window.closeSubscriptionModal = closeSubscriptionModal;
window.closeUserDetailsModal = closeUserDetailsModal;
window.handleBulkAction = handleBulkAction;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeUserManagement();
});

console.log('👥 Enhanced User management script loaded');
