/**
 * General Settings Management JavaScript
 * إدارة الإعدادات العامة - JavaScript
 */

// Global variables
let currentSettings = {};
let isLoading = false;

/**
 * Load and render general settings form
 */
function loadGeneralSettingsContent() {
    console.log('🔧 Loading general settings content...');

    const container = document.getElementById('generalSettingsContent');
    if (!container) {
        console.error('General settings container not found');
        return;
    }

    // Show loading state
    showLoadingState(container);

    // Load settings from server
    fetchGeneralSettings()
        .then(data => {
            if (data.success) {
                currentSettings = data.data;
                renderGeneralSettingsForm(data.data);
            } else {
                throw new Error(data.message || 'فشل في تحميل الإعدادات');
            }
        })
        .catch(error => {
            console.error('Error loading general settings:', error);
            showErrorState(container, error.message);
        });
}

/**
 * Fetch settings from server
 */
async function fetchGeneralSettings() {
    try {
        const response = await fetch('php/general_settings.php?action=get_all');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        throw new Error('خطأ في الاتصال بالخادم: ' + error.message);
    }
}

/**
 * Show loading state
 */
function showLoadingState(container) {
    container.innerHTML = `
        <div class="loading-state" style="text-align: center; padding: 40px;">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
            </div>
            <p style="color: #666;">جاري تحميل الإعدادات العامة...</p>
        </div>
    `;
}

/**
 * Show error state
 */
function showErrorState(container, message) {
    container.innerHTML = `
        <div class="error-state" style="text-align: center; padding: 40px; color: #dc3545;">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
            </div>
            <h4>خطأ في تحميل الإعدادات العامة</h4>
            <p>${message}</p>
            <button class="btn btn-primary" onclick="loadGeneralSettingsContent()">
                <i class="fas fa-redo"></i> إعادة المحاولة
            </button>
        </div>
    `;
}

/**
 * Render the general settings form
 */
function renderGeneralSettingsForm(settingsGroups) {
    const container = document.getElementById('generalSettingsContent');

    let formHTML = `
        <div class="settings-container">
            <div class="settings-header">
                <h2><i class="fas fa-cog"></i> الإعدادات العامة</h2>
                <p>إدارة الإعدادات الأساسية للموقع والنظام</p>
            </div>

            <form id="generalSettingsForm" class="settings-form">
    `;

    // Render each settings group
    const groupTitles = {
        'site': 'إعدادات الموقع',
        'localization': 'اللغة والمنطقة',
        'email': 'إعدادات البريد الإلكتروني',
        'notifications': 'الإشعارات',
        'security': 'الأمان',
        'performance': 'الأداء'
    };

    const groupIcons = {
        'site': 'fas fa-globe',
        'localization': 'fas fa-language',
        'email': 'fas fa-envelope',
        'notifications': 'fas fa-bell',
        'security': 'fas fa-shield-alt',
        'performance': 'fas fa-tachometer-alt'
    };

    for (const [groupKey, settings] of Object.entries(settingsGroups)) {
        const groupTitle = groupTitles[groupKey] || groupKey;
        const groupIcon = groupIcons[groupKey] || 'fas fa-cog';

        formHTML += `
            <div class="settings-group" style="margin-bottom: 30px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
                <div class="settings-group-header" style="background: #f8f9fa; padding: 15px; border-bottom: 1px solid #e0e0e0;">
                    <h3 style="margin: 0; color: #333;"><i class="${groupIcon}"></i> ${groupTitle}</h3>
                </div>
                <div class="settings-group-content" style="padding: 20px;">
        `;

        settings.forEach(setting => {
            formHTML += renderSettingField(setting);
        });

        formHTML += `
                </div>
            </div>
        `;
    }

    formHTML += `
                <div class="settings-actions" style="text-align: center; padding: 20px; border-top: 1px solid #e0e0e0; margin-top: 20px;">
                    <button type="submit" class="btn btn-primary" style="margin-left: 10px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetGeneralSettings()" style="margin-left: 10px; padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                    <button type="button" class="btn btn-info" onclick="showSettingsHistory()" style="padding: 10px 20px; background: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        <i class="fas fa-history"></i> سجل التغييرات
                    </button>
                </div>
            </form>
        </div>
    `;

    container.innerHTML = formHTML;

    // Bind form events
    bindGeneralSettingsEvents();
}

/**
 * Render individual setting field
 */
function renderSettingField(setting) {
    const value = setting.setting_value || '';
    const isRequired = setting.is_required == 1;
    const requiredAttr = isRequired ? 'required' : '';
    const requiredMark = isRequired ? '<span class="required" style="color: red;">*</span>' : '';

    let fieldHTML = `
        <div class="form-group" style="margin-bottom: 20px;">
            <label for="${setting.setting_key}" style="display: block; margin-bottom: 5px; font-weight: bold;">
                ${setting.setting_label_ar}
                ${requiredMark}
            </label>
    `;

    switch (setting.setting_type) {
        case 'text':
        case 'email':
        case 'url':
        case 'number':
            fieldHTML += `
                <input type="${setting.setting_type}"
                       id="${setting.setting_key}"
                       name="${setting.setting_key}"
                       value="${escapeHtml(value)}"
                       class="form-control"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"
                       ${requiredAttr}>
            `;
            break;

        case 'textarea':
            fieldHTML += `
                <textarea id="${setting.setting_key}"
                         name="${setting.setting_key}"
                         class="form-control"
                         rows="3"
                         style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"
                         ${requiredAttr}>${escapeHtml(value)}</textarea>
            `;
            break;

        case 'boolean':
            const checked = value == '1' ? 'checked' : '';
            fieldHTML += `
                <div class="form-check" style="margin-top: 5px;">
                    <input type="checkbox"
                           id="${setting.setting_key}"
                           name="${setting.setting_key}"
                           value="1"
                           class="form-check-input"
                           style="margin-left: 8px;"
                           ${checked}>
                    <label class="form-check-label" for="${setting.setting_key}">
                        تفعيل
                    </label>
                </div>
            `;
            break;

        case 'select':
            fieldHTML += renderSelectField(setting, value);
            break;

        case 'file':
            fieldHTML += `
                <input type="file"
                       id="${setting.setting_key}"
                       name="${setting.setting_key}"
                       class="form-control"
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"
                       accept="image/*">
                ${value ? `<div class="current-file" style="margin-top: 5px; font-size: 0.9em; color: #666;">الملف الحالي: <a href="../${value}" target="_blank">${value}</a></div>` : ''}
            `;
            break;
    }

    if (setting.setting_description_ar) {
        fieldHTML += `<small class="form-text text-muted" style="display: block; margin-top: 5px; color: #666; font-size: 0.9em;">${setting.setting_description_ar}</small>`;
    }

    fieldHTML += `</div>`;

    return fieldHTML;
}

/**
 * Render select field with options
 */
function renderSelectField(setting, value) {
    let options = '';

    switch (setting.setting_key) {
        case 'default_language':
            options = `
                <option value="ar" ${value === 'ar' ? 'selected' : ''}>العربية</option>
                <option value="en" ${value === 'en' ? 'selected' : ''}>English</option>
                <option value="fr" ${value === 'fr' ? 'selected' : ''}>Français</option>
            `;
            break;

        case 'timezone':
            options = `
                <option value="Asia/Riyadh" ${value === 'Asia/Riyadh' ? 'selected' : ''}>الرياض (GMT+3)</option>
                <option value="Africa/Algiers" ${value === 'Africa/Algiers' ? 'selected' : ''}>الجزائر (GMT+1)</option>
                <option value="Africa/Cairo" ${value === 'Africa/Cairo' ? 'selected' : ''}>القاهرة (GMT+2)</option>
                <option value="UTC" ${value === 'UTC' ? 'selected' : ''}>UTC</option>
            `;
            break;

        case 'currency':
            options = `
                <option value="DZD" ${value === 'DZD' ? 'selected' : ''}>دينار جزائري (DZD)</option>
                <option value="SAR" ${value === 'SAR' ? 'selected' : ''}>ريال سعودي (SAR)</option>
                <option value="USD" ${value === 'USD' ? 'selected' : ''}>دولار أمريكي (USD)</option>
                <option value="EUR" ${value === 'EUR' ? 'selected' : ''}>يورو (EUR)</option>
            `;
            break;

        case 'date_format':
            options = `
                <option value="Y-m-d" ${value === 'Y-m-d' ? 'selected' : ''}>2024-01-20</option>
                <option value="d/m/Y" ${value === 'd/m/Y' ? 'selected' : ''}>20/01/2024</option>
                <option value="d-m-Y" ${value === 'd-m-Y' ? 'selected' : ''}>20-01-2024</option>
            `;
            break;

        case 'smtp_encryption':
            options = `
                <option value="tls" ${value === 'tls' ? 'selected' : ''}>TLS</option>
                <option value="ssl" ${value === 'ssl' ? 'selected' : ''}>SSL</option>
                <option value="none" ${value === 'none' ? 'selected' : ''}>بدون تشفير</option>
            `;
            break;

        default:
            options = `<option value="${escapeHtml(value)}">${escapeHtml(value)}</option>`;
    }

    return `
        <select id="${setting.setting_key}"
                name="${setting.setting_key}"
                class="form-control"
                style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            ${options}
        </select>
    `;
}

/**
 * Bind form events
 */
function bindGeneralSettingsEvents() {
    const form = document.getElementById('generalSettingsForm');
    if (form) {
        form.addEventListener('submit', handleGeneralSettingsSubmit);
    }
}

/**
 * Handle form submission
 */
async function handleGeneralSettingsSubmit(event) {
    event.preventDefault();

    if (isLoading) return;

    isLoading = true;
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    try {
        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData(event.target);
        formData.append('action', 'update');

        // Send request
        const response = await fetch('php/general_settings.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
            // Reload settings to reflect changes
            setTimeout(() => {
                loadGeneralSettingsContent();
            }, 1000);
        } else {
            throw new Error(result.message || 'فشل في حفظ الإعدادات');
        }

    } catch (error) {
        console.error('Error saving settings:', error);
        showNotification('خطأ في حفظ الإعدادات: ' + error.message, 'error');
    } finally {
        isLoading = false;
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

/**
 * Reset settings to original values
 */
function resetGeneralSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        loadGeneralSettingsContent();
    }
}

/**
 * Show settings history
 */
function showSettingsHistory() {
    // TODO: Implement settings history modal
    showNotification('سجل التغييرات قيد التطوير', 'info');
}

/**
 * Utility function to escape HTML
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Use existing notification system if available
    if (typeof notificationManager !== 'undefined') {
        switch (type) {
            case 'success':
                notificationManager.showSuccess(message);
                break;
            case 'error':
                notificationManager.showError(message);
                break;
            default:
                notificationManager.showInfo(message);
        }
    } else {
        // Fallback to alert
        alert(message);
    }
}

// Make functions globally available
window.loadGeneralSettingsContent = loadGeneralSettingsContent;
window.resetGeneralSettings = resetGeneralSettings;
window.showSettingsHistory = showSettingsHistory;
