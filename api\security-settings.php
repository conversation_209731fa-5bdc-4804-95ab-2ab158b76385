<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Default security settings
$defaultSettings = [
    'authentication' => [
        'requireStrongPassword' => true,
        'enableTwoFactor' => false,
        'sessionTimeout' => 30,
        'maxLoginAttempts' => 5
    ],
    'accessControl' => [
        'enableIPWhitelist' => false,
        'ipWhitelist' => [],
        'enableGeoBlocking' => false,
        'blockedCountries' => []
    ],
    'dataProtection' => [
        'enableDataEncryption' => true,
        'enableBackupEncryption' => true,
        'backupRetention' => 30,
        'dataRetention' => 365
    ],
    'monitoring' => [
        'enableActivityLogging' => true,
        'logRetentionDays' => 90,
        'alertOnSuspiciousActivity' => true,
        'notifyAdminOnFailedLogin' => true
    ]
];

// Handle GET request
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    echo json_encode([
        'success' => true,
        'settings' => $defaultSettings
    ]);
    exit();
}

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    
    if ($data) {
        // Here you would typically validate and save the settings
        // For now, we'll just return success
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ الإعدادات بنجاح'
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'بيانات غير صالحة'
        ]);
    }
    exit();
}

// Handle unsupported methods
http_response_code(405);
echo json_encode([
    'success' => false,
    'message' => 'طريقة غير مدعومة'
]);