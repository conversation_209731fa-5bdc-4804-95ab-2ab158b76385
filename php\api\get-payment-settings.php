<?php
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Prevent any output before JSON
ob_start();

try {
    // Database connection
    require_once '../config/database.php';
    
    $response = [
        'success' => true,
        'data' => [
            'payment_methods' => [
                'paypal' => [
                    'enabled' => true,
                    'mode' => 'sandbox',
                    'client_id' => 'test_client_id',
                    'currency' => 'USD'
                ],
                'stripe' => [
                    'enabled' => false,
                    'mode' => 'test',
                    'publishable_key' => '',
                    'currency' => 'USD'
                ],
                'bank_transfer' => [
                    'enabled' => true,
                    'account_details' => 'بنك الأهلي المصري - رقم الحساب: *********'
                ],
                'cash_on_delivery' => [
                    'enabled' => true,
                    'fee' => 10,
                    'currency' => 'EGP'
                ]
            ],
            'currency_settings' => [
                'default_currency' => 'EGP',
                'supported_currencies' => ['EGP', 'USD', 'EUR', 'SAR'],
                'exchange_rates' => [
                    'USD' => 30.85,
                    'EUR' => 33.20,
                    'SAR' => 8.23
                ]
            ],
            'tax_settings' => [
                'tax_enabled' => true,
                'tax_rate' => 14,
                'tax_inclusive' => false
            ],
            'shipping_settings' => [
                'free_shipping_threshold' => 500,
                'default_shipping_cost' => 50,
                'express_shipping_cost' => 100
            ]
        ],
        'message' => 'Payment settings retrieved successfully'
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => 'Failed to retrieve payment settings',
        'message' => $e->getMessage(),
        'data' => []
    ];
}

// Clear any output buffer and send JSON
ob_clean();
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;
?>
