<?php

/**
 * Database Connectivity Test API
 * Tests all admin panel sections for proper database connectivity
 * Provides comprehensive report on API status and database table access
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../ApiErrorHandler.php';
require_once __DIR__ . '/../../config/environment.php';

// Initialize error handler
ApiErrorHandler::init();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

try {
    $pdo = EnvironmentConfig::getInstance()->getDatabaseConnection();
    
    // Define all admin panel sections and their corresponding APIs/tables
    $adminSections = [
        'users_management' => [
            'name' => 'إدارة المستخدمين',
            'api' => 'users.php?action=list',
            'table' => 'users',
            'description' => 'إدارة المستخدمين العاديين والمديرين'
        ],
        'roles_management' => [
            'name' => 'إدارة الأدوار',
            'api' => 'roles-fixed.php?action=list',
            'table' => 'user_roles',
            'description' => 'إدارة أدوار المستخدمين والصلاحيات'
        ],
        'categories_management' => [
            'name' => 'إدارة الفئات',
            'api' => 'categories-fixed.php?action=list',
            'table' => 'categories',
            'description' => 'إدارة فئات المنتجات'
        ],
        'products_management' => [
            'name' => 'إدارة المنتجات',
            'api' => 'products.php?action=list',
            'table' => 'produits',
            'description' => 'إدارة المنتجات والخدمات'
        ],
        'stores_management' => [
            'name' => 'إدارة المتاجر',
            'api' => 'stores.php?action=list',
            'table' => 'stores',
            'description' => 'إدارة المتاجر وأصحابها'
        ],
        'subscriptions_management' => [
            'name' => 'إدارة الاشتراكات',
            'api' => 'subscriptions-fixed.php?action=plans',
            'table' => 'subscription_plans',
            'description' => 'إدارة خطط الاشتراك والمشتركين'
        ],
        'orders_management' => [
            'name' => 'إدارة الطلبات',
            'api' => 'orders.php?action=list',
            'table' => 'commandes',
            'description' => 'إدارة الطلبات والمبيعات'
        ],
        'general_settings' => [
            'name' => 'الإعدادات العامة',
            'api' => 'general-settings.php?action=get',
            'table' => 'general_settings',
            'description' => 'إعدادات النظام العامة'
        ],
        'payment_settings' => [
            'name' => 'إعدادات الدفع',
            'api' => 'payment-settings.php?action=get',
            'table' => 'payment_settings',
            'description' => 'إعدادات طرق الدفع'
        ],
        'security_settings' => [
            'name' => 'إعدادات الأمان',
            'api' => 'security-settings.php?action=get',
            'table' => 'security_settings',
            'description' => 'إعدادات الأمان والحماية'
        ]
    ];

    $testResults = [];
    $overallStatus = 'success';
    $totalSections = count($adminSections);
    $workingSections = 0;
    $failedSections = 0;

    foreach ($adminSections as $sectionKey => $section) {
        $result = [
            'section' => $sectionKey,
            'name' => $section['name'],
            'description' => $section['description'],
            'api_status' => 'unknown',
            'table_status' => 'unknown',
            'table_count' => 0,
            'api_response_time' => 0,
            'errors' => []
        ];

        // Test database table access
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$section['table']}");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            
            $result['table_status'] = 'success';
            $result['table_count'] = (int)$count;
        } catch (Exception $e) {
            $result['table_status'] = 'error';
            $result['errors'][] = 'Database table error: ' . $e->getMessage();
        }

        // Test API endpoint
        try {
            $apiUrl = "http://localhost:8000/php/api/{$section['api']}";
            $startTime = microtime(true);
            
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => 5,
                    'ignore_errors' => true
                ]
            ]);
            
            $response = file_get_contents($apiUrl, false, $context);
            $endTime = microtime(true);
            
            $result['api_response_time'] = round(($endTime - $startTime) * 1000, 2); // ms
            
            if ($response !== false) {
                $httpCode = 200;
                if (isset($http_response_header)) {
                    foreach ($http_response_header as $header) {
                        if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                            $httpCode = (int)$matches[1];
                            break;
                        }
                    }
                }
                
                if ($httpCode === 200) {
                    $jsonData = json_decode($response, true);
                    if (json_last_error() === JSON_ERROR_NONE && isset($jsonData['success']) && $jsonData['success']) {
                        $result['api_status'] = 'success';
                        $workingSections++;
                    } else {
                        $result['api_status'] = 'error';
                        $result['errors'][] = 'API returned invalid JSON or error response';
                        $failedSections++;
                    }
                } else {
                    $result['api_status'] = 'error';
                    $result['errors'][] = "API returned HTTP $httpCode";
                    $failedSections++;
                }
            } else {
                $result['api_status'] = 'error';
                $result['errors'][] = 'API request failed';
                $failedSections++;
            }
        } catch (Exception $e) {
            $result['api_status'] = 'error';
            $result['errors'][] = 'API test error: ' . $e->getMessage();
            $failedSections++;
        }

        $testResults[] = $result;
    }

    // Determine overall status
    if ($failedSections > 0) {
        $overallStatus = $failedSections >= $totalSections / 2 ? 'critical' : 'warning';
    }

    // Get database information
    $dbInfo = [];
    try {
        $stmt = $pdo->query("SELECT VERSION() as version");
        $dbInfo['version'] = $stmt->fetch()['version'];
        
        $stmt = $pdo->query("SELECT DATABASE() as database_name");
        $dbInfo['database'] = $stmt->fetch()['database_name'];
        
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $dbInfo['total_tables'] = count($tables);
        $dbInfo['tables'] = $tables;
    } catch (Exception $e) {
        $dbInfo['error'] = $e->getMessage();
    }

    // Send comprehensive response
    ApiErrorHandler::sendSuccess([
        'overall_status' => $overallStatus,
        'summary' => [
            'total_sections' => $totalSections,
            'working_sections' => $workingSections,
            'failed_sections' => $failedSections,
            'success_rate' => round(($workingSections / $totalSections) * 100, 1)
        ],
        'database_info' => $dbInfo,
        'section_results' => $testResults,
        'recommendations' => generateRecommendations($testResults)
    ], 'تم إجراء اختبار الاتصال بقاعدة البيانات بنجاح');

} catch (Exception $e) {
    ApiErrorHandler::sendError(
        'خطأ في اختبار الاتصال بقاعدة البيانات: ' . $e->getMessage(),
        500,
        'DATABASE_TEST_ERROR'
    );
}

function generateRecommendations($testResults) {
    $recommendations = [];
    
    foreach ($testResults as $result) {
        if ($result['api_status'] === 'error' || $result['table_status'] === 'error') {
            $recommendations[] = [
                'section' => $result['name'],
                'priority' => $result['api_status'] === 'error' && $result['table_status'] === 'error' ? 'high' : 'medium',
                'issue' => implode(', ', $result['errors']),
                'action' => $result['api_status'] === 'error' ? 'إصلاح API أو إنشاء API جديد' : 'فحص هيكل قاعدة البيانات'
            ];
        }
    }
    
    return $recommendations;
}
