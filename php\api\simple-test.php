<?php
echo "=== Simple PHP Test ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Current directory: " . __DIR__ . "\n";

// Test .env file loading
echo "\n=== Testing .env file ===\n";
$envPath = __DIR__ . '/../../.env';
echo "Looking for .env at: $envPath\n";

if (file_exists($envPath)) {
    echo "✓ .env file found\n";
    $envContent = file_get_contents($envPath);
    $lines = explode("\n", $envContent);
    foreach ($lines as $line) {
        if (strpos($line, 'DB_') === 0) {
            echo "  $line\n";
        }
    }
} else {
    echo "✗ .env file not found\n";
}

// Test database connection with hardcoded values from .env
echo "\n=== Testing Database Connection ===\n";
try {
    $host = 'localhost';
    $port = '3307';
    $dbname = 'mossab-landing-page';
    $username = 'root';
    $password = '';
    
    echo "Attempting connection to: $host:$port/$dbname with user: $username\n";
    
    $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "✓ Connected to MySQL server\n";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Database '$dbname' exists\n";
        
        // Connect to database
        $pdo->exec("USE `$dbname`");
        echo "✓ Connected to database\n";
        
        // Show tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "✓ Found " . count($tables) . " tables\n";
        if (count($tables) > 0) {
            echo "  Tables: " . implode(', ', $tables) . "\n";
        }
    } else {
        echo "✗ Database '$dbname' does not exist\n";
        echo "Creating database...\n";
        $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✓ Database created\n";
    }
    
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
