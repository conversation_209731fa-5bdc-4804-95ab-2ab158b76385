<?php
/**
 * Debug Dashboard Statistics
 * Test and debug dashboard statistics API
 */

require_once '../php/config.php';

// Set content type and charset
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص إحصائيات لوحة التحكم</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: right; }
        th { background: #f8f9fa; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص إحصائيات لوحة التحكم</h1>
            <p>فحص وتشخيص مشاكل عرض الإحصائيات</p>
        </div>

<?php
try {
    echo '<div class="section">';
    echo '<h2>🔗 اختبار الاتصال بقاعدة البيانات</h2>';
    
    // Test database connection using different methods
    try {
        $pdo = getPDOConnection();
        echo '<div class="success">✅ اتصال PDO ناجح (getPDOConnection)</div>';
        
        // Test basic query
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
        $productCount = $stmt->fetch()['count'];
        echo '<div class="info">📊 عدد المنتجات (PDO): ' . $productCount . '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">❌ خطأ في PDO: ' . $e->getMessage() . '</div>';
    }
    
    try {
        require_once '../config/database.php';
        $db = Database::getInstance();
        echo '<div class="success">✅ اتصال Database::getInstance ناجح</div>';
        
        // Test basic query
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM produits");
        $stmt->execute();
        $productCount = $stmt->fetch()['count'];
        echo '<div class="info">📊 عدد المنتجات (Database): ' . $productCount . '</div>';
        
    } catch (Exception $e) {
        echo '<div class="error">❌ خطأ في Database::getInstance: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';

    echo '<div class="section">';
    echo '<h2>📊 اختبار استعلامات الإحصائيات</h2>';
    
    $pdo = getPDOConnection();
    
    // Test each statistic query individually
    $queries = [
        'المنتجات الإجمالية' => "SELECT COUNT(*) as count FROM produits",
        'المنتجات النشطة' => "SELECT COUNT(*) as count FROM produits WHERE actif = 1",
        'الطلبات الإجمالية' => "SELECT COUNT(*) as count FROM commandes",
        'الطلبات المعلقة' => "SELECT COUNT(*) as count FROM commandes WHERE statut = 'en_attente'",
        'صفحات الهبوط' => "SELECT COUNT(*) as count FROM landing_pages",
        'الفئات' => "SELECT COUNT(*) as count FROM categories",
        'المستخدمين' => "SELECT COUNT(*) as count FROM users"
    ];
    
    echo '<table>';
    echo '<tr><th>الإحصائية</th><th>العدد</th><th>الحالة</th></tr>';
    
    foreach ($queries as $name => $query) {
        try {
            $stmt = $pdo->query($query);
            $result = $stmt->fetch();
            $count = $result['count'] ?? 0;
            echo '<tr>';
            echo '<td>' . $name . '</td>';
            echo '<td>' . $count . '</td>';
            echo '<td><span style="color: green;">✅ نجح</span></td>';
            echo '</tr>';
        } catch (Exception $e) {
            echo '<tr>';
            echo '<td>' . $name . '</td>';
            echo '<td>-</td>';
            echo '<td><span style="color: red;">❌ فشل: ' . $e->getMessage() . '</span></td>';
            echo '</tr>';
        }
    }
    echo '</table>';
    echo '</div>';

    echo '<div class="section">';
    echo '<h2>🌐 اختبار API الإحصائيات</h2>';
    
    // Test the dashboard API
    $apiUrl = 'http://localhost:8000/php/api/dashboard-stats.php';
    echo '<div class="info">🔗 رابط API: <a href="' . $apiUrl . '" target="_blank">' . $apiUrl . '</a></div>';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents($apiUrl, false, $context);
    
    if ($response === false) {
        echo '<div class="error">❌ فشل في الوصول إلى API</div>';
    } else {
        echo '<div class="success">✅ تم الوصول إلى API بنجاح</div>';
        
        $data = json_decode($response, true);
        if ($data === null) {
            echo '<div class="error">❌ فشل في تحليل JSON</div>';
            echo '<pre>' . htmlspecialchars($response) . '</pre>';
        } else {
            echo '<div class="success">✅ تم تحليل JSON بنجاح</div>';
            echo '<h4>📋 بيانات API:</h4>';
            echo '<pre>' . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        }
    }
    echo '</div>';

    echo '<div class="section">';
    echo '<h2>🔧 اختبار JavaScript Dashboard</h2>';
    echo '<div class="info">سيتم اختبار تحميل الإحصائيات عبر JavaScript...</div>';
    echo '<div id="js-test-results"></div>';
    echo '</div>';

} catch (Exception $e) {
    echo '<div class="error">❌ خطأ عام: ' . $e->getMessage() . '</div>';
}
?>

        <div class="section">
            <h2>🔄 إجراءات الإصلاح</h2>
            <div style="text-align: center; margin-top: 30px;">
                <a href="create-demo-data.php" class="btn">🔄 إعادة إنشاء البيانات التجريبية</a>
                <a href="verify-demo-data.php" class="btn">✅ التحقق من البيانات</a>
                <a href="index.html" class="btn">⚙️ لوحة التحكم</a>
            </div>
        </div>
    </div>

    <script>
        // Test JavaScript API call
        async function testDashboardAPI() {
            const resultsDiv = document.getElementById('js-test-results');
            
            try {
                resultsDiv.innerHTML = '<div style="color: blue;">🔄 جاري اختبار API...</div>';
                
                const response = await fetch('../php/api/dashboard-stats.php');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div style="color: green;">✅ JavaScript API Test نجح</div>
                        <div style="margin-top: 10px;">
                            <strong>الإحصائيات:</strong><br>
                            المنتجات: ${data.data.products?.total || 0}<br>
                            الطلبات: ${data.data.orders?.total || 0}<br>
                            صفحات الهبوط: ${data.data.landing_pages?.total || 0}<br>
                            المبيعات: ${data.data.sales?.formatted_total || '0 دج'}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div style="color: red;">❌ API Error: ${data.message}</div>`;
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `<div style="color: red;">❌ JavaScript Error: ${error.message}</div>`;
            }
        }
        
        // Run test when page loads
        document.addEventListener('DOMContentLoaded', testDashboardAPI);
    </script>
</body>
</html>
