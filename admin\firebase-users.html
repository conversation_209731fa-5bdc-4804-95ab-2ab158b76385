<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>إدارة المستخدمين - Firebase</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/admin.css" />
    <style>
      .users-management {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
      }

      .users-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .users-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;
      }

      .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
      }

      .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
      }

      .users-filters {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .filters-row {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
      }

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .filter-group label {
        font-size: 0.9rem;
        color: #495057;
        font-weight: 500;
      }

      .filter-group input,
      .filter-group select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
      }

      .users-table-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .users-table {
        width: 100%;
        border-collapse: collapse;
      }

      .users-table th,
      .users-table td {
        padding: 15px;
        text-align: right;
        border-bottom: 1px solid #eee;
      }

      .users-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }

      .user-avatar-placeholder {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #667eea;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.2rem;
      }

      .user-details {
        flex: 1;
      }

      .user-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
      }

      .user-email {
        font-size: 0.85rem;
        color: #6c757d;
      }

      .role-select {
        padding: 5px 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background: white;
        font-size: 14px;
      }

      .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
      }

      .status-active {
        background: #d4edda;
        color: #155724;
      }

      .status-inactive {
        background: #f8d7da;
        color: #721c24;
      }

      .user-actions {
        display: flex;
        gap: 5px;
      }

      .btn {
        padding: 6px 12px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.85rem;
        transition: all 0.3s ease;
      }

      .btn-sm {
        padding: 4px 8px;
        font-size: 0.75rem;
      }

      .btn-toggle {
        background: #ffc107;
        color: #212529;
      }

      .btn-danger {
        background: #dc3545;
        color: white;
      }

      .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      }

      .pagination {
        display: flex;
        justify-content: center;
        gap: 5px;
        padding: 20px;
      }

      .pagination-btn {
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .pagination-btn:hover,
      .pagination-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
      }

      .loading {
        text-align: center;
        padding: 40px;
        color: #6c757d;
      }

      .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="users-management">
      <!-- Header -->
      <div class="users-header">
        <div>
          <h1><i class="fas fa-users"></i> إدارة المستخدمين</h1>
          <p>إدارة مستخدمي النظام عبر Firebase</p>
        </div>
        <div>
          <button class="btn btn-primary" onclick="refreshUsers()">
            <i class="fas fa-sync-alt"></i> تحديث
          </button>
          <a href="index.html" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
          </a>
        </div>
      </div>

      <!-- Statistics -->
      <div class="users-stats" id="usersStats">
        <div class="stat-card">
          <div class="stat-number" id="totalUsers">-</div>
          <div class="stat-label">إجمالي المستخدمين</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="activeUsers">-</div>
          <div class="stat-label">المستخدمون النشطون</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="adminUsers">-</div>
          <div class="stat-label">المديرون</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="regularUsers">-</div>
          <div class="stat-label">المستخدمون العاديون</div>
        </div>
      </div>

      <!-- Filters -->
      <div class="users-filters">
        <div class="filters-row">
          <div class="filter-group">
            <label>البحث</label>
            <input
              type="text"
              id="searchInput"
              placeholder="البحث بالاسم أو البريد الإلكتروني..."
            />
          </div>
          <div class="filter-group">
            <label>الدور</label>
            <select id="roleFilter">
              <option value="">جميع الأدوار</option>
              <option value="super_admin">مدير رئيسي</option>
              <option value="admin">مدير</option>
              <option value="owner">مالك</option>
              <option value="manager">مدير متجر</option>
              <option value="user">مستخدم</option>
            </select>
          </div>
          <div class="filter-group">
            <label>الحالة</label>
            <select id="statusFilter">
              <option value="">جميع الحالات</option>
              <option value="true">نشط</option>
              <option value="false">غير نشط</option>
            </select>
          </div>
          <div class="filter-group">
            <label>&nbsp;</label>
            <button class="btn btn-primary" onclick="applyFilters()">
              <i class="fas fa-filter"></i> تطبيق الفلاتر
            </button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div class="loading" id="loadingState">
        <div class="spinner"></div>
        <p>جاري تحميل المستخدمين...</p>
      </div>

      <!-- Users Table -->
      <div id="usersContainer">
        <!-- Users will be loaded here -->
      </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module" src="js/firebase-config.js"></script>
    <script type="module" src="js/firebase-user-manager.js"></script>

    <!-- Main Script -->
    <script type="module">
      let currentPage = 1;
      const usersPerPage = 10;

      // Initialize page
      document.addEventListener("DOMContentLoaded", () => {
        setTimeout(() => {
          if (window.firebaseAuth?.isAuthenticated()) {
            loadUsers();
          } else {
            window.location.href = "login.html";
          }
        }, 1000);
      });

      // Load users
      window.loadUsers = async (page = 1) => {
        currentPage = page;
        document.getElementById("loadingState").style.display = "block";
        document.getElementById("usersContainer").innerHTML = "";

        try {
          const result = await window.firebaseUserManager.loadUsers();

          if (result.success) {
            const paginatedData = window.firebaseUserManager.getPaginatedUsers(
              page,
              usersPerPage
            );
            displayUsers(paginatedData.users, paginatedData.pagination);
            updateStats(window.firebaseUserManager.getUserStats());
          } else {
            showError("فشل في تحميل المستخدمين: " + result.error);
          }
        } catch (error) {
          showError("خطأ في تحميل المستخدمين: " + error.message);
        }

        document.getElementById("loadingState").style.display = "none";
      };

      // Display users
      function displayUsers(users, pagination) {
        const html = window.firebaseUserManager.generateUserManagementHTML(
          users,
          pagination
        );
        document.getElementById("usersContainer").innerHTML = html;

        // Add event listeners for role changes
        document.querySelectorAll(".role-select").forEach((select) => {
          select.addEventListener("change", async (e) => {
            const userId = e.target.dataset.userId;
            const newRole = e.target.value;
            const currentRole = e.target.dataset.currentRole;

            if (
              confirm(
                `هل أنت متأكد من تغيير دور المستخدم إلى "${
                  e.target.options[e.target.selectedIndex].text
                }"؟`
              )
            ) {
              const result = await window.firebaseUserManager.updateUserRole(
                userId,
                newRole
              );
              if (result.success) {
                showSuccess("تم تحديث دور المستخدم بنجاح");
                e.target.dataset.currentRole = newRole;
              } else {
                showError("فشل في تحديث دور المستخدم: " + result.error);
                e.target.value = currentRole; // Revert
              }
            } else {
              e.target.value = currentRole; // Revert
            }
          });
        });
      }

      // Update statistics
      function updateStats(stats) {
        document.getElementById("totalUsers").textContent = stats.total;
        document.getElementById("activeUsers").textContent = stats.active;
        document.getElementById("adminUsers").textContent =
          (stats.roles.admin || 0) +
          (stats.roles.super_admin || 0) +
          (stats.roles.owner || 0);
        document.getElementById("regularUsers").textContent =
          stats.roles.user || 0;
      }

      // Toggle user status
      window.toggleUserStatus = async (userId) => {
        const result = await window.firebaseUserManager.toggleUserStatus(
          userId
        );
        if (result.success) {
          showSuccess(
            `تم ${result.newStatus ? "تفعيل" : "تعطيل"} المستخدم بنجاح`
          );
          loadUsers(currentPage); // Refresh current page
        } else {
          showError("فشل في تحديث حالة المستخدم: " + result.error);
        }
      };

      // Delete user
      window.deleteUser = async (userId) => {
        if (
          confirm(
            "هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه."
          )
        ) {
          const result = await window.firebaseUserManager.deleteUser(userId);
          if (result.success) {
            showSuccess("تم حذف المستخدم بنجاح");
            loadUsers(currentPage); // Refresh current page
          } else {
            showError("فشل في حذف المستخدم: " + result.error);
          }
        }
      };

      // Load users page
      window.loadUsersPage = (page) => {
        loadUsers(page);
      };

      // Refresh users
      window.refreshUsers = () => {
        loadUsers(currentPage);
      };

      // Apply filters
      window.applyFilters = () => {
        // For now, just reload - you can implement filtering logic here
        loadUsers(1);
      };

      // Utility functions
      function showError(message) {
        // You can implement a toast notification system here
        alert("خطأ: " + message);
      }

      function showSuccess(message) {
        // You can implement a toast notification system here
        alert("نجح: " + message);
      }
    </script>
  </body>
</html>
