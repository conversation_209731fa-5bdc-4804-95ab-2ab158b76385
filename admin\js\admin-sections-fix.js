/**
 * Admin Navigation Fix - Complete Navigation System
 * إصلاح نظام التنقل الكامل للوحة التحكم
 */

(function() {
    "use strict";

    console.log("🔧 Admin Navigation Fix loading...");

    // Navigation state management
    let currentSection = 'dashboard';
    let navigationInitialized = false;

    // Initialize navigation system
    function initializeNavigation() {
        if (navigationInitialized) {
            console.log("Navigation already initialized");
            return;
        }

        console.log("🚀 Initializing navigation system...");

        // Ensure only dashboard is visible initially
        hideAllSections();
        showSection('dashboard');
        setActiveNavItem('dashboard');

        // Setup navigation event listeners
        setupNavigationListeners();

        navigationInitialized = true;
        console.log("✅ Navigation system initialized");
    }

    // Hide all content sections
    function hideAllSections() {
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
            section.style.setProperty('display', 'none', 'important');
            section.style.setProperty('opacity', '0', 'important');
            section.style.setProperty('visibility', 'hidden', 'important');
            section.style.setProperty('position', 'absolute', 'important');
            section.style.setProperty('left', '-9999px', 'important');
        });
    }

    // Show specific section
    function showSection(sectionId) {
        console.log(`📄 Showing section: ${sectionId}`);

        const section = document.getElementById(sectionId);
        if (section) {
            section.classList.add('active');
            section.style.setProperty('display', 'block', 'important');
            section.style.setProperty('opacity', '1', 'important');
            section.style.setProperty('visibility', 'visible', 'important');
            section.style.setProperty('position', 'static', 'important');
            section.style.setProperty('left', 'auto', 'important');

            // Load section-specific content
            loadSectionContent(sectionId);

            currentSection = sectionId;
            console.log(`✅ Section ${sectionId} is now active`);
        } else {
            console.error(`❌ Section ${sectionId} not found`);
        }
    }

    // Set active navigation item
    function setActiveNavItem(sectionId) {
        // Remove active class from all nav items
        const navItems = document.querySelectorAll('.admin-nav ul li');
        navItems.forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current nav item
        const activeNavItem = document.querySelector(`[data-section="${sectionId}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }
    }

    // Setup navigation event listeners
    function setupNavigationListeners() {
        console.log("🔗 Setting up navigation listeners...");

        // Main navigation items
        const navItems = document.querySelectorAll('.admin-nav ul li[data-section]');
        console.log(`Found ${navItems.length} navigation items`);

        navItems.forEach((item, index) => {
            // Remove existing listeners to prevent duplicates
            const newItem = item.cloneNode(true);
            item.parentNode.replaceChild(newItem, item);

            newItem.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const sectionId = this.getAttribute('data-section');
                console.log(`🖱️ Navigation clicked: ${sectionId}`);

                if (sectionId && sectionId !== currentSection) {
                    navigateToSection(sectionId);
                }
            });

            console.log(`✅ Listener added to: ${newItem.getAttribute('data-section')}`);
        });

        // Settings sub-navigation
        const settingCards = document.querySelectorAll('.setting-card[data-section]');
        settingCards.forEach(card => {
            const newCard = card.cloneNode(true);
            card.parentNode.replaceChild(newCard, card);

            newCard.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const sectionId = this.getAttribute('data-section');
                console.log(`🖱️ Settings card clicked: ${sectionId}`);

                navigateToSection(sectionId);
            });
        });

        console.log("✅ Navigation listeners setup complete");
    }

    // Navigate to section
    function navigateToSection(sectionId) {
        console.log(`🧭 Navigating to section: ${sectionId}`);

        // Hide all sections
        hideAllSections();

        // Show target section
        showSection(sectionId);

        // Update navigation
        setActiveNavItem(sectionId);

        console.log(`✅ Navigation to ${sectionId} complete`);
    }

    // Load section-specific content
    function loadSectionContent(sectionId) {
        console.log(`📦 Loading content for section: ${sectionId}`);

        switch(sectionId) {
            case 'dashboard':
                // Check if enhanced dashboard already exists
                const dashboard = document.getElementById('dashboard');
                const enhancedDashboard = dashboard ? dashboard.querySelector('.dashboard-header') : null;

                if (!enhancedDashboard && typeof loadDashboard === 'function') {
                    loadDashboard();
                } else if (enhancedDashboard) {
                    console.log('✅ Enhanced dashboard detected - preserving existing content');
                }
                break;
            case 'books':
                if (typeof loadProducts === 'function') {
                    loadProducts();
                }
                break;
            case 'orders':
                if (typeof loadOrders === 'function') {
                    loadOrders();
                }
                break;
            case 'landingPages':
                if (typeof landingPagesManager !== 'undefined' && landingPagesManager.init) {
                    landingPagesManager.init();
                }
                break;
            case 'reports':
                if (typeof loadReportsContent === 'function') {
                    loadReportsContent();
                }
                break;
            case 'settings':
                // Settings section is static, no loading needed
                break;
            default:
                // For sub-sections like categoriesManagement, etc.
                loadSubSectionContent(sectionId);
                break;
        }
    }

    // Load sub-section content (for settings sub-sections)
    function loadSubSectionContent(sectionId) {
        console.log(`📋 Loading sub-section: ${sectionId}`);

        // First, make sure settings nav item is active
        setActiveNavItem('settings');

        // Load specific sub-section content
        switch(sectionId) {
            case 'categoriesManagement':
                // Load categories management directly
                loadCategoriesManagementDirect();
                break;
            case 'paymentSettings':
                // Load payment settings content
                if (typeof loadPaymentSettingsContent === 'function') {
                    loadPaymentSettingsContent();
                } else {
                    // Load the script if not already loaded
                    const script = document.createElement('script');
                    script.src = 'js/load-payment-settings.js';
                    script.onload = () => {
                        if (typeof loadPaymentSettingsContent === 'function') {
                            loadPaymentSettingsContent();
                        }
                    };
                    document.head.appendChild(script);
                }
                break;
            case 'generalSettings':
                if (typeof loadGeneralSettingsContent === 'function') {
                    loadGeneralSettingsContent();
                }
                break;
            case 'storeSettings':
                if (typeof loadStoreSettingsContent === 'function') {
                    loadStoreSettingsContent();
                }
                break;
            case 'userManagement':
                if (typeof loadUserManagementContent === 'function') {
                    loadUserManagementContent();
                }
                break;
            case 'storesManagement':
                if (typeof loadStoresManagementContent === 'function') {
                    loadStoresManagementContent();
                }
                break;
            case 'rolesManagement':
                if (typeof loadRolesManagementContent === 'function') {
                    loadRolesManagementContent();
                }
                break;
            case 'subscriptionsManagement':
                if (typeof loadSubscriptionsManagementContent === 'function') {
                    loadSubscriptionsManagementContent();
                }
                break;
            case 'securitySettings':
                if (typeof loadSecuritySettingsContent === 'function') {
                    loadSecuritySettingsContent();
                }
                break;
            case 'systemTesting':
                if (typeof loadSystemTestingContent === 'function') {
                    loadSystemTestingContent();
                }
                break;
            default:
                console.warn(`Unknown sub-section: ${sectionId}`);
        }
    }

    // Force clean state on page load
    function forceCleanState() {
        console.log("🧹 Forcing clean navigation state...");

        // Hide all sections immediately with !important styles
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            if (section.id !== 'dashboard') {
                section.classList.remove('active');
                section.style.setProperty('display', 'none', 'important');
                section.style.setProperty('opacity', '0', 'important');
                section.style.setProperty('visibility', 'hidden', 'important');
                section.style.setProperty('position', 'absolute', 'important');
                section.style.setProperty('left', '-9999px', 'important');
            }
        });

        // Ensure dashboard is visible
        const dashboard = document.getElementById('dashboard');
        if (dashboard) {
            dashboard.classList.add('active');
            dashboard.style.setProperty('display', 'block', 'important');
            dashboard.style.setProperty('opacity', '1', 'important');
            dashboard.style.setProperty('visibility', 'visible', 'important');
            dashboard.style.setProperty('position', 'static', 'important');
            dashboard.style.setProperty('left', 'auto', 'important');
        }

        // Set dashboard nav as active
        setActiveNavItem('dashboard');

        console.log("✅ Clean state enforced with !important styles");
    }

    // Public API
    window.adminNavigation = {
        navigateToSection: navigateToSection,
        showSection: showSection,
        hideAllSections: hideAllSections,
        getCurrentSection: () => currentSection,
        forceCleanState: forceCleanState,
        reinitialize: () => {
            navigationInitialized = false;
            initializeNavigation();
        }
    };

    // Make showSection globally available for onclick handlers
    window.showSection = showSection;

    // Initialize everything when DOM is ready
    function initializeWhenReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => {
                    forceCleanState();
                    initializeNavigation();
                }, 100);
            });
        } else {
            setTimeout(() => {
                forceCleanState();
                initializeNavigation();
            }, 100);
        }

        // Also run after a delay to ensure all scripts are loaded
        setTimeout(() => {
            if (!navigationInitialized) {
                console.log("🔄 Reinitializing navigation...");
                forceCleanState();
                initializeNavigation();
            }
        }, 2000);
    }

    // Start initialization
    initializeWhenReady();

    console.log("✅ Admin Navigation Fix loaded successfully");

    // Categories Management Direct Function
    window.loadCategoriesManagementDirect = function() {
        console.log('🗂️ تحميل إدارة الفئات مباشرة...');

        const container = document.getElementById('categoriesManagementContent');
        if (!container) {
            console.error('❌ لم يتم العثور على حاوي إدارة الفئات');
            return;
        }

        console.log('✅ تم العثور على الحاوي');

        // Show loading immediately
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <div>
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 15px;"></i>
                </div>
                <p style="color: #666;">جاري تحميل إدارة الفئات...</p>
                <p style="color: #999; font-size: 0.9em;">يتم الآن جلب البيانات من الخادم...</p>
            </div>
        `;

        // Try to fetch data
        console.log('📡 محاولة جلب البيانات...');

        fetch('php/categories.php?action=get_all')
            .then(response => {
                console.log('📡 استجابة الخادم:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return response.json();
            })
            .then(data => {
                console.log('📦 البيانات المستلمة:', data);

                if (data.success) {
                    console.log('✅ نجح جلب البيانات');
                    renderCategoriesInterfaceDirect(container, data.data);
                } else {
                    throw new Error(data.message || 'فشل في جلب البيانات');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في جلب البيانات:', error);
                showErrorInterfaceDirect(container, error.message);
            });
    };

})();

// Categories Management Helper Functions
function renderCategoriesInterfaceDirect(container, data) {
    console.log('🎨 رسم واجهة إدارة الفئات...');

    if (!data || !data.categories) {
        console.error('❌ بيانات غير صحيحة');
        showErrorInterfaceDirect(container, 'بيانات الفئات غير صحيحة');
        return;
    }

    const categories = data.categories;
    const mainCategories = categories.filter(c => c.parent_id === null);
    const subCategories = categories.filter(c => c.parent_id !== null);
    const featuredCategories = categories.filter(c => c.is_featured == 1);

    const html = `
        <div style="max-width: 1200px; margin: 0 auto; padding: 20px;">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 20px;">
                    <div>
                        <h2 style="margin: 0; font-size: 1.8rem;"><i class="fas fa-sitemap"></i> إدارة الفئات</h2>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">إدارة وتنظيم فئات المنتجات والمحتوى</p>
                    </div>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button onclick="alert('إضافة فئة جديدة - قيد التطوير')" style="padding: 10px 20px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-plus"></i> إضافة فئة جديدة
                        </button>
                        <button onclick="loadCategoriesManagementDirect()" style="padding: 10px 20px; background: transparent; color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold;">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #667eea; margin-bottom: 15px;">
                        <i class="fas fa-folder"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${data.total}</h3>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">إجمالي الفئات</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #28a745; margin-bottom: 15px;">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${mainCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات الرئيسية</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 15px;">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${subCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات الفرعية</p>
                </div>
                <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); text-align: center;">
                    <div style="font-size: 2.5rem; color: #ffc107; margin-bottom: 15px;">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 2.2rem; color: #333; font-weight: 700;">${featuredCategories.length}</h3>
                    <p style="margin: 8px 0 0 0; color: #666; font-size: 0.95rem;">الفئات المميزة</p>
                </div>
            </div>

            <!-- Categories List -->
            <div style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden;">
                <div style="padding: 20px; border-bottom: 1px solid #e0e0e0; background: #f8f9fa;">
                    <h3 style="margin: 0; color: #333; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-sitemap"></i>
                        عرض هرمي للفئات
                        <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem;">${data.total}</span>
                    </h3>
                </div>

                <div style="padding: 25px;">
                    ${renderCategoriesListDirect(categories)}
                </div>
            </div>

            <!-- Success Message -->
            <div style="margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
                <i class="fas fa-check-circle"></i> <strong>تم تحميل إدارة الفئات بنجاح!</strong>
                <br>تم عرض ${data.total} فئة مع الهيكل الهرمي الكامل.
            </div>
        </div>
    `;

    console.log('✅ تم إنشاء HTML');
    container.innerHTML = html;
    console.log('✅ تم تحديث الحاوي بنجاح');
}

function renderCategoriesListDirect(categories) {
    console.log('🌳 رسم قائمة الفئات...');

    const mainCategories = categories.filter(c => c.parent_id === null);
    let html = '<div>';

    mainCategories.forEach(mainCat => {
        const subCategories = categories.filter(c => c.parent_id == mainCat.id);
        const featuredIcon = mainCat.is_featured == 1 ? '⭐' : '';

        html += `
            <div style="margin-bottom: 20px; padding: 20px; border: 2px solid ${mainCat.color}; border-radius: 12px; background: linear-gradient(135deg, ${mainCat.color}15 0%, #ffffff 100%);">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                    <div style="color: ${mainCat.color}; font-size: 2rem;">
                        <i class="${mainCat.icon || 'fas fa-folder'}"></i>
                    </div>
                    <div>
                        <h3 style="margin: 0; color: #333; font-size: 1.4rem;">${mainCat.name_ar} ${featuredIcon}</h3>
                        <p style="margin: 5px 0 0 0; color: #666;">${mainCat.description_ar || 'لا يوجد وصف'}</p>
                    </div>
                </div>

                ${subCategories.length > 0 ? `
                    <div style="margin-right: 40px;">
                        <h4 style="color: #555; margin-bottom: 10px;">الفئات الفرعية (${subCategories.length}):</h4>
                        ${subCategories.map(subCat => `
                            <div style="margin-bottom: 10px; padding: 15px; background: white; border-radius: 8px; border-right: 4px solid ${subCat.color};">
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <i class="${subCat.icon || 'fas fa-folder'}" style="color: ${subCat.color};"></i>
                                    <strong>${subCat.name_ar}</strong>
                                    ${subCat.is_featured == 1 ? '⭐' : ''}
                                </div>
                                <p style="margin: 5px 0 0 25px; color: #666; font-size: 0.9em;">${subCat.description_ar || 'لا يوجد وصف'}</p>
                            </div>
                        `).join('')}
                    </div>
                ` : '<p style="margin-right: 40px; color: #999; font-style: italic;">لا توجد فئات فرعية</p>'}
            </div>
        `;
    });

    html += '</div>';
    return html;
}

function showErrorInterfaceDirect(container, message) {
    console.log('❌ عرض واجهة الخطأ:', message);

    container.innerHTML = `
        <div style="text-align: center; padding: 60px 20px; color: #dc3545;">
            <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.7;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 style="margin: 0 0 15px 0; color: #dc3545;">خطأ في تحميل إدارة الفئات</h3>
            <p style="margin: 0 0 25px 0; color: #666; font-size: 1.1rem;">${message}</p>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <button onclick="loadCategoriesManagementDirect()" style="padding: 12px 24px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
                <a href="php/categories.php?action=get_all" target="_blank" style="padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
                    <i class="fas fa-external-link-alt"></i> اختبار API مباشر
                </a>
            </div>
        </div>
    `;
}
