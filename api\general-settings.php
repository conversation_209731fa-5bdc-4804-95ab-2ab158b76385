<?php
/**
 * General Settings API
 * API للإعدادات العامة
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            getSettings();
            break;
        case 'POST':
            saveSettings();
            break;
        default:
            throw new Exception('Method not allowed');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getSettings() {
    global $pdo;
    
    try {
        // Create settings table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS general_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                description VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // Insert default settings if they don't exist
        $defaultSettings = [
            ['store_name', 'Mossaab Store', 'اسم المتجر'],
            ['store_slogan', 'متجر إلكتروني متميز', 'شعار المتجر'],
            ['store_email', '<EMAIL>', 'بريد المتجر'],
            ['store_phone', '+*********** 456', 'هاتف المتجر'],
            ['store_address', 'الجزائر العاصمة، الجزائر', 'عنوان المتجر'],
            ['currency', 'DZD', 'العملة'],
            ['tax_rate', '19.00', 'معدل الضريبة'],
            ['min_order_amount', '500', 'الحد الأدنى للطلب'],
            ['free_shipping_threshold', '5000', 'حد الشحن المجاني'],
            ['timezone', 'Africa/Algiers', 'المنطقة الزمنية'],
            ['language', 'ar', 'لغة النظام'],
            ['maintenance_mode', '0', 'وضع الصيانة'],
            ['enable_registration', '1', 'تفعيل التسجيل']
        ];
        
        foreach ($defaultSettings as $setting) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO general_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }
        
        // Get all settings
        $stmt = $pdo->query("SELECT setting_key, setting_value, updated_at FROM general_settings");
        $settingsData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convert to structured format
        $settings = [
            'store' => [
                'name' => '',
                'slogan' => '',
                'email' => '',
                'phone' => '',
                'address' => ''
            ],
            'business' => [
                'currency' => 'DZD',
                'taxRate' => 19.00,
                'minOrderAmount' => 500,
                'freeShippingThreshold' => 5000
            ],
            'system' => [
                'timezone' => 'Africa/Algiers',
                'language' => 'ar',
                'maintenanceMode' => false,
                'enableRegistration' => true
            ]
        ];
        
        $lastUpdated = null;
        
        foreach ($settingsData as $setting) {
            $key = $setting['setting_key'];
            $value = $setting['setting_value'];
            $updated = $setting['updated_at'];
            
            if (!$lastUpdated || $updated > $lastUpdated) {
                $lastUpdated = $updated;
            }
            
            switch ($key) {
                case 'store_name':
                    $settings['store']['name'] = $value;
                    break;
                case 'store_slogan':
                    $settings['store']['slogan'] = $value;
                    break;
                case 'store_email':
                    $settings['store']['email'] = $value;
                    break;
                case 'store_phone':
                    $settings['store']['phone'] = $value;
                    break;
                case 'store_address':
                    $settings['store']['address'] = $value;
                    break;
                case 'currency':
                    $settings['business']['currency'] = $value;
                    break;
                case 'tax_rate':
                    $settings['business']['taxRate'] = floatval($value);
                    break;
                case 'min_order_amount':
                    $settings['business']['minOrderAmount'] = floatval($value);
                    break;
                case 'free_shipping_threshold':
                    $settings['business']['freeShippingThreshold'] = floatval($value);
                    break;
                case 'timezone':
                    $settings['system']['timezone'] = $value;
                    break;
                case 'language':
                    $settings['system']['language'] = $value;
                    break;
                case 'maintenance_mode':
                    $settings['system']['maintenanceMode'] = $value === '1';
                    break;
                case 'enable_registration':
                    $settings['system']['enableRegistration'] = $value === '1';
                    break;
            }
        }
        
        echo json_encode([
            'success' => true,
            'settings' => $settings,
            'lastUpdated' => $lastUpdated
        ]);
        
    } catch (Exception $e) {
        throw new Exception('Failed to load settings: ' . $e->getMessage());
    }
}

function saveSettings() {
    global $pdo;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid input data');
        }
        
        $pdo->beginTransaction();
        
        // Map structured data to database keys
        $settingsMap = [];
        
        if (isset($input['store'])) {
            $settingsMap['store_name'] = $input['store']['name'] ?? '';
            $settingsMap['store_slogan'] = $input['store']['slogan'] ?? '';
            $settingsMap['store_email'] = $input['store']['email'] ?? '';
            $settingsMap['store_phone'] = $input['store']['phone'] ?? '';
            $settingsMap['store_address'] = $input['store']['address'] ?? '';
        }
        
        if (isset($input['business'])) {
            $settingsMap['currency'] = $input['business']['currency'] ?? 'DZD';
            $settingsMap['tax_rate'] = $input['business']['taxRate'] ?? 19.00;
            $settingsMap['min_order_amount'] = $input['business']['minOrderAmount'] ?? 500;
            $settingsMap['free_shipping_threshold'] = $input['business']['freeShippingThreshold'] ?? 5000;
        }
        
        if (isset($input['system'])) {
            $settingsMap['timezone'] = $input['system']['timezone'] ?? 'Africa/Algiers';
            $settingsMap['language'] = $input['system']['language'] ?? 'ar';
            $settingsMap['maintenance_mode'] = ($input['system']['maintenanceMode'] ?? false) ? '1' : '0';
            $settingsMap['enable_registration'] = ($input['system']['enableRegistration'] ?? true) ? '1' : '0';
        }
        
        // Update settings
        foreach ($settingsMap as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT INTO general_settings (setting_key, setting_value) 
                VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$key, $value]);
        }
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ الإعدادات بنجاح',
            'lastUpdated' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception('Failed to save settings: ' . $e->getMessage());
    }
}
?>
