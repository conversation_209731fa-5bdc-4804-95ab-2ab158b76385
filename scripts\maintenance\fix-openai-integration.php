<?php
/**
 * Fix OpenAI Integration Issues
 * Comprehensive solution for API key persistence, .env integration, and activation
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح تكامل OpenAI</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #00c851 0%, #007e33 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #00c851 0%, #007e33 100%);
            color: white;
            border-radius: 12px;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: linear-gradient(135deg, #00c851 0%, #007e33 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .api-key-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .openai-config {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .config-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .config-icon {
            margin-left: 10px;
            font-size: 24px;
        }
        .test-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-checkbox {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 إصلاح تكامل OpenAI</h1>
            <p>حل شامل لمشاكل حفظ API key وتفعيل OpenAI وتكامل .env</p>
        </div>

        <?php
        $allIssuesFixed = true;
        $fixedIssues = [];
        $remainingIssues = [];

        try {
            require_once '../php/config.php';
            
            // Fix 1: Check .env File Integration
            echo '<div class="fix-section">';
            echo '<h3>📁 إصلاح 1: فحص تكامل ملف .env</h3>';
            
            $envFile = '../.env';
            if (file_exists($envFile)) {
                echo '<div class="result pass">✅ ملف .env موجود</div>';
                
                $envContent = file_get_contents($envFile);
                if (strpos($envContent, 'OPENAI_API_KEY') !== false) {
                    echo '<div class="result pass">✅ مفتاح OPENAI_API_KEY موجود في .env</div>';
                    
                    // Extract OpenAI API key
                    preg_match('/OPENAI_API_KEY=(.*)/', $envContent, $matches);
                    $envApiKey = isset($matches[1]) ? trim($matches[1]) : '';
                    
                    if (!empty($envApiKey)) {
                        echo '<div class="result pass">✅ مفتاح OpenAI API مُعين في .env</div>';
                        echo '<div class="api-key-display">مفتاح API: ' . substr($envApiKey, 0, 20) . '...' . substr($envApiKey, -10) . '</div>';
                        $fixedIssues[] = 'OpenAI API key found in .env';
                    } else {
                        echo '<div class="result warning">⚠️ مفتاح OpenAI API فارغ في .env</div>';
                    }
                } else {
                    echo '<div class="result fail">❌ مفتاح OPENAI_API_KEY غير موجود في .env</div>';
                    $remainingIssues[] = 'OPENAI_API_KEY missing from .env';
                    $allIssuesFixed = false;
                }
            } else {
                echo '<div class="result fail">❌ ملف .env غير موجود</div>';
                $remainingIssues[] = '.env file missing';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 2: Test Database Table
            echo '<div class="fix-section">';
            echo '<h3>🗄️ إصلاح 2: فحص جدول قاعدة البيانات</h3>';
            
            if (isset($conn) && $conn instanceof PDO) {
                echo '<div class="result pass">✅ اتصال قاعدة البيانات متاح</div>';
                
                // Create ai_settings table if it doesn't exist
                $createTableSQL = "
                CREATE TABLE IF NOT EXISTS ai_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    provider VARCHAR(50) NOT NULL UNIQUE,
                    api_key TEXT,
                    model VARCHAR(100),
                    max_tokens INT DEFAULT 1000,
                    temperature DECIMAL(3,2) DEFAULT 0.7,
                    is_active BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                $conn->exec($createTableSQL);
                echo '<div class="result pass">✅ تم إنشاء/التحقق من جدول ai_settings</div>';
                
                // Insert default OpenAI settings if not exists
                $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE provider = 'openai'");
                $stmt->execute();
                $openaiSettings = $stmt->fetch();
                
                if (!$openaiSettings) {
                    $stmt = $conn->prepare("
                        INSERT INTO ai_settings (provider, api_key, model, max_tokens, temperature, is_active) 
                        VALUES ('openai', '', 'gpt-3.5-turbo', 1000, 0.7, 0)
                    ");
                    $stmt->execute();
                    echo '<div class="result pass">✅ تم إدراج إعدادات OpenAI الافتراضية</div>';
                } else {
                    echo '<div class="result pass">✅ إعدادات OpenAI موجودة في قاعدة البيانات</div>';
                }
                
                $fixedIssues[] = 'AI settings table ready';
                
            } else {
                echo '<div class="result fail">❌ اتصال قاعدة البيانات غير متاح</div>';
                $remainingIssues[] = 'Database connection failed';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 3: Test AI API Endpoint
            echo '<div class="fix-section">';
            echo '<h3>🔗 إصلاح 3: اختبار AI API Endpoint</h3>';
            
            $aiAPIFile = '../php/api/ai.php';
            if (file_exists($aiAPIFile)) {
                echo '<div class="result pass">✅ ملف AI API موجود</div>';
                
                // Test get_config action
                try {
                    ob_start();
                    $_SERVER['REQUEST_METHOD'] = 'GET';
                    $_GET = ['action' => 'get_config'];
                    
                    include $aiAPIFile;
                    $output = ob_get_clean();
                    
                    $data = json_decode($output, true);
                    if ($data && isset($data['success']) && $data['success']) {
                        echo '<div class="result pass">✅ AI API get_config يعمل بنجاح</div>';
                        
                        if (isset($data['data']['openai'])) {
                            $openaiConfig = $data['data']['openai'];
                            echo '<div class="result pass">✅ إعدادات OpenAI متاحة في API</div>';
                            echo '<div class="result info">📊 حالة OpenAI: ' . $openaiConfig['status'] . '</div>';
                            $fixedIssues[] = 'AI API get_config working';
                        }
                    } else {
                        echo '<div class="result fail">❌ AI API get_config لا يعطي استجابة صالحة</div>';
                        $remainingIssues[] = 'AI API get_config failed';
                        $allIssuesFixed = false;
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="result fail">❌ خطأ في AI API: ' . $e->getMessage() . '</div>';
                    $remainingIssues[] = 'AI API error: ' . $e->getMessage();
                    $allIssuesFixed = false;
                }
                
            } else {
                echo '<div class="result fail">❌ ملف AI API غير موجود</div>';
                $remainingIssues[] = 'AI API file missing';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 4: OpenAI Configuration Test
            echo '<div class="fix-section">';
            echo '<h3>🧠 إصلاح 4: اختبار تكوين OpenAI</h3>';
            
            echo '<div class="openai-config">';
            echo '<div class="config-title">';
            echo '<span class="config-icon">🧠</span>';
            echo 'تكوين OpenAI الحالي';
            echo '</div>';
            
            // Get current OpenAI settings
            $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE provider = 'openai'");
            $stmt->execute();
            $currentSettings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($currentSettings) {
                echo '<div class="result info">📋 النموذج: ' . $currentSettings['model'] . '</div>';
                echo '<div class="result info">🔢 Max Tokens: ' . $currentSettings['max_tokens'] . '</div>';
                echo '<div class="result info">🌡️ Temperature: ' . $currentSettings['temperature'] . '</div>';
                echo '<div class="result info">🔑 API Key: ' . (empty($currentSettings['api_key']) ? 'غير مُعين' : 'مُعين') . '</div>';
                echo '<div class="result info">⚡ نشط: ' . ($currentSettings['is_active'] ? 'نعم' : 'لا') . '</div>';
            }
            
            echo '</div>';
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
            $allIssuesFixed = false;
        }

        // Interactive Test Form
        echo '<div class="fix-section">';
        echo '<h3>🧪 اختبار تفاعلي لحفظ إعدادات OpenAI</h3>';
        
        echo '<div class="test-form">';
        echo '<form id="openaiTestForm">';
        echo '<div class="form-group">';
        echo '<label class="form-label">مفتاح OpenAI API:</label>';
        echo '<input type="text" id="apiKey" class="form-input" placeholder="sk-..." value="' . ($envApiKey ?? '') . '">';
        echo '</div>';
        
        echo '<div class="form-group">';
        echo '<label class="form-label">النموذج:</label>';
        echo '<select id="model" class="form-select">';
        echo '<option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>';
        echo '<option value="gpt-4">GPT-4</option>';
        echo '<option value="gpt-4-turbo">GPT-4 Turbo</option>';
        echo '</select>';
        echo '</div>';
        
        echo '<div class="form-group">';
        echo '<label class="form-label">Max Tokens:</label>';
        echo '<input type="number" id="maxTokens" class="form-input" value="1000" min="1" max="4000">';
        echo '</div>';
        
        echo '<div class="form-group">';
        echo '<label class="form-label">Temperature:</label>';
        echo '<input type="number" id="temperature" class="form-input" value="0.7" min="0" max="2" step="0.1">';
        echo '</div>';
        
        echo '<div class="form-group">';
        echo '<label class="form-label">';
        echo '<input type="checkbox" id="isActive" class="form-checkbox"> تفعيل OpenAI';
        echo '</label>';
        echo '</div>';
        
        echo '<button type="button" onclick="testSaveOpenAI()" class="fix-button">💾 اختبار حفظ الإعدادات</button>';
        echo '<button type="button" onclick="testLoadOpenAI()" class="fix-button">📥 اختبار تحميل الإعدادات</button>';
        echo '</form>';
        
        echo '<div id="testResults" style="margin-top: 20px;"></div>';
        echo '</div>';
        echo '</div>';

        // Summary
        echo '<div class="fix-section">';
        echo '<h3>📊 ملخص إصلاحات OpenAI</h3>';
        
        if ($allIssuesFixed) {
            echo '<div class="result pass">🎉 تم إصلاح جميع مشاكل تكامل OpenAI!</div>';
            echo '<div class="result pass">✅ OpenAI جاهز للاستخدام</div>';
        } else {
            echo '<div class="result warning">⚠️ تم إصلاح معظم المشاكل، بعض المشاكل تحتاج تدخل يدوي</div>';
        }
        
        if (!empty($fixedIssues)) {
            echo '<h4>✅ المشاكل المُصلحة:</h4>';
            echo '<ul>';
            foreach ($fixedIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        if (!empty($remainingIssues)) {
            echo '<h4>⚠️ المشاكل المتبقية:</h4>';
            echo '<ul>';
            foreach ($remainingIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<p><a href="index.html" class="fix-button">🏠 فتح لوحة التحكم</a></p>';
        echo '<p><a href="ai-settings.html" class="fix-button">🤖 اختبار AI Settings</a></p>';
        echo '<p><a href="test-ai-settings-navigation.php" class="fix-button">🧪 اختبار شامل</a></p>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Test saving OpenAI settings
        async function testSaveOpenAI() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="result info">🔄 جاري اختبار حفظ الإعدادات...</div>';
            
            const data = {
                provider: 'openai',
                api_key: document.getElementById('apiKey').value,
                model: document.getElementById('model').value,
                max_tokens: parseInt(document.getElementById('maxTokens').value),
                temperature: parseFloat(document.getElementById('temperature').value),
                is_active: document.getElementById('isActive').checked
            };
            
            try {
                const response = await fetch('../php/api/ai.php?action=update_config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultsDiv.innerHTML = '<div class="result pass">✅ تم حفظ إعدادات OpenAI بنجاح!</div>';
                    console.log('Save result:', result);
                } else {
                    resultsDiv.innerHTML = '<div class="result fail">❌ فشل في حفظ الإعدادات: ' + (result.message || 'خطأ غير معروف') + '</div>';
                }
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="result fail">❌ خطأ في الشبكة: ' + error.message + '</div>';
                console.error('Save error:', error);
            }
        }
        
        // Test loading OpenAI settings
        async function testLoadOpenAI() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="result info">🔄 جاري اختبار تحميل الإعدادات...</div>';
            
            try {
                const response = await fetch('../php/api/ai.php?action=get_config');
                const result = await response.json();
                
                if (result.success && result.data.openai) {
                    const openaiConfig = result.data.openai;
                    
                    // Update form with loaded data
                    document.getElementById('apiKey').value = openaiConfig.api_key || '';
                    document.getElementById('model').value = openaiConfig.model || 'gpt-3.5-turbo';
                    document.getElementById('maxTokens').value = openaiConfig.max_tokens || 1000;
                    document.getElementById('temperature').value = openaiConfig.temperature || 0.7;
                    document.getElementById('isActive').checked = openaiConfig.is_active || false;
                    
                    resultsDiv.innerHTML = '<div class="result pass">✅ تم تحميل إعدادات OpenAI بنجاح!</div>';
                    console.log('Load result:', openaiConfig);
                } else {
                    resultsDiv.innerHTML = '<div class="result fail">❌ فشل في تحميل الإعدادات: ' + (result.message || 'خطأ غير معروف') + '</div>';
                }
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="result fail">❌ خطأ في الشبكة: ' + error.message + '</div>';
                console.error('Load error:', error);
            }
        }
        
        // Auto-load settings on page load
        document.addEventListener('DOMContentLoaded', testLoadOpenAI);
    </script>
</body>
</html>
