<?php

/**
 * System Settings Database Setup
 * إعداد قاعدة البيانات لإعدادات النظام
 */

require_once '../config/db_env.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // Create database if it doesn't exist
    $tempPdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $tempPdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $tempPdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $tempPdo = null;

    // Use the existing PDO connection from db_simple.php

    echo json_encode([
        'success' => true,
        'message' => 'تم الاتصال بقاعدة البيانات بنجاح',
        'step' => 'database_connected'
    ]);

    // Read and execute schema file
    $schemaFile = __DIR__ . '/system-settings-schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception('ملف المخطط غير موجود');
    }

    $schema = file_get_contents($schemaFile);

    // Split SQL statements
    $statements = array_filter(
        array_map('trim', explode(';', $schema)),
        function ($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );

    $results = [];
    $successCount = 0;
    $errorCount = 0;

    foreach ($statements as $statement) {
        try {
            $pdo->exec($statement);
            $successCount++;

            // Extract table name for reporting
            if (preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                $results[] = [
                    'type' => 'table',
                    'name' => $matches[1],
                    'status' => 'success',
                    'message' => 'تم إنشاء الجدول بنجاح'
                ];
            } elseif (preg_match('/INSERT.*?INTO.*?`?(\w+)`?/i', $statement, $matches)) {
                $results[] = [
                    'type' => 'data',
                    'name' => $matches[1],
                    'status' => 'success',
                    'message' => 'تم إدراج البيانات الافتراضية'
                ];
            }
        } catch (PDOException $e) {
            $errorCount++;

            // Extract table name for error reporting
            $tableName = 'unknown';
            if (preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches)) {
                $tableName = $matches[1];
            } elseif (preg_match('/INSERT.*?INTO.*?`?(\w+)`?/i', $statement, $matches)) {
                $tableName = $matches[1];
            }

            $results[] = [
                'type' => 'error',
                'name' => $tableName,
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    // Verify tables were created
    $tables = [
        'categories' => 'الفئات',
        'payment_methods' => 'طرق الدفع',
        'general_settings' => 'الإعدادات العامة',
        'store_settings' => 'إعدادات المتاجر',
        'users' => 'المستخدمين',
        'stores' => 'المتاجر',
        'roles' => 'الأدوار',
        'subscriptions' => 'الاشتراكات',
        'user_subscriptions' => 'اشتراكات المستخدمين',
        'security_settings' => 'إعدادات الأمان',
        'system_logs' => 'سجلات النظام'
    ];

    $verification = [];
    foreach ($tables as $table => $nameAr) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;

            if ($exists) {
                // Get row count
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
                $count = $countStmt->fetch(PDO::FETCH_ASSOC)['count'];

                $verification[] = [
                    'table' => $table,
                    'name_ar' => $nameAr,
                    'exists' => true,
                    'count' => $count,
                    'status' => 'success'
                ];
            } else {
                $verification[] = [
                    'table' => $table,
                    'name_ar' => $nameAr,
                    'exists' => false,
                    'count' => 0,
                    'status' => 'error'
                ];
            }
        } catch (Exception $e) {
            $verification[] = [
                'table' => $table,
                'name_ar' => $nameAr,
                'exists' => false,
                'count' => 0,
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    // Create system admin user if not exists
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE role_id = 4");
        $stmt->execute();
        $adminCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($adminCount == 0) {
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, first_name, last_name, role_id, status, email_verified)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                'admin',
                '<EMAIL>',
                $adminPassword,
                'مصعب',
                'المدير',
                4, // super_admin
                'active',
                1
            ]);

            $results[] = [
                'type' => 'admin',
                'name' => 'admin_user',
                'status' => 'success',
                'message' => 'تم إنشاء حساب المدير الافتراضي'
            ];
        }
    } catch (Exception $e) {
        $results[] = [
            'type' => 'admin',
            'name' => 'admin_user',
            'status' => 'error',
            'message' => 'فشل في إنشاء حساب المدير: ' . $e->getMessage()
        ];
    }

    // Final response
    echo json_encode([
        'success' => $errorCount === 0,
        'message' => $errorCount === 0 ?
            'تم إعداد قاعدة البيانات بنجاح' :
            "تم الإعداد مع $errorCount أخطاء",
        'statistics' => [
            'total_statements' => count($statements),
            'success_count' => $successCount,
            'error_count' => $errorCount
        ],
        'results' => $results,
        'verification' => $verification,
        'next_steps' => [
            'تسجيل الدخول باستخدام: <EMAIL>',
            'كلمة المرور الافتراضية: admin123',
            'يُنصح بتغيير كلمة المرور فوراً',
            'مراجعة إعدادات الأمان'
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'فشل في إعداد قاعدة البيانات: ' . $e->getMessage(),
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], JSON_UNESCAPED_UNICODE);
}
