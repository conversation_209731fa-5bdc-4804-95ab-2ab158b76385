<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الأمان - لوحة التحكم</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/security-settings.css">
    
    <!-- Required Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/security-settings.js"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body dir="rtl">
    <!-- Security Settings Content -->
    <div class="security-settings-content dashboard-layout">
        <!-- Header Section -->
        <div class="security-settings-header">
                            <div class="section-title-wrapper">
                                <div class="section-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="section-title-content">
                                    <h3 class="section-title">إعدادات الأمان</h3>
                                    <p class="section-subtitle">إدارة أمان النظام والحماية من التهديدات</p>
                                </div>
                            </div>
                            <div class="security-summary">
                                <div class="summary-item">
                                    <span class="summary-label">مستوى الأمان:</span>
                                    <span class="summary-value security-level" id="securityLevel">متوسط</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">آخر فحص:</span>
                                    <span class="summary-value" id="lastSecurityCheck">--</span>
                                </div>
                            </div>
                        </div>

                        <!-- Security Grid -->
                        <div class="security-grid-container">
                            <!-- Authentication Settings -->
                            <div class="security-card enhanced-card">
                                <div class="card-header">
                                    <div class="card-icon auth-icon">
                                        <i class="fas fa-key"></i>
                                    </div>
                                    <div class="card-title-section">
                                        <h4 class="card-title">إعدادات المصادقة</h4>
                                        <p class="card-description">تكوين طرق تسجيل الدخول والمصادقة</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form id="authSettingsForm" class="enhanced-form">
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="requireStrongPassword" class="form-check-input">
                                                <label for="requireStrongPassword" class="form-check-label">
                                                    <span class="checkbox-text">كلمات مرور قوية</span>
                                                    <span class="checkbox-description">إجبار المستخدمين على استخدام كلمات مرور قوية</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="enableTwoFactor" class="form-check-input">
                                                <label for="enableTwoFactor" class="form-check-label">
                                                    <span class="checkbox-text">المصادقة الثنائية</span>
                                                    <span class="checkbox-description">تفعيل المصادقة الثنائية للمديرين</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="sessionTimeout" class="enhanced-label">
                                                    <i class="fas fa-clock"></i>
                                                    انتهاء الجلسة (دقيقة)
                                                </label>
                                                <input type="number" id="sessionTimeout" class="enhanced-input" min="5" max="1440" value="30">
                                            </div>
                                            <div class="form-group">
                                                <label for="maxLoginAttempts" class="enhanced-label">
                                                    <i class="fas fa-ban"></i>
                                                    محاولات تسجيل الدخول
                                                </label>
                                                <input type="number" id="maxLoginAttempts" class="enhanced-input" min="3" max="10" value="5">
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Access Control -->
                            <div class="security-card enhanced-card">
                                <div class="card-header">
                                    <div class="card-icon access-icon">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div class="card-title-section">
                                        <h4 class="card-title">التحكم في الوصول</h4>
                                        <p class="card-description">إدارة صلاحيات الوصول والحماية</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form id="accessControlForm" class="enhanced-form">
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="enableIPWhitelist" class="form-check-input">
                                                <label for="enableIPWhitelist" class="form-check-label">
                                                    <span class="checkbox-text">قائمة IP المسموحة</span>
                                                    <span class="checkbox-description">السماح فقط لعناوين IP محددة</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group" id="ipWhitelistGroup" style="display: none;">
                                            <label for="ipWhitelist" class="enhanced-label">
                                                <i class="fas fa-list"></i>
                                                عناوين IP المسموحة
                                            </label>
                                            <textarea id="ipWhitelist" class="enhanced-textarea" rows="4" placeholder="***********&#10;********&#10;***********"></textarea>
                                            <div class="input-hint">أدخل عنوان IP واحد في كل سطر</div>
                                        </div>
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="enableGeoBlocking" class="form-check-input">
                                                <label for="enableGeoBlocking" class="form-check-label">
                                                    <span class="checkbox-text">الحظر الجغرافي</span>
                                                    <span class="checkbox-description">حظر الوصول من دول معينة</span>
                                                </label>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Data Protection -->
                            <div class="security-card enhanced-card">
                                <div class="card-header">
                                    <div class="card-icon data-icon">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="card-title-section">
                                        <h4 class="card-title">حماية البيانات</h4>
                                        <p class="card-description">تشفير وحماية البيانات الحساسة</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form id="dataProtectionForm" class="enhanced-form">
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="enableDataEncryption" class="form-check-input">
                                                <label for="enableDataEncryption" class="form-check-label">
                                                    <span class="checkbox-text">تشفير البيانات</span>
                                                    <span class="checkbox-description">تشفير البيانات الحساسة في قاعدة البيانات</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="enableBackupEncryption" class="form-check-input">
                                                <label for="enableBackupEncryption" class="form-check-label">
                                                    <span class="checkbox-text">تشفير النسخ الاحتياطية</span>
                                                    <span class="checkbox-description">تشفير ملفات النسخ الاحتياطية</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="backupRetention" class="enhanced-label">
                                                    <i class="fas fa-calendar"></i>
                                                    الاحتفاظ بالنسخ (أيام)
                                                </label>
                                                <input type="number" id="backupRetention" class="enhanced-input" min="7" max="365" value="30">
                                            </div>
                                            <div class="form-group">
                                                <label for="dataRetention" class="enhanced-label">
                                                    <i class="fas fa-trash-alt"></i>
                                                    حذف البيانات القديمة (أيام)
                                                </label>
                                                <input type="number" id="dataRetention" class="enhanced-input" min="30" max="3650" value="365">
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Security Monitoring -->
                            <div class="security-card enhanced-card">
                                <div class="card-header">
                                    <div class="card-icon monitor-icon">
                                        <i class="fas fa-eye"></i>
                                    </div>
                                    <div class="card-title-section">
                                        <h4 class="card-title">مراقبة الأمان</h4>
                                        <p class="card-description">تسجيل ومراقبة الأنشطة المشبوهة</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form id="monitoringForm" class="enhanced-form">
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="enableActivityLogging" class="form-check-input">
                                                <label for="enableActivityLogging" class="form-check-label">
                                                    <span class="checkbox-text">تسجيل الأنشطة</span>
                                                    <span class="checkbox-description">تسجيل جميع أنشطة المستخدمين</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="enableSecurityAlerts" class="form-check-input">
                                                <label for="enableSecurityAlerts" class="form-check-label">
                                                    <span class="checkbox-text">تنبيهات الأمان</span>
                                                    <span class="checkbox-description">إرسال تنبيهات عند اكتشاف أنشطة مشبوهة</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="alertEmail" class="enhanced-label">
                                                <i class="fas fa-envelope"></i>
                                                بريد التنبيهات
                                            </label>
                                            <input type="email" id="alertEmail" class="enhanced-input" placeholder="<EMAIL>">
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- SSL/HTTPS Settings -->
                            <div class="security-card enhanced-card">
                                <div class="card-header">
                                    <div class="card-icon ssl-icon">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                    <div class="card-title-section">
                                        <h4 class="card-title">إعدادات SSL/HTTPS</h4>
                                        <p class="card-description">تكوين شهادات الأمان والتشفير</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form id="sslSettingsForm" class="enhanced-form">
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="forceHTTPS" class="form-check-input">
                                                <label for="forceHTTPS" class="form-check-label">
                                                    <span class="checkbox-text">إجبار HTTPS</span>
                                                    <span class="checkbox-description">إعادة توجيه جميع الطلبات إلى HTTPS</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="enhanced-checkbox">
                                                <input type="checkbox" id="enableHSTS" class="form-check-input">
                                                <label for="enableHSTS" class="form-check-label">
                                                    <span class="checkbox-text">تفعيل HSTS</span>
                                                    <span class="checkbox-description">HTTP Strict Transport Security</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="ssl-status">
                                            <div class="status-item">
                                                <span class="status-label">حالة الشهادة:</span>
                                                <span class="status-value" id="sslStatus">غير محدد</span>
                                            </div>
                                            <div class="status-item">
                                                <span class="status-label">تاريخ الانتهاء:</span>
                                                <span class="status-value" id="sslExpiry">--</span>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Security Audit -->
                            <div class="security-card enhanced-card">
                                <div class="card-header">
                                    <div class="card-icon audit-icon">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <div class="card-title-section">
                                        <h4 class="card-title">تدقيق الأمان</h4>
                                        <p class="card-description">فحص وتقييم مستوى الأمان</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="audit-controls">
                                        <button type="button" class="btn btn-primary enhanced-btn" onclick="runSecurityAudit()">
                                            <i class="fas fa-play"></i>
                                            تشغيل فحص الأمان
                                        </button>
                                        <button type="button" class="btn btn-secondary enhanced-btn" onclick="downloadAuditReport()">
                                            <i class="fas fa-download"></i>
                                            تحميل التقرير
                                        </button>
                                    </div>
                                    <div class="audit-results" id="auditResults">
                                        <div class="audit-placeholder">
                                            <i class="fas fa-shield-alt"></i>
                                            <p>قم بتشغيل فحص الأمان لعرض النتائج</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="security-actions">
                            <button type="button" class="btn btn-save enhanced-save-btn" onclick="saveSecuritySettings()">
                                <i class="fas fa-save"></i>
                                <span class="btn-text">حفظ إعدادات الأمان</span>
                            </button>
                            <button type="button" class="btn btn-warning enhanced-test-btn" onclick="testSecuritySettings()">
                                <i class="fas fa-vial"></i>
                                <span class="btn-text">اختبار الإعدادات</span>
                            </button>
                            <button type="button" class="btn btn-danger enhanced-reset-btn" onclick="resetSecuritySettings()">
                                <i class="fas fa-undo"></i>
                                <span class="btn-text">استعادة الافتراضي</span>
                            </button>
                        </div>
        </div>
    </div>

    <script src="js/security-settings.js"></script>
</body>
</html>
