<?php
/**
 * Fix Demo User Store Association
 * Links the demo user to their store properly
 */

require_once 'php/config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>إصلاح ربط المستخدم التجريبي بالمتجر</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔗 إصلاح ربط المستخدم التجريبي بالمتجر</h1>";

try {
    $pdo = getPDOConnection();
    
    echo "<div class='section'>";
    echo "<h2>1️⃣ فحص المستخدم التجريبي</h2>";
    
    // Get demo user
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $demoUser = $stmt->fetch();
    
    if (!$demoUser) {
        throw new Exception('Demo user not found');
    }
    
    echo "<div class='info'>";
    echo "<h4>👤 معلومات المستخدم التجريبي:</h4>";
    echo "<ul>";
    echo "<li>ID: {$demoUser['id']}</li>";
    echo "<li>الاسم: {$demoUser['first_name']} {$demoUser['last_name']}</li>";
    echo "<li>البريد الإلكتروني: {$demoUser['email']}</li>";
    echo "<li>Store ID الحالي: " . ($demoUser['store_id'] ?? 'غير محدد') . "</li>";
    echo "<li>الدور: {$demoUser['role_id']}</li>";
    echo "<li>الاشتراك: {$demoUser['subscription_id']}</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ فحص المتجر التجريبي</h2>";
    
    // Get demo store
    $stmt = $pdo->prepare("SELECT * FROM stores WHERE user_id = ? OR store_slug = ?");
    $stmt->execute([$demoUser['id'], 'mossaab-store']);
    $demoStore = $stmt->fetch();
    
    if (!$demoStore) {
        echo "<div class='info'>ℹ️ لم يتم العثور على متجر للمستخدم التجريبي، سيتم إنشاؤه...</div>";
        
        // Create demo store
        $stmt = $pdo->prepare("
            INSERT INTO stores (user_id, store_name, store_slug, description, status, theme, settings, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $settings = json_encode([
            'currency' => 'DZD',
            'language' => 'ar',
            'timezone' => 'Africa/Algiers',
            'theme_color' => '#667eea',
            'allow_reviews' => true,
            'auto_approve_products' => false
        ]);
        
        $stmt->execute([
            $demoUser['id'],
            'متجر مصعب التجريبي',
            'mossaab-store',
            'متجر شامل للكتب والإلكترونيات والحقائب والمنتجات المتنوعة',
            'active',
            'default',
            $settings
        ]);
        
        $storeId = $pdo->lastInsertId();
        echo "<div class='success'>✅ تم إنشاء المتجر التجريبي بنجاح (ID: {$storeId})</div>";
        
        // Get the newly created store
        $stmt = $pdo->prepare("SELECT * FROM stores WHERE id = ?");
        $stmt->execute([$storeId]);
        $demoStore = $stmt->fetch();
    } else {
        echo "<div class='info'>ℹ️ تم العثور على المتجر التجريبي</div>";
    }
    
    echo "<div class='info'>";
    echo "<h4>🏪 معلومات المتجر التجريبي:</h4>";
    echo "<ul>";
    echo "<li>ID: {$demoStore['id']}</li>";
    echo "<li>اسم المتجر: {$demoStore['store_name']}</li>";
    echo "<li>الرابط: {$demoStore['store_slug']}</li>";
    echo "<li>مالك المتجر: {$demoStore['user_id']}</li>";
    echo "<li>الحالة: {$demoStore['status']}</li>";
    echo "<li>إجمالي المنتجات: {$demoStore['total_products']}</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ ربط المستخدم بالمتجر</h2>";
    
    // Update user's store_id
    if ($demoUser['store_id'] != $demoStore['id']) {
        $stmt = $pdo->prepare("UPDATE users SET store_id = ? WHERE id = ?");
        $stmt->execute([$demoStore['id'], $demoUser['id']]);
        echo "<div class='success'>✅ تم ربط المستخدم التجريبي بالمتجر بنجاح</div>";
    } else {
        echo "<div class='info'>ℹ️ المستخدم مرتبط بالمتجر بالفعل</div>";
    }
    
    // Update store's user_id if needed
    if ($demoStore['user_id'] != $demoUser['id']) {
        $stmt = $pdo->prepare("UPDATE stores SET user_id = ? WHERE id = ?");
        $stmt->execute([$demoUser['id'], $demoStore['id']]);
        echo "<div class='success'>✅ تم تحديث مالك المتجر بنجاح</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4️⃣ فحص المنتجات والصفحات</h2>";
    
    // Count products for this store
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM produits WHERE store_id = ?");
    $stmt->execute([$demoStore['id']]);
    $productCount = $stmt->fetchColumn();
    
    // Count landing pages for this store
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM landing_pages WHERE store_id = ?");
    $stmt->execute([$demoStore['id']]);
    $landingPageCount = $stmt->fetchColumn();
    
    echo "<div class='info'>";
    echo "<h4>📊 إحصائيات المتجر:</h4>";
    echo "<ul>";
    echo "<li>📦 عدد المنتجات: {$productCount}</li>";
    echo "<li>🌐 عدد صفحات الهبوط: {$landingPageCount}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Update store's total_products
    $stmt = $pdo->prepare("UPDATE stores SET total_products = ? WHERE id = ?");
    $stmt->execute([$productCount, $demoStore['id']]);
    echo "<div class='success'>✅ تم تحديث إحصائيات المتجر</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>5️⃣ التحقق من الاشتراك والصلاحيات</h2>";
    
    // Check subscription
    $stmt = $pdo->prepare("
        SELECT sp.*, ur.display_name_ar as role_name
        FROM users u
        LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
        LEFT JOIN user_roles ur ON u.role_id = ur.id
        WHERE u.id = ?
    ");
    $stmt->execute([$demoUser['id']]);
    $userDetails = $stmt->fetch();
    
    if ($userDetails) {
        echo "<div class='info'>";
        echo "<h4>📋 تفاصيل الاشتراك والصلاحيات:</h4>";
        echo "<ul>";
        echo "<li>الدور: " . ($userDetails['role_name'] ?? 'غير محدد') . "</li>";
        echo "<li>نوع الاشتراك: " . ($userDetails['display_name_ar'] ?? 'غير محدد') . "</li>";
        echo "<li>الحد الأقصى للمنتجات: " . ($userDetails['max_products'] ?? 'غير محدد') . "</li>";
        echo "<li>الحد الأقصى لصفحات الهبوط: " . ($userDetails['max_landing_pages'] ?? 'غير محدد') . "</li>";
        echo "<li>مساحة التخزين: " . ($userDetails['max_storage_mb'] ?? 'غير محدد') . " ميجابايت</li>";
        echo "</ul>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص النتائج</h2>";
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إصلاح ربط المستخدم التجريبي بالمتجر بنجاح!</h3>";
    echo "<ul>";
    echo "<li>👤 المستخدم: {$demoUser['first_name']} {$demoUser['last_name']} (ID: {$demoUser['id']})</li>";
    echo "<li>🏪 المتجر: {$demoStore['store_name']} (ID: {$demoStore['id']})</li>";
    echo "<li>📦 المنتجات: {$productCount}</li>";
    echo "<li>🌐 صفحات الهبوط: {$landingPageCount}</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔗 روابط مفيدة:</h4>";
    echo "<ul>";
    echo "<li><a href='/store.php?slug={$demoStore['store_slug']}' target='_blank'>زيارة المتجر التجريبي</a></li>";
    echo "<li><a href='/dashboard/' target='_blank'>لوحة تحكم المتجر</a></li>";
    echo "<li><a href='/admin/' target='_blank'>لوحة التحكم الإدارية</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h4>🔑 بيانات الدخول:</h4>";
    echo "<ul>";
    echo "<li>البريد الإلكتروني: <EMAIL></li>";
    echo "<li>كلمة المرور: demo123</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
