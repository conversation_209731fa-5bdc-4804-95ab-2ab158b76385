// Store Settings Module
const storeSettings = (() => {
    // Default settings
    const getDefaultSettings = () => ({
        general: {
            storeName: '',
            description: '',
            email: '',
            phone: '',
            address: '',
            timezone: 'UTC+3',
            language: 'ar'
        },
        payment: {
            currency: 'SAR',
            currencySymbol: '﷼',
            taxRate: 15,
            paymentMethods: {
                cash: true,
                bankTransfer: false,
                creditCard: false
            }
        },
        shipping: {
            enableShipping: true,
            freeShippingThreshold: 200,
            defaultShippingRate: 30,
            internationalShipping: false
        },
        notification: {
            orderConfirmation: true,
            shipmentUpdates: true,
            stockAlerts: false,
            marketingEmails: false,
            smsNotifications: false
        },
        display: {
            productsPerPage: 12,
            showOutOfStock: true,
            enableWishlist: true,
            enableCompare: false,
            theme: 'default'
        },
        social: {
            facebook: '',
            twitter: '',
            instagram: '',
            whatsapp: ''
        },
        business: {
            companyName: '',
            vatNumber: '',
            crNumber: '',
            bankAccount: ''
        }
    });

    // Validation rules
    const validationRules = {
        'general.storeName': { required: true, label: 'اسم المتجر' },
        'general.email': { required: true, type: 'email', label: 'البريد الإلكتروني' },
        'general.phone': { required: true, pattern: /^\+?[0-9]{10,}$/, label: 'رقم الهاتف' },
        'payment.taxRate': { type: 'number', min: 0, max: 100, label: 'نسبة الضريبة' },
        'shipping.freeShippingThreshold': { type: 'number', min: 0, label: 'حد الشحن المجاني' },
        'shipping.defaultShippingRate': { type: 'number', min: 0, label: 'تكلفة الشحن الافتراضية' },
        'display.productsPerPage': { type: 'number', min: 1, max: 100, label: 'عدد المنتجات في الصفحة' },
        'social.facebook': { type: 'url', label: 'رابط فيسبوك' },
        'social.twitter': { type: 'url', label: 'رابط تويتر' },
        'social.instagram': { type: 'url', label: 'رابط انستغرام' },
        'business.vatNumber': { pattern: /^[0-9]{15}$/, label: 'رقم ضريبة القيمة المضافة' },
        'business.crNumber': { pattern: /^[0-9]{10}$/, label: 'رقم السجل التجاري' }
    };

    // Load settings data
    const loadStoreSettingsData = async () => {
        try {
            const response = await fetch('/api/store/settings');
            if (!response.ok) {
                throw new Error('Failed to load settings');
            }
            return await response.json();
        } catch (error) {
            console.error('Error loading settings:', error);
            return getDefaultSettings();
        }
    };

    // Populate form fields
    const populateFormFields = (settings) => {
        Object.entries(settings).forEach(([section, sectionData]) => {
            Object.entries(sectionData).forEach(([key, value]) => {
                const input = document.querySelector(`[name="${section}.${key}"]`);
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = value;
                    } else if (input.type === 'select-one') {
                        input.value = value;
                    } else {
                        input.value = value;
                    }
                }
            });
        });
    };

    // Initialize tooltips
    const initializeTooltips = () => {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        tooltips.forEach(element => {
            new Tooltip(element, {
                title: element.getAttribute('data-tooltip'),
                placement: 'top',
                trigger: 'hover'
            });
        });
    };

    // Save settings
    const saveStoreSettings = async (data) => {
        const errors = settingsCore.validateSettings(data, validationRules);
        if (errors.length > 0) {
            showValidationErrors(errors);
            return false;
        }

        try {
            await settingsCore.saveToAPI('/api/store/settings', data, {
                successMessage: 'تم حفظ إعدادات المتجر بنجاح',
                errorMessage: 'فشل حفظ إعدادات المتجر',
                loadingMessage: 'جاري حفظ الإعدادات...'
            });
            return true;
        } catch (error) {
            return false;
        }
    };

    // Collect form data
    const collectFormData = () => {
        const form = document.querySelector('#storeSettingsForm');
        const formData = new FormData(form);
        const settings = getDefaultSettings();

        for (const [key, value] of formData.entries()) {
            const [section, field] = key.split('.');
            if (settings[section] && field in settings[section]) {
                settings[section][field] = value;
            }
        }

        return settings;
    };

    // Show validation errors
    const showValidationErrors = (errors) => {
        const errorContainer = document.querySelector('#validationErrors');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
    };

    // Reset settings
    const resetStoreSettings = () => {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
            const defaultSettings = getDefaultSettings();
            populateFormFields(defaultSettings);
            settingsCore.showNotification('info', 'تم إعادة تعيين الإعدادات إلى القيم الافتراضية');
        }
    };

    // Initialize
    const initialize = async () => {
        try {
            const settings = await loadStoreSettingsData();
            populateFormFields(settings);
            initializeTooltips();

            // Form submit handler
            const form = document.querySelector('#storeSettingsForm');
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                const data = collectFormData();
                await saveStoreSettings(data);
            });

            // Reset button handler
            const resetButton = document.querySelector('#resetSettings');
            if (resetButton) {
                resetButton.addEventListener('click', resetStoreSettings);
            }

            settingsCore.initialize();
        } catch (error) {
            console.error('Error initializing store settings:', error);
            settingsCore.showNotification('error', 'فشل تحميل إعدادات المتجر');
        }
    };

    // Public API
    return { initialize };
})();

// Initialize on DOM load
document.addEventListener('DOMContentLoaded', storeSettings.initialize);
