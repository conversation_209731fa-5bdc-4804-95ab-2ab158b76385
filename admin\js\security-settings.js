/**
 * Security Settings Management
 * Handles all security-related settings functionality
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeSecuritySettings();
    initializeSecurityDashboard();
    initializePasswordPolicy();
    initializeThreatDetection();
});

// Default security settings with enhanced features
const securitySettings = {
    authentication: {
        requireStrongPassword: true,
        enableTwoFactor: false,
        sessionTimeout: 30,
        maxLoginAttempts: 5,
        passwordPolicy: {
            minLength: 8,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecial: true
        },
        twoFactorConfig: {
            type: 'google_authenticator',
            backupCodes: 5,
            rememberDevice: false
        }
    },
    accessControl: {
        enableIPWhitelist: false,
        ipWhitelist: [],
        enableIPBlacklist: true,
        ipBlacklist: [],
        enableGeoBlocking: false,
        blockedCountries: [],
        rateLimiting: {
            enabled: true,
            maxRequests: 100,
            timeWindow: 60 // seconds
        },
        activeSessions: []
    },
    dataProtection: {
        enableDataEncryption: true,
        enableBackupEncryption: true,
        backupRetention: 30,
        dataRetention: 365,
        encryptionAlgorithm: 'AES-256-GCM',
        autoBackup: {
            enabled: true,
            frequency: 'daily',
            time: '02:00'
        }
    },
    monitoring: {
        enableActivityLogging: true,
        logRetentionDays: 90,
        alertOnSuspiciousActivity: true,
        notifyAdminOnFailedLogin: true,
        alertEmail: '',
        securityAudit: {
            enabled: true,
            frequency: 'daily',
            lastRun: null
        },
        threatDetection: {
            enabled: true,
            sensitivity: 'medium',
            autoBlock: true
        }
    },
    ssl: {
        forceHTTPS: true,
        hsts: true,
        certificateInfo: null
    }
};

/**
 * Load security settings content (called from admin panel)
 */
function loadSecuritySettingsContent() {
    console.log('🔒 تحميل محتوى إعدادات الأمان من لوحة التحكم...');
    showSecuritySettings();
}

/**
 * Show security settings interface
 */
function showSecuritySettings() {
    console.log('🔒 بدء تحميل إعدادات الأمان...');

    // Try multiple possible container IDs
    let container = document.getElementById('securitySettingsContent') ||
                   document.getElementById('mainContent');

    if (!container) {
        console.error('❌ لم يتم العثور على حاوي المحتوى');
        console.log('Available containers:',
            Array.from(document.querySelectorAll('[id*="Content"]')).map(el => el.id));
        return;
    }

    console.log('✅ تم العثور على الحاوي:', container.id);

    // Show loading state
    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <div style="width: 50px; height: 50px; border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
            <p style="color: #666; font-size: 1.1rem;">جاري تحميل إعدادات الأمان...</p>
        </div>
    `;

    // Load security settings interface
    setTimeout(() => {
        loadSecuritySettingsInterface();
    }, 500);
}

/**
 * Load enhanced security settings interface with real-time monitoring
 */
function loadSecuritySettingsInterface() {
    // Try multiple possible container IDs
    const container = document.getElementById('securitySettingsContent') ||
                     document.getElementById('mainContent');

    if (!container) {
        console.error('❌ لم يتم العثور على حاوي واجهة إعدادات الأمان');
        return;
    }

    console.log('✅ تحميل واجهة إعدادات الأمان في:', container.id);

    // Initialize real-time monitoring
    initializeSecurityMonitoring();

    // Start periodic security checks
    startSecurityAudit();

    // Initialize WebSocket connection for real-time updates (optional)
    try {
        initializeSecurityWebSocket();
    } catch (error) {
        console.log('WebSocket not available, using polling instead');
    }

    container.innerHTML = `
        <div class="security-settings-container">
            <!-- Header -->
            <div class="security-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center;">
                <h1 style="margin: 0 0 10px 0; font-size: 2rem;">
                    <i class="fas fa-shield-alt"></i> إعدادات الأمان
                </h1>
                <p style="margin: 0; opacity: 0.9; font-size: 1.1rem;">إدارة وتكوين إعدادات الأمان والحماية</p>
                <div style="margin-top: 20px;">
                    <button onclick="runSecurityAudit()" style="padding: 12px 24px; background: white; color: #667eea; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-search"></i> فحص الأمان
                    </button>
                    <button onclick="exportSecuritySettings()" style="padding: 12px 24px; background: rgba(255,255,255,0.2); color: white; border: 2px solid white; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 5px;">
                        <i class="fas fa-download"></i> تصدير الإعدادات
                    </button>
                </div>
            </div>

            <!-- Security Dashboard -->
            <div class="security-dashboard" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div class="security-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #28a745, #20c997); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                            <i class="fas fa-shield-check" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333; font-size: 1.2rem;">مستوى الأمان</h3>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">التقييم الحالي</p>
                        </div>
                    </div>
                    <div id="securityLevel" style="font-size: 1.5rem; font-weight: bold; color: #28a745;">عالي</div>
                    <div style="margin-top: 10px; background: #f8f9fa; border-radius: 8px; padding: 10px;">
                        <div id="securityScore" style="font-size: 0.9rem; color: #666;">النقاط: 85/100</div>
                    </div>
                </div>

                <div class="security-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #007bff, #0056b3); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                            <i class="fas fa-users-cog" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333; font-size: 1.2rem;">المستخدمون النشطون</h3>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">الجلسات الحالية</p>
                        </div>
                    </div>
                    <div id="activeUsers" style="font-size: 1.5rem; font-weight: bold; color: #007bff;">3</div>
                    <div style="margin-top: 10px; background: #f8f9fa; border-radius: 8px; padding: 10px;">
                        <div style="font-size: 0.9rem; color: #666;">آخر نشاط: منذ 5 دقائق</div>
                    </div>
                </div>

                <div class="security-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ffc107, #e0a800); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                            <i class="fas fa-exclamation-triangle" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333; font-size: 1.2rem;">التنبيهات الأمنية</h3>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">آخر 24 ساعة</p>
                        </div>
                    </div>
                    <div id="securityAlerts" style="font-size: 1.5rem; font-weight: bold; color: #ffc107;">2</div>
                    <div style="margin-top: 10px; background: #f8f9fa; border-radius: 8px; padding: 10px;">
                        <div style="font-size: 0.9rem; color: #666;">محاولات دخول فاشلة</div>
                    </div>
                </div>

                <div class="security-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 15px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #dc3545, #c82333); border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-left: 15px;">
                            <i class="fas fa-ban" style="color: white; font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #333; font-size: 1.2rem;">IP محظورة</h3>
                            <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">القائمة السوداء</p>
                        </div>
                    </div>
                    <div id="blockedIPs" style="font-size: 1.5rem; font-weight: bold; color: #dc3545;">12</div>
                    <div style="margin-top: 10px; background: #f8f9fa; border-radius: 8px; padding: 10px;">
                        <div style="font-size: 0.9rem; color: #666;">تم الحظر تلقائياً</div>
                    </div>
                </div>
            </div>

            <!-- Security Settings Tabs -->
            <div class="security-tabs" style="background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); overflow: hidden;">
                <div class="tab-headers" style="display: flex; background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                    <button class="tab-header active" onclick="switchSecurityTab('authentication', this)" style="flex: 1; padding: 15px 20px; border: none; background: transparent; cursor: pointer; font-weight: bold; color: #495057; border-bottom: 3px solid transparent;">
                        <i class="fas fa-key"></i> المصادقة
                    </button>
                    <button class="tab-header" onclick="switchSecurityTab('access', this)" style="flex: 1; padding: 15px 20px; border: none; background: transparent; cursor: pointer; font-weight: bold; color: #495057; border-bottom: 3px solid transparent;">
                        <i class="fas fa-lock"></i> التحكم في الوصول
                    </button>
                    <button class="tab-header" onclick="switchSecurityTab('monitoring', this)" style="flex: 1; padding: 15px 20px; border: none; background: transparent; cursor: pointer; font-weight: bold; color: #495057; border-bottom: 3px solid transparent;">
                        <i class="fas fa-eye"></i> المراقبة
                    </button>
                    <button class="tab-header" onclick="switchSecurityTab('backup', this)" style="flex: 1; padding: 15px 20px; border: none; background: transparent; cursor: pointer; font-weight: bold; color: #495057; border-bottom: 3px solid transparent;">
                        <i class="fas fa-database"></i> النسخ الاحتياطي
                    </button>
                </div>

                <div class="tab-content" style="padding: 30px;">
                    <div id="authenticationTab" class="tab-pane active">
                        <!-- Authentication settings will be loaded here -->
                    </div>
                    <div id="accessTab" class="tab-pane" style="display: none;">
                        <!-- Access control settings will be loaded here -->
                    </div>
                    <div id="monitoringTab" class="tab-pane" style="display: none;">
                        <!-- Monitoring settings will be loaded here -->
                    </div>
                    <div id="backupTab" class="tab-pane" style="display: none;">
                        <!-- Backup settings will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .tab-header.active {
                background: white !important;
                color: #667eea !important;
                border-bottom-color: #667eea !important;
            }

            .security-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                transition: all 0.3s ease;
            }
        </style>
    `;

    // Load the authentication tab by default
    loadAuthenticationTab();

    // Load security data
    loadSecurityData();
}

/**
 * Initialize security settings page with real-time monitoring
 */
function initializeSecuritySettings() {
    console.log('🔒 تهيئة إعدادات الأمان...');

    // Load settings
    loadSecuritySettings();

    // Initialize charts
    initializeSecurityCharts();

    // Add event listeners
    initializeEventListeners();

    // Initialize password strength meter
    initializePasswordStrengthMeter();

    // Initialize IP validation
    initializeIPValidation();
}

/**
 * Initialize password strength meter
 */
function initializePasswordStrengthMeter() {
    const passwordInput = document.getElementById('passwordPolicy');
    if (passwordInput) {
        passwordInput.addEventListener('input', (e) => {
            const strength = calculatePasswordStrength(e.target.value);
            updatePasswordStrengthMeter(strength);
        });
    }
}

/**
 * Initialize IP validation
 */
function initializeIPValidation() {
    const ipWhitelist = document.getElementById('ipWhitelist');
    const ipBlacklist = document.getElementById('ipBlacklist');

    if (ipWhitelist) {
        ipWhitelist.addEventListener('input', validateIPList);
    }
    if (ipBlacklist) {
        ipBlacklist.addEventListener('input', validateIPList);
    }
}

/**
 * Initialize security monitoring with WebSocket
 */
function initializeSecurityMonitoring() {
    const ws = new WebSocket(`ws://${window.location.host}/security-ws`);

    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        updateSecurityDashboard(data);
    };

    ws.onerror = (error) => {
        console.error('خطأ في اتصال WebSocket:', error);
        showNotification('تحذير: فشل في اتصال المراقبة المباشرة', 'warning');
    };
}

/**
 * Initialize security charts using Chart.js
 */
function initializeSecurityCharts() {
    // Login attempts chart
    const loginAttemptsCtx = document.getElementById('loginAttemptsChart');
    if (loginAttemptsCtx) {
        new Chart(loginAttemptsCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'محاولات تسجيل الدخول',
                    data: [],
                    borderColor: '#667eea',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    // Security incidents chart
    const incidentsCtx = document.getElementById('securityIncidentsChart');
    if (incidentsCtx) {
        new Chart(incidentsCtx, {
            type: 'bar',
            data: {
                labels: ['منخفض', 'متوسط', 'عالي', 'حرج'],
                datasets: [{
                    label: 'الحوادث الأمنية',
                    data: [0, 0, 0, 0],
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
}

/**
 * Start periodic security audit
 */
function startSecurityAudit() {
    const runAudit = async () => {
        try {
            const response = await fetch('/api/security-settings.php?action=audit');
            const data = await response.json();

            if (data.success) {
                updateSecurityScore(data.score);
                updateSecurityRecommendations(data.recommendations);
            }
        } catch (error) {
            console.error('خطأ في تشغيل التدقيق الأمني:', error);
        }
    };

    // Run initial audit
    runAudit();

    // Schedule periodic audits
    setInterval(runAudit, 3600000); // Every hour
}

/**
 * Update security dashboard with real-time data
 */
function updateSecurityDashboard(data) {
    // Update security level and score
    document.getElementById('securityLevel').textContent = data.level;
    document.getElementById('securityScore').textContent = `النقاط: ${data.score}/100`;

    // Update active users
    document.getElementById('activeUsers').textContent = data.activeUsers;

    // Update security alerts
    document.getElementById('securityAlerts').textContent = data.alerts;

    // Update blocked IPs
    document.getElementById('blockedIPs').textContent = data.blockedIPs;

    // Update charts
    updateSecurityCharts(data);

    // Show notifications for new alerts
    if (data.newAlerts && data.newAlerts.length > 0) {
        data.newAlerts.forEach(alert => {
            showNotification(alert.message, alert.type);
        });
    }
}

/**
 * Show notification toast
 */
function showNotification(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `security-toast ${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => toast.classList.add('show'), 100);

    // Remove after 5 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, 5000);
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Password strength meter
    const passwordInput = document.getElementById('adminPassword');
    if (passwordInput) {
        passwordInput.addEventListener('input', (e) => {
            const strength = calculatePasswordStrength(e.target.value);
            updatePasswordStrengthMeter(strength);
        });
    }

    // IP whitelist/blacklist management
    const ipListTextarea = document.getElementById('ipWhitelist');
    if (ipListTextarea) {
        ipListTextarea.addEventListener('change', validateIPList);
    }

    // Security settings form submission
    const securityForms = document.querySelectorAll('.enhanced-form');
    securityForms.forEach(form => {
        form.addEventListener('submit', handleSecuritySettingsSave);
    });

    // Real-time session management
    const sessionTable = document.getElementById('activeSessions');
    if (sessionTable) {
        sessionTable.addEventListener('click', (e) => {
            if (e.target.classList.contains('terminate-session')) {
                handleSessionTermination(e.target.dataset.sessionId);
            }
        });
    }

    addSecuritySettingsListeners();

    // Initialize security level indicator
    initializeSecurityLevelIndicator();

    // Initialize password strength meter
    initializePasswordStrengthMeter();

    // Initialize IP validation
    initializeIPValidation();

    console.log('✅ Security settings initialized');
}

/**
 * Load security settings
 */
async function loadSecuritySettings() {
    try {
        const response = await fetch('../api/security-settings.php');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.settings) {
            Object.assign(securitySettings, data.settings);
            applySettingsToUI();
            updateSecurityLevel();
            console.log('📥 Security settings loaded successfully');
        } else {
            throw new Error(data.message || 'Failed to load security settings');
        }
    } catch (error) {
        console.error('❌ Error loading security settings:', error);
        showNotification('خطأ في تحميل إعدادات الأمان: ' + error.message, 'error');
    }
}

/**
 * Apply settings to UI elements
 */
function applySettingsToUI() {
    // Authentication settings
    const requireStrongPassword = document.getElementById('requireStrongPassword');
    const enableTwoFactor = document.getElementById('enableTwoFactor');
    const sessionTimeout = document.getElementById('sessionTimeout');
    const maxLoginAttempts = document.getElementById('maxLoginAttempts');

    // Access control settings
    const enableIPWhitelist = document.getElementById('enableIPWhitelist');
    const ipWhitelist = document.getElementById('ipWhitelist');
    const enableGeoBlocking = document.getElementById('enableGeoBlocking');

    // Data protection settings
    const enableDataEncryption = document.getElementById('enableDataEncryption');
    const enableBackupEncryption = document.getElementById('enableBackupEncryption');
    const backupRetention = document.getElementById('backupRetention');
    const dataRetention = document.getElementById('dataRetention');

    // Apply settings only if elements exist
    if (requireStrongPassword) requireStrongPassword.checked = securitySettings.authentication.requireStrongPassword;
    if (enableTwoFactor) enableTwoFactor.checked = securitySettings.authentication.enableTwoFactor;
    if (sessionTimeout) sessionTimeout.value = securitySettings.authentication.sessionTimeout;
    if (maxLoginAttempts) maxLoginAttempts.value = securitySettings.authentication.maxLoginAttempts;

    if (enableIPWhitelist) enableIPWhitelist.checked = securitySettings.accessControl.enableIPWhitelist;
    if (ipWhitelist) ipWhitelist.value = securitySettings.accessControl.ipWhitelist.join('\n');
    if (enableGeoBlocking) enableGeoBlocking.checked = securitySettings.accessControl.enableGeoBlocking;

    if (enableDataEncryption) enableDataEncryption.checked = securitySettings.dataProtection.enableDataEncryption;
    if (enableBackupEncryption) enableBackupEncryption.checked = securitySettings.dataProtection.enableBackupEncryption;
    if (backupRetention) backupRetention.value = securitySettings.dataProtection.backupRetention;
    if (dataRetention) dataRetention.value = securitySettings.dataProtection.dataRetention;

    // Show/hide dependent fields
    toggleIPWhitelistField();
}

/**
 * Add security settings specific event listeners
 */
function addSecuritySettingsListeners() {
    // Save button click handler
    const saveBtn = document.querySelector('.enhanced-save-btn');
    if (saveBtn) {
        saveBtn.addEventListener('click', saveSecuritySettings);
    }

    // IP Whitelist toggle
    const ipWhitelistToggle = document.getElementById('enableIPWhitelist');
    if (ipWhitelistToggle) {
        ipWhitelistToggle.addEventListener('change', toggleIPWhitelistField);
    }

    // Form change tracking
    document.querySelectorAll('input, select, textarea').forEach(element => {
        element.addEventListener('change', () => {
            const settingsState = { isDirty: true };
            // updateSaveButtonState(); // Commented out as function is not needed
            if (element.type === 'checkbox' && element.id === 'enableIPWhitelist') {
                toggleIPWhitelistField();
            }
        });
    });
}

/**
 * Toggle IP Whitelist field visibility
 */
function toggleIPWhitelistField() {
    const ipWhitelistGroup = document.getElementById('ipWhitelistGroup');
    const enableIPWhitelist = document.getElementById('enableIPWhitelist');

    if (ipWhitelistGroup && enableIPWhitelist) {
        ipWhitelistGroup.style.display = enableIPWhitelist.checked ? 'block' : 'none';
    }
}

/**
 * Save security settings
 */
async function saveSecuritySettings() {
    try {
        // Show loading state
        const saveBtn = document.querySelector('.enhanced-save-btn');
        if (!saveBtn) {
            throw new Error('Save button not found');
        }

        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        saveBtn.disabled = true;

        try {
            // Collect and validate settings
            const newSettings = collectSettingsFromUI();
            if (!validateSecuritySettings(newSettings)) {
                throw new Error('يرجى التحقق من صحة جميع الإعدادات');
            }

            // Save to API
            const response = await fetch('../api/security-settings.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'save_settings',
                    settings: newSettings
                })
            });

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || 'Failed to save settings');
            }

            // Update local state
            Object.assign(securitySettings, newSettings);

            // Update security level
            updateSecurityLevel();

            // Show success notification
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
        } finally {
            // Restore button state regardless of success/failure
            if (saveBtn) {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            }
        }
    } catch (error) {
        console.error('❌ Error saving security settings:', error);
        showNotification(error.message, 'error');
    }
}

/**
 * Collect settings from UI
 */
function collectSettingsFromUI() {
    // Get references to all required elements
    const requireStrongPassword = document.getElementById('requireStrongPassword');
    const enableTwoFactor = document.getElementById('enableTwoFactor');
    const sessionTimeout = document.getElementById('sessionTimeout');
    const maxLoginAttempts = document.getElementById('maxLoginAttempts');
    const enableIPWhitelist = document.getElementById('enableIPWhitelist');
    const ipWhitelist = document.getElementById('ipWhitelist');
    const enableGeoBlocking = document.getElementById('enableGeoBlocking');
    const enableDataEncryption = document.getElementById('enableDataEncryption');
    const enableBackupEncryption = document.getElementById('enableBackupEncryption');
    const backupRetention = document.getElementById('backupRetention');
    const dataRetention = document.getElementById('dataRetention');

    return {
        authentication: {
            requireStrongPassword: requireStrongPassword ? requireStrongPassword.checked : false,
            enableTwoFactor: enableTwoFactor ? enableTwoFactor.checked : false,
            sessionTimeout: sessionTimeout ? (parseInt(sessionTimeout.value) || 30) : 30,
            maxLoginAttempts: maxLoginAttempts ? (parseInt(maxLoginAttempts.value) || 5) : 5
        },
        accessControl: {
            enableIPWhitelist: enableIPWhitelist ? enableIPWhitelist.checked : false,
            ipWhitelist: ipWhitelist ? ipWhitelist.value.split('\n').filter(ip => ip.trim()) : [],
            enableGeoBlocking: enableGeoBlocking ? enableGeoBlocking.checked : false
        },
        dataProtection: {
            enableDataEncryption: enableDataEncryption ? enableDataEncryption.checked : false,
            enableBackupEncryption: enableBackupEncryption ? enableBackupEncryption.checked : false,
            backupRetention: backupRetention ? (parseInt(backupRetention.value) || 30) : 30,
            dataRetention: dataRetention ? (parseInt(dataRetention.value) || 365) : 365
        }
    };
}

/**
 * Validate security settings
 */
function validateSecuritySettings(settings) {
    // Session timeout validation
    if (settings.authentication.sessionTimeout < 5 || settings.authentication.sessionTimeout > 1440) {
        showNotification('يجب أن تكون مدة الجلسة بين 5 و 1440 دقيقة', 'error');
        return false;
    }

    // Login attempts validation
    if (settings.authentication.maxLoginAttempts < 3 || settings.authentication.maxLoginAttempts > 10) {
        showNotification('يجب أن يكون عدد محاولات تسجيل الدخول بين 3 و 10', 'error');
        return false;
    }

    // IP Whitelist validation
    if (settings.accessControl.enableIPWhitelist && settings.accessControl.ipWhitelist.length === 0) {
        showNotification('يجب إضافة عنوان IP واحد على الأقل عند تفعيل القائمة البيضاء', 'error');
        return false;
    }

    // Validate IP format if whitelist is enabled
    if (settings.accessControl.enableIPWhitelist) {
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const invalidIPs = settings.accessControl.ipWhitelist.filter(ip => !ipRegex.test(ip.trim()));

        if (invalidIPs.length > 0) {
            showNotification(`عناوين IP غير صالحة: ${invalidIPs.join(', ')}`, 'error');
            return false;
        }
    }

    return true;
}

/**
 * Initialize security level indicator
 */
function initializeSecurityLevelIndicator() {
    const securityLevelElement = document.getElementById('securityLevel');
    if (securityLevelElement) {
        updateSecurityLevel();
    }
}

/**
 * Update security level based on current settings
 */
function updateSecurityLevel() {
    const securityScore = calculateSecurityScore();
    const securityLevelElement = document.getElementById('securityLevel');

    if (securityLevelElement) {
        let level, color;

        if (securityScore >= 80) {
            level = 'مرتفع';
            color = '#28a745';
        } else if (securityScore >= 60) {
            level = 'متوسط';
            color = '#ffc107';
        } else {
            level = 'منخفض';
            color = '#dc3545';
        }

        securityLevelElement.textContent = level;
        securityLevelElement.style.color = color;
    }
}

/**
 * Calculate security score based on enabled settings
 */
function calculateSecurityScore() {
    let score = 0;
    const settings = securitySettings;

    // Authentication settings (40 points)
    if (settings.authentication.requireStrongPassword) score += 15;
    if (settings.authentication.enableTwoFactor) score += 15;
    if (settings.authentication.maxLoginAttempts <= 5) score += 5;
    if (settings.authentication.sessionTimeout <= 30) score += 5;

    // Access Control (30 points)
    if (settings.accessControl.enableIPWhitelist) score += 15;
    if (settings.accessControl.enableGeoBlocking) score += 15;

    // Data Protection (30 points)
    if (settings.dataProtection.enableDataEncryption) score += 15;
    if (settings.dataProtection.enableBackupEncryption) score += 10;
    if (settings.dataProtection.backupRetention <= 30) score += 5;

    return score;
}

/**
 * Switch between security tabs
 */
function switchSecurityTab(tabName, clickedElement) {
    // Hide all tabs
    document.querySelectorAll('.tab-pane').forEach(tab => {
        tab.style.display = 'none';
    });

    // Remove active class from all headers
    document.querySelectorAll('.tab-header').forEach(header => {
        header.classList.remove('active');
    });

    // Show selected tab
    const selectedTab = document.getElementById(tabName + 'Tab');
    if (selectedTab) {
        selectedTab.style.display = 'block';
    }

    // Add active class to clicked header
    if (clickedElement) {
        clickedElement.classList.add('active');
    }

    // Load tab content
    switch(tabName) {
        case 'authentication':
            loadAuthenticationTab();
            break;
        case 'access':
            loadAccessControlTab();
            break;
        case 'monitoring':
            loadMonitoringTab();
            break;
        case 'backup':
            loadBackupTab();
            break;
    }
}

/**
 * Load authentication tab content
 */
function loadAuthenticationTab() {
    const tab = document.getElementById('authenticationTab');
    if (!tab) return;

    tab.innerHTML = `
        <div class="settings-section">
            <h3 style="color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-key" style="color: #667eea;"></i>
                إعدادات المصادقة
            </h3>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div class="setting-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6;">
                    <h4 style="margin: 0 0 15px 0; color: #495057;">كلمات المرور</h4>

                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="requireStrongPassword" checked style="transform: scale(1.2);">
                            <span>طلب كلمات مرور قوية</span>
                        </label>
                        <small style="color: #666; margin-right: 30px; display: block; margin-top: 5px;">
                            يجب أن تحتوي على 8 أحرف على الأقل مع أرقام ورموز
                        </small>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الحد الأدنى لطول كلمة المرور:</label>
                        <input type="number" id="passwordMinLength" value="8" min="6" max="32" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">انتهاء صلاحية كلمة المرور (أيام):</label>
                        <input type="number" id="passwordExpiry" value="90" min="30" max="365" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                </div>

                <div class="setting-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6;">
                    <h4 style="margin: 0 0 15px 0; color: #495057;">الجلسات</h4>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">مهلة انتهاء الجلسة (دقيقة):</label>
                        <input type="number" id="sessionTimeout" value="30" min="5" max="1440" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="autoLogout" checked style="transform: scale(1.2);">
                            <span>تسجيل الخروج التلقائي</span>
                        </label>
                        <small style="color: #666; margin-right: 30px; display: block; margin-top: 5px;">
                            تسجيل خروج تلقائي عند عدم النشاط
                        </small>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="rememberMe" style="transform: scale(1.2);">
                            <span>السماح بـ "تذكرني"</span>
                        </label>
                    </div>
                </div>

                <div class="setting-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6;">
                    <h4 style="margin: 0 0 15px 0; color: #495057;">المصادقة الثنائية</h4>

                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="enableTwoFactor" style="transform: scale(1.2);">
                            <span>تفعيل المصادقة الثنائية</span>
                        </label>
                        <small style="color: #666; margin-right: 30px; display: block; margin-top: 5px;">
                            طبقة حماية إضافية للمديرين
                        </small>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">طريقة المصادقة الثنائية:</label>
                        <select id="twoFactorMethod" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            <option value="email">البريد الإلكتروني</option>
                            <option value="sms">رسائل SMS</option>
                            <option value="app">تطبيق المصادقة</option>
                        </select>
                    </div>
                </div>

                <div class="setting-card" style="background: #f8f9fa; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6;">
                    <h4 style="margin: 0 0 15px 0; color: #495057;">محاولات تسجيل الدخول</h4>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الحد الأقصى للمحاولات الفاشلة:</label>
                        <input type="number" id="maxLoginAttempts" value="5" min="3" max="10" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">مدة الحظر (دقيقة):</label>
                        <input type="number" id="lockoutDuration" value="15" min="5" max="60" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" id="notifyFailedLogin" checked style="transform: scale(1.2);">
                            <span>إشعار عند فشل تسجيل الدخول</span>
                        </label>
                    </div>
                </div>
            </div>

            <div style="margin-top: 30px; text-align: center;">
                <button onclick="saveAuthenticationSettings()" style="padding: 12px 30px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 10px;">
                    <i class="fas fa-save"></i> حفظ إعدادات المصادقة
                </button>
                <button onclick="resetAuthenticationSettings()" style="padding: 12px 30px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-weight: bold; margin: 0 10px;">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
            </div>
        </div>
    `;
}

/**
 * Load security data for dashboard
 */
function loadSecurityData() {
    try {
        // Update security level
        const securityLevel = document.getElementById('securityLevel');
        const securityScore = document.getElementById('securityScore');

        if (!securityLevel || !securityScore) {
            throw new Error('Security level or score elements not found');
        }

        // Calculate security score based on current settings
        const score = calculateSecurityScore();
        securityScore.textContent = `النقاط: ${score}/100`;

        let levelText, levelColor;
        if (score >= 80) {
            levelText = 'عالي';
            levelColor = '#28a745';
        } else if (score >= 60) {
            levelText = 'متوسط';
            levelColor = '#ffc107';
        } else {
            levelText = 'منخفض';
            levelColor = '#dc3545';
        }

        securityLevel.textContent = levelText;
        securityLevel.style.color = levelColor;

    } catch (error) {
        console.error('Error loading security data:', error);
        showNotification('خطأ في تحميل بيانات الأمان: ' + error.message, 'error');
    }
}

/**
 * Save authentication settings
 */
async function saveAuthenticationSettings() {
    try {
        // Get all required elements
        const elements = {
            requireStrongPassword: document.getElementById('requireStrongPassword'),
            passwordMinLength: document.getElementById('passwordMinLength'),
            passwordExpiry: document.getElementById('passwordExpiry'),
            sessionTimeout: document.getElementById('sessionTimeout'),
            autoLogout: document.getElementById('autoLogout'),
            rememberMe: document.getElementById('rememberMe'),
            enableTwoFactor: document.getElementById('enableTwoFactor'),
            twoFactorMethod: document.getElementById('twoFactorMethod'),
            maxLoginAttempts: document.getElementById('maxLoginAttempts'),
            lockoutDuration: document.getElementById('lockoutDuration'),
            notifyFailedLogin: document.getElementById('notifyFailedLogin')
        };

        // Check if all elements exist
        for (const [key, element] of Object.entries(elements)) {
            if (!element) {
                throw new Error(`Element not found: ${key}`);
            }
        }

        const settings = {
            requireStrongPassword: elements.requireStrongPassword.checked,
            passwordMinLength: parseInt(elements.passwordMinLength.value) || 8,
            passwordExpiry: parseInt(elements.passwordExpiry.value) || 90,
            sessionTimeout: parseInt(elements.sessionTimeout.value) || 30,
            autoLogout: elements.autoLogout.checked,
            rememberMe: elements.rememberMe.checked,
            enableTwoFactor: elements.enableTwoFactor.checked,
            twoFactorMethod: elements.twoFactorMethod.value,
            maxLoginAttempts: parseInt(elements.maxLoginAttempts.value) || 5,
            lockoutDuration: parseInt(elements.lockoutDuration.value) || 15,
            notifyFailedLogin: elements.notifyFailedLogin.checked
        };

        // Validate settings
        if (settings.passwordMinLength < 6 || settings.passwordMinLength > 32) {
            showNotification('طول كلمة المرور يجب أن يكون بين 6 و 32 حرف', 'error');
            return;
        }

        if (settings.sessionTimeout < 5 || settings.sessionTimeout > 1440) {
            showNotification('مهلة الجلسة يجب أن تكون بين 5 و 1440 دقيقة', 'error');
            return;
        }

        // Save to server
        const response = await fetch('../php/api/security-settings.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'save_authentication',
                settings: settings
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification('تم حفظ إعدادات المصادقة بنجاح!', 'success');
            loadSecurityData(); // Refresh security score
        } else {
            throw new Error(result.message || 'خطأ غير معروف');
        }

    } catch (error) {
        console.error('Error saving authentication settings:', error);
        showNotification('خطأ في حفظ الإعدادات: ' + error.message, 'error');
    }
}

/**
 * Run security audit
 */
function runSecurityAudit() {
    alert('فحص الأمان - قيد التطوير\nسيتم إضافة هذه الميزة قريباً');
}

/**
 * Export security settings
 */
function exportSecuritySettings() {
    alert('تصدير إعدادات الأمان - قيد التطوير\nسيتم إضافة هذه الميزة قريباً');
}

// Placeholder functions for other tabs
function loadAccessControlTab() {
    const tab = document.getElementById('accessTab');
    if (!tab) return;
    tab.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><i class="fas fa-lock" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i><h3>إعدادات التحكم في الوصول</h3><p>قيد التطوير...</p></div>';
}

function loadMonitoringTab() {
    const tab = document.getElementById('monitoringTab');
    if (!tab) return;
    tab.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><i class="fas fa-eye" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i><h3>إعدادات المراقبة</h3><p>قيد التطوير...</p></div>';
}

function loadBackupTab() {
    const tab = document.getElementById('backupTab');
    if (!tab) return;
    tab.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><i class="fas fa-database" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i><h3>إعدادات النسخ الاحتياطي</h3><p>قيد التطوير...</p></div>';
}

function resetAuthenticationSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين إعدادات المصادقة إلى القيم الافتراضية؟')) {
        try {
            // Get all required elements
            const elements = {
                requireStrongPassword: document.getElementById('requireStrongPassword'),
                passwordMinLength: document.getElementById('passwordMinLength'),
                passwordExpiry: document.getElementById('passwordExpiry'),
                sessionTimeout: document.getElementById('sessionTimeout'),
                autoLogout: document.getElementById('autoLogout'),
                rememberMe: document.getElementById('rememberMe'),
                enableTwoFactor: document.getElementById('enableTwoFactor'),
                twoFactorMethod: document.getElementById('twoFactorMethod'),
                maxLoginAttempts: document.getElementById('maxLoginAttempts'),
                lockoutDuration: document.getElementById('lockoutDuration'),
                notifyFailedLogin: document.getElementById('notifyFailedLogin')
            };

            // Check if all elements exist
            for (const [key, element] of Object.entries(elements)) {
                if (!element) {
                    throw new Error(`Element not found: ${key}`);
                }
            }

            // Reset to default values
            elements.requireStrongPassword.checked = true;
            elements.passwordMinLength.value = 8;
            elements.passwordExpiry.value = 90;
            elements.sessionTimeout.value = 30;
            elements.autoLogout.checked = true;
            elements.rememberMe.checked = false;
            elements.enableTwoFactor.checked = false;
            elements.twoFactorMethod.value = 'email';
            elements.maxLoginAttempts.value = 5;
            elements.lockoutDuration.value = 15;
            elements.notifyFailedLogin.checked = true;

            showNotification('تم إعادة تعيين الإعدادات إلى القيم الافتراضية', 'success');
        } catch (error) {
            console.error('Error resetting authentication settings:', error);
            showNotification('خطأ في إعادة تعيين الإعدادات: ' + error.message, 'error');
        }
    }
}

/**
 * Initialize Security Dashboard
 * تهيئة لوحة معلومات الأمان
 */
function initializeSecurityDashboard() {
    loadSecurityDashboard();

    // Auto-refresh dashboard every 30 seconds
    setInterval(loadSecurityDashboard, 30000);
}

/**
 * Load Security Dashboard Data
 * تحميل بيانات لوحة معلومات الأمان
 */
async function loadSecurityDashboard() {
    try {
        const response = await fetch('php/api/security-settings.php?action=dashboard');
        const result = await response.json();

        if (result.success) {
            updateDashboardUI(result.data);
        } else {
            console.error('Failed to load dashboard:', result.message);
        }
    } catch (error) {
        console.error('Error loading security dashboard:', error);
    }
}

/**
 * Update Dashboard UI
 * تحديث واجهة لوحة المعلومات
 */
function updateDashboardUI(data) {
    // Update security metrics
    const metrics = data.metrics || {};

    // Update security level indicator
    const securityLevelElement = document.getElementById('securityLevel');
    if (securityLevelElement) {
        securityLevelElement.textContent = data.security_level || 'غير محدد';
        securityLevelElement.className = `security-level ${getSecurityLevelClass(data.security_level)}`;
    }

    // Update metrics cards
    updateMetricCard('totalEvents', metrics.total_events || 0);
    updateMetricCard('highSeverity', metrics.high_severity || 0);
    updateMetricCard('uniqueIps', metrics.unique_ips || 0);
    updateMetricCard('blockedIps', metrics.blocked_ips || 0);
    updateMetricCard('activeSessions', metrics.active_sessions || 0);

    // Update recent events
    updateRecentEvents(data.recent_events || []);
}

/**
 * Get security level CSS class
 */
function getSecurityLevelClass(level) {
    switch (level) {
        case 'عالي': return 'high';
        case 'متوسط': return 'medium';
        case 'منخفض': return 'low';
        default: return 'unknown';
    }
}

/**
 * Update metric card
 */
function updateMetricCard(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

/**
 * Update recent events list
 */
function updateRecentEvents(events) {
    const container = document.getElementById('recentEvents');
    if (!container) return;

    container.innerHTML = events.map(event => `
        <div class="event-item ${event.severity}">
            <div class="event-icon">
                <i class="fas ${getEventIcon(event.action)}"></i>
            </div>
            <div class="event-details">
                <div class="event-action">${event.action}</div>
                <div class="event-description">${event.description}</div>
                <div class="event-time">${formatDateTime(event.created_at)}</div>
            </div>
            <div class="event-status ${event.status}">
                ${getStatusText(event.status)}
            </div>
        </div>
    `).join('');
}

/**
 * Get event icon based on action
 */
function getEventIcon(action) {
    const icons = {
        'admin_login': 'fa-sign-in-alt',
        'failed_login': 'fa-exclamation-triangle',
        'password_change': 'fa-key',
        'security_settings_update': 'fa-cog',
        'suspicious_activity': 'fa-shield-alt'
    };
    return icons[action] || 'fa-info-circle';
}

/**
 * Get status text in Arabic
 */
function getStatusText(status) {
    const statusMap = {
        'success': 'نجح',
        'failed': 'فشل',
        'blocked': 'محظور'
    };
    return statusMap[status] || status;
}

/**
 * Initialize Password Policy
 * تهيئة سياسة كلمات المرور
 */
function initializePasswordPolicy() {
    loadPasswordPolicy();

    // Add event listeners for password policy form
    const form = document.getElementById('passwordPolicyForm');
    if (form) {
        form.addEventListener('submit', savePasswordPolicy);
    }

    // Add real-time password strength checker
    const passwordInput = document.getElementById('testPassword');
    if (passwordInput) {
        passwordInput.addEventListener('input', checkPasswordStrength);
    }
}

/**
 * Load Password Policy Settings
 */
async function loadPasswordPolicy() {
    try {
        const response = await fetch('php/api/security-settings.php?action=password_policy');
        const result = await response.json();

        if (result.success) {
            updatePasswordPolicyUI(result.data);
        }
    } catch (error) {
        console.error('Error loading password policy:', error);
    }
}

/**
 * Update Password Policy UI
 */
function updatePasswordPolicyUI(policy) {
    const elements = {
        minLength: document.getElementById('minLength'),
        requireUppercase: document.getElementById('requireUppercase'),
        requireLowercase: document.getElementById('requireLowercase'),
        requireNumbers: document.getElementById('requireNumbers'),
        requireSymbols: document.getElementById('requireSymbols'),
        maxAgeDays: document.getElementById('maxAgeDays'),
        historyCount: document.getElementById('historyCount'),
        complexityScore: document.getElementById('complexityScore')
    };

    Object.keys(elements).forEach(key => {
        const element = elements[key];
        if (element && policy[key] !== undefined) {
            if (element.type === 'checkbox') {
                element.checked = policy[key];
            } else {
                element.value = policy[key];
            }
        }
    });
}

/**
 * Save Password Policy
 */
async function savePasswordPolicy(event) {
    event.preventDefault();

    try {
        const formData = new FormData(event.target);
        const policy = {
            min_length: parseInt(formData.get('minLength')),
            require_uppercase: formData.get('requireUppercase') === 'on',
            require_lowercase: formData.get('requireLowercase') === 'on',
            require_numbers: formData.get('requireNumbers') === 'on',
            require_symbols: formData.get('requireSymbols') === 'on',
            max_age_days: parseInt(formData.get('maxAgeDays')),
            history_count: parseInt(formData.get('historyCount')),
            complexity_score: parseInt(formData.get('complexityScore'))
        };

        const response = await fetch('php/api/security-settings.php?action=update_password_policy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(policy)
        });

        const result = await response.json();

        if (result.success) {
            showNotification('تم حفظ سياسة كلمات المرور بنجاح', 'success');
        } else {
            showNotification('فشل في حفظ سياسة كلمات المرور: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error saving password policy:', error);
        showNotification('خطأ في حفظ سياسة كلمات المرور', 'error');
    }
}

/**
 * Check Password Strength
 * فحص قوة كلمة المرور
 */
function checkPasswordStrength(event) {
    const password = event.target.value;
    const strengthIndicator = document.getElementById('passwordStrength');

    if (!strengthIndicator) return;

    const strength = calculatePasswordStrength(password);

    strengthIndicator.className = `password-strength ${strength.level}`;
    strengthIndicator.innerHTML = `
        <div class="strength-bar">
            <div class="strength-fill" style="width: ${strength.score}%"></div>
        </div>
        <div class="strength-text">${strength.text}</div>
    `;
}

/**
 * Calculate Password Strength
 */
function calculatePasswordStrength(password) {
    let score = 0;
    let feedback = [];

    // Length check
    if (password.length >= 8) score += 20;
    else feedback.push('يجب أن تكون 8 أحرف على الأقل');

    // Uppercase check
    if (/[A-Z]/.test(password)) score += 20;
    else feedback.push('يجب أن تحتوي على أحرف كبيرة');

    // Lowercase check
    if (/[a-z]/.test(password)) score += 20;
    else feedback.push('يجب أن تحتوي على أحرف صغيرة');

    // Numbers check
    if (/\d/.test(password)) score += 20;
    else feedback.push('يجب أن تحتوي على أرقام');

    // Special characters check
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 20;
    else feedback.push('يجب أن تحتوي على رموز خاصة');

    let level, text;
    if (score >= 80) {
        level = 'strong';
        text = 'قوية';
    } else if (score >= 60) {
        level = 'medium';
        text = 'متوسطة';
    } else if (score >= 40) {
        level = 'weak';
        text = 'ضعيفة';
    } else {
        level = 'very-weak';
        text = 'ضعيفة جداً';
    }

    return { score, level, text, feedback };
}

/**
 * Initialize Threat Detection
 * تهيئة كشف التهديدات
 */
function initializeThreatDetection() {
    loadThreatDetectionSettings();

    // Add event listeners for threat detection controls
    const enableButton = document.getElementById('enableThreatDetection');
    if (enableButton) {
        enableButton.addEventListener('change', toggleThreatDetection);
    }
}

/**
 * Load Threat Detection Settings
 */
async function loadThreatDetectionSettings() {
    try {
        const response = await fetch('php/api/security-settings.php?action=threat_detection');
        const result = await response.json();

        if (result.success) {
            updateThreatDetectionUI(result.data);
        }
    } catch (error) {
        console.error('Error loading threat detection settings:', error);
    }
}

/**
 * Update Threat Detection UI
 */
function updateThreatDetectionUI(settings) {
    const elements = {
        enabled: document.getElementById('enableThreatDetection'),
        maxLoginAttempts: document.getElementById('maxLoginAttempts'),
        lockoutDuration: document.getElementById('lockoutDuration'),
        suspiciousThreshold: document.getElementById('suspiciousThreshold'),
        autoBlock: document.getElementById('autoBlockEnabled'),
        emailAlerts: document.getElementById('emailAlerts'),
        smsAlerts: document.getElementById('smsAlerts')
    };

    Object.keys(elements).forEach(key => {
        const element = elements[key];
        if (element && settings[key] !== undefined) {
            if (element.type === 'checkbox') {
                element.checked = settings[key];
            } else {
                element.value = settings[key];
            }
        }
    });
}

/**
 * Toggle Threat Detection
 */
function toggleThreatDetection(event) {
    const isEnabled = event.target.checked;
    const threatDetectionPanel = document.getElementById('threatDetectionPanel');

    if (threatDetectionPanel) {
        threatDetectionPanel.style.display = isEnabled ? 'block' : 'none';
    }

    // Save the setting
    saveThreatDetectionSetting('threat_detection_enabled', isEnabled);
}

/**
 * Save Threat Detection Setting
 */
async function saveThreatDetectionSetting(key, value) {
    try {
        const response = await fetch('php/api/security-settings.php?action=update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ [key]: value })
        });

        const result = await response.json();

        if (!result.success) {
            console.error('Failed to save threat detection setting:', result.message);
        }
    } catch (error) {
        console.error('Error saving threat detection setting:', error);
    }
}

// Make functions globally available
window.initializeSecuritySettings = initializeSecuritySettings;
window.loadSecuritySettings = loadSecuritySettings;
window.loadSecuritySettingsContent = loadSecuritySettingsContent;
window.showSecuritySettings = showSecuritySettings;
window.switchSecurityTab = switchSecurityTab;
window.saveAuthenticationSettings = saveAuthenticationSettings;
window.resetAuthenticationSettings = resetAuthenticationSettings;
window.runSecurityAudit = runSecurityAudit;
window.exportSecuritySettings = exportSecuritySettings;
window.saveSecuritySettings = saveSecuritySettings;
window.calculateSecurityScore = calculateSecurityScore;
window.initializeSecurityDashboard = initializeSecurityDashboard;
window.loadSecurityDashboard = loadSecurityDashboard;
window.initializePasswordPolicy = initializePasswordPolicy;
window.savePasswordPolicy = savePasswordPolicy;
window.checkPasswordStrength = checkPasswordStrength;
window.initializeThreatDetection = initializeThreatDetection;
window.toggleThreatDetection = toggleThreatDetection;

/**
 * Initialize Security WebSocket (Optional)
 * تهيئة WebSocket للأمان (اختياري)
 */
function initializeSecurityWebSocket() {
    try {
        const ws = new WebSocket('ws://localhost:8000/security-ws');

        ws.onopen = function() {
            console.log('🔗 Security WebSocket connected');
        };

        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            handleSecurityUpdate(data);
        };

        ws.onerror = function(error) {
            console.log('خطأ في اتصال WebSocket:', error);
        };

        ws.onclose = function() {
            console.log('🔌 Security WebSocket disconnected');
        };
    } catch (error) {
        console.log('WebSocket not supported or server not available');
    }
}

/**
 * Handle Security Update from WebSocket
 */
function handleSecurityUpdate(data) {
    if (data.type === 'security_alert') {
        showNotification(data.message, 'warning');
    } else if (data.type === 'dashboard_update') {
        loadSecurityDashboard();
    }
}

/**
 * Update Security Score
 * تحديث نقاط الأمان
 */
function updateSecurityScore(score) {
    const scoreElement = document.getElementById('securityScore');
    if (scoreElement) {
        scoreElement.textContent = score + '%';

        // Update score color based on value
        let scoreClass = 'low';
        if (score >= 80) scoreClass = 'high';
        else if (score >= 60) scoreClass = 'medium';

        scoreElement.className = `security-score ${scoreClass}`;
    }

    // Update progress bar if exists
    const progressBar = document.getElementById('securityProgressBar');
    if (progressBar) {
        progressBar.style.width = score + '%';
        progressBar.className = `progress-bar ${scoreClass}`;
    }
}

/**
 * Update Security Recommendations
 * تحديث توصيات الأمان
 */
function updateSecurityRecommendations(recommendations) {
    const container = document.getElementById('securityRecommendations');
    if (!container || !recommendations) return;

    container.innerHTML = recommendations.map(rec => `
        <div class="recommendation-item ${rec.priority}">
            <div class="rec-icon">
                <i class="fas ${rec.icon || 'fa-info-circle'}"></i>
            </div>
            <div class="rec-content">
                <h4>${rec.title}</h4>
                <p>${rec.description}</p>
                ${rec.action ? `<button class="btn btn-sm btn-primary" onclick="${rec.action}">${rec.actionText}</button>` : ''}
            </div>
        </div>
    `).join('');
}

/**
 * Update Security Charts
 * تحديث مخططات الأمان
 */
function updateSecurityCharts(data) {
    // Update threat level chart
    updateThreatLevelChart(data.threatLevels || {});

    // Update activity timeline
    updateActivityTimeline(data.recentActivity || []);

    // Update geographic distribution
    updateGeographicChart(data.geoData || {});
}

/**
 * Update Threat Level Chart
 */
function updateThreatLevelChart(threatLevels) {
    const chartContainer = document.getElementById('threatLevelChart');
    if (!chartContainer) return;

    const total = Object.values(threatLevels).reduce((sum, val) => sum + val, 0);

    chartContainer.innerHTML = Object.entries(threatLevels).map(([level, count]) => {
        const percentage = total > 0 ? (count / total * 100).toFixed(1) : 0;
        return `
            <div class="threat-level-item">
                <div class="threat-level-bar">
                    <div class="threat-level-fill ${level}" style="width: ${percentage}%"></div>
                </div>
                <div class="threat-level-info">
                    <span class="level-name">${getThreatLevelName(level)}</span>
                    <span class="level-count">${count}</span>
                </div>
            </div>
        `;
    }).join('');
}

/**
 * Update Activity Timeline
 */
function updateActivityTimeline(activities) {
    const timelineContainer = document.getElementById('activityTimeline');
    if (!timelineContainer) return;

    timelineContainer.innerHTML = activities.slice(0, 10).map(activity => `
        <div class="timeline-item">
            <div class="timeline-time">${formatTime(activity.timestamp)}</div>
            <div class="timeline-content">
                <div class="timeline-title">${activity.action}</div>
                <div class="timeline-description">${activity.description}</div>
            </div>
            <div class="timeline-status ${activity.status}">${activity.status}</div>
        </div>
    `).join('');
}

/**
 * Update Geographic Chart
 */
function updateGeographicChart(geoData) {
    const geoContainer = document.getElementById('geographicChart');
    if (!geoContainer) return;

    const sortedCountries = Object.entries(geoData)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);

    geoContainer.innerHTML = sortedCountries.map(([country, count]) => `
        <div class="geo-item">
            <div class="geo-country">${country}</div>
            <div class="geo-bar">
                <div class="geo-fill" style="width: ${(count / Math.max(...Object.values(geoData))) * 100}%"></div>
            </div>
            <div class="geo-count">${count}</div>
        </div>
    `).join('');
}

/**
 * Get Threat Level Name in Arabic
 */
function getThreatLevelName(level) {
    const names = {
        'low': 'منخفض',
        'medium': 'متوسط',
        'high': 'عالي',
        'critical': 'حرج'
    };
    return names[level] || level;
}

/**
 * Format Time for Timeline
 */
function formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString('ar-DZ', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Handle Security Settings Save
 * معالجة حفظ إعدادات الأمان
 */
async function handleSecuritySettingsSave(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const settings = {};

    // Convert form data to settings object
    for (let [key, value] of formData.entries()) {
        settings[key] = value;
    }

    try {
        const response = await fetch('php/api/security-settings.php?action=update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });

        const result = await response.json();

        if (result.success) {
            showNotification('تم حفظ إعدادات الأمان بنجاح', 'success');

            // Refresh dashboard data
            loadSecurityDashboard();
        } else {
            showNotification('فشل في حفظ إعدادات الأمان: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error saving security settings:', error);
        showNotification('خطأ في حفظ إعدادات الأمان', 'error');
    }
}

// Export new functions
window.initializeSecurityWebSocket = initializeSecurityWebSocket;
window.updateSecurityScore = updateSecurityScore;
window.updateSecurityRecommendations = updateSecurityRecommendations;
window.updateSecurityCharts = updateSecurityCharts;
window.handleSecuritySettingsSave = handleSecuritySettingsSave;

console.log('✅ Security settings module loaded successfully');
