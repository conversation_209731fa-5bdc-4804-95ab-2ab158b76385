/**
 * Dashboard Coordination System
 * Ensures all JavaScript modules work together without conflicts
 */

(function() {
    'use strict';
    
    console.log('🔧 Dashboard Coordination System loading...');
    
    // Global coordination state
    window.dashboardCoordination = {
        isEnhancedDashboard: false,
        protectionActive: false,
        loadingInProgress: false,
        modules: {
            dashboardCleanup: false,
            multiUserInterface: false,
            adminSectionsFix: false,
            themeManager: false
        }
    };
    
    // Module registration system
    function registerModule(moduleName, moduleInstance) {
        console.log(`📦 Registering module: ${moduleName}`);
        window.dashboardCoordination.modules[moduleName] = moduleInstance;
        
        // Check if enhanced dashboard exists when module registers
        checkEnhancedDashboard();
    }
    
    // Check for enhanced dashboard
    function checkEnhancedDashboard() {
        const dashboard = document.getElementById('dashboard');
        if (dashboard) {
            const enhancedDashboard = dashboard.querySelector('.dashboard-header');
            if (enhancedDashboard) {
                window.dashboardCoordination.isEnhancedDashboard = true;
                console.log('✅ Enhanced dashboard detected by coordination system');
                
                // Notify all modules about enhanced dashboard
                notifyModulesAboutEnhancedDashboard();
            }
        }
    }
    
    // Notify modules about enhanced dashboard
    function notifyModulesAboutEnhancedDashboard() {
        // Disable dashboard cleanup
        if (window.dashboardCleanup) {
            window.dashboardCleanup.disabled = true;
            console.log('🛡️ Dashboard cleanup disabled');
        }
        
        // Disable multi-user dashboard loading
        if (window.loadEnhancedDashboard) {
            const originalFunction = window.loadEnhancedDashboard;
            window.loadEnhancedDashboard = function() {
                console.log('🛡️ Multi-user dashboard loading blocked by coordination system');
                return false;
            };
        }
        
        // Override loadDashboard function
        if (window.loadDashboard) {
            const originalLoadDashboard = window.loadDashboard;
            window.loadDashboard = function() {
                console.log('🛡️ loadDashboard() blocked by coordination system');
                return false;
            };
        }
    }
    
    // Safe navigation function
    function safeNavigateToSection(sectionId) {
        console.log(`🧭 Safe navigation to section: ${sectionId}`);
        
        // If navigating to dashboard and enhanced dashboard exists, preserve it
        if (sectionId === 'dashboard' && window.dashboardCoordination.isEnhancedDashboard) {
            console.log('🛡️ Preserving enhanced dashboard during navigation');
            
            // Just show the section without loading content
            const section = document.getElementById(sectionId);
            if (section) {
                // Hide all sections
                const allSections = document.querySelectorAll('.content-section');
                allSections.forEach(s => {
                    s.classList.remove('active');
                    s.style.display = 'none';
                });
                
                // Show target section
                section.classList.add('active');
                section.style.display = 'block';
                
                // Update navigation
                updateNavigation(sectionId);
                
                return true;
            }
        }
        
        // For other sections, use normal navigation
        if (typeof showAdminSection === 'function') {
            return showAdminSection(sectionId);
        }
        
        return false;
    }
    
    // Update navigation state
    function updateNavigation(sectionId) {
        // Remove active class from all nav items
        const navItems = document.querySelectorAll('.admin-nav ul li');
        navItems.forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to current nav item
        const activeNavItem = document.querySelector(`[data-section="${sectionId}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }
    }
    
    // Initialize coordination system
    function initializeCoordination() {
        console.log('🚀 Initializing dashboard coordination system...');
        
        // Check for enhanced dashboard immediately
        checkEnhancedDashboard();
        
        // Set up periodic checks
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            if (!window.dashboardCoordination.isEnhancedDashboard) {
                checkEnhancedDashboard();
            }
            
            checkCount++;
            if (checkCount > 20 || window.dashboardCoordination.isEnhancedDashboard) {
                clearInterval(checkInterval);
            }
        }, 500);
        
        // Override global navigation functions
        window.safeNavigateToSection = safeNavigateToSection;
        
        console.log('✅ Dashboard coordination system initialized');
    }
    
    // Auto-initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCoordination);
    } else {
        initializeCoordination();
    }
    
    // Export coordination functions
    window.dashboardCoordination.register = registerModule;
    window.dashboardCoordination.checkEnhanced = checkEnhancedDashboard;
    window.dashboardCoordination.safeNavigate = safeNavigateToSection;
    
    console.log('✅ Dashboard Coordination System loaded successfully');
    
})();
