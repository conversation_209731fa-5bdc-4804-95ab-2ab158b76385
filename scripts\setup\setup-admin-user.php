<?php
/**
 * Setup Admin User and Tables
 * Creates the admins table and default admin user if they don't exist
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد مستخدم الإدارة</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .fix-button:hover {
            background: #0056b3;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .credentials {
            background: #fff3cd;
            border: 2px solid #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .credentials strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إعداد مستخدم الإدارة</h1>
        <p>هذا السكريبت ينشئ جدول المديرين ومستخدم إدارة افتراضي إذا لم يكونا موجودين.</p>

        <?php
        try {
            // Load configuration
            require_once '../php/config.php';
            
            echo '<div class="result info">📋 تم تحميل التكوين بنجاح</div>';
            
            // Get database connection
            $pdo = getPDOConnection();
            echo '<div class="result pass">✅ اتصال قاعدة البيانات ناجح</div>';
            
            // Check if admins table exists
            $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
            if ($stmt->rowCount() > 0) {
                echo '<div class="result pass">✅ جدول admins موجود</div>';
            } else {
                echo '<div class="result warning">⚠️ جدول admins غير موجود - جاري الإنشاء...</div>';
                
                // Create admins table
                $createAdminsTable = "
                CREATE TABLE IF NOT EXISTS `admins` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY,
                    `nom_utilisateur` VARCHAR(50) NOT NULL UNIQUE,
                    `mot_de_passe` VARCHAR(255) NOT NULL,
                    `email` VARCHAR(100),
                    `nom_complet` VARCHAR(100),
                    `role` ENUM('admin', 'super_admin') DEFAULT 'admin',
                    `actif` TINYINT(1) DEFAULT 1,
                    `derniere_connexion` TIMESTAMP NULL,
                    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_nom_utilisateur (nom_utilisateur),
                    INDEX idx_actif (actif)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                $pdo->exec($createAdminsTable);
                echo '<div class="result pass">✅ تم إنشاء جدول admins بنجاح</div>';
            }
            
            // Check if there are any admin users
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM admins");
            $adminCount = $stmt->fetch()['count'];
            
            if ($adminCount > 0) {
                echo '<div class="result pass">✅ يوجد ' . $adminCount . ' مستخدم إدارة</div>';
                
                // Show existing admins
                $stmt = $pdo->query("SELECT nom_utilisateur, email, nom_complet, role, actif FROM admins");
                $admins = $stmt->fetchAll();
                
                echo '<div class="result info">📋 المستخدمين الموجودين:</div>';
                echo '<ul>';
                foreach ($admins as $admin) {
                    $status = $admin['actif'] ? 'نشط' : 'غير نشط';
                    echo '<li><strong>' . htmlspecialchars($admin['nom_utilisateur']) . '</strong> (' . htmlspecialchars($admin['role']) . ') - ' . $status . '</li>';
                }
                echo '</ul>';
                
            } else {
                echo '<div class="result warning">⚠️ لا يوجد مستخدمين إدارة - جاري إنشاء مستخدم افتراضي...</div>';
                
                // Create default admin user
                $defaultUsername = 'admin';
                $defaultPassword = 'admin123';
                $hashedPassword = password_hash($defaultPassword, PASSWORD_DEFAULT);
                
                $insertAdmin = $pdo->prepare("
                    INSERT INTO admins (nom_utilisateur, mot_de_passe, email, nom_complet, role, actif) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $insertAdmin->execute([
                    $defaultUsername,
                    $hashedPassword,
                    '<EMAIL>',
                    'مدير النظام',
                    'super_admin',
                    1
                ]);
                
                echo '<div class="result pass">✅ تم إنشاء مستخدم الإدارة الافتراضي</div>';
                
                // Show credentials
                echo '<div class="credentials">';
                echo '<h3>🔑 بيانات تسجيل الدخول الافتراضية</h3>';
                echo '<p><strong>اسم المستخدم:</strong> ' . $defaultUsername . '</p>';
                echo '<p><strong>كلمة المرور:</strong> ' . $defaultPassword . '</p>';
                echo '<p><strong>⚠️ تحذير:</strong> يرجى تغيير كلمة المرور فور تسجيل الدخول!</p>';
                echo '</div>';
            }
            
            // Test admin login functionality
            echo '<h3>🧪 اختبار وظيفة تسجيل الدخول</h3>';
            
            try {
                // Include admin class
                require_once '../php/admin.php';
                echo '<div class="result pass">✅ تم تحميل فئة Admin بنجاح</div>';
                
                // Test if we can create an Admin instance
                $admin = new Admin();
                echo '<div class="result pass">✅ تم إنشاء كائن Admin بنجاح</div>';
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في فئة Admin: ' . $e->getMessage() . '</div>';
            }
            
            // Test store settings table
            echo '<h3>🏪 اختبار جدول إعدادات المتجر</h3>';
            
            $stmt = $pdo->query("SHOW TABLES LIKE 'store_settings'");
            if ($stmt->rowCount() > 0) {
                echo '<div class="result pass">✅ جدول store_settings موجود</div>';
            } else {
                echo '<div class="result warning">⚠️ جدول store_settings غير موجود - جاري الإنشاء...</div>';
                
                $createStoreSettings = "
                CREATE TABLE IF NOT EXISTS `store_settings` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY,
                    `setting_key` VARCHAR(100) NOT NULL UNIQUE,
                    `setting_value` TEXT,
                    `setting_type` ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
                    `is_active` BOOLEAN DEFAULT TRUE,
                    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_setting_key (setting_key),
                    INDEX idx_is_active (is_active)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                $pdo->exec($createStoreSettings);
                echo '<div class="result pass">✅ تم إنشاء جدول store_settings</div>';
                
                // Insert default store settings
                $defaultSettings = [
                    ['store_name', 'متجر مصعب', 'string'],
                    ['store_description', 'متجر إلكتروني للكتب والمنتجات', 'string'],
                    ['store_email', '<EMAIL>', 'string'],
                    ['store_phone', '+213 XXX XXX XXX', 'string'],
                    ['store_address', 'الجزائر', 'string'],
                    ['currency', 'DZD', 'string'],
                    ['tax_rate', '19', 'float'],
                    ['shipping_cost', '500', 'float'],
                    ['free_shipping_threshold', '5000', 'float'],
                    ['store_status', '1', 'boolean']
                ];
                
                $insertSetting = $pdo->prepare("
                    INSERT IGNORE INTO store_settings (setting_key, setting_value, setting_type) 
                    VALUES (?, ?, ?)
                ");
                
                foreach ($defaultSettings as $setting) {
                    $insertSetting->execute($setting);
                }
                
                echo '<div class="result pass">✅ تم إدراج الإعدادات الافتراضية للمتجر</div>';
            }
            
            echo '<h3>🎉 تم الانتهاء بنجاح!</h3>';
            echo '<div class="result pass">✅ جميع الجداول والمستخدمين جاهزون</div>';
            echo '<div class="result pass">✅ يمكنك الآن تسجيل الدخول إلى لوحة التحكم</div>';
            
            echo '<h4>🚀 الخطوات التالية:</h4>';
            echo '<p><a href="index.html" class="fix-button">فتح لوحة التحكم</a></p>';
            echo '<p><a href="fix-critical-admin-errors.php" class="fix-button">تشغيل اختبار شامل</a></p>';
            echo '<p><a href="../php/admin.php?action=test" class="fix-button" target="_blank">اختبار Admin API</a></p>';
            
        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '<pre>' . $e->getTraceAsString() . '</pre>';
            echo '</div>';
        }
        ?>

        <!-- JavaScript Test -->
        <div style="margin-top: 20px;">
            <h3>🧪 اختبار JavaScript للـ Admin API</h3>
            <div id="jsTestResult">جاري الاختبار...</div>
            
            <script>
                async function testAdminAPI() {
                    const resultDiv = document.getElementById('jsTestResult');
                    
                    try {
                        // Test admin.php endpoint
                        const response = await fetch('../php/admin.php?action=test', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (response.ok) {
                            resultDiv.innerHTML = `
                                <div class="result pass">✅ Admin API يعمل - Status: ${response.status}</div>
                                <div class="result pass">🔗 API متاح ومتصل</div>
                            `;
                        } else {
                            resultDiv.innerHTML = `
                                <div class="result warning">⚠️ Admin API Status: ${response.status}</div>
                                <div class="result info">💡 قد يحتاج إعداد إضافي</div>
                            `;
                        }
                    } catch (error) {
                        resultDiv.innerHTML = `
                            <div class="result fail">❌ خطأ في Admin API: ${error.message}</div>
                            <div class="result info">💡 تأكد من تشغيل الخادم على localhost:8000</div>
                        `;
                    }
                }
                
                // Run test after page loads
                document.addEventListener('DOMContentLoaded', testAdminAPI);
            </script>
        </div>

    </div>
</body>
</html>
