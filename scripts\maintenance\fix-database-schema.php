<?php
/**
 * Fix Database Schema Issues
 * إصلاح مشاكل مخطط قاعدة البيانات
 */

require_once __DIR__ . '/../php/config.php';

// Alternative path if the above doesn't work
if (!function_exists('getPDOConnection')) {
    require_once __DIR__ . '/../config/config.php';
}

header('Content-Type: text/html; charset=utf-8');

echo "<h2>🔧 إصلاح مخطط قاعدة البيانات</h2>";

try {
    $pdo = getPDOConnection();
    echo "<div style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

    // Check if role_permissions table exists and its structure
    echo "<h3>📋 فحص جدول role_permissions</h3>";

    $stmt = $pdo->query("SHOW TABLES LIKE 'role_permissions'");
    $tableExists = $stmt->rowCount() > 0;

    if ($tableExists) {
        echo "<div style='color: blue;'>ℹ️ جدول role_permissions موجود</div>";

        // Check table structure
        $stmt = $pdo->query("DESCRIBE role_permissions");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $hasGrantedColumn = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'granted') {
                $hasGrantedColumn = true;
                break;
            }
        }

        if (!$hasGrantedColumn) {
            echo "<div style='color: orange;'>⚠️ عمود 'granted' مفقود، سيتم إضافته</div>";

            // Add granted column
            $pdo->exec("ALTER TABLE role_permissions ADD COLUMN granted TINYINT(1) DEFAULT 1");
            echo "<div style='color: green;'>✅ تم إضافة عمود 'granted' بنجاح</div>";
        } else {
            echo "<div style='color: green;'>✅ عمود 'granted' موجود</div>";
        }
    } else {
        echo "<div style='color: red;'>❌ جدول role_permissions غير موجود، سيتم إنشاؤه</div>";

        // Create role_permissions table
        $createTableSQL = "
        CREATE TABLE role_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_id INT NOT NULL,
            permission_id INT NOT NULL,
            granted TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            UNIQUE KEY unique_role_permission (role_id, permission_id),
            INDEX idx_role_id (role_id),
            INDEX idx_permission_id (permission_id),
            INDEX idx_granted (granted)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $pdo->exec($createTableSQL);
        echo "<div style='color: green;'>✅ تم إنشاء جدول role_permissions بنجاح</div>";
    }

    // Check other required tables
    echo "<h3>📋 فحص الجداول المطلوبة الأخرى</h3>";

    $requiredTables = [
        'user_roles' => "
        CREATE TABLE IF NOT EXISTS user_roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) UNIQUE NOT NULL,
            display_name_ar VARCHAR(100) NOT NULL,
            display_name_en VARCHAR(100) NOT NULL,
            description TEXT,
            level INT DEFAULT 1,
            color VARCHAR(7) DEFAULT '#007bff',
            icon VARCHAR(50) DEFAULT 'fas fa-user',
            is_active TINYINT(1) DEFAULT 1,
            is_system TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        'permissions' => "
        CREATE TABLE IF NOT EXISTS permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) UNIQUE NOT NULL,
            display_name_ar VARCHAR(100) NOT NULL,
            display_name_en VARCHAR(100) NOT NULL,
            category VARCHAR(50) NOT NULL,
            description TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        'user_role_assignments' => "
        CREATE TABLE IF NOT EXISTS user_role_assignments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            role_id INT NOT NULL,
            assigned_by INT,
            is_active TINYINT(1) DEFAULT 1,
            assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,

            UNIQUE KEY unique_user_role (user_id, role_id),
            INDEX idx_user_id (user_id),
            INDEX idx_role_id (role_id),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];

    foreach ($requiredTables as $tableName => $createSQL) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$tableName'");
        if ($stmt->rowCount() > 0) {
            echo "<div style='color: green;'>✅ جدول $tableName موجود</div>";
        } else {
            echo "<div style='color: orange;'>⚠️ جدول $tableName مفقود، سيتم إنشاؤه</div>";
            $pdo->exec($createSQL);
            echo "<div style='color: green;'>✅ تم إنشاء جدول $tableName بنجاح</div>";
        }
    }

    // Insert default roles if they don't exist
    echo "<h3>👥 إدراج الأدوار الافتراضية</h3>";

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_roles");
    $roleCount = $stmt->fetch()['count'];

    if ($roleCount == 0) {
        echo "<div style='color: orange;'>⚠️ لا توجد أدوار، سيتم إدراج الأدوار الافتراضية</div>";

        $defaultRoles = [
            ['super_admin', 'مدير عام', 'Super Admin', 'مدير عام للنظام', 1, '#dc3545', 'fas fa-crown'],
            ['admin', 'مدير', 'Admin', 'مدير النظام', 2, '#007bff', 'fas fa-user-shield'],
            ['store_manager', 'مدير متجر', 'Store Manager', 'مدير متجر إلكتروني', 3, '#28a745', 'fas fa-store'],
            ['user', 'مستخدم', 'User', 'مستخدم عادي', 4, '#6c757d', 'fas fa-user']
        ];

        $insertRoleSQL = "INSERT INTO user_roles (name, display_name_ar, display_name_en, description, level, color, icon, is_system) VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
        $stmt = $pdo->prepare($insertRoleSQL);

        foreach ($defaultRoles as $role) {
            $stmt->execute($role);
            echo "<div style='color: green;'>✅ تم إدراج دور: {$role[1]}</div>";
        }
    } else {
        echo "<div style='color: green;'>✅ الأدوار موجودة ($roleCount دور)</div>";
    }

    // Insert default permissions if they don't exist
    echo "<h3>🔐 إدراج الصلاحيات الافتراضية</h3>";

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM permissions");
    $permissionCount = $stmt->fetch()['count'];

    if ($permissionCount == 0) {
        echo "<div style='color: orange;'>⚠️ لا توجد صلاحيات، سيتم إدراج الصلاحيات الافتراضية</div>";

        $defaultPermissions = [
            ['manage_users', 'إدارة المستخدمين', 'Manage Users', 'user_management'],
            ['manage_stores', 'إدارة المتاجر', 'Manage Stores', 'store_management'],
            ['manage_products', 'إدارة المنتجات', 'Manage Products', 'product_management'],
            ['manage_orders', 'إدارة الطلبات', 'Manage Orders', 'order_management'],
            ['manage_settings', 'إدارة الإعدادات', 'Manage Settings', 'system_settings'],
            ['view_reports', 'عرض التقارير', 'View Reports', 'reporting'],
            ['manage_security', 'إدارة الأمان', 'Manage Security', 'security']
        ];

        $insertPermissionSQL = "INSERT INTO permissions (name, display_name_ar, display_name_en, category) VALUES (?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertPermissionSQL);

        foreach ($defaultPermissions as $permission) {
            $stmt->execute($permission);
            echo "<div style='color: green;'>✅ تم إدراج صلاحية: {$permission[1]}</div>";
        }
    } else {
        echo "<div style='color: green;'>✅ الصلاحيات موجودة ($permissionCount صلاحية)</div>";
    }

    echo "<h3>🎉 تم إصلاح مخطط قاعدة البيانات بنجاح!</h3>";
    echo "<div style='color: green; font-weight: bold;'>✅ جميع الجداول والبيانات الافتراضية جاهزة للاستخدام</div>";

} catch (Exception $e) {
    echo "<div style='color: red;'>❌ خطأ: " . $e->getMessage() . "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}
h2, h3 {
    color: #333;
}
div {
    margin: 5px 0;
    padding: 5px;
    border-radius: 4px;
}
</style>
