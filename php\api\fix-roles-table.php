<?php

/**
 * Fix Roles Table Schema
 * Specifically fixes the roles table structure issues
 * Works both via web server and CLI
 */

// Detect if running via CLI or web
$isCLI = php_sapi_name() === 'cli';

// Set headers only for web requests
if (!$isCLI) {
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');

    // Handle OPTIONS request
    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        exit(0);
    }
}

// Error handling
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Database configuration - try multiple paths
$configPaths = [
    '../config/database.php',
    __DIR__ . '/../config/database.php',
    __DIR__ . '/../../config/database.php',
    '../config.php',
    __DIR__ . '/../config.php',
    __DIR__ . '/../../config.php'
];

$configLoaded = false;
foreach ($configPaths as $configPath) {
    if (file_exists($configPath)) {
        try {
            require_once $configPath;
            $configLoaded = true;
            break;
        } catch (Exception $e) {
            continue;
        }
    }
}

// If no config found, use direct database connection
if (!$configLoaded) {
    // Direct database configuration
    function getDatabaseConnection()
    {
        $host = 'localhost';
        $port = '3307';
        $dbname = 'poultraydz';
        $username = 'root';
        $password = 'root';

        $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
        return new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
    }
}

try {
    // Get database connection
    $pdo = getDatabaseConnection();

    $fixes = [];
    $errors = [];
    $warnings = [];

    // Step 1: Check if roles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() == 0) {
        // Create roles table from scratch
        $createTableSQL = "
            CREATE TABLE roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL DEFAULT '',
                display_name_ar VARCHAR(100) NOT NULL DEFAULT '',
                description TEXT DEFAULT NULL,
                permissions JSON DEFAULT NULL,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $pdo->exec($createTableSQL);
        $fixes[] = "Created roles table with proper structure";

        // Insert default roles
        $defaultRoles = [
            ['admin', 'Administrator', 'مدير النظام', 'Full system access'],
            ['seller', 'Seller', 'بائع', 'Can manage own products and orders'],
            ['user', 'User', 'مستخدم', 'Basic user access'],
            ['moderator', 'Moderator', 'مشرف', 'Can moderate content'],
            ['editor', 'Editor', 'محرر', 'Can edit content']
        ];

        $insertStmt = $pdo->prepare("
            INSERT INTO roles (name, display_name, display_name_ar, description)
            VALUES (?, ?, ?, ?)
        ");

        foreach ($defaultRoles as $role) {
            $insertStmt->execute($role);
        }

        $fixes[] = "Inserted " . count($defaultRoles) . " default roles";
    } else {
        $fixes[] = "Roles table exists, checking structure";

        // Step 2: Get current table structure
        $stmt = $pdo->query("DESCRIBE roles");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $existingColumns = [];
        foreach ($columns as $column) {
            $existingColumns[$column['Field']] = $column;
        }

        // Step 3: Check and add missing display_name column
        if (!isset($existingColumns['display_name'])) {
            $pdo->exec("ALTER TABLE roles ADD COLUMN display_name VARCHAR(100) NOT NULL DEFAULT '' AFTER name");
            $fixes[] = "Added missing 'display_name' column";

            // Update existing roles with display names
            $updateDisplayNames = [
                'admin' => 'Administrator',
                'seller' => 'Seller',
                'user' => 'User',
                'moderator' => 'Moderator',
                'editor' => 'Editor'
            ];

            foreach ($updateDisplayNames as $name => $displayName) {
                $stmt = $pdo->prepare("UPDATE roles SET display_name = ? WHERE name = ?");
                $stmt->execute([$displayName, $name]);
            }

            $fixes[] = "Updated existing roles with display names";
        } else {
            $fixes[] = "display_name column already exists";
        }

        // Step 4: Check and fix display_name_ar column
        if (!isset($existingColumns['display_name_ar'])) {
            $pdo->exec("ALTER TABLE roles ADD COLUMN display_name_ar VARCHAR(100) NOT NULL DEFAULT '' AFTER display_name");
            $fixes[] = "Added missing 'display_name_ar' column";
        } else {
            // Check if it has proper default value
            $currentColumn = $existingColumns['display_name_ar'];
            if ($currentColumn['Default'] === null && $currentColumn['Null'] === 'NO') {
                $pdo->exec("ALTER TABLE roles MODIFY COLUMN display_name_ar VARCHAR(100) NOT NULL DEFAULT ''");
                $fixes[] = "Fixed display_name_ar column to have proper default value";
            } else {
                $fixes[] = "display_name_ar column already has proper default value";
            }
        }

        // Update existing roles with Arabic names if empty
        $updateArabicNames = [
            'admin' => 'مدير النظام',
            'seller' => 'بائع',
            'user' => 'مستخدم',
            'moderator' => 'مشرف',
            'editor' => 'محرر'
        ];

        foreach ($updateArabicNames as $name => $arabicName) {
            $stmt = $pdo->prepare("UPDATE roles SET display_name_ar = ? WHERE name = ? AND (display_name_ar = '' OR display_name_ar IS NULL)");
            $stmt->execute([$arabicName, $name]);
        }

        $fixes[] = "Updated roles with Arabic display names where missing";

        // Step 5: Add other missing columns if needed
        $requiredColumns = [
            'description' => "TEXT DEFAULT NULL",
            'permissions' => "JSON DEFAULT NULL",
            'is_active' => "TINYINT(1) DEFAULT 1",
            'created_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
            'updated_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ];

        foreach ($requiredColumns as $columnName => $columnDefinition) {
            if (!isset($existingColumns[$columnName])) {
                $pdo->exec("ALTER TABLE roles ADD COLUMN $columnName $columnDefinition");
                $fixes[] = "Added missing '$columnName' column";
            }
        }
    }

    // Step 6: Verify the fixes by testing a query
    try {
        $testStmt = $pdo->query("SELECT id, name, display_name, display_name_ar FROM roles LIMIT 1");
        $testResult = $testStmt->fetch();

        if ($testResult) {
            $fixes[] = "Verification query successful - roles table is working";
        } else {
            $warnings[] = "Roles table is empty but structure is correct";
        }
    } catch (Exception $e) {
        $errors[] = "Verification query failed: " . $e->getMessage();
    }

    // Step 7: Get final table structure for confirmation
    $stmt = $pdo->query("DESCRIBE roles");
    $finalColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $columnNames = array_column($finalColumns, 'Field');
    $fixes[] = "Final table structure: " . implode(', ', $columnNames);

    // Prepare results
    $results = [
        'success' => true,
        'message' => 'Roles table schema fixes completed successfully',
        'fixes' => $fixes,
        'warnings' => $warnings,
        'errors' => $errors,
        'table_structure' => $finalColumns,
        'total_fixes' => count($fixes),
        'total_warnings' => count($warnings),
        'total_errors' => count($errors)
    ];

    // Output results based on context
    if ($isCLI) {
        echo "=== Roles Table Fix Results ===\n";
        echo "Status: " . ($results['success'] ? 'SUCCESS' : 'FAILED') . "\n";
        echo "Message: " . $results['message'] . "\n\n";

        if (!empty($fixes)) {
            echo "Fixes Applied:\n";
            foreach ($fixes as $fix) {
                echo "  ✓ $fix\n";
            }
            echo "\n";
        }

        if (!empty($warnings)) {
            echo "Warnings:\n";
            foreach ($warnings as $warning) {
                echo "  ⚠ $warning\n";
            }
            echo "\n";
        }

        if (!empty($errors)) {
            echo "Errors:\n";
            foreach ($errors as $error) {
                echo "  ✗ $error\n";
            }
            echo "\n";
        }

        echo "Summary: " . count($fixes) . " fixes, " . count($warnings) . " warnings, " . count($errors) . " errors\n";
    } else {
        echo json_encode($results, JSON_UNESCAPED_UNICODE);
    }
} catch (Exception $e) {
    $errorResults = [
        'success' => false,
        'message' => 'Failed to fix roles table: ' . $e->getMessage(),
        'error' => $e->getMessage(),
        'fixes' => [],
        'errors' => ['Database error: ' . $e->getMessage()]
    ];

    if ($isCLI) {
        echo "=== Roles Table Fix Results ===\n";
        echo "Status: FAILED\n";
        echo "Error: " . $e->getMessage() . "\n";
    } else {
        echo json_encode($errorResults, JSON_UNESCAPED_UNICODE);
    }
}
