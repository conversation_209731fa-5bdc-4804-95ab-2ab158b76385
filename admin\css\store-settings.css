/**
 * Store Settings Styles
 * أنماط إعدادات المتجر
 */

/* Container Styles */
.store-settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Styles */
.store-settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.store-settings-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.store-settings-header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 1rem;
}

.header-actions {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.header-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.header-btn.primary {
    background: white;
    color: #667eea;
}

.header-btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid white;
}

.header-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Statistics Cards */
.settings-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.stat-icon.general { color: #667eea; }
.stat-icon.currency { color: #28a745; }
.stat-icon.shipping { color: #17a2b8; }
.stat-icon.email { color: #ffc107; }
.stat-icon.appearance { color: #e83e8c; }

.stat-number {
    margin: 0;
    font-size: 2.2rem;
    color: #333;
    font-weight: 700;
}

.stat-label {
    margin: 8px 0 0 0;
    color: #666;
    font-size: 0.95rem;
}

/* Settings Navigation */
.settings-nav {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.settings-nav-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.settings-nav-header h3 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-tabs {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    list-style: none;
    background: #f8f9fa;
}

.nav-tab {
    flex: 1;
    min-width: 150px;
}

.nav-tab-link {
    display: block;
    padding: 15px 20px;
    text-decoration: none;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 500;
}

.nav-tab-link:hover {
    background: #e9ecef;
    color: #333;
}

.nav-tab-link.active {
    background: white;
    color: #667eea;
    border-bottom-color: #667eea;
}

.nav-tab-badge {
    background: #667eea;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.75rem;
    margin-right: 5px;
}

/* Settings Content */
.settings-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.settings-section {
    display: none;
    padding: 30px;
}

.settings-section.active {
    display: block;
}

.settings-section-title {
    margin: 0 0 25px 0;
    color: #333;
    font-size: 1.4rem;
    font-weight: 600;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Form Styles */
.settings-form {
    display: grid;
    gap: 25px;
}

.form-group {
    display: grid;
    gap: 8px;
}

.form-group.row {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-label {
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-label.required::after {
    content: '*';
    color: #dc3545;
    font-weight: bold;
}

.form-input {
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
    border-color: #dc3545;
}

.form-input.success {
    border-color: #28a745;
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.form-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.form-help {
    font-size: 0.875rem;
    color: #666;
    margin-top: 5px;
}

.form-error {
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 5px;
}

.form-success {
    font-size: 0.875rem;
    color: #28a745;
    margin-top: 5px;
}

/* Color Input */
.color-input-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-input {
    width: 50px;
    height: 40px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    padding: 0;
}

.color-value {
    flex: 1;
}

/* File Input */
.file-input-wrapper {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.file-input-label:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

/* Action Buttons */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
    margin-top: 30px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn.primary {
    background: #667eea;
    color: white;
}

.btn.primary:hover {
    background: #5a67d8;
}

.btn.secondary {
    background: #6c757d;
    color: white;
}

.btn.secondary:hover {
    background: #5a6268;
}

.btn.success {
    background: #28a745;
    color: white;
}

.btn.success:hover {
    background: #218838;
}

.btn.danger {
    background: #dc3545;
    color: white;
}

.btn.danger:hover {
    background: #c82333;
}

.btn.outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn.outline:hover {
    background: #667eea;
    color: white;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
    .store-settings-container {
        padding: 15px;
    }
    
    .settings-stats {
        grid-template-columns: 1fr;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .form-group.row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .header-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .store-settings-header {
        padding: 20px 15px;
    }
    
    .settings-section {
        padding: 20px 15px;
    }
    
    .stat-card {
        padding: 20px 15px;
    }
}
