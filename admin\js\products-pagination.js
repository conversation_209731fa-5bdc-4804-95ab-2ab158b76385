/**
 * Products Pagination System
 * Handles pagination, search, and smooth loading for products
 */

(function() {
    "use strict";

    // Pagination state
    let currentPage = 1;
    let pageSize = 20;
    let totalProducts = 0;
    let allProducts = [];
    let filteredProducts = [];
    let searchTerm = '';

    console.log("🔧 Products Pagination System loading...");

    // Initialize pagination system
    function initProductsPagination() {
        console.log("🚀 Initializing products pagination...");

        // Set default page size
        const pageSizeSelect = document.getElementById('productsPageSize');
        if (pageSizeSelect) {
            pageSizeSelect.value = pageSize;
        }

        // Load products with pagination
        loadProductsWithPagination();
    }

    // Load products with pagination
    async function loadProductsWithPagination() {
        try {
            console.log('📦 Loading products with pagination...');
            showLoadingIndicator(true);

            const response = await fetch('../php/api/products.php');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            console.log('API Response:', result);

            // Handle both array and object responses
            let data = [];
            if (Array.isArray(result)) {
                data = result;
            } else if (result.success && Array.isArray(result.data)) {
                data = result.data;
            } else if (result.success && Array.isArray(result.products)) {
                data = result.products;
            } else if (result.data) {
                data = result.data;
            } else if (result.products) {
                data = result.products;
            } else {
                console.error('Invalid API response format:', result);
                throw new Error('Invalid data format: no products found in response');
            }

            // Store all products
            allProducts = data;
            totalProducts = data.length;

            // Apply search filter if exists
            applySearchFilter();

            // Render current page
            renderProductsPage();

            // Update pagination controls
            updatePaginationControls();

            console.log(`✅ Loaded ${totalProducts} products successfully`);

        } catch (error) {
            console.error('Error loading products:', error);
            showError('حدث خطأ أثناء تحميل المنتجات');
        } finally {
            showLoadingIndicator(false);
        }
    }

    // Apply search filter
    function applySearchFilter() {
        if (!searchTerm.trim()) {
            filteredProducts = [...allProducts];
        } else {
            const term = searchTerm.toLowerCase().trim();
            filteredProducts = allProducts.filter(product =>
                product.titre.toLowerCase().includes(term) ||
                product.type.toLowerCase().includes(term) ||
                product.prix.toString().includes(term)
            );
        }

        // Reset to first page when filtering
        currentPage = 1;
    }

    // Render products for current page
    function renderProductsPage() {
        const tbody = document.querySelector('#booksTable tbody');
        if (!tbody) return;

        // Clear existing content
        tbody.innerHTML = '';

        // Calculate pagination
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, filteredProducts.length);
        const pageProducts = filteredProducts.slice(startIndex, endIndex);

        // Create document fragment for better performance
        const fragment = document.createDocumentFragment();

        pageProducts.forEach((product, index) => {
            const globalIndex = startIndex + index;
            const productNumber = String(globalIndex + 1).padStart(3, '0');

            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>
                    <input type="checkbox" class="product-checkbox" value="${product.id}" onchange="updateSelectedProductsCount()">
                </td>
                <td>
                    <span class="product-number">#${productNumber}</span>
                </td>
                <td><img src="${product.image_url || '../images/default-product.jpg'}" alt="${product.titre}" width="50" onerror="this.src='../images/default-product.jpg'"></td>
                <td>${product.titre}</td>
                <td>${getProductTypeText(product.type)}</td>
                <td>${product.prix} دج</td>
                <td>${product.stock}</td>
                <td>
                    <span class="status-badge status-${product.actif ? 'active' : 'inactive'}">
                        ${product.actif ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <button onclick="editProduct(${product.id})" class="action-button" title="تعديل المنتج">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="toggleProductStatus(${product.id}, ${product.actif})"
                            class="action-button toggle-status-btn"
                            data-product-id="${product.id}"
                            data-current-status="${product.actif}"
                            style="background: ${product.actif ? '#e67e22' : '#27ae60'};"
                            title="${product.actif ? 'تعطيل المنتج' : 'تفعيل المنتج'}">
                        <i class="fas ${product.actif ? 'fa-pause' : 'fa-play'}"></i>
                        <span class="status-text">${product.actif ? 'مفعل' : 'معطل'}</span>
                    </button>
                    <button onclick="deleteProduct(${product.id})" class="action-button" style="background: #e74c3c;" title="حذف المنتج">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            fragment.appendChild(tr);

            // Add View More link if product has landing page
            if (product.landing_page_url) {
                addViewMoreLink(tr, product);
            }
        });

        // Append all rows at once for better performance
        tbody.appendChild(fragment);

        // Update products info
        updateProductsInfo();
    }

    // Update pagination controls
    function updatePaginationControls() {
        const totalPages = Math.ceil(filteredProducts.length / pageSize);

        // Update navigation buttons
        const prevBtn = document.getElementById('prevProductsPage');
        const nextBtn = document.getElementById('nextProductsPage');

        if (prevBtn) prevBtn.disabled = currentPage <= 1;
        if (nextBtn) nextBtn.disabled = currentPage >= totalPages;

        // Update page numbers
        renderPageNumbers(totalPages);
    }

    // Render page numbers
    function renderPageNumbers(totalPages) {
        const pagesContainer = document.getElementById('productsPaginationPages');
        if (!pagesContainer) return;

        pagesContainer.innerHTML = '';

        // Show max 5 page numbers
        const maxPages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxPages / 2));
        let endPage = Math.min(totalPages, startPage + maxPages - 1);

        // Adjust start page if we're near the end
        if (endPage - startPage < maxPages - 1) {
            startPage = Math.max(1, endPage - maxPages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.className = i === currentPage ? 'active' : '';
            pageBtn.onclick = () => goToPage(i);
            pagesContainer.appendChild(pageBtn);
        }
    }

    // Update products info display
    function updateProductsInfo() {
        const infoElement = document.getElementById('productsInfo');
        if (!infoElement) return;

        const startIndex = (currentPage - 1) * pageSize + 1;
        const endIndex = Math.min(currentPage * pageSize, filteredProducts.length);

        if (filteredProducts.length === 0) {
            infoElement.textContent = 'لا توجد منتجات';
        } else {
            infoElement.textContent = `عرض ${startIndex}-${endIndex} من ${filteredProducts.length} منتج`;
        }
    }

    // Show/hide loading indicator
    function showLoadingIndicator(show) {
        const indicator = document.getElementById('productsLoadingIndicator');
        const table = document.getElementById('booksTable');

        if (indicator) {
            indicator.style.display = show ? 'block' : 'none';
        }
        if (table) {
            table.style.opacity = show ? '0.5' : '1';
        }
    }

    // Show error message
    function showError(message) {
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError(message);
        } else {
            alert(message);
        }
    }

    // Get product type text in Arabic
    function getProductTypeText(type) {
        const types = {
            'book': 'كتاب',
            'backpack': 'حقيبة ظهر',
            'laptop': 'حاسوب محمول',
            'smartphone': 'هاتف ذكي',
            'accessory': 'إكسسوار',
            'sports': 'رياضة',
            'beauty': 'تجميل',
            'game': 'لعبة',
            'clothing': 'ملابس',
            'home': 'منزل'
        };
        return types[type] || type;
    }

    // Public functions for pagination controls
    window.changeProductsPageSize = function() {
        const select = document.getElementById('productsPageSize');
        if (select) {
            pageSize = parseInt(select.value);
            currentPage = 1; // Reset to first page
            renderProductsPage();
            updatePaginationControls();
        }
    };

    window.searchProducts = function() {
        const input = document.getElementById('productsSearchInput');
        const clearBtn = document.getElementById('clearSearchBtn');

        if (input) {
            searchTerm = input.value;
            applySearchFilter();
            renderProductsPage();
            updatePaginationControls();

            // Show/hide clear button
            if (clearBtn) {
                clearBtn.style.display = searchTerm.trim() ? 'inline-block' : 'none';
            }
        }
    };

    window.clearProductsSearch = function() {
        const input = document.getElementById('productsSearchInput');
        const clearBtn = document.getElementById('clearSearchBtn');

        if (input) {
            input.value = '';
            searchTerm = '';
            applySearchFilter();
            renderProductsPage();
            updatePaginationControls();
        }

        if (clearBtn) {
            clearBtn.style.display = 'none';
        }
    };

    // Pagination navigation functions
    window.goToPage = function(page) {
        currentPage = page;
        renderProductsPage();
        updatePaginationControls();
    };

    window.goToProductsPage = function(page) {
        currentPage = page;
        renderProductsPage();
        updatePaginationControls();
    };

    window.previousProductsPage = function() {
        if (currentPage > 1) {
            currentPage--;
            renderProductsPage();
            updatePaginationControls();
        }
    };

    window.nextProductsPage = function() {
        const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
        if (currentPage < totalPages) {
            currentPage++;
            renderProductsPage();
            updatePaginationControls();
        }
    };

    window.previousProductsPage = function() {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    };

    window.nextProductsPage = function() {
        const totalPages = Math.ceil(filteredProducts.length / pageSize);
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    };

    /**
     * Add View More link for products with landing pages
     */
    function addViewMoreLink(tr, product) {
        if (!product.landing_page_url) return;

        // Create a new row for the landing page link
        const linkRow = document.createElement('tr');
        linkRow.className = 'landing-page-link-row';
        linkRow.style.display = 'none'; // Initially hidden

        linkRow.innerHTML = `
            <td colspan="8" style="padding: 10px; background: #f8f9fa; border-top: none;">
                <div style="display: flex; align-items: center; gap: 10px; justify-content: center;">
                    <i class="fas fa-external-link-alt" style="color: #667eea;"></i>
                    <a href="${product.landing_page_url}" target="_blank"
                       style="color: #667eea; text-decoration: none; font-weight: 500;">
                        عرض صفحة الهبوط للمنتج
                    </a>
                </div>
            </td>
        `;

        // Add toggle functionality to the main row
        tr.style.cursor = 'pointer';
        tr.addEventListener('click', function(e) {
            // Don't toggle if clicking on action buttons
            if (e.target.closest('.action-button')) return;

            const isVisible = linkRow.style.display !== 'none';
            linkRow.style.display = isVisible ? 'none' : 'table-row';
        });

        // Insert the link row after the product row
        tr.parentNode.insertBefore(linkRow, tr.nextSibling);
    }

    // Replace the original loadProducts function
    window.loadProducts = loadProductsWithPagination;

    // Make pagination functions globally available
    window.addViewMoreLink = addViewMoreLink;
    window.initProductsPagination = initProductsPagination;
    window.changeProductsPerPage = changeProductsPageSize;
    window.changeProductsPageSize = changeProductsPageSize; // Ensure both names work
    window.searchProducts = searchProducts;
    window.goToProductsPage = goToProductsPage;
    window.previousProductsPage = previousProductsPage;
    window.nextProductsPage = nextProductsPage;

    // Ensure product management functions are available (fallback declarations)
    // These functions should be defined in admin.js, but we provide fallbacks here
    if (typeof window.editProduct !== 'function') {
        window.editProduct = function(productId) {
            console.log('🖊️ editProduct called with ID:', productId);
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showInfo('وظيفة تعديل المنتج قيد التطوير...');
            } else {
                alert('وظيفة تعديل المنتج قيد التطوير...');
            }
        };
    }

    if (typeof window.viewProduct !== 'function') {
        window.viewProduct = function(productId) {
            console.log('👁️ viewProduct called with ID:', productId);
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showInfo('وظيفة عرض المنتج قيد التطوير...');
            } else {
                alert('وظيفة عرض المنتج قيد التطوير...');
            }
        };
    }

    if (typeof window.deleteProduct !== 'function') {
        window.deleteProduct = function(productId) {
            console.log('🗑️ deleteProduct called with ID:', productId);
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                if (typeof notificationManager !== 'undefined') {
                    notificationManager.showInfo('وظيفة حذف المنتج قيد التطوير...');
                } else {
                    alert('وظيفة حذف المنتج قيد التطوير...');
                }
            }
        };
    }

    if (typeof window.viewLandingPage !== 'function') {
        window.viewLandingPage = function(productId) {
            console.log('🚀 viewLandingPage called with ID:', productId);
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showInfo('وظيفة عرض صفحة الهبوط قيد التطوير...');
            } else {
                alert('وظيفة عرض صفحة الهبوط قيد التطوير...');
            }
        };
    }

    console.log('✅ Product management functions ensured to be available');
    console.log('   - editProduct:', typeof window.editProduct);
    console.log('   - viewProduct:', typeof window.viewProduct);
    console.log('   - deleteProduct:', typeof window.deleteProduct);
    console.log('   - viewLandingPage:', typeof window.viewLandingPage);

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initProductsPagination);
    } else {
        initProductsPagination();
    }

    console.log("✅ Products Pagination System loaded successfully");

})();
