<?php

/**
 * Configuration loader for Mossaab Landing Page
 * Loads and validates environment variables from .env file
 */

if (!class_exists('Config')) {
    class Config
    {
        private static $config = [];
        private static $initialized = false;

        public static function init()
        {
            if (self::$initialized) {
                return;
            }

            // Load .env file
            $envFile = dirname(__DIR__) . '/.env';
            if (!file_exists($envFile)) {
                die('Error: .env file not found');
            }

            // Parse .env file
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos(trim($line), '#') === 0) {
                    continue;
                }

                list($key, $value) = explode('=', $line, 2) + [NULL, NULL];
                if (!empty($key)) {
                    self::$config[trim($key)] = trim($value ?? '');
                }
            }

            self::validateRequiredConfig();
            self::$initialized = true;
        }

        private static function validateRequiredConfig()
        {
            $required = [
                'DB_HOST',
                'DB_PORT',
                'DB_USERNAME',
                'DB_DATABASE',
                'APP_URL'
            ];

            $missing = [];
            foreach ($required as $key) {
                if (empty(self::$config[$key])) {
                    $missing[] = $key;
                }
            }

            if (!empty($missing)) {
                die('Error: Missing required configuration: ' . implode(', ', $missing));
            }
        }

        public static function get($key, $default = null)
        {
            if (!self::$initialized) {
                self::init();
            }
            return self::$config[$key] ?? $default;
        }

        public static function getDbConfig()
        {
            return [
                'host' => self::get('DB_HOST'),
                'port' => self::get('DB_PORT'),
                'username' => self::get('DB_USERNAME'),
                'password' => self::get('DB_PASSWORD'),
                'database' => self::get('DB_DATABASE')
            ];
        }

        public static function getAIConfig()
        {
            return [
                'openai' => [
                    'key' => self::get('OPENAI_API_KEY'),
                    'enabled' => !empty(self::get('OPENAI_API_KEY'))
                ],
                'anthropic' => [
                    'key' => self::get('ANTHROPIC_API_KEY'),
                    'enabled' => !empty(self::get('ANTHROPIC_API_KEY'))
                ],
                'gemini' => [
                    'key' => self::get('GEMINI_API_KEY'),
                    'enabled' => !empty(self::get('GEMINI_API_KEY'))
                ]
            ];
        }

        public static function getSmtpConfig()
        {
            return [
                'host' => self::get('SMTP_HOST'),
                'port' => self::get('SMTP_PORT'),
                'username' => self::get('SMTP_USERNAME'),
                'password' => self::get('SMTP_PASSWORD'),
                'encryption' => self::get('SMTP_ENCRYPTION')
            ];
        }

        public static function getAppConfig()
        {
            return [
                'env' => self::get('APP_ENV', 'production'),
                'debug' => filter_var(self::get('APP_DEBUG', false), FILTER_VALIDATE_BOOLEAN),
                'url' => self::get('APP_URL'),
                'upload' => [
                    'max_size' => intval(self::get('MAX_UPLOAD_SIZE', 10485760)),
                    'allowed_types' => explode(',', self::get('ALLOWED_IMAGE_TYPES', 'jpg,jpeg,png,gif'))
                ],
                'cache' => [
                    'enabled' => filter_var(self::get('CACHE_ENABLED', true), FILTER_VALIDATE_BOOLEAN),
                    'lifetime' => intval(self::get('CACHE_LIFETIME', 3600))
                ]
            ];
        }

        public static function getSecurityConfig()
        {
            return [
                'session_lifetime' => intval(self::get('SESSION_LIFETIME', 120)),
                'csrf_token_lifetime' => intval(self::get('CSRF_TOKEN_LIFETIME', 3600)),
                'max_login_attempts' => intval(self::get('MAX_LOGIN_ATTEMPTS', 5)),
                'login_lockout_time' => intval(self::get('LOGIN_LOCKOUT_TIME', 900))
            ];
        }

        public static function getShippingConfig()
        {
            return [
                'default_cost' => floatval(self::get('DEFAULT_SHIPPING_COST', 50)),
                'free_threshold' => floatval(self::get('FREE_SHIPPING_THRESHOLD', 1000))
            ];
        }

        public static function getPaymentConfig()
        {
            return [
                'stripe' => [
                    'public_key' => self::get('STRIPE_PUBLIC_KEY'),
                    'secret_key' => self::get('STRIPE_SECRET_KEY')
                ],
                'paypal' => [
                    'client_id' => self::get('PAYPAL_CLIENT_ID'),
                    'client_secret' => self::get('PAYPAL_CLIENT_SECRET'),
                    'sandbox' => filter_var(self::get('PAYPAL_SANDBOX', true), FILTER_VALIDATE_BOOLEAN)
                ]
            ];
        }

        /**
         * Update an environment variable in the .env file
         * @param string $key The environment variable name
         * @param string $value The new value
         * @return bool Whether the update was successful
         */
        public static function updateEnvVar($key, $value)
        {
            $envFile = dirname(__DIR__) . '/.env';

            if (!is_writable($envFile)) {
                error_log('Config: .env file is not writable');
                return false;
            }

            // Read current content
            $content = file_get_contents($envFile);
            $lines = explode("\n", $content);
            $found = false;
            $newLines = [];

            // Update existing line or add new one
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '#') === 0) {
                    $newLines[] = $line;
                    continue;
                }

                $parts = explode('=', $line, 2);
                if (count($parts) !== 2) {
                    $newLines[] = $line;
                    continue;
                }

                $envKey = trim($parts[0]);
                if ($envKey === $key) {
                    $newLines[] = $key . '=' . $value;
                    $found = true;
                    self::$config[$key] = $value; // Update in-memory config
                } else {
                    $newLines[] = $line;
                }
            }

            // Add new line if key wasn't found
            if (!$found) {
                $newLines[] = $key . '=' . $value;
                self::$config[$key] = $value;
            }

            // Write back to file
            $success = file_put_contents($envFile, implode("\n", $newLines));

            if ($success === false) {
                error_log('Config: Failed to write .env file');
                return false;
            }

            return true;
        }
    }
}

// Initialize configuration
Config::init();

// Set error reporting based on APP_DEBUG
if (Config::get('APP_DEBUG', false)) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    error_reporting(0);
}

// Function to get PDO connection
function getPDOConnection() {
    try {
        $dbConfig = Config::getDbConfig();
        
        $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        
        return $pdo;
    } catch (Exception $e) {
        error_log("Database connection error: " . $e->getMessage());
        throw new Exception("Unable to connect to database: " . $e->getMessage());
    }
}

// Create global database connection for backward compatibility
try {
    $pdo = getPDOConnection();
    $conn = $pdo; // Legacy compatibility
    error_log("Database connection established");
} catch (Exception $e) {
    error_log("Failed to establish database connection: " . $e->getMessage());
    // Don't die here, let individual scripts handle the error
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
