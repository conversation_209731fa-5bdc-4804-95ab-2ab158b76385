/**
 * Multi-User Admin Interface CSS
 * Enhanced styling for multi-user admin interface
 */

/* Enhanced Dashboard Styles */
.enhanced-dashboard {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 20px 0;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.dashboard-header h2 {
    color: #2c3e50;
    margin: 0;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-role {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-role.admin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.user-role.seller {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

/* Enhanced Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 5px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
    border-left-color: #007bff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-card.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

.stat-card.info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #3ca55c 0%, #b5ac49 100%);
    color: white;
}

.stat-card.warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3 {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.stat-content p {
    margin: 5px 0 0 0;
    font-size: 0.95rem;
    opacity: 0.9;
}

/* Admin Section Styles */
.admin-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.admin-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.sellers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.seller-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #007bff;
    transition: all 0.3s ease;
}

.seller-card:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.seller-info h4 {
    color: #2c3e50;
    margin: 0 0 5px 0;
    font-weight: 600;
}

.seller-info p {
    color: #6c757d;
    margin: 0 0 5px 0;
    font-weight: 500;
}

.seller-info small {
    color: #adb5bd;
}

.seller-stats {
    margin-top: 15px;
    display: flex;
    gap: 15px;
}

.seller-stats .stat {
    background: #007bff;
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Recent Activity Styles */
.recent-activity {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.recent-activity h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.activity-content p {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-weight: 500;
}

.activity-content small {
    color: #6c757d;
}

/* Quick Actions Styles */
.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    padding: 15px 20px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.action-btn.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

.action-btn.info {
    background: linear-gradient(135deg, #3ca55c 0%, #b5ac49 100%);
    color: white;
}

.action-btn.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

/* Enhanced Products Styles */
.enhanced-products {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 20px 0;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.products-header h2 {
    color: #2c3e50;
    margin: 0;
    font-weight: 600;
}

.products-stats {
    margin-bottom: 30px;
}

.products-stats .stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.products-stats .stat-card h4 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    color: white;
}

.products-stats .stat-card p {
    margin: 0;
    color: white;
    opacity: 0.9;
}

/* Owner Info Styles */
.owner-info {
    font-size: 0.9rem;
}

.owner-info strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 3px;
}

.owner-info small {
    color: #6c757d;
    display: block;
    margin-bottom: 5px;
}

.owner-info .badge {
    font-size: 0.75rem;
}

/* Empty State Styles */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.empty-state i {
    margin-bottom: 20px;
    color: #adb5bd;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 25px;
}

/* Loading and Error States */
.loading-container, .error-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .sellers-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-header,
    .products-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .seller-stats {
        flex-direction: column;
        gap: 8px;
    }

    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.enhanced-dashboard,
.enhanced-products {
    animation: fadeInUp 0.6s ease-out;
}

/* Reports and Statistics Styles */
.reports-dashboard {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 20px 0;
}

.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.reports-header h2 {
    color: #2c3e50;
    margin: 0;
    font-weight: 600;
}

.reports-actions {
    display: flex;
    gap: 10px;
}

.reports-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.reports-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-5px);
}

.overview-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.overview-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

.overview-card.info {
    background: linear-gradient(135deg, #3ca55c 0%, #b5ac49 100%);
    color: white;
}

.overview-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.card-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.card-content h4 {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0 0 5px 0;
}

.card-content p {
    margin: 0 0 5px 0;
    font-size: 1rem;
    opacity: 0.9;
}

.card-content small {
    font-size: 0.85rem;
    opacity: 0.8;
}

.sales-analytics {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.sales-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.sales-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.sales-card h4 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-weight: 600;
}

.sales-amount {
    font-size: 1.8rem;
    font-weight: 700;
    color: #007bff;
    margin: 0 0 5px 0;
}

.sales-card small {
    color: #6c757d;
}

.product-analytics {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.product-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.product-stat {
    background: white;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stat-label {
    color: #2c3e50;
    font-weight: 500;
}

.stat-value {
    color: #007bff;
    font-weight: 700;
    font-size: 1.1rem;
}

.top-products, .top-users {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.top-products h4, .top-users h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-weight: 600;
}

.products-list, .users-list {
    max-height: 300px;
    overflow-y: auto;
}

.product-item, .user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.product-item:last-child, .user-item:last-child {
    border-bottom: none;
}

.product-info strong, .user-info strong {
    color: #2c3e50;
    display: block;
}

.product-price {
    color: #007bff;
    font-weight: 600;
}

.user-info small {
    color: #6c757d;
}

.product-status, .user-stats {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.user-stat-item {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.user-analytics {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.users-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.user-stat {
    background: white;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.recent-activity {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.activity-timeline {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.activity-item:hover {
    transform: translateX(5px);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.activity-content p {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-weight: 500;
}

.activity-content small {
    color: #6c757d;
}

.reports-footer {
    text-align: center;
    padding: 20px;
    border-top: 1px solid #e9ecef;
    margin-top: 30px;
}

/* Print Styles */
@media print {
    .enhanced-dashboard,
    .enhanced-products,
    .reports-dashboard {
        background: white !important;
        box-shadow: none !important;
    }

    .action-btn,
    .btn,
    .reports-actions {
        display: none !important;
    }
}

/* Products Pagination Styles */
.products-pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 20px;
    font-size: 14px;
    color: #6c757d;
}

.pagination-info select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
}

.pagination-search {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-search input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 250px;
    font-size: 14px;
}

.pagination-search button {
    padding: 8px 12px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.pagination-search button:hover {
    background: #c82333;
}

.products-pagination-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.products-pagination-nav button {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: #fff;
    color: #495057;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.products-pagination-nav button:hover:not(:disabled) {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.products-pagination-nav button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-pages {
    display: flex;
    gap: 5px;
}

.pagination-pages button {
    min-width: 40px;
    padding: 8px 12px;
}

.pagination-pages button.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.loading-indicator {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-size: 16px;
}

.loading-indicator i {
    margin-left: 10px;
}

/* Landing Pages Styles */
.landing-pages-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.landing-pages-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.landing-pages-header h3 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.landing-pages-header .subtitle {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.landing-pages-filters {
    display: flex;
    gap: 15px;
    padding: 20px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.search-input, .filter-select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    min-width: 200px;
    transition: border-color 0.3s ease;
}

.search-input:focus, .filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.landing-pages-table {
    width: 100%;
    border-collapse: collapse;
}

.landing-pages-table th,
.landing-pages-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.landing-pages-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.landing-page-title {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.landing-page-url {
    font-size: 0.9em;
}

.landing-page-url a {
    text-decoration: none;
    transition: color 0.3s ease;
}

.landing-page-url a:hover {
    text-decoration: underline;
}

.owner-info, .product-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.stats-cell {
    text-align: center;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-badge.secondary {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

/* Pagination Controls for Landing Pages */
.pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.per-page-select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
}

.pagination-stats {
    font-size: 14px;
    color: #6c757d;
}

/* Pagination Navigation for Landing Pages */
.pagination-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    padding: 20px 0;
}

.pagination-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.pagination-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.pagination-pages {
    display: flex;
    gap: 5px;
}

.pagination-number {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.pagination-number:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.pagination-number.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Responsive Design for Landing Pages */
@media (max-width: 768px) {
    .landing-pages-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .landing-pages-filters {
        flex-direction: column;
    }

    .search-input, .filter-select {
        min-width: 100%;
    }

    .landing-pages-table {
        font-size: 0.9rem;
    }

    .landing-pages-table th,
    .landing-pages-table td {
        padding: 10px 8px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }

    .pagination-controls {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

/* Enhanced Modal Styles for Product Management */
.enhanced-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.enhanced-modal .modal-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
    direction: rtl;
}

.enhanced-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.enhanced-modal .modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.enhanced-modal .close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.enhanced-modal .close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

.enhanced-modal .modal-body {
    padding: 30px;
}

.enhanced-modal .modal-footer {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 10px 10px;
}

/* Product View Specific Styles */
.product-view-container {
    direction: rtl;
    text-align: right;
}

.product-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.detail-item label {
    font-weight: bold;
    color: #495057;
    display: block;
    margin-bottom: 5px;
}

.detail-item p {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin: 0;
    border: 1px solid #e9ecef;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.status-inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.owner-info-section {
    margin-top: 20px;
    padding: 15px;
    background: #e3f2fd;
    border-radius: 8px;
    border-right: 4px solid #2196f3;
}

.owner-info-section h4 {
    margin: 0 0 10px 0;
    color: #1976d2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.landing-page-section {
    margin-top: 20px;
    padding: 15px;
    background: #f3e5f5;
    border-radius: 8px;
    border-right: 4px solid #9c27b0;
}

.landing-page-section h4 {
    margin: 0 0 10px 0;
    color: #7b1fa2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.landing-page-section a {
    color: #7b1fa2;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: color 0.3s ease;
}

.landing-page-section a:hover {
    color: #4a148c;
    text-decoration: underline;
}

/* Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-1px);
}

/* Animation Keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design for Product Modal */
@media (max-width: 768px) {
    .enhanced-modal .modal-content {
        max-width: 95vw;
        margin: 10px;
    }

    .enhanced-modal .modal-header,
    .enhanced-modal .modal-body,
    .enhanced-modal .modal-footer {
        padding: 15px 20px;
    }

    .product-details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .enhanced-modal .modal-footer {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}
