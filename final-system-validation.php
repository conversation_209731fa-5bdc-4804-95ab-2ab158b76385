<?php

/**
 * Final System Validation
 * التحقق النهائي من النظام
 */

require_once 'php/config.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق النهائي من النظام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        h1,
        h2,
        h3 {
            color: #333;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .test-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .test-button {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-pass {
            background: #28a745;
        }

        .status-fail {
            background: #dc3545;
        }

        .status-warning {
            background: #ffc107;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }

        th {
            background: #f8f9fa;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🎯 التحقق النهائي من النظام</h1>
        <p>فحص شامل لجميع الإصلاحات والوظائف</p>

        <?php
        $overallStatus = 'success';
        $testResults = [];

        try {
            $pdo = getPDOConnection();

            // Test 1: Database Connection
            echo "<div class='section success'>";
            echo "<h2>✅ 1. اختبار الاتصال بقاعدة البيانات</h2>";
            echo "<p>تم الاتصال بقاعدة البيانات بنجاح</p>";
            $testResults['database_connection'] = 'PASS';
            echo "</div>";

            // Test 2: Migration Transaction Fix
            echo "<div class='section'>";
            echo "<h2>🔧 2. اختبار إصلاح خطأ الترحيل</h2>";

            $migrationFile = 'database/run_safe_migration.php';
            if (file_exists($migrationFile)) {
                $content = file_get_contents($migrationFile);

                // Check if transaction handling is fixed
                if (strpos($content, 'if ($pdo->inTransaction())') !== false) {
                    echo "<div class='success'>✅ تم إصلاح معالجة المعاملات</div>";
                    $testResults['transaction_fix'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ لم يتم إصلاح معالجة المعاملات</div>";
                    $testResults['transaction_fix'] = 'FAIL';
                    $overallStatus = 'error';
                }

                // Check if final verification is outside transaction
                if (strpos($content, '// Final verification (outside transaction)') !== false) {
                    echo "<div class='success'>✅ تم نقل التحقق النهائي خارج المعاملة</div>";
                    $testResults['verification_fix'] = 'PASS';
                } else {
                    echo "<div class='error'>❌ التحقق النهائي لا يزال داخل المعاملة</div>";
                    $testResults['verification_fix'] = 'FAIL';
                    $overallStatus = 'error';
                }
            } else {
                echo "<div class='error'>❌ ملف الترحيل الآمن غير موجود</div>";
                $testResults['migration_file'] = 'FAIL';
                $overallStatus = 'error';
            }
            echo "</div>";

            // Test 3: Database Structure Validation
            echo "<div class='section'>";
            echo "<h2>🗄️ 3. التحقق من هيكل قاعدة البيانات</h2>";

            // Check produits table
            $stmt = $pdo->query("SHOW TABLES LIKE 'produits'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✅ جدول produits موجود</div>";

                $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
                $count = $stmt->fetch()['count'];
                echo "<div class='info'>📊 عدد المنتجات: $count</div>";

                $testResults['produits_table'] = 'PASS';
            } else {
                echo "<div class='error'>❌ جدول produits غير موجود</div>";
                $testResults['produits_table'] = 'FAIL';
                $overallStatus = 'error';
            }

            // Check foreign keys
            $stmt = $pdo->query("
                SELECT COUNT(*) as count
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE()
                AND REFERENCED_TABLE_NAME = 'produits'
            ");
            $fkCount = $stmt->fetch()['count'];

            if ($fkCount > 0) {
                echo "<div class='success'>✅ المفاتيح الخارجية تشير إلى produits ($fkCount)</div>";
                $testResults['foreign_keys'] = 'PASS';
            } else {
                echo "<div class='warning'>⚠️ لا توجد مفاتيح خارجية تشير إلى produits</div>";
                $testResults['foreign_keys'] = 'WARNING';
            }
            echo "</div>";

            // Test 4: Role Management Files
            echo "<div class='section'>";
            echo "<h2>🛡️ 4. اختبار ملفات إدارة الأدوار</h2>";

            $roleFiles = [
                'admin/js/users-management.js' => 'ملف JavaScript لإدارة المستخدمين',
                'php/api/roles.php' => 'API إدارة الأدوار',
                'php/api/users.php' => 'API إدارة المستخدمين'
            ];

            foreach ($roleFiles as $file => $description) {
                if (file_exists($file)) {
                    echo "<div class='success'>✅ $description موجود</div>";
                    $testResults["file_$file"] = 'PASS';
                } else {
                    echo "<div class='error'>❌ $description غير موجود</div>";
                    $testResults["file_$file"] = 'FAIL';
                    $overallStatus = 'error';
                }
            }

            // Check if role management functions exist in JS file
            if (file_exists('admin/js/users-management.js')) {
                $jsContent = file_get_contents('admin/js/users-management.js');

                $functions = [
                    'loadRolesManagementContent' => 'تحميل محتوى إدارة الأدوار',
                    'showRolesManagement' => 'عرض إدارة الأدوار',
                    'loadRolesManagementInterface' => 'تحميل واجهة إدارة الأدوار',
                    'saveRole' => 'حفظ الدور',
                    'editRole' => 'تعديل الدور',
                    'deleteRole' => 'حذف الدور'
                ];

                echo "<h4>وظائف JavaScript:</h4>";
                foreach ($functions as $func => $desc) {
                    if (strpos($jsContent, "function $func") !== false) {
                        echo "<div class='success'>✅ $desc ($func)</div>";
                        $testResults["js_function_$func"] = 'PASS';
                    } else {
                        echo "<div class='error'>❌ $desc ($func) مفقودة</div>";
                        $testResults["js_function_$func"] = 'FAIL';
                        $overallStatus = 'error';
                    }
                }
            }
            echo "</div>";

            // Test 5: User Roles Table
            echo "<div class='section'>";
            echo "<h2>👥 5. اختبار جدول أدوار المستخدمين</h2>";

            $stmt = $pdo->query("SHOW TABLES LIKE 'user_roles'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✅ جدول user_roles موجود</div>";

                $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_roles");
                $rolesCount = $stmt->fetch()['count'];
                echo "<div class='info'>📊 عدد الأدوار: $rolesCount</div>";

                if ($rolesCount > 0) {
                    $stmt = $pdo->query("SELECT name, display_name_ar, level FROM user_roles ORDER BY level DESC LIMIT 5");
                    $roles = $stmt->fetchAll();

                    echo "<h4>الأدوار الموجودة:</h4>";
                    echo "<table>";
                    echo "<tr><th>الاسم</th><th>الاسم العربي</th><th>المستوى</th></tr>";
                    foreach ($roles as $role) {
                        echo "<tr>";
                        echo "<td>{$role['name']}</td>";
                        echo "<td>{$role['display_name_ar']}</td>";
                        echo "<td>{$role['level']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }

                $testResults['user_roles_table'] = 'PASS';
            } else {
                echo "<div class='error'>❌ جدول user_roles غير موجود</div>";
                $testResults['user_roles_table'] = 'FAIL';
                $overallStatus = 'error';
            }
            echo "</div>";

            // Test 6: Users Table Structure
            echo "<div class='section'>";
            echo "<h2>👤 6. اختبار هيكل جدول المستخدمين</h2>";

            $stmt = $pdo->query("DESCRIBE users");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            $requiredColumns = ['username', 'first_name', 'last_name', 'role_id', 'email', 'password'];
            $missingColumns = array_diff($requiredColumns, $columns);

            if (empty($missingColumns)) {
                echo "<div class='success'>✅ جميع الأعمدة المطلوبة موجودة</div>";
                $testResults['users_table_structure'] = 'PASS';
            } else {
                echo "<div class='error'>❌ أعمدة مفقودة: " . implode(', ', $missingColumns) . "</div>";
                $testResults['users_table_structure'] = 'FAIL';
                $overallStatus = 'error';
            }

            // Check users count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $usersCount = $stmt->fetch()['count'];
            echo "<div class='info'>📊 عدد المستخدمين: $usersCount</div>";
            echo "</div>";

            // Test Summary
            echo "<div class='section'>";
            echo "<h2>📊 7. ملخص النتائج</h2>";

            $totalTests = count($testResults);
            $passedTests = count(array_filter($testResults, function ($result) {
                return $result === 'PASS';
            }));
            $failedTests = count(array_filter($testResults, function ($result) {
                return $result === 'FAIL';
            }));
            $warningTests = count(array_filter($testResults, function ($result) {
                return $result === 'WARNING';
            }));

            echo "<div class='test-grid'>";

            echo "<div class='test-card' style='background: #d4edda; border-color: #c3e6cb;'>";
            echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ نجح</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #155724;'>$passedTests</p>";
            echo "</div>";

            echo "<div class='test-card' style='background: #f8d7da; border-color: #f5c6cb;'>";
            echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ فشل</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #721c24;'>$failedTests</p>";
            echo "</div>";

            echo "<div class='test-card' style='background: #fff3cd; border-color: #ffeaa7;'>";
            echo "<h3 style='color: #856404; margin: 0 0 10px 0;'>⚠️ تحذير</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #856404;'>$warningTests</p>";
            echo "</div>";

            echo "<div class='test-card' style='background: #d1ecf1; border-color: #bee5eb;'>";
            echo "<h3 style='color: #0c5460; margin: 0 0 10px 0;'>📊 المجموع</h3>";
            echo "<p style='font-size: 2rem; font-weight: bold; margin: 0; color: #0c5460;'>$totalTests</p>";
            echo "</div>";

            echo "</div>";

            $successRate = round(($passedTests / $totalTests) * 100, 1);

            if ($successRate >= 90) {
                echo "<div class='success'>";
                echo "<h3>🎉 ممتاز! معدل النجاح: $successRate%</h3>";
                echo "<p>النظام يعمل بشكل ممتاز وجميع الإصلاحات الحرجة تمت بنجاح</p>";
            } elseif ($successRate >= 75) {
                echo "<div class='warning'>";
                echo "<h3>⚠️ جيد - معدل النجاح: $successRate%</h3>";
                echo "<p>النظام يعمل بشكل جيد مع بعض المشاكل البسيطة</p>";
            } else {
                echo "<div class='error'>";
                echo "<h3>❌ يحتاج إلى تحسين - معدل النجاح: $successRate%</h3>";
                echo "<p>هناك مشاكل كبيرة تحتاج إلى إصلاح فوري</p>";
            }
            echo "</div>";
            echo "</div>";

            // Quick Action Links
            echo "<div class='section info'>";
            echo "<h3>🔗 روابط سريعة للاختبار</h3>";
            echo "<a href='database/run_safe_migration.php' class='test-button'>🔧 تشغيل الترحيل الآمن</a>";
            echo "<a href='admin/index.html' class='test-button'>🏠 لوحة التحكم الرئيسية</a>";
            echo "<a href='admin/users-management-standalone.html' class='test-button'>👥 إدارة المستخدمين المستقلة</a>";
            echo "<a href='test-role-management-function.html' class='test-button'>🧪 اختبار وظائف الأدوار</a>";
            echo "<a href='database/verify_migration_cli.php' class='test-button'>📋 التحقق من الترحيل</a>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='section error'>";
            echo "<h2>❌ خطأ في التحقق</h2>";
            echo "<p>حدث خطأ أثناء التحقق من النظام: " . $e->getMessage() . "</p>";
            echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
            echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
            echo "</div>";
        }
        ?>
    </div>
</body>

</html>
