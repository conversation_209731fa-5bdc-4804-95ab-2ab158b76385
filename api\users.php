<?php

/**
 * Users API Endpoint
 * نقطة نهاية API لإدارة المستخدمين
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                getUserById($_GET['id']);
            } else {
                getUsers();
            }
            break;
        case 'POST':
            createUser();
            break;
        case 'PUT':
            updateUser();
            break;
        case 'DELETE':
            if (isset($_GET['id'])) {
                deleteUser($_GET['id']);
            }
            break;
        default:
            throw new Exception('Method not allowed');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getUsers()
{
    global $pdo;

    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 10;
    $search = $_GET['search'] ?? '';
    $role = $_GET['role'] ?? '';
    $status = $_GET['status'] ?? '';

    $offset = ($page - 1) * $limit;

    // Build WHERE clause
    $where = [];
    $params = [];

    if ($search) {
        $where[] = "(CONCAT(u.first_name, ' ', u.last_name) LIKE ? OR u.username LIKE ? OR u.email LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if ($role) {
        if (is_numeric($role)) {
            $where[] = "u.role_id = ?";
            $params[] = $role;
        } else {
            $where[] = "ur.name = ?";
            $params[] = $role;
        }
    }

    if ($status) {
        $where[] = "u.status = ?";
        $params[] = $status;
    }

    $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

    // Get total count
    $countSql = "SELECT COUNT(*) FROM users u LEFT JOIN user_roles ur ON u.role_id = ur.id $whereClause";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetchColumn();

    // Get users with role information
    $sql = "SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name, u.email,
                   ur.name as role, ur.display_name_ar as role_display, u.status, u.created_at, u.last_login,
                   u.first_name, u.last_name, u.phone, u.role_id, u.subscription_id
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            $whereClause
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?";

    $params[] = $limit;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get stats
    $statsStmt = $pdo->query("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN ur.name = 'admin' OR ur.name = 'super_admin' THEN 1 ELSE 0 END) as admin
        FROM users u
        LEFT JOIN user_roles ur ON u.role_id = ur.id
    ");
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'users' => $users,
        'total' => $total,
        'stats' => $stats
    ]);
}

function getUserById($id)
{
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        echo json_encode([
            'success' => true,
            'user' => $user
        ]);
    } else {
        throw new Exception('User not found');
    }
}

function createUser()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid input data');
    }

    // Support both old 'name' field and new consolidated structure
    $name = $input['name'] ?? $input['username'] ?? '';
    $email = $input['email'] ?? '';
    $password = $input['password'] ?? '';
    $role = $input['role'] ?? 'customer';
    $status = $input['status'] ?? 'active';

    if (!$name || !$email || !$password) {
        throw new Exception('Name/username, email and password are required');
    }

    // Check if email exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        throw new Exception('Email already exists');
    }

    // Generate username from name if not provided
    $username = $input['username'] ?? strtolower(str_replace(' ', '_', $name)) . '_' . time();

    // Check if username exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    if ($stmt->fetch()) {
        $username .= '_' . time();
    }

    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    // Split name into first and last name
    $nameParts = explode(' ', $name, 2);
    $firstName = $nameParts[0];
    $lastName = $nameParts[1] ?? '';

    // Map role to role_id
    $roleMap = ['customer' => 4, 'admin' => 2, 'editor' => 3, 'super_admin' => 1];
    $roleId = $roleMap[$role] ?? 4;

    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password, first_name, last_name, role_id, subscription_id, status, email_verified, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");

    $stmt->execute([$username, $email, $hashedPassword, $firstName, $lastName, $roleId, 1, $status, 1]);

    echo json_encode([
        'success' => true,
        'message' => 'User created successfully',
        'user_id' => $pdo->lastInsertId()
    ]);
}

function updateUser()
{
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['id'])) {
        throw new Exception('Invalid input data');
    }

    $id = $input['id'];
    $name = $input['name'] ?? $input['username'] ?? '';
    $email = $input['email'] ?? '';
    $role = $input['role'] ?? '';
    $status = $input['status'] ?? '';

    if (!$name || !$email) {
        throw new Exception('Name/username and email are required');
    }

    // Check if email exists for other users
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
    $stmt->execute([$email, $id]);
    if ($stmt->fetch()) {
        throw new Exception('Email already exists');
    }

    // Split name into first and last name
    $nameParts = explode(' ', $name, 2);
    $firstName = $nameParts[0];
    $lastName = $nameParts[1] ?? '';

    // Map role to role_id if needed
    $roleMap = ['customer' => 4, 'admin' => 2, 'editor' => 3, 'super_admin' => 1];
    $roleId = is_numeric($role) ? $role : ($roleMap[$role] ?? 4);

    // Generate username if not provided
    $username = $input['username'] ?? strtolower(str_replace(' ', '_', $name));

    $stmt = $pdo->prepare("
        UPDATE users
        SET username = ?, email = ?, first_name = ?, last_name = ?, role_id = ?, status = ?, updated_at = NOW()
        WHERE id = ?
    ");

    $stmt->execute([$username, $email, $firstName, $lastName, $roleId, $status, $id]);

    echo json_encode([
        'success' => true,
        'message' => 'User updated successfully'
    ]);
}

function deleteUser($id)
{
    global $pdo;

    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$id]);

    echo json_encode([
        'success' => true,
        'message' => 'User deleted successfully'
    ]);
}
