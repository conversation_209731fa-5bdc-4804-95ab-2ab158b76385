<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل ترحيل قاعدة البيانات - نظام إدارة المتاجر</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .success {
            color: #10b981;
            background: #d1fae5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #10b981;
        }
        .error {
            color: #ef4444;
            background: #fee2e2;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ef4444;
        }
        .info {
            color: #3b82f6;
            background: #dbeafe;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #3b82f6;
        }
        .output {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #5a67d8;
        }
        .button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏪 تشغيل ترحيل قاعدة البيانات - نظام إدارة المتاجر</h1>
        
        <div class="info">
            <h3>📋 ما سيتم إنشاؤه:</h3>
            <ul>
                <li><strong>جدول المتاجر (stores)</strong> - الجدول الرئيسي للمتاجر</li>
                <li><strong>جدول فئات المتجر (store_categories)</strong> - فئات المنتجات لكل متجر</li>
                <li><strong>جدول منتجات المتجر (store_products)</strong> - ربط المنتجات بالمتاجر</li>
                <li><strong>جدول طلبات المتجر (store_orders)</strong> - ربط الطلبات بالمتاجر</li>
                <li><strong>إضافة عمود store_id</strong> - للجداول الموجودة (produits, landing_pages)</li>
            </ul>
        </div>

        <button class="button" onclick="runMigration()" id="runBtn">🚀 تشغيل الترحيل</button>
        <button class="button" onclick="checkTables()" id="checkBtn">🔍 فحص الجداول</button>

        <div id="output"></div>
    </div>

    <script>
        async function runMigration() {
            const outputDiv = document.getElementById('output');
            const runBtn = document.getElementById('runBtn');
            
            runBtn.disabled = true;
            runBtn.textContent = '⏳ جاري التشغيل...';
            
            outputDiv.innerHTML = '<div class="info">🚀 بدء تشغيل ترحيل قاعدة البيانات...</div>';
            
            try {
                const response = await fetch('../php/migrations/create_stores_table.php');
                const text = await response.text();
                
                if (response.ok) {
                    outputDiv.innerHTML = `
                        <div class="success">✅ تم تشغيل الترحيل بنجاح!</div>
                        <div class="output">${text}</div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="error">❌ فشل في تشغيل الترحيل</div>
                        <div class="output">${text}</div>
                    `;
                }
            } catch (error) {
                outputDiv.innerHTML = `
                    <div class="error">❌ خطأ في الاتصال: ${error.message}</div>
                `;
            }
            
            runBtn.disabled = false;
            runBtn.textContent = '🚀 تشغيل الترحيل';
        }

        async function checkTables() {
            const outputDiv = document.getElementById('output');
            const checkBtn = document.getElementById('checkBtn');
            
            checkBtn.disabled = true;
            checkBtn.textContent = '⏳ جاري الفحص...';
            
            outputDiv.innerHTML = '<div class="info">🔍 فحص الجداول...</div>';
            
            try {
                const response = await fetch('../php/api/stores.php');
                const data = await response.json();
                
                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="success">✅ جدول المتاجر موجود ويعمل بشكل صحيح</div>
                        <div class="info">📊 عدد المتاجر: ${data.total || 0}</div>
                        <div class="output">${JSON.stringify(data, null, 2)}</div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="error">❌ مشكلة في جدول المتاجر: ${data.message}</div>
                    `;
                }
            } catch (error) {
                outputDiv.innerHTML = `
                    <div class="error">❌ خطأ في فحص الجداول: ${error.message}</div>
                    <div class="info">💡 قد تحتاج إلى تشغيل الترحيل أولاً</div>
                `;
            }
            
            checkBtn.disabled = false;
            checkBtn.textContent = '🔍 فحص الجداول';
        }

        // Auto-check on page load
        window.addEventListener('load', () => {
            setTimeout(checkTables, 1000);
        });
    </script>
</body>
</html>
