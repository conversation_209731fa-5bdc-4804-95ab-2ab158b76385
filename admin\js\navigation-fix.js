/**
 * Navigation Fix Script
 * Fixes navigation issues and ensures all sidebar links are clickable
 */

console.log('🔧 Navigation Fix Script loading...');

// Force navigation initialization
function forceInitNavigation() {
    console.log('🔄 Force initializing navigation...');
    
    // Find all navigation items
    const navItems = document.querySelectorAll('.admin-nav ul li, .sidebar ul li, nav ul li');
    console.log(`Found ${navItems.length} navigation items`);
    
    if (navItems.length === 0) {
        console.error('❌ No navigation items found!');
        return;
    }
    
    // Remove existing event listeners and add new ones
    navItems.forEach((item, index) => {
        console.log(`Processing navigation item ${index}:`, item);
        
        // Remove existing listeners by cloning the element
        const newItem = item.cloneNode(true);
        item.parentNode.replaceChild(newItem, item);
        
        // Add click event listener
        newItem.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('🖱️ Navigation item clicked:', this);
            
            // Handle logout
            if (this.textContent.includes('تسجيل الخروج') || this.classList.contains('logout')) {
                console.log('Logout clicked');
                if (typeof logout === 'function') {
                    logout();
                }
                return;
            }
            
            // Get section ID
            const sectionId = this.getAttribute('data-section');
            console.log('Section ID:', sectionId);
            
            if (!sectionId) {
                console.warn('No data-section attribute found');
                return;
            }
            
            // Remove active class from all items
            navItems.forEach(navItem => {
                navItem.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Show the corresponding section
            showSectionForced(sectionId);
        });
        
        // Make sure the item is clickable
        newItem.style.cursor = 'pointer';
        newItem.style.pointerEvents = 'auto';
    });
    
    console.log('✅ Navigation initialization complete');
}

// Force show section function
function showSectionForced(sectionId) {
    console.log(`🔄 Force showing section: ${sectionId}`);
    
    // Hide all sections
    const allSections = document.querySelectorAll('.content-section, .admin-section, [id$="Content"], [id$="Section"]');
    allSections.forEach(section => {
        section.style.display = 'none';
        section.classList.remove('active');
    });
    
    // Show the target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.style.display = 'block';
        targetSection.classList.add('active');
        console.log(`✅ Section ${sectionId} shown`);
    } else {
        console.warn(`⚠️ Section ${sectionId} not found, creating content...`);
        createSectionContent(sectionId);
    }
    
    // Load section-specific content
    loadSectionContentForced(sectionId);
}

// Force load section content
function loadSectionContentForced(sectionId) {
    console.log(`🔄 Force loading content for section: ${sectionId}`);
    
    try {
        switch (sectionId) {
            case 'dashboard':
                console.log('Loading dashboard...');
                if (typeof loadDashboard === 'function') {
                    loadDashboard();
                }
                break;
                
            case 'books':
                console.log('Loading products...');
                if (typeof loadProducts === 'function') {
                    loadProducts();
                } else if (typeof window.loadProducts === 'function') {
                    window.loadProducts();
                } else {
                    console.error('❌ loadProducts function not found');
                    createProductsContent();
                }
                break;
                
            case 'orders':
                console.log('Loading orders...');
                if (typeof loadOrders === 'function') {
                    loadOrders();
                } else {
                    createOrdersContent();
                }
                break;
                
            case 'landingPages':
                console.log('Loading landing pages...');
                if (typeof landingPagesManager !== 'undefined' && landingPagesManager.loadLandingPages) {
                    landingPagesManager.loadLandingPages();
                } else {
                    createLandingPagesContent();
                }
                break;
                
            case 'reports':
                console.log('Loading reports...');
                if (typeof loadReportsAndStatistics === 'function') {
                    loadReportsAndStatistics();
                } else {
                    createReportsContent();
                }
                break;
                
            default:
                console.log(`Loading default content for: ${sectionId}`);
                if (typeof window[`load${sectionId.charAt(0).toUpperCase() + sectionId.slice(1)}`] === 'function') {
                    window[`load${sectionId.charAt(0).toUpperCase() + sectionId.slice(1)}`]();
                }
        }
    } catch (error) {
        console.error(`❌ Error loading content for ${sectionId}:`, error);
    }
}

// Create section content if it doesn't exist
function createSectionContent(sectionId) {
    console.log(`🔧 Creating content for section: ${sectionId}`);
    
    const mainContent = document.querySelector('.main-content');
    if (!mainContent) {
        console.error('❌ Main content container not found');
        return;
    }
    
    // Create section element
    const section = document.createElement('div');
    section.id = sectionId;
    section.className = 'content-section active';
    section.style.display = 'block';
    
    // Add basic content
    section.innerHTML = `
        <div style="padding: 20px;">
            <h2 style="color: #2d3748; margin-bottom: 20px;">
                <i class="fas fa-${getSectionIcon(sectionId)}"></i>
                ${getSectionTitle(sectionId)}
            </h2>
            <div id="${sectionId}Content">
                <div style="text-align: center; padding: 40px; color: #4a5568;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <p>جاري تحميل المحتوى...</p>
                </div>
            </div>
        </div>
    `;
    
    mainContent.appendChild(section);
    console.log(`✅ Section ${sectionId} created`);
}

// Helper functions
function getSectionIcon(sectionId) {
    const icons = {
        'dashboard': 'tachometer-alt',
        'books': 'box',
        'orders': 'shopping-cart',
        'landingPages': 'file-alt',
        'reports': 'chart-bar',
        'settings': 'cog',
        'categories': 'tags',
        'usersManagement': 'users',
        'rolesManagement': 'user-shield',
        'securitySettings': 'shield-alt',
        'subscriptionsManagement': 'credit-card'
    };
    return icons[sectionId] || 'file';
}

function getSectionTitle(sectionId) {
    const titles = {
        'dashboard': 'لوحة المعلومات',
        'books': 'إدارة المنتجات',
        'orders': 'الطلبات',
        'landingPages': 'صفحات الهبوط',
        'reports': 'التقارير والإحصائيات',
        'settings': 'الإعدادات',
        'categories': 'إدارة الفئات',
        'usersManagement': 'إدارة المستخدمين',
        'rolesManagement': 'إدارة الأدوار',
        'securitySettings': 'إعدادات الأمان',
        'subscriptionsManagement': 'إدارة الاشتراكات'
    };
    return titles[sectionId] || sectionId;
}

// Create basic content for missing sections
function createProductsContent() {
    const container = document.getElementById('booksContent') || document.getElementById('books');
    if (container) {
        container.innerHTML = `
            <div style="padding: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>قائمة المنتجات</h3>
                    <button class="btn btn-primary" onclick="alert('إضافة منتج جديد')">
                        <i class="fas fa-plus"></i> إضافة منتج
                    </button>
                </div>
                <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <p style="text-align: center; color: #4a5568;">جاري تحميل المنتجات...</p>
                </div>
            </div>
        `;
    }
}

function createOrdersContent() {
    const container = document.getElementById('ordersContent') || document.getElementById('orders');
    if (container) {
        container.innerHTML = `
            <div style="padding: 20px;">
                <h3 style="margin-bottom: 20px;">قائمة الطلبات</h3>
                <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <p style="text-align: center; color: #4a5568;">جاري تحميل الطلبات...</p>
                </div>
            </div>
        `;
    }
}

function createLandingPagesContent() {
    const container = document.getElementById('landingPagesContent') || document.getElementById('landingPages');
    if (container) {
        container.innerHTML = `
            <div style="padding: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>صفحات الهبوط</h3>
                    <button class="btn btn-primary" onclick="alert('إضافة صفحة هبوط جديدة')">
                        <i class="fas fa-plus"></i> إضافة صفحة
                    </button>
                </div>
                <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <p style="text-align: center; color: #4a5568;">جاري تحميل صفحات الهبوط...</p>
                </div>
            </div>
        `;
    }
}

function createReportsContent() {
    const container = document.getElementById('reportsContent') || document.getElementById('reports');
    if (container) {
        container.innerHTML = `
            <div style="padding: 20px;">
                <h3 style="margin-bottom: 20px;">التقارير والإحصائيات</h3>
                <div style="background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <p style="text-align: center; color: #4a5568;">جاري تحميل التقارير...</p>
                </div>
            </div>
        `;
    }
}

// Initialize navigation when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Navigation Fix - DOM Content Loaded');
    
    // Wait a bit for other scripts to load
    setTimeout(() => {
        forceInitNavigation();
    }, 1000);
    
    // Also try again after a longer delay
    setTimeout(() => {
        forceInitNavigation();
    }, 3000);
});

// Also initialize when window loads
window.addEventListener('load', function() {
    console.log('🔧 Navigation Fix - Window Loaded');
    setTimeout(() => {
        forceInitNavigation();
    }, 500);
});

// Make functions globally available
window.forceInitNavigation = forceInitNavigation;
window.showSectionForced = showSectionForced;

console.log('✅ Navigation Fix Script loaded successfully');
