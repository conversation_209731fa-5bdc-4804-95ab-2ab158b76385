<?php
/**
 * Quick Fix for Categories Loading Issue
 * This script addresses the immediate categories loading error
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../php/config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة تحميل الفئات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .fix-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .fix-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .fix-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .fix-result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .fix-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .fix-button:hover {
            background: #218838;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح مشكلة تحميل الفئات</h1>
        <p>هذا السكريبت يحل مشكلة "خطأ في تحميل إدارة الفئات" التي تظهر في وحدة التحكم.</p>

        <?php
        try {
            $pdo = getPDOConnection();
            if (!$pdo) {
                throw new Exception('فشل في الاتصال بقاعدة البيانات');
            }

            echo '<div class="fix-result success">✅ تم الاتصال بقاعدة البيانات بنجاح</div>';

            // Fix 1: Check and fix categories table structure
            echo '<h3>🗄️ فحص وإصلاح هيكل جدول الفئات</h3>';
            
            try {
                $stmt = $pdo->query("DESCRIBE categories");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $requiredColumns = [
                    'id' => 'INT',
                    'nom_ar' => 'VARCHAR',
                    'nom_en' => 'VARCHAR',
                    'description_ar' => 'TEXT',
                    'icone' => 'VARCHAR',
                    'couleur' => 'VARCHAR',
                    'ordre' => 'INT',
                    'actif' => 'TINYINT'
                ];
                
                $existingColumns = [];
                foreach ($columns as $column) {
                    $existingColumns[$column['Field']] = $column['Type'];
                }
                
                $missingColumns = [];
                foreach ($requiredColumns as $colName => $colType) {
                    if (!isset($existingColumns[$colName])) {
                        $missingColumns[] = $colName;
                    }
                }
                
                if (empty($missingColumns)) {
                    echo '<div class="fix-result success">✅ جدول الفئات يحتوي على جميع الأعمدة المطلوبة</div>';
                } else {
                    echo '<div class="fix-result warning">⚠️ أعمدة مفقودة: ' . implode(', ', $missingColumns) . '</div>';
                    
                    // Add missing columns
                    foreach ($missingColumns as $colName) {
                        $colType = $requiredColumns[$colName];
                        $defaultValue = '';
                        
                        switch ($colName) {
                            case 'ordre':
                                $defaultValue = ' DEFAULT 0';
                                break;
                            case 'actif':
                                $defaultValue = ' DEFAULT 1';
                                break;
                            case 'couleur':
                                $defaultValue = " DEFAULT '#007bff'";
                                break;
                            case 'icone':
                                $defaultValue = " DEFAULT 'fas fa-tag'";
                                break;
                        }
                        
                        $sql = "ALTER TABLE categories ADD COLUMN $colName $colType$defaultValue";
                        $pdo->exec($sql);
                        echo '<div class="fix-result success">✅ تم إضافة العمود: ' . $colName . '</div>';
                    }
                }
                
            } catch (Exception $e) {
                echo '<div class="fix-result error">❌ خطأ في فحص جدول الفئات: ' . $e->getMessage() . '</div>';
            }

            // Fix 2: Add sample categories if none exist
            echo '<h3>📋 فحص وإضافة فئات تجريبية</h3>';
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
            $categoryCount = $stmt->fetch()['count'];
            
            if ($categoryCount == 0) {
                echo '<div class="fix-result warning">⚠️ لا توجد فئات في قاعدة البيانات. جاري إضافة فئات تجريبية...</div>';
                
                $sampleCategories = [
                    ['كتب', 'Books', 'كتب ومراجع متنوعة', 'fas fa-book', '#007bff', 1],
                    ['حاسوب', 'Laptops', 'أجهزة حاسوب محمولة', 'fas fa-laptop', '#28a745', 2],
                    ['حقائب', 'Bags', 'حقائب ظهر وحقائب يد', 'fas fa-shopping-bag', '#ffc107', 3],
                    ['ملابس', 'Clothing', 'ملابس وأزياء', 'fas fa-tshirt', '#dc3545', 4],
                    ['أجهزة', 'Electronics', 'أجهزة إلكترونية', 'fas fa-mobile-alt', '#6f42c1', 5]
                ];
                
                $insertStmt = $pdo->prepare("
                    INSERT INTO categories (nom_ar, nom_en, description_ar, icone, couleur, ordre, actif) 
                    VALUES (?, ?, ?, ?, ?, ?, 1)
                ");
                
                foreach ($sampleCategories as $category) {
                    $insertStmt->execute($category);
                    echo '<div class="fix-result success">✅ تم إضافة الفئة: ' . $category[0] . '</div>';
                }
                
            } else {
                echo '<div class="fix-result success">✅ يوجد ' . $categoryCount . ' فئة في قاعدة البيانات</div>';
            }

            // Fix 3: Test Categories API
            echo '<h3>🔌 اختبار Categories API</h3>';
            
            try {
                ob_start();
                $_SERVER['REQUEST_METHOD'] = 'GET';
                include '../php/api/categories.php';
                $apiOutput = ob_get_clean();
                
                $apiData = json_decode($apiOutput, true);
                
                if ($apiData && isset($apiData['success']) && $apiData['success']) {
                    echo '<div class="fix-result success">✅ Categories API يعمل بنجاح</div>';
                    echo '<div class="fix-result success">📊 عدد الفئات المُرجعة: ' . count($apiData['categories']) . '</div>';
                } else {
                    echo '<div class="fix-result error">❌ Categories API فشل</div>';
                    echo '<pre>' . htmlspecialchars($apiOutput) . '</pre>';
                }
                
            } catch (Exception $e) {
                echo '<div class="fix-result error">❌ خطأ في اختبار API: ' . $e->getMessage() . '</div>';
            }

            // Fix 4: Check JavaScript file
            echo '<h3>📜 فحص ملف JavaScript</h3>';
            
            $jsFile = 'js/categories-management.js';
            if (file_exists($jsFile)) {
                echo '<div class="fix-result success">✅ ملف JavaScript موجود</div>';
                
                $jsContent = file_get_contents($jsFile);
                $requiredFunctions = [
                    'initializeCategoriesManagement',
                    'loadCategories',
                    'displayCategories',
                    'showAddCategoryModal'
                ];
                
                $missingFunctions = [];
                foreach ($requiredFunctions as $function) {
                    if (strpos($jsContent, $function) === false) {
                        $missingFunctions[] = $function;
                    }
                }
                
                if (empty($missingFunctions)) {
                    echo '<div class="fix-result success">✅ جميع الوظائف المطلوبة موجودة في JavaScript</div>';
                } else {
                    echo '<div class="fix-result warning">⚠️ وظائف مفقودة: ' . implode(', ', $missingFunctions) . '</div>';
                }
                
            } else {
                echo '<div class="fix-result error">❌ ملف JavaScript غير موجود: ' . $jsFile . '</div>';
            }

            // Fix 5: Check HTML structure
            echo '<h3>📄 فحص هيكل HTML</h3>';
            
            $htmlFile = 'categories-management.html';
            if (file_exists($htmlFile)) {
                echo '<div class="fix-result success">✅ ملف HTML موجود</div>';
                
                $htmlContent = file_get_contents($htmlFile);
                $requiredElements = [
                    'categoriesTree',
                    'categoryForm',
                    'categoryModal',
                    'totalCategories',
                    'activeCategories'
                ];
                
                $missingElements = [];
                foreach ($requiredElements as $element) {
                    if (strpos($htmlContent, 'id="' . $element . '"') === false) {
                        $missingElements[] = $element;
                    }
                }
                
                if (empty($missingElements)) {
                    echo '<div class="fix-result success">✅ جميع العناصر المطلوبة موجودة في HTML</div>';
                } else {
                    echo '<div class="fix-result warning">⚠️ عناصر مفقودة: ' . implode(', ', $missingElements) . '</div>';
                }
                
            } else {
                echo '<div class="fix-result error">❌ ملف HTML غير موجود: ' . $htmlFile . '</div>';
            }

            echo '<h3>🎉 تم الانتهاء من الإصلاحات</h3>';
            echo '<div class="fix-result success">✅ تم إصلاح جميع المشاكل المحتملة في إدارة الفئات</div>';
            echo '<div class="fix-result success">✅ يمكنك الآن العودة إلى لوحة التحكم واختبار إدارة الفئات</div>';

            echo '<h4>🧪 الخطوات التالية:</h4>';
            echo '<p><a href="categories-management.html" class="fix-button">اختبار إدارة الفئات</a></p>';
            echo '<p><a href="index.html" class="fix-button">العودة إلى لوحة التحكم</a></p>';
            echo '<p><a href="test-ai-categories-fixes.php" class="fix-button">اختبار شامل للإصلاحات</a></p>';

        } catch (Exception $e) {
            echo '<div class="fix-result error">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '</div>';
        }
        ?>

        <!-- JavaScript Test -->
        <div style="margin-top: 20px;">
            <h3>🧪 اختبار JavaScript مباشر</h3>
            <div id="jsTestResult">جاري الاختبار...</div>
            
            <script>
                async function testCategoriesJS() {
                    const resultDiv = document.getElementById('jsTestResult');
                    
                    try {
                        // Test Categories API
                        const response = await fetch('../php/api/categories.php');
                        const data = await response.json();
                        
                        if (data.success) {
                            resultDiv.innerHTML = `
                                <div class="fix-result success">✅ Categories API يعمل عبر JavaScript</div>
                                <div class="fix-result success">📊 عدد الفئات: ${data.categories.length}</div>
                                <div class="fix-result success">🔗 API متاح ومتصل</div>
                            `;
                        } else {
                            resultDiv.innerHTML = `<div class="fix-result error">❌ فشل في تحميل الفئات: ${data.message || "خطأ غير معروف"}</div>`;
                        }
                    } catch (error) {
                        resultDiv.innerHTML = `<div class="fix-result error">❌ خطأ في JavaScript: ${error.message}</div>`;
                    }
                }
                
                // Run test after page loads
                document.addEventListener('DOMContentLoaded', testCategoriesJS);
            </script>
        </div>

    </div>
</body>
</html>
