<?php
/**
 * Comprehensive System Test
 * اختبار شامل للنظام
 * 
 * This script tests the entire system after database consolidation
 */

session_start();
require_once '../config/config.php';

// Set execution time limit
set_time_limit(300);

// HTML header
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل للنظام</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .test-item { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; border-radius: 4px; }
        .success { border-left-color: #28a745; background: #d4edda; color: #155724; }
        .error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }
        .warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; color: #0c5460; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; font-size: 12px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 8px 12px; border: 1px solid #ddd; text-align: right; }
        th { background: #f8f9fa; font-weight: bold; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .summary-item { flex: 1; padding: 15px; border-radius: 8px; text-align: center; }
        .summary-success { background: #d4edda; color: #155724; }
        .summary-error { background: #f8d7da; color: #721c24; }
        .summary-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار شامل للنظام</h1>
            <p>فحص شامل للنظام بعد توحيد قاعدة البيانات</p>
        </div>

<?php

class ComprehensiveSystemTest {
    private $pdo;
    private $results = [];
    private $passedTests = 0;
    private $failedTests = 0;
    private $warningTests = 0;
    
    public function __construct() {
        try {
            $dbConfig = Config::getDbConfig();
            $dsn = sprintf(
                "mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4",
                $dbConfig['host'],
                $dbConfig['port'],
                $dbConfig['database']
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ];
            
            $this->pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $options);
            $this->log('success', 'تم الاتصال بقاعدة البيانات بنجاح');
        } catch (Exception $e) {
            $this->log('error', 'فشل الاتصال بقاعدة البيانات: ' . $e->getMessage());
            throw $e;
        }
    }
    
    private function log($type, $message, $details = null) {
        $this->results[] = [
            'type' => $type,
            'message' => $message,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Count test results
        switch ($type) {
            case 'success':
                $this->passedTests++;
                break;
            case 'error':
                $this->failedTests++;
                break;
            case 'warning':
                $this->warningTests++;
                break;
        }
        
        // Output immediately for real-time feedback
        $class = $type;
        echo "<div class='test-item $class'>";
        echo "<strong>" . date('H:i:s') . "</strong> - $message";
        if ($details) {
            echo "<div class='code'>$details</div>";
        }
        echo "</div>";
        flush();
    }
    
    public function testDatabaseStructure() {
        $this->log('info', '🗄️ اختبار بنية قاعدة البيانات...');
        
        try {
            // Test consolidated users table
            $stmt = $this->pdo->query("DESCRIBE users");
            $userColumns = $stmt->fetchAll();
            
            $requiredColumns = ['id', 'username', 'email', 'password', 'first_name', 'last_name', 'status', 'created_at'];
            $existingColumns = array_column($userColumns, 'Field');
            
            $missingColumns = array_diff($requiredColumns, $existingColumns);
            if (empty($missingColumns)) {
                $this->log('success', '✅ جدول users يحتوي على جميع الأعمدة المطلوبة');
            } else {
                $this->log('error', '❌ أعمدة مفقودة في جدول users: ' . implode(', ', $missingColumns));
            }
            
            // Check for migration tracking fields
            if (in_array('migrated_from', $existingColumns)) {
                $this->log('success', '✅ حقول تتبع الترحيل موجودة');
            } else {
                $this->log('warning', '⚠️ حقول تتبع الترحيل غير موجودة (قد يكون الترحيل لم يتم بعد)');
            }
            
            // Test admins table
            $stmt = $this->pdo->query("DESCRIBE admins");
            $adminColumns = $stmt->fetchAll();
            
            if (count($adminColumns) > 0) {
                $this->log('success', '✅ جدول admins موجود ومنفصل');
            } else {
                $this->log('error', '❌ جدول admins غير موجود');
            }
            
            // Check if utilisateurs table still exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'utilisateurs'");
            if ($stmt->rowCount() > 0) {
                $this->log('warning', '⚠️ جدول utilisateurs ما زال موجوداً (يجب حذفه بعد الترحيل)');
            } else {
                $this->log('success', '✅ جدول utilisateurs تم حذفه بنجاح');
            }
            
        } catch (Exception $e) {
            $this->log('error', 'خطأ في اختبار بنية قاعدة البيانات: ' . $e->getMessage());
        }
    }
    
    public function testUserData() {
        $this->log('info', '👥 اختبار بيانات المستخدمين...');
        
        try {
            // Count users
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users");
            $userCount = $stmt->fetch()['count'];
            
            if ($userCount > 0) {
                $this->log('success', "✅ يوجد $userCount مستخدم في النظام");
                
                // Check for demo user
                $stmt = $this->pdo->prepare("SELECT * FROM users WHERE email = ?");
                $stmt->execute(['<EMAIL>']);
                $demoUser = $stmt->fetch();
                
                if ($demoUser) {
                    $this->log('success', '✅ المستخدم التجريبي موجود');
                    $this->log('info', 'بيانات المستخدم التجريبي: ' . $demoUser['username'] . ' (' . $demoUser['email'] . ')');
                } else {
                    $this->log('warning', '⚠️ المستخدم التجريبي غير موجود');
                }
                
                // Check migration data
                $stmt = $this->pdo->query("SELECT migrated_from, COUNT(*) as count FROM users GROUP BY migrated_from");
                $migrationStats = $stmt->fetchAll();
                
                foreach ($migrationStats as $stat) {
                    $source = $stat['migrated_from'] ?: 'غير محدد';
                    $this->log('info', "مستخدمين مرحلين من $source: " . $stat['count']);
                }
                
            } else {
                $this->log('error', '❌ لا يوجد مستخدمين في النظام');
            }
            
            // Count admins
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM admins");
            $adminCount = $stmt->fetch()['count'];
            
            if ($adminCount > 0) {
                $this->log('success', "✅ يوجد $adminCount مدير في النظام");
            } else {
                $this->log('warning', '⚠️ لا يوجد مديرين في النظام');
            }
            
        } catch (Exception $e) {
            $this->log('error', 'خطأ في اختبار بيانات المستخدمين: ' . $e->getMessage());
        }
    }
    
    public function testAPIEndpoints() {
        $this->log('info', '🌐 اختبار نقاط النهاية للـ API...');
        
        $endpoints = [
            'php/api/users.php?action=list' => 'Users API',
            'php/api/user-auth.php?action=check' => 'User Auth API',
            'php/api/admin-dashboard.php?action=users' => 'Admin Dashboard API',
            'php/api/stores.php' => 'Stores API',
            'admin/php/users_management.php?action=get_all' => 'Users Management API'
        ];
        
        foreach ($endpoints as $endpoint => $name) {
            try {
                $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/../' . $endpoint;
                
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 10,
                        'ignore_errors' => true
                    ]
                ]);
                
                $response = @file_get_contents($url, false, $context);
                
                if ($response !== false) {
                    $data = json_decode($response, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        if (isset($data['success']) && $data['success']) {
                            $this->log('success', "✅ $name يعمل بشكل صحيح");
                        } else {
                            $this->log('warning', "⚠️ $name يعيد خطأ: " . ($data['message'] ?? 'غير محدد'));
                        }
                    } else {
                        $this->log('warning', "⚠️ $name يعيد استجابة غير صالحة");
                    }
                } else {
                    $this->log('error', "❌ $name غير متاح");
                }
                
            } catch (Exception $e) {
                $this->log('error', "❌ خطأ في اختبار $name: " . $e->getMessage());
            }
        }
    }
    
    public function runAllTests() {
        $this->log('info', '🚀 بدء الاختبار الشامل للنظام...');
        
        $this->testDatabaseStructure();
        $this->testUserData();
        $this->testAPIEndpoints();
        
        return [
            'passed' => $this->passedTests,
            'failed' => $this->failedTests,
            'warnings' => $this->warningTests,
            'total' => $this->passedTests + $this->failedTests + $this->warningTests
        ];
    }
}

// Run the comprehensive test
try {
    $tester = new ComprehensiveSystemTest();
    $summary = $tester->runAllTests();
    
    echo "<div class='summary'>";
    echo "<div class='summary-item summary-success'>";
    echo "<h3>✅ نجح</h3>";
    echo "<h2>{$summary['passed']}</h2>";
    echo "</div>";
    
    echo "<div class='summary-item summary-warning'>";
    echo "<h3>⚠️ تحذيرات</h3>";
    echo "<h2>{$summary['warnings']}</h2>";
    echo "</div>";
    
    echo "<div class='summary-item summary-error'>";
    echo "<h3>❌ فشل</h3>";
    echo "<h2>{$summary['failed']}</h2>";
    echo "</div>";
    echo "</div>";
    
    if ($summary['failed'] == 0) {
        echo "<div class='test-item success'>";
        echo "<h3>🎉 تم اجتياز جميع الاختبارات الأساسية!</h3>";
        echo "<p>النظام جاهز للاستخدام بعد توحيد قاعدة البيانات.</p>";
        echo "</div>";
    } else {
        echo "<div class='test-item error'>";
        echo "<h3>⚠️ يوجد مشاكل تحتاج إلى إصلاح</h3>";
        echo "<p>يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل استخدام النظام.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='test-item error'>";
    echo "<h3>❌ فشل في تشغيل الاختبارات</h3>";
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

        <div style="text-align: center; margin: 30px 0;">
            <a href="database-consolidation-migration.php" class="btn btn-primary">
                🔄 تشغيل ترحيل قاعدة البيانات
            </a>
            <a href="users-management-standalone.html" class="btn btn-success">
                👥 اختبار إدارة المستخدمين
            </a>
            <a href="index.html" class="btn btn-primary">
                🏠 العودة للوحة الإدارة
            </a>
        </div>
    </div>
</body>
</html>
