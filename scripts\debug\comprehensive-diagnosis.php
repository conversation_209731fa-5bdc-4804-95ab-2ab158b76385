<?php
require_once __DIR__ . '/../php/config.php';

echo "🔍 COMPREHENSIVE SYSTEM DIAGNOSIS\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    $pdo = getPDOConnection();
    
    // ISSUE 1: Admin Panel Store Management
    echo "🏪 ISSUE 1: ADMIN PANEL STORE MANAGEMENT\n";
    echo "-" . str_repeat("-", 45) . "\n";
    
    // Test stores API
    echo "📡 Testing Stores API...\n";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/php/api/stores.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $apiResponse = curl_exec($ch);
    $apiHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "   HTTP Status: {$apiHttpCode}\n";
    
    if ($apiHttpCode === 200) {
        $apiData = json_decode($apiResponse, true);
        if ($apiData && $apiData['success']) {
            echo "   ✅ API Response: SUCCESS\n";
            echo "   📊 Total stores: {$apiData['total']}\n";
            echo "   📋 Message: {$apiData['message']}\n";
        } else {
            echo "   ❌ API Response: INVALID JSON\n";
            echo "   📄 Raw response: " . substr($apiResponse, 0, 200) . "...\n";
        }
    } else {
        echo "   ❌ API Response: HTTP ERROR\n";
        echo "   📄 Response: " . substr($apiResponse, 0, 200) . "...\n";
    }
    
    // Check JavaScript files
    echo "\n📜 Checking JavaScript Files...\n";
    $jsFiles = [
        'admin/js/stores-management.js' => 'Stores Management Script',
        'admin/js/admin.js' => 'Main Admin Script'
    ];
    
    foreach ($jsFiles as $file => $description) {
        if (file_exists($file)) {
            echo "   ✅ {$description}: EXISTS\n";
            
            // Check for key functions
            $content = file_get_contents($file);
            if (strpos($content, 'initializeStoresManagement') !== false) {
                echo "      ✅ Contains initializeStoresManagement function\n";
            }
            if (strpos($content, 'loadStores') !== false) {
                echo "      ✅ Contains loadStores function\n";
            }
        } else {
            echo "   ❌ {$description}: MISSING\n";
        }
    }
    
    echo "\n";
    
    // ISSUE 2: Demo Store Product Filtering
    echo "🛍️ ISSUE 2: DEMO STORE PRODUCT FILTERING\n";
    echo "-" . str_repeat("-", 45) . "\n";
    
    // Check database structure
    echo "🗄️ Database Structure Analysis...\n";
    
    // Check stores table
    $stmt = $pdo->query("SELECT * FROM stores WHERE store_slug = 'mossaab-store'");
    $demoStore = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($demoStore) {
        echo "   ✅ Demo store found:\n";
        echo "      ID: {$demoStore['id']}\n";
        echo "      Name: {$demoStore['store_name']}\n";
        echo "      Status: {$demoStore['status']}\n";
        echo "      User ID: {$demoStore['user_id']}\n";
    } else {
        echo "   ❌ Demo store NOT FOUND\n";
    }
    
    // Check products table structure
    echo "\n📦 Products Table Analysis...\n";
    $stmt = $pdo->query("DESCRIBE produits");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasStoreId = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'store_id') {
            $hasStoreId = true;
            echo "   ✅ store_id column exists: {$column['Type']}\n";
            break;
        }
    }
    
    if (!$hasStoreId) {
        echo "   ❌ store_id column MISSING from produits table\n";
    }
    
    // Check store_products table
    echo "\n🔗 Store-Products Relationship...\n";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM store_products");
        $storeProductsCount = $stmt->fetchColumn();
        echo "   📊 store_products table rows: {$storeProductsCount}\n";
        
        if ($storeProductsCount === 0) {
            echo "   ⚠️ No relationships in store_products table\n";
        }
    } catch (Exception $e) {
        echo "   ❌ store_products table doesn't exist or error: " . $e->getMessage() . "\n";
    }
    
    // Check current product distribution
    echo "\n📊 Current Product Distribution...\n";
    
    if ($demoStore) {
        // Products with store_id = demo store
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM produits WHERE store_id = ? AND actif = 1");
        $stmt->execute([$demoStore['id']]);
        $storeSpecific = $stmt->fetchColumn();
        
        // Products with store_id = NULL (global)
        $stmt = $pdo->query("SELECT COUNT(*) FROM produits WHERE store_id IS NULL AND actif = 1");
        $globalProducts = $stmt->fetchColumn();
        
        // Total active products
        $stmt = $pdo->query("SELECT COUNT(*) FROM produits WHERE actif = 1");
        $totalProducts = $stmt->fetchColumn();
        
        echo "   📈 Store-specific products (store_id = {$demoStore['id']}): {$storeSpecific}\n";
        echo "   🌐 Global products (store_id = NULL): {$globalProducts}\n";
        echo "   📊 Total active products: {$totalProducts}\n";
        
        if ($storeSpecific === 0) {
            echo "   ⚠️ ISSUE: No store-specific products found!\n";
        }
        
        if ($totalProducts === 45 && $storeSpecific < 10) {
            echo "   ⚠️ ISSUE: Expected 10 store-specific products, found {$storeSpecific}\n";
        }
    }
    
    // Check store.php query logic
    echo "\n🔍 Store Page Query Analysis...\n";
    
    if (file_exists('store.php')) {
        $storePhpContent = file_get_contents('store.php');
        
        // Check for product query
        if (strpos($storePhpContent, 'store_id = ?') !== false) {
            echo "   ✅ store.php contains store_id filtering\n";
        } else {
            echo "   ❌ store.php missing store_id filtering\n";
        }
        
        // Check for OR store_id IS NULL
        if (strpos($storePhpContent, 'store_id IS NULL') !== false) {
            echo "   ✅ store.php includes global products (store_id IS NULL)\n";
        } else {
            echo "   ❌ store.php missing global products inclusion\n";
        }
    } else {
        echo "   ❌ store.php file not found\n";
    }
    
    // RECOMMENDATIONS
    echo "\n🎯 DIAGNOSIS SUMMARY & RECOMMENDATIONS\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    echo "\n📋 ISSUE 1 - Admin Panel Store Management:\n";
    if ($apiHttpCode !== 200) {
        echo "   ❌ Stores API not working - needs fixing\n";
        echo "   🔧 Recommendation: Check API endpoint and database connection\n";
    } else {
        echo "   ✅ Stores API working\n";
        echo "   🔧 Recommendation: Check JavaScript initialization and HTML loading\n";
    }
    
    echo "\n📋 ISSUE 2 - Demo Store Product Filtering:\n";
    if ($storeSpecific === 0) {
        echo "   ❌ No store-specific products found\n";
        echo "   🔧 Recommendation: Create proper store-product relationships\n";
    } elseif ($storeSpecific < 10) {
        echo "   ⚠️ Insufficient store-specific products ({$storeSpecific}/10)\n";
        echo "   🔧 Recommendation: Add more store-specific products\n";
    } else {
        echo "   ✅ Store-specific products exist ({$storeSpecific})\n";
        echo "   🔧 Recommendation: Check store.php filtering logic\n";
    }
    
    if ($totalProducts === 45) {
        echo "   ⚠️ Store showing all 45 products instead of filtered subset\n";
        echo "   🔧 Recommendation: Fix product filtering in store.php\n";
    }
    
} catch (Exception $e) {
    echo "❌ Critical Error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "Diagnosis completed at " . date('Y-m-d H:i:s') . "\n";
?>
