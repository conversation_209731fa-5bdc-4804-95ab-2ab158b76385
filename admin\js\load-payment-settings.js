/**
 * Payment Settings Content Loader
 */

function loadPaymentSettingsContent() {
    console.log('Loading payment settings content...');

    // Get the container for payment settings
    const container = document.getElementById('paymentSettingsContent');
    if (!container) {
        console.error('Payment settings container not found');
        return;
    }

    // Load the payment settings HTML content
    fetch('payment-settings.html')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            // Create a temporary container to parse the HTML
            const temp = document.createElement('div');
            temp.innerHTML = html;

            // Extract the main content from the loaded HTML
            const paymentSettingsContent = temp.querySelector('.payment-settings-content');
            if (!paymentSettingsContent) {
                throw new Error('Payment settings content not found in the loaded HTML');
            }

            // Update the container with the new content
            container.innerHTML = paymentSettingsContent.innerHTML;

            // Initialize payment settings functionality
            if (typeof initializePaymentSettings === 'function') {
                initializePaymentSettings();
            }

            console.log('✅ Payment settings content loaded successfully');
        })
        .catch(error => {
            console.error('Error loading payment settings:', error);
            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    حدث خطأ أثناء تحميل إعدادات الدفع. يرجى تحديث الصفحة والمحاولة مرة أخرى.
                </div>
            `;
        });
}