<?php
/**
 * Debug API Issues
 * Comprehensive testing and debugging of API endpoints
 */

require_once 'php/config.php';

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>تشخيص مشاكل APIs</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
        .api-test { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .response-code { padding: 5px 10px; border-radius: 3px; color: white; font-weight: bold; }
        .code-200 { background: #28a745; }
        .code-500 { background: #dc3545; }
        .code-0 { background: #6c757d; }
        .code-other { background: #ffc107; color: #212529; }
        .json-output { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; font-size: 12px; }
        .timing { color: #6c757d; font-size: 12px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔍 تشخيص مشاكل APIs</h1>";

// Function to test API endpoint
function testApiEndpoint($url, $method = 'GET', $data = null, $timeout = 10) {
    $start_time = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'API Debugger/1.0');
    
    if ($method === 'POST' && $data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    $end_time = microtime(true);
    $duration = round(($end_time - $start_time) * 1000, 2);
    
    return [
        'url' => $url,
        'method' => $method,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error,
        'duration_ms' => $duration,
        'info' => $info
    ];
}

// Function to get response code class
function getResponseCodeClass($code) {
    if ($code == 200) return 'code-200';
    if ($code >= 500) return 'code-500';
    if ($code == 0) return 'code-0';
    return 'code-other';
}

try {
    echo "<div class='section'>";
    echo "<h2>1️⃣ اختبار APIs الأساسية</h2>";
    
    $base_url = 'http://localhost:8000';
    
    $apis_to_test = [
        [
            'name' => 'Templates API',
            'url' => $base_url . '/php/api/templates.php?action=get_templates',
            'method' => 'GET'
        ],
        [
            'name' => 'Landing Pages API',
            'url' => $base_url . '/php/api/landing-pages.php',
            'method' => 'GET'
        ],
        [
            'name' => 'User Auth API',
            'url' => $base_url . '/php/api/user-auth.php?action=check',
            'method' => 'GET'
        ],
        [
            'name' => 'Users API',
            'url' => $base_url . '/php/api/users.php?action=list',
            'method' => 'GET'
        ],
        [
            'name' => 'Subscription Limits API',
            'url' => $base_url . '/php/api/subscription-limits.php?action=limits',
            'method' => 'GET'
        ],
        [
            'name' => 'Products API',
            'url' => $base_url . '/php/api/products.php',
            'method' => 'GET'
        ]
    ];
    
    foreach ($apis_to_test as $api) {
        echo "<div class='api-test'>";
        echo "<h4>{$api['name']}</h4>";
        
        $result = testApiEndpoint($api['url'], $api['method']);
        
        $code_class = getResponseCodeClass($result['http_code']);
        echo "<p>الرابط: <code>{$api['url']}</code></p>";
        echo "<p>رمز الاستجابة: <span class='response-code {$code_class}'>{$result['http_code']}</span></p>";
        echo "<p class='timing'>الوقت: {$result['duration_ms']} مللي ثانية</p>";
        
        if ($result['error']) {
            echo "<div class='error'>❌ خطأ cURL: {$result['error']}</div>";
        }
        
        if ($result['http_code'] == 200) {
            echo "<div class='success'>✅ API يعمل بشكل صحيح</div>";
            
            $json_data = json_decode($result['response'], true);
            if ($json_data) {
                echo "<div class='info'>📄 استجابة JSON صحيحة</div>";
                if (isset($json_data['success'])) {
                    echo "<p>حالة النجاح: " . ($json_data['success'] ? 'نعم' : 'لا') . "</p>";
                }
            } else {
                echo "<div class='warning'>⚠️ استجابة غير صحيحة أو ليست JSON</div>";
            }
        } elseif ($result['http_code'] >= 500) {
            echo "<div class='error'>❌ خطأ في الخادم (500)</div>";
        } elseif ($result['http_code'] == 0) {
            echo "<div class='error'>❌ فشل في الاتصال (timeout أو مشكلة شبكة)</div>";
        } else {
            echo "<div class='warning'>⚠️ رمز استجابة غير متوقع: {$result['http_code']}</div>";
        }
        
        // Show response preview
        if ($result['response'] && strlen($result['response']) > 0) {
            $preview = substr($result['response'], 0, 500);
            echo "<details><summary>عرض الاستجابة</summary>";
            echo "<div class='json-output'>" . htmlspecialchars($preview) . "</div>";
            echo "</details>";
        }
        
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>2️⃣ اختبار APIs مباشرة (بدون cURL)</h2>";
    
    echo "<div class='api-test'>";
    echo "<h4>اختبار Templates API مباشرة</h4>";
    
    try {
        // Test templates API directly
        $_GET['action'] = 'get_templates';
        ob_start();
        include 'php/api/templates.php';
        $templates_output = ob_get_clean();
        
        echo "<div class='success'>✅ Templates API يعمل مباشرة</div>";
        echo "<div class='json-output'>" . htmlspecialchars(substr($templates_output, 0, 500)) . "</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في Templates API: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='api-test'>";
    echo "<h4>اختبار SubscriptionLimits مباشرة</h4>";
    
    try {
        require_once 'php/SubscriptionLimits.php';
        $limitsManager = new SubscriptionLimits();
        
        // Get demo user
        $pdo = getPDOConnection();
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $demoUser = $stmt->fetch();
        
        if ($demoUser) {
            $usage = $limitsManager->getUserUsage($demoUser['id']);
            echo "<div class='success'>✅ SubscriptionLimits يعمل مباشرة</div>";
            echo "<div class='info'>الاستخدام: " . json_encode($usage, JSON_UNESCAPED_UNICODE) . "</div>";
        } else {
            echo "<div class='warning'>⚠️ المستخدم التجريبي غير موجود</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في SubscriptionLimits: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>3️⃣ فحص ملفات APIs</h2>";
    
    $api_files = [
        'php/api/templates.php',
        'php/api/landing-pages.php',
        'php/api/user-auth.php',
        'php/api/users.php',
        'php/api/subscription-limits.php'
    ];
    
    foreach ($api_files as $file) {
        echo "<div class='api-test'>";
        echo "<h4>ملف: {$file}</h4>";
        
        if (file_exists($file)) {
            echo "<div class='success'>✅ الملف موجود</div>";
            
            $file_size = filesize($file);
            echo "<p>حجم الملف: " . number_format($file_size) . " بايت</p>";
            
            $file_perms = substr(sprintf('%o', fileperms($file)), -4);
            echo "<p>صلاحيات الملف: {$file_perms}</p>";
            
            // Check for syntax errors
            $syntax_check = shell_exec("php -l {$file} 2>&1");
            if (strpos($syntax_check, 'No syntax errors') !== false) {
                echo "<div class='success'>✅ لا توجد أخطاء في بناء الجملة</div>";
            } else {
                echo "<div class='error'>❌ أخطاء في بناء الجملة:</div>";
                echo "<div class='json-output'>" . htmlspecialchars($syntax_check) . "</div>";
            }
        } else {
            echo "<div class='error'>❌ الملف غير موجود</div>";
        }
        
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>4️⃣ فحص إعدادات الخادم</h2>";
    
    echo "<div class='api-test'>";
    echo "<h4>إعدادات PHP</h4>";
    
    $php_settings = [
        'max_execution_time' => ini_get('max_execution_time'),
        'memory_limit' => ini_get('memory_limit'),
        'post_max_size' => ini_get('post_max_size'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'default_socket_timeout' => ini_get('default_socket_timeout'),
        'session.gc_maxlifetime' => ini_get('session.gc_maxlifetime')
    ];
    
    echo "<ul>";
    foreach ($php_settings as $setting => $value) {
        echo "<li><strong>{$setting}:</strong> {$value}</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='api-test'>";
    echo "<h4>حالة قاعدة البيانات</h4>";
    
    try {
        $pdo = getPDOConnection();
        echo "<div class='success'>✅ الاتصال بقاعدة البيانات يعمل</div>";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT COUNT(*) as user_count FROM users");
        $result = $stmt->fetch();
        echo "<p>عدد المستخدمين: {$result['user_count']}</p>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>✅ ملخص التشخيص</h2>";
    
    echo "<div class='info'>";
    echo "<h4>🔧 الإصلاحات المقترحة:</h4>";
    echo "<ul>";
    echo "<li>تشغيل سكريبت إصلاح قاعدة البيانات: <a href='/fix-database-schema-issues.php' target='_blank'>fix-database-schema-issues.php</a></li>";
    echo "<li>التحقق من أن الخادم المحلي يعمل على المنفذ 8000</li>";
    echo "<li>فحص ملفات السجل للأخطاء</li>";
    echo "<li>التأكد من صلاحيات الملفات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h4>⚠️ ملاحظات مهمة:</h4>";
    echo "<ul>";
    echo "<li>إذا كانت APIs تعمل مباشرة ولكن تفشل عبر cURL، فالمشكلة في إعدادات الخادم</li>";
    echo "<li>أخطاء 500 تعني مشاكل في الكود أو قاعدة البيانات</li>";
    echo "<li>أخطاء timeout (رمز 0) تعني مشاكل في الشبكة أو الخادم</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ عام في التشخيص: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</div></body></html>";
?>
