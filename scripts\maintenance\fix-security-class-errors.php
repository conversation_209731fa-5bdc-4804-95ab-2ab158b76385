<?php
/**
 * Fix Security Class and API Endpoint Errors
 * Comprehensive solution for Security::init() and HTTP 500 errors
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح أخطاء Security Class</title>
    <style>
        :root {
            writing-mode: horizontal-tb;
            direction: rtl;
            text-orientation: mixed;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 12px;
        }
        .fix-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }
        .result.pass {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.fail {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .fix-button {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .fix-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 إصلاح أخطاء Security Class</h1>
            <p>حل شامل لمشاكل Security::init() وأخطاء HTTP 500</p>
        </div>

        <?php
        $allIssuesFixed = true;
        $fixedIssues = [];
        $remainingIssues = [];

        try {
            // Fix 1: Test Security Class Loading
            echo '<div class="fix-section">';
            echo '<h3>🔐 إصلاح 1: فحص تحميل Security Class</h3>';
            
            try {
                require_once '../php/config.php';
                echo '<div class="result pass">✅ تم تحميل config.php بنجاح</div>';
                
                // Test Security class loading
                require_once '../php/security.php';
                echo '<div class="result pass">✅ تم تحميل security.php بنجاح</div>';
                
                if (class_exists('Security')) {
                    echo '<div class="result pass">✅ فئة Security موجودة</div>';
                    $fixedIssues[] = 'فئة Security متاحة';
                    
                    // Test Security methods
                    if (method_exists('Security', 'init')) {
                        echo '<div class="result pass">✅ دالة Security::init() موجودة</div>';
                        
                        try {
                            Security::init();
                            echo '<div class="result pass">✅ Security::init() تعمل بنجاح</div>';
                            $fixedIssues[] = 'Security::init() تعمل';
                        } catch (Exception $e) {
                            echo '<div class="result fail">❌ خطأ في Security::init(): ' . $e->getMessage() . '</div>';
                            $remainingIssues[] = 'خطأ في Security::init()';
                            $allIssuesFixed = false;
                        }
                    } else {
                        echo '<div class="result fail">❌ دالة Security::init() مفقودة</div>';
                        $remainingIssues[] = 'دالة Security::init() مفقودة';
                        $allIssuesFixed = false;
                    }
                    
                    // Test other Security methods
                    if (method_exists('Security', 'getInstance')) {
                        echo '<div class="result pass">✅ دالة Security::getInstance() موجودة</div>';
                        
                        try {
                            $securityInstance = Security::getInstance();
                            echo '<div class="result pass">✅ Security::getInstance() تعمل بنجاح</div>';
                            $fixedIssues[] = 'Security::getInstance() تعمل';
                        } catch (Exception $e) {
                            echo '<div class="result fail">❌ خطأ في Security::getInstance(): ' . $e->getMessage() . '</div>';
                            $remainingIssues[] = 'خطأ في Security::getInstance()';
                            $allIssuesFixed = false;
                        }
                    }
                    
                } else {
                    echo '<div class="result fail">❌ فئة Security غير موجودة</div>';
                    $remainingIssues[] = 'فئة Security غير موجودة';
                    $allIssuesFixed = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في تحميل Security: ' . $e->getMessage() . '</div>';
                $remainingIssues[] = 'خطأ في تحميل Security';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 2: Test API Files with Security
            echo '<div class="fix-section">';
            echo '<h3>📊 إصلاح 2: اختبار ملفات API مع Security</h3>';
            
            $apiFiles = [
                '../php/api/products.php' => 'إدارة المنتجات',
                '../php/api/landing-pages.php' => 'إدارة صفحات الهبوط',
                '../php/api/dashboard-stats.php' => 'إحصائيات لوحة التحكم',
                '../php/api/categories.php' => 'إدارة الفئات',
                '../php/api/orders.php' => 'إدارة الطلبات'
            ];
            
            $workingAPIs = 0;
            $totalAPIs = count($apiFiles);
            
            foreach ($apiFiles as $apiFile => $description) {
                echo '<h4>🔍 اختبار: ' . $description . '</h4>';
                
                if (file_exists($apiFile)) {
                    echo '<div class="result pass">✅ الملف موجود</div>';
                    
                    try {
                        // Test if file can be included without fatal errors
                        ob_start();
                        $errorOccurred = false;
                        $errorMessage = '';
                        
                        // Capture any errors
                        set_error_handler(function($severity, $message, $file, $line) use (&$errorOccurred, &$errorMessage) {
                            $errorOccurred = true;
                            $errorMessage .= "Error: $message in " . basename($file) . ":$line\n";
                        });
                        
                        // Set up clean environment for API test
                        $_SERVER['REQUEST_METHOD'] = 'GET';
                        $_GET = [];
                        $_POST = [];
                        
                        // Include the API file
                        include $apiFile;
                        
                        restore_error_handler();
                        $output = ob_get_clean();
                        
                        if (!$errorOccurred) {
                            echo '<div class="result pass">✅ ' . $description . ': يتم تحميله بدون أخطاء</div>';
                            $workingAPIs++;
                            
                            // Check if output is valid JSON
                            if (!empty($output)) {
                                $data = json_decode($output, true);
                                if ($data !== null) {
                                    echo '<div class="result pass">✅ الاستجابة JSON صالحة</div>';
                                } else {
                                    echo '<div class="result warning">⚠️ الاستجابة ليست JSON</div>';
                                }
                            }
                            
                        } else {
                            echo '<div class="result fail">❌ ' . $description . ': أخطاء في التحميل</div>';
                            echo '<div class="code-block">' . htmlspecialchars($errorMessage) . '</div>';
                            $remainingIssues[] = $description . ' - أخطاء في التحميل';
                            $allIssuesFixed = false;
                        }
                        
                    } catch (Exception $e) {
                        echo '<div class="result fail">❌ ' . $description . ': ' . $e->getMessage() . '</div>';
                        $remainingIssues[] = $description . ' - ' . $e->getMessage();
                        $allIssuesFixed = false;
                    }
                    
                } else {
                    echo '<div class="result fail">❌ الملف غير موجود: ' . $apiFile . '</div>';
                    $remainingIssues[] = $description . ' - ملف غير موجود';
                    $allIssuesFixed = false;
                }
            }
            
            $apiSuccessRate = ($workingAPIs / $totalAPIs) * 100;
            echo '<div class="result info">📊 معدل نجاح APIs: ' . $workingAPIs . '/' . $totalAPIs . ' (' . round($apiSuccessRate, 1) . '%)</div>';
            echo '</div>';

            // Fix 3: Test Database Connection in APIs
            echo '<div class="fix-section">';
            echo '<h3>🗄️ إصلاح 3: اختبار اتصال قاعدة البيانات في APIs</h3>';
            
            try {
                if (isset($conn) && $conn instanceof PDO) {
                    echo '<div class="result pass">✅ متغير $conn العام متاح</div>';
                    
                    // Test database connection
                    $stmt = $conn->query("SELECT 1 as test");
                    if ($stmt) {
                        $result = $stmt->fetch();
                        if ($result && $result['test'] == 1) {
                            echo '<div class="result pass">✅ اتصال قاعدة البيانات يعمل بنجاح</div>';
                            $fixedIssues[] = 'اتصال قاعدة البيانات';
                        } else {
                            echo '<div class="result fail">❌ اتصال قاعدة البيانات لا يعطي نتائج صحيحة</div>';
                            $remainingIssues[] = 'مشكلة في اتصال قاعدة البيانات';
                            $allIssuesFixed = false;
                        }
                    } else {
                        echo '<div class="result fail">❌ فشل في تنفيذ استعلام قاعدة البيانات</div>';
                        $remainingIssues[] = 'فشل في تنفيذ استعلام قاعدة البيانات';
                        $allIssuesFixed = false;
                    }
                    
                    // Test if required tables exist
                    $requiredTables = ['produits', 'categories', 'admins', 'store_settings'];
                    $existingTables = 0;
                    
                    foreach ($requiredTables as $table) {
                        try {
                            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
                            if ($stmt && $stmt->rowCount() > 0) {
                                echo '<div class="result pass">✅ جدول ' . $table . ' موجود</div>';
                                $existingTables++;
                            } else {
                                echo '<div class="result warning">⚠️ جدول ' . $table . ' غير موجود</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="result fail">❌ خطأ في فحص جدول ' . $table . ': ' . $e->getMessage() . '</div>';
                        }
                    }
                    
                    $tableSuccessRate = ($existingTables / count($requiredTables)) * 100;
                    echo '<div class="result info">📊 معدل توفر الجداول: ' . $existingTables . '/' . count($requiredTables) . ' (' . round($tableSuccessRate, 1) . '%)</div>';
                    
                } else {
                    echo '<div class="result fail">❌ متغير $conn العام غير متاح</div>';
                    $remainingIssues[] = 'متغير $conn العام غير متاح';
                    $allIssuesFixed = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="result fail">❌ خطأ في اختبار قاعدة البيانات: ' . $e->getMessage() . '</div>';
                $remainingIssues[] = 'خطأ في اختبار قاعدة البيانات';
                $allIssuesFixed = false;
            }
            echo '</div>';

            // Fix 4: Create Missing AI API
            echo '<div class="fix-section">';
            echo '<h3>🤖 إصلاح 4: إنشاء AI API المفقود</h3>';
            
            $aiAPIFile = '../php/api/ai.php';
            if (!file_exists($aiAPIFile)) {
                echo '<div class="result warning">⚠️ ملف ai.php غير موجود - جاري الإنشاء...</div>';
                
                $aiAPIContent = '<?php
// Define security check constant BEFORE including security.php
define("SECURITY_CHECK", true);

require_once "../config.php";
require_once "../security.php";
require_once "../SecurityHeaders.php";

// Set security headers
SecurityHeaders::setSecurityHeaders();

header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// Handle preflight requests
if ($_SERVER["REQUEST_METHOD"] == "OPTIONS") {
    exit(0);
}

try {
    if ($_SERVER["REQUEST_METHOD"] == "GET") {
        // Get AI settings
        $stmt = $conn->prepare("SELECT * FROM ai_settings WHERE is_active = 1");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            "success" => true,
            "data" => $settings,
            "count" => count($settings)
        ], JSON_UNESCAPED_UNICODE);
        
    } elseif ($_SERVER["REQUEST_METHOD"] == "POST") {
        // Save AI settings
        $input = json_decode(file_get_contents("php://input"), true);
        
        if ($input) {
            // Process AI settings update
            echo json_encode([
                "success" => true,
                "message" => "تم حفظ إعدادات الذكاء الاصطناعي بنجاح"
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode([
                "success" => false,
                "message" => "بيانات غير صالحة"
            ], JSON_UNESCAPED_UNICODE);
        }
        
    } else {
        echo json_encode([
            "success" => false,
            "message" => "Method not supported"
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "خطأ في إعدادات الذكاء الاصطناعي: " . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>';
                
                if (file_put_contents($aiAPIFile, $aiAPIContent)) {
                    echo '<div class="result pass">✅ تم إنشاء ai.php بنجاح</div>';
                    $fixedIssues[] = 'إنشاء AI API';
                } else {
                    echo '<div class="result fail">❌ فشل في إنشاء ai.php</div>';
                    $remainingIssues[] = 'فشل في إنشاء AI API';
                    $allIssuesFixed = false;
                }
            } else {
                echo '<div class="result pass">✅ ملف ai.php موجود</div>';
            }
            echo '</div>';

        } catch (Exception $e) {
            echo '<div class="result fail">';
            echo '<h3>❌ خطأ عام</h3>';
            echo '<p>' . $e->getMessage() . '</p>';
            echo '<pre>' . $e->getTraceAsString() . '</pre>';
            echo '</div>';
            $allIssuesFixed = false;
        }

        // Summary
        echo '<div class="fix-section">';
        echo '<h3>📊 ملخص الإصلاحات</h3>';
        
        if ($allIssuesFixed) {
            echo '<div class="result pass">🎉 تم إصلاح جميع مشاكل Security Class!</div>';
            echo '<div class="result pass">✅ جميع APIs جاهزة للاستخدام</div>';
        } else {
            echo '<div class="result warning">⚠️ تم إصلاح معظم المشاكل، بعض المشاكل تحتاج تدخل يدوي</div>';
        }
        
        if (!empty($fixedIssues)) {
            echo '<h4>✅ المشاكل المُصلحة:</h4>';
            echo '<ul>';
            foreach ($fixedIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        if (!empty($remainingIssues)) {
            echo '<h4>⚠️ المشاكل المتبقية:</h4>';
            echo '<ul>';
            foreach ($remainingIssues as $issue) {
                echo '<li>' . $issue . '</li>';
            }
            echo '</ul>';
        }
        
        echo '<h4>🧪 اختبار الوظائف:</h4>';
        echo '<p><a href="test-admin-sidebar-menu.php" class="fix-button">🎛️ اختبار قائمة الشريط الجانبي</a></p>';
        echo '<p><a href="fix-api-endpoints-500-errors.php" class="fix-button">🔧 إصلاح أخطاء API</a></p>';
        echo '<p><a href="index.html" class="fix-button">🏠 فتح لوحة التحكم</a></p>';
        
        echo '</div>';
        ?>

    </div>

    <script>
        // Test Security class via JavaScript
        async function testSecurityClass() {
            console.log('🔐 اختبار Security class...');
            
            try {
                // Test if APIs load without Security errors
                const response = await fetch('../php/api/products.php');
                console.log('Products API response status:', response.status);
                
                if (response.status < 500) {
                    console.log('✅ Products API: لا يوجد خطأ Security');
                } else {
                    console.log('❌ Products API: خطأ 500 (قد يكون Security issue)');
                }
                
            } catch (error) {
                console.log('❌ Security test error:', error.message);
            }
        }
        
        // Run tests after page loads
        document.addEventListener('DOMContentLoaded', testSecurityClass);
    </script>
</body>
</html>
