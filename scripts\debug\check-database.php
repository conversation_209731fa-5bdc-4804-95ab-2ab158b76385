<?php
require_once 'php/config.php';

echo "<h1>Vérification de la Base de Données</h1>";

try {
    // Test de connexion
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
    echo "<p>Host: " . DB_HOST . ":" . DB_PORT . "</p>";
    echo "<p>Database: " . DB_NAME . "</p>";
    
    // Lister les tables
    echo "<h2>📋 Tables existantes :</h2>";
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p style='color: red;'>❌ Aucune table trouvée dans la base de données !</p>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li><strong>$table</strong></li>";
        }
        echo "</ul>";
    }
    
    // Vérifier les tables importantes
    $requiredTables = ['produits', 'commandes', 'landing_pages', 'landing_page_images'];
    
    echo "<h2>🔍 Vérification des tables importantes :</h2>";
    foreach ($requiredTables as $table) {
        if (in_array($table, $tables)) {
            echo "<p style='color: green;'>✅ Table '$table' existe</p>";
            
            // Compter les enregistrements
            $stmt = $conn->query("SELECT COUNT(*) as count FROM `$table`");
            $count = $stmt->fetch()['count'];
            echo "<p style='margin-left: 20px;'>📊 Nombre d'enregistrements : $count</p>";
            
            // Afficher la structure
            echo "<details style='margin-left: 20px;'>";
            echo "<summary>Structure de la table</summary>";
            $stmt = $conn->query("DESCRIBE `$table`");
            $columns = $stmt->fetchAll();
            echo "<table border='1' style='margin: 10px 0;'>";
            echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
            foreach ($columns as $col) {
                echo "<tr>";
                echo "<td>{$col['Field']}</td>";
                echo "<td>{$col['Type']}</td>";
                echo "<td>{$col['Null']}</td>";
                echo "<td>{$col['Key']}</td>";
                echo "<td>{$col['Default']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</details>";
            
        } else {
            echo "<p style='color: red;'>❌ Table '$table' manquante</p>";
        }
    }
    
    // Test des données de landing pages
    if (in_array('landing_pages', $tables)) {
        echo "<h2>🔍 Données des Landing Pages :</h2>";
        $stmt = $conn->query("
            SELECT lp.*, p.titre as product_title
            FROM landing_pages lp
            LEFT JOIN produits p ON lp.produit_id = p.id
            LIMIT 5
        ");
        $landingPages = $stmt->fetchAll();
        
        if (empty($landingPages)) {
            echo "<p style='color: orange;'>⚠️ Aucune landing page trouvée</p>";
        } else {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Titre</th><th>Produit</th><th>URL</th></tr>";
            foreach ($landingPages as $page) {
                echo "<tr>";
                echo "<td>{$page['id']}</td>";
                echo "<td>{$page['titre']}</td>";
                echo "<td>{$page['product_title']}</td>";
                echo "<td>{$page['lien_url']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Test des données de produits
    if (in_array('produits', $tables)) {
        echo "<h2>🔍 Données des Produits :</h2>";
        $stmt = $conn->query("SELECT * FROM produits LIMIT 5");
        $products = $stmt->fetchAll();
        
        if (empty($products)) {
            echo "<p style='color: orange;'>⚠️ Aucun produit trouvé</p>";
        } else {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Titre</th><th>Prix</th><th>Actif</th></tr>";
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>{$product['id']}</td>";
                echo "<td>{$product['titre']}</td>";
                echo "<td>{$product['prix']} دج</td>";
                echo "<td>" . ($product['actif'] ? '✅' : '❌') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Test des données de commandes
    if (in_array('commandes', $tables)) {
        echo "<h2>🔍 Données des Commandes :</h2>";
        $stmt = $conn->query("SELECT * FROM commandes LIMIT 5");
        $orders = $stmt->fetchAll();
        
        if (empty($orders)) {
            echo "<p style='color: orange;'>⚠️ Aucune commande trouvée</p>";
        } else {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Client</th><th>Montant</th><th>Statut</th><th>Date</th></tr>";
            foreach ($orders as $order) {
                echo "<tr>";
                echo "<td>{$order['id']}</td>";
                echo "<td>{$order['nom_client']}</td>";
                echo "<td>{$order['montant_total']} دج</td>";
                echo "<td>{$order['statut']}</td>";
                echo "<td>{$order['date_commande']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ Erreur de connexion à la base de données</h2>";
    echo "<p>Erreur : " . $e->getMessage() . "</p>";
    echo "<p>Code : " . $e->getCode() . "</p>";
}
?>
