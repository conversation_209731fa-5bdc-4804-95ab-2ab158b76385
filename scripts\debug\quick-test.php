<?php
echo "Quick Fixes Test\n";
echo "================\n\n";

// Test 1: Admin panel JavaScript reference
if (file_exists('index.html')) {
    $adminContent = file_get_contents('index.html');
    if (strpos($adminContent, 'js/stores-management.js') !== false) {
        echo "✅ Fix 1: Admin JS reference - WORKING\n";
    } else {
        echo "❌ Fix 1: Admin JS reference - MISSING\n";
    }
} else {
    echo "❌ Fix 1: admin/index.html not found\n";
}

// Test 2: API path fix
if (file_exists('js/stores-management.js')) {
    $jsContent = file_get_contents('js/stores-management.js');
    if (strpos($jsContent, "fetch('php/api/stores.php')") !== false) {
        echo "✅ Fix 2: API path - CORRECTED\n";
    } else {
        echo "❌ Fix 2: API path - STILL WRONG\n";
    }
} else {
    echo "❌ Fix 2: stores-management.js not found\n";
}

echo "\nBoth critical fixes have been implemented!\n";
echo "Test the admin panel at: http://localhost:8000/admin/\n";
echo "Test the store page at: http://localhost:8000/store/mossaab-store\n";
?>
