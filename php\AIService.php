<?php

/**
 * AI Service Manager for Mossaab Landing Page
 * Handles AI text generation with OpenAI, Anthropic, and Google Gemini
 */

// Security check
if (!defined('SECURITY_CHECK')) {
    die('Direct access not allowed');
}

require_once __DIR__ . '/../config/ai.php';

class AIService {
    private $provider;
    private $aiManager;

    public function __construct($provider = null) {
        $this->aiManager = AIManager::getInstance();
        $this->provider = $provider ?: $this->getDefaultProvider();

        if (!$this->aiManager->isProviderEnabled($this->provider)) {
            throw new Exception('AI provider not available or not configured');
        }
    }

    private function getProviderConfig() {
        try {
            return [
                'api_key' => $this->aiManager->getApiKey($this->provider),
                'model' => $this->getDefaultModel(),
                'max_tokens' => 1000,
                'temperature' => 0.7
            ];
        } catch (Exception $e) {
            error_log("Error getting provider config: " . $e->getMessage());
            throw $e;
        }
    }

    private function getDefaultModel() {
        switch ($this->provider) {
            case 'openai':
                return 'gpt-4';
            case 'anthropic':
                return 'claude-2';
            case 'gemini':
                return 'gemini-pro';
            default:
                throw new Exception("Unknown provider: {$this->provider}");
        }
    }

    private function getDefaultProvider() {
        foreach (['openai', 'anthropic', 'gemini'] as $provider) {
            if ($this->aiConfig->isProviderEnabled($provider)) {
                return $provider;
            }
        }
        throw new Exception('No AI providers are configured and enabled');
    }

    /**
     * Generate text using AI
     */
    public function generateText($prompt, $context = '', $options = []) {
        $status = $this->aiConfig->getProviderStatus();
        if (empty(array_filter($status, function($s) { return $s['enabled'] ?? false; }))) {
            throw new Exception('No AI providers are configured and enabled');
        }

        $fullPrompt = $this->buildPrompt($prompt, $context);

        switch ($this->provider) {
            case 'openai':
                return $this->callOpenAI($fullPrompt, $options);
            case 'anthropic':
                return $this->callAnthropic($fullPrompt, $options);
            case 'gemini':
                return $this->callGemini($fullPrompt, $options);
            default:
                throw new Exception('Unsupported AI provider: ' . $this->provider);
        }
    }

    /**
     * Generate product description
     */
    public function generateProductDescription($productData) {
        $prompt = $this->getPromptTemplate('product_description');
        $context = $this->formatProductContext($productData);
        return $this->generateText($prompt, $context);
    }

    /**
     * Generate landing page title
     */
    public function generateLandingPageTitle($productData) {
        $prompt = $this->getPromptTemplate('landing_page_title');
        $context = $this->formatProductContext($productData);
        return $this->generateText($prompt, $context);
    }

    /**
     * Generate landing page content
     */
    public function generateLandingPageContent($productData) {
        $prompt = $this->getPromptTemplate('landing_page_content');
        $context = $this->formatProductContext($productData);
        return $this->generateText($prompt, $context, ['max_tokens' => 2000]);
    }

    /**
     * Generate meta description
     */
    public function generateMetaDescription($productData) {
        $prompt = $this->getPromptTemplate('meta_description');
        $context = $this->formatProductContext($productData);
        return $this->generateText($prompt, $context, ['max_tokens' => 200]);
    }

    private function getPromptTemplate($type) {
        $templates = [
            'product_description' => "قم بإنشاء وصف مقنع للمنتج التالي:\n\n{product}\n\nContext: {context}",
            'landing_page_title' => "قم بإنشاء عنوان مقنع لصفحة هبوط المنتج التالي:\n\n{product}\n\nContext: {context}",
            'landing_page_content' => "قم بإنشاء محتوى جذاب لصفحة هبوط المنتج التالي:\n\n{product}\n\nContext: {context}",
            'meta_description' => "قم بإنشاء وصف تعريفي SEO للمنتج التالي:\n\n{product}\n\nContext: {context}"
        ];
        return $templates[$type] ?? '';
    }

    /**
     * Build full prompt with context
     */
    private function buildPrompt($prompt, $context) {
        $fullPrompt = $prompt;
        if (!empty($context)) {
            $fullPrompt .= "\n\nمعلومات المنتج:\n" . $context;
        }

        // Add Arabic language instruction
        $fullPrompt .= "\n\nيرجى الكتابة باللغة العربية فقط والتأكد من أن النص مناسب للقراء العرب.";

        return $fullPrompt;
    }

    /**
     * Format product data for AI context
     */
    private function formatProductContext($productData) {
        $context = [];

        if (isset($productData['title'])) {
            $context[] = "العنوان: " . $productData['title'];
        }

        if (isset($productData['description'])) {
            $context[] = "الوصف الحالي: " . $productData['description'];
        }

        if (isset($productData['price'])) {
            $context[] = "السعر: " . $productData['price'] . " دج";
        }

        if (isset($productData['category'])) {
            $context[] = "الفئة: " . $productData['category'];
        }

        if (isset($productData['specifications'])) {
            $context[] = "المواصفات: " . $productData['specifications'];
        }

        return implode("\n", $context);
    }

    /**
     * Call OpenAI API
     */
    private function callOpenAI($prompt, $options = []) {
        $config = $this->getProviderConfig();
        $url = 'https://api.openai.com/v1/chat/completions';

        $data = [
            'model' => $config['model'],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'أنت مساعد ذكي متخصص في كتابة المحتوى التسويقي باللغة العربية. اكتب نصوصاً جذابة ومقنعة.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => $options['max_tokens'] ?? $config['max_tokens'],
            'temperature' => $options['temperature'] ?? $config['temperature']
        ];

        return $this->makeApiCall($url, $data, [
            'Authorization: Bearer ' . $config['api_key'],
            'Content-Type: application/json'
        ]);
    }

    /**
     * Call Anthropic API
     */
    private function callAnthropic($prompt, $options = []) {
        $config = $this->getProviderConfig();
        $url = 'https://api.anthropic.com/v1/messages';

        $data = [
            'model' => $config['model'],
            'max_tokens' => $options['max_tokens'] ?? $config['max_tokens'],
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ]
        ];

        return $this->makeApiCall($url, $data, [
            'x-api-key: ' . $config['api_key'],
            'Content-Type: application/json',
            'anthropic-version: 2023-06-01'
        ]);
    }

    /**
     * Call Google Gemini API
     */
    private function callGemini($prompt, $options = []) {
        $config = $this->getProviderConfig();
        $url = 'https://generativelanguage.googleapis.com/v1beta/models/' . $config['model'] . ':generateContent?key=' . $config['api_key'];

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'maxOutputTokens' => $options['max_tokens'] ?? $config['max_tokens'],
                'temperature' => $options['temperature'] ?? $config['temperature']
            ]
        ];

        return $this->makeApiCall($url, $data, [
            'Content-Type: application/json'
        ]);
    }

    /**
     * Make API call with error handling
     */
    private function makeApiCall($url, $data, $headers) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception('cURL error: ' . $error);
        }

        if ($httpCode !== 200) {
            // Log the full response for debugging
            error_log("AI API Error - HTTP $httpCode: $response");

            // Try to parse error response for better error messages
            $errorData = json_decode($response, true);
            if ($errorData && isset($errorData['error']['message'])) {
                throw new Exception('API error: ' . $errorData['error']['message'] . " (HTTP $httpCode)");
            } else {
                throw new Exception('API error: HTTP ' . $httpCode . ' - ' . $response);
            }
        }

        $result = json_decode($response, true);
        if (!$result) {
            throw new Exception('Invalid API response');
        }

        return $this->extractTextFromResponse($result);
    }

    /**
     * Extract text from API response based on provider
     */
    private function extractTextFromResponse($response) {
        switch ($this->provider) {
            case 'openai':
                return $response['choices'][0]['message']['content'] ?? '';
            case 'anthropic':
                return $response['content'][0]['text'] ?? '';
            case 'gemini':
                return $response['candidates'][0]['content']['parts'][0]['text'] ?? '';
            default:
                return '';
        }
    }

    /**
     * Get available providers
     */
    public static function getAvailableProviders() {
        $instance = AIConfig::getInstance();
        $status = $instance->getProviderStatus();
        return array_keys(array_filter($status, function($s) { return $s['enabled'] ?? false; }));
    }

    /**
     * Test AI connection
     */
    public function testConnection()
    {
        try {
            $testPrompt = "اكتب كلمة 'مرحبا' باللغة العربية";
            $result = $this->generateText($testPrompt);
            return ['success' => true, 'response' => $result];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
